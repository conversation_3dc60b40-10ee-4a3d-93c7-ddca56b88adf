namespace :automated_tasks do
  desc "Fixes automated tasks bodies"
  task fix_task_body: :environment do
    Company.includes(automated_tasks: :task_action).find_in_batches do |company|
      company.each do |company|
        company.automated_tasks.each do |task|
          action = task.task_action
          action.value = action.value.gsub("{assignment_name}", "{task_assignees}")
          action.save!
        end
      end
    end
  end

  desc "Removes old automated tasks"
  task create_default_tasks: :environment do
    Company.includes(company_mailers: :automated_task).find_each do |company|
      admin = company.admin_company_users.order(:created_at).first
      admin_mailers = admin&.company_user_mailers&.to_a || []
      DefaultMailer.find_each do |default_mailer|
        admin_mailer = admin_mailers.select { |m| m.default_mailer_id == default_mailer.id }[0]
        company_mailer = CompanyMailer.find_or_create_by(default_mailer: default_mailer, company: company)
        company_mailer.update_automated_task
        automated_task = company_mailer.automated_task
        automated_task.disabled_at = DateTime.now if admin_mailer && automated_task && !automated_task.id && !admin_mailer.opted_in
        company_mailer.automated_task&.save!
      end
    end
  end

  desc "Change Subject Class of Event Detail"
  task change_subject_class: :environment do
    AutomatedTasks::ObjectSubjectType.find_by(name: "/a form field/ with any form value", subject_class: "FormFieldAny")&.update_columns(subject_class: "AnyObject")
  end

  desc "Removes old automated tasks"
  task clean: :environment do
    AutomatedTasks::AutomatedTask.find_each do |task|
      detail = task.task_event.event_details.first
      detail = detail.children.first while detail.children.present?

      if task.task_event.event_details.first.event_subject_type.parent.blank? || detail.event_subject_type.type == "AutomatedTasks::ValueType"
        puts "#{task.id} is old; removing..."
        task.destroy
      end
    end

    AutomatedTasks::ActionType.where(model: 'HelpTicketAssignment').destroy_all
    AutomatedTasks::EventType.where(model: 'HelpTicketAssignment').destroy_all

    ['AssignPriority', 'SetStatus', 'AssignAgent'].each do |name|
      AutomatedTasks::ActionType.where(action_class: name).find_each do |action_type|
        action_type.task_actions.find_each do |task_action|
          task_action.automated_task.destroy
        end
      end
      AutomatedTasks::ActionType.where(action_class: name).destroy_all
    end

    AutomatedTasks::EventSubjectType.find_each do |subject_type|
      subject_type.destroy if subject_type.parent.blank?
    end

    AutomatedTasks::EventType.find_each do |event_type|
      event_type.destroy if event_type.children.blank?
    end
  end

  desc "Update email select value"
  task update_email_select_value: :environment do
    automated_tasks = Msp::Templates::AutomatedTask.joins(task_actions: :action_type).where(action_type: {action_class: 'SendEmail'}).pluck(:id)
    Msp::Templates::TaskAction.where(msp_templates_automated_task_id: automated_tasks)
      .where("value LIKE ?", "%\"option\":%")
      .find_each do |action|
      parsed_value = JSON.parse(action.value)
      puts JSON.pretty_generate(action.attributes)
      parsed_value['option'] = { "value"=>1, "name"=>"Customize an email" }
      action.update!(value: parsed_value.to_json)
    end
  end

  desc "Converts automated tasks over to new format"
  task convert: :environment do
    ActiveRecord::Base.transaction do
      AutomatedTasks::AutomatedTask.find_each do |task|
        # We need to create a new automated tasks
        task_event = task.task_event
        task_action = task.task_action
        event_type = task.task_event.event_type
        model = event_type.model

        new_task = AutomatedTasks::AutomatedTask.new(company_id: task.company_id, contributor_id: task.contributor_id, order: task.order)
        new_task.task_event = AutomatedTasks::TaskEvent.new(event_type_id: task_event.event_type_id)

        subject_class = task.task_event.event_details.first.event_subject_type.subject_class
        subject_type = event_type.children.find_by(subject_class: subject_class)

        # Subject and description underwent the largest changes
        if (subject_class == 'TicketSubject' || subject_class == 'TicketDescription')
          child = task_event.event_details.first.children.first&.children&.first
          raise "Missing child for task_event #{task_event.id}; aborting..." unless child
          value = nil
          form = task.company.custom_forms.find_by(form_name: "Base Ticket")
          field = nil
          if subject_class == 'TicketSubject'
            field = form.custom_form_fields.find_by(name: 'subject')
          elsif subject_class == 'TicketDescription'
            field = form.custom_form_fields.find_by(name: 'description')
          end
          form_attributes = form.attributes.slice("id", "form_name")
          field_attributes = field.attributes.slice("id", "label", "field_attribute_type")
          value = {custom_form: form_attributes, form_field: field_attributes}.to_json

          subject_type = event_type.children.find_by(subject_class: 'TicketFormField')
          raise "Missing subject type TicketFormField" unless subject_type
          field_detail = AutomatedTasks::EventDetail.new(event_subject_type: subject_type, value: value)
          new_task.task_event.event_details << field_detail

          my_class = task_event.event_details.first.children.first.event_subject_type.subject_class
          value = task_event.event_details.first.children.first.children.first.value

          if my_class == "StringContains" || my_class == "StringEquals"
            subject_type = subject_type.children.find_by(subject_class: 'FormValueEqual')
            raise "Missing subject type FormValueEqual" unless subject_type
          elsif my_class == "StringNotContains" || my_class == "StringNotEquals"
            subject_type = subject_type.children.find_by(subject_class: 'FormValueNotEqual')
            raise "Missing subject type FormValueNotEqual" unless subject_type
          end
          task_event.event_details.first.children.first.event_subject_type
          value_detail = AutomatedTasks::EventDetail.new(event_subject_type: subject_type, value: value)
          field_detail.children << value_detail
        elsif subject_class == 'TaskDescription'
          child = task_event.event_details.first.children.first
          # This can be replaced with just any project task
          if child.event_subject_type.subject_class == "AnyString"
            subject_type = new_task.task_event.event_type.children.find_by(subject_class: 'AnyObject')
            any_detail = AutomatedTasks::EventDetail.new(event_subject_type: subject_type)
            new_task.task_event.event_details << any_detail
          else
            child = child.children.first
            raise "Missing child for task_event #{task_event.id}; aborting..." unless child
            subject_type = event_type.children.find_by(subject_class: 'TaskDescription')
            raise "Missing subject type TaskDescription" unless subject_type
            description_detail = AutomatedTasks::EventDetail.new(event_subject_type: subject_type)
            new_task.task_event.event_details << description_detail

            raise "Missing children for event #{task_event.id}" unless task_event.event_details.first&.children&.first&.event_subject_type
            my_class = task_event.event_details.first.children.first.event_subject_type.subject_class
            value = task_event.event_details.first.children.first.children.first.value
            subject_type = subject_type.children.find_by(subject_class: my_class)
            string_detail = AutomatedTasks::EventDetail.new(event_subject_type: subject_type, value: value)
            description_detail.children << string_detail
          end
        elsif model == 'HelpTicketAssignment'
          ticket_added = AutomatedTasks::EventType.find_by(event_class: 'TicketAdded')
          new_task.task_event = AutomatedTasks::TaskEvent.new(event_type: ticket_added)
          child = task_event.event_details.first
          if child.event_subject_type.subject_class == 'AnyObject'
            any_ticket_type = ticket_added.children.find_by(subject_class: 'AnyTicket')
            detail = AutomatedTasks::EventDetail.new(event_subject_type: any_ticket_type)
            new_task.task_event.event_details << detail
          elsif child.event_subject_type.subject_class == 'AgentIncluded'
            ticket_form_type = ticket_added.children.find_by(subject_class: 'TicketFormField')
            raise "Missing TicketFormField" unless ticket_form_type
            custom_form = task.company.custom_forms.find_by(form_name: 'Base Ticket')
            raise "Missing Base Ticket for company #{task.company.subdomain}" unless custom_form
            form_field = custom_form.custom_form_fields.find_by(name: 'assigned_to')
            raise "Missing Priority field for form #{custom_form.id}" unless form_field
            value = {
              custom_form: custom_form.attributes.slice('id', 'form_name'),
              form_field: form_field.attributes.slice('id', 'label', 'field_attribute_type'),
            }
            form_detail = AutomatedTasks::EventDetail.new(event_subject_type: ticket_form_type, value: value.to_json)
            new_task.task_event.event_details << form_detail
            detail = task_event.event_details.first.children.first
            value_form_type = ticket_form_type.children.find_by(subject_class: 'FormValueEqual')
            raise "Missing type FormValueEqual" unless value_form_type
            value_detail = AutomatedTasks::EventDetail.new(event_subject_type: value_form_type, value: detail.value)
            form_detail.children << value_detail
          else
            raise "Unsupported type #{child.event_subject_type.subject_class}"
          end
        elsif event_type.event_class == 'TimeEntryAdded' && event_type.children.blank?
          AutomatedTasks::EventType.where(event_class: 'TimeEntryAdded').find_each do |event_type|
            if event_type.children.present?
              new_task.task_event = AutomatedTasks::TaskEvent.new(event_type_id: event_type.id)
              task_event.event_details.each do |event_detail|
                old_subject_type = event_detail.event_subject_type
                new_subject_type = event_type.children.find_by(subject_class: old_subject_type.subject_class)
                value = event_detail.children&.first&.value
                new_task.task_event.event_details << AutomatedTasks::EventDetail.new(value: value, event_subject_type: new_subject_type)
              end
            end
          end
        elsif event_type.event_class == 'TicketPrioritized' && event_type.children.blank?
          ticket_added = AutomatedTasks::EventType.find_by(event_class: 'TicketAdded')
          new_task.task_event = AutomatedTasks::TaskEvent.new(event_type: ticket_added)
          ticket_form_type = ticket_added.children.find_by(subject_class: 'TicketFormField')
          raise "Missing TicketFormField" unless ticket_form_type
          custom_form = task.company.custom_forms.find_by(form_name: 'Base Ticket')
          raise "Missing Base Ticket for company #{task.company.subdomain}" unless custom_form
          form_field = custom_form.custom_form_fields.find_by(name: 'priority')
          raise "Missing Priority field for form #{custom_form.id}" unless form_field
          value = {
            custom_form: custom_form.attributes.slice('id', 'form_name'),
            form_field: form_field.attributes.slice('id', 'label', 'field_attribute_type'),
          }
          form_detail = AutomatedTasks::EventDetail.new(event_subject_type: ticket_form_type, value: value.to_json)
          new_task.task_event.event_details << form_detail
          subject_class = task_event.event_details.first.event_subject_type.subject_class
          raise "Unsupported type found #{subject_class}" unless subject_class == "PriorityEquals"
          detail = task_event.event_details.first.children.first

          value_form_type = ticket_form_type.children.find_by(subject_class: 'FormValueEqual')
          raise "Missing type FormValueEqual" unless value_form_type
          value_detail = AutomatedTasks::EventDetail.new(event_subject_type: value_form_type, value: detail.value)
          form_detail.children << value_detail
        else
          # Otherwise, it's just moving the value
          detail = task_event.event_details.first
          subject_type = event_type.children.find_by(subject_class: subject_class)
          raise "Missing subject type #{subject_class} for #{event_type.event_class}" unless subject_type
          event_detail = AutomatedTasks::EventDetail.new(event_subject_type: subject_type)
          new_task.task_event.event_details << event_detail

          while detail&.children.present?
            detail = detail.children.first
            if detail.value
              event_detail.value = detail.value
              detail = nil
            else
              subject_class = detail.event_subject_type.subject_class
              child_type = subject_type.children.find_by(subject_class: subject_class)
              raise "Missing subject type #{subject_class} for #{subject_type.subject_class}" unless child_type
              new_detail = AutomatedTasks::EventDetail.new(event_subject_type: child_type)
              event_detail.children << new_detail
              event_detail = new_detail
            end
          end
        end

        action_value = JSON.parse(task_action.value)
        action_class = task.task_action.action_type.action_class
        if (action_class == 'SendEmail')
          action_value['target'] ||= 'specified'
          action_type = AutomatedTasks::ActionType.find_by(model: model, action_class: 'SendEmail')
          value = action_value
        elsif (action_class == 'AssignPriority')
          # "{\"custom_form_id\":2,\"form_field_id\":16,\"form_value\":\"medium\"}"
          action_type = AutomatedTasks::ActionType.find_by(model: model, action_class: 'SetFormField')
          raise "Missing type SetFormField model #{model}" unless action_type
          custom_form = task.company.custom_forms.find_by(form_name: 'Base Ticket')
          form_field = custom_form.custom_form_fields.find_by(name: 'priority')
          value = { custom_form_id: custom_form.id, form_field_id: form_field.id, form_value: action_value['priority'] }
        elsif (action_class == 'SetStatus')
          action_type = AutomatedTasks::ActionType.find_by(model: model, action_class: 'SetFormField')
          raise "Missing type SetFormField model #{model}" unless action_type
          custom_form = task.company.custom_forms.find_by(form_name: 'Base Ticket')
          form_field = custom_form.custom_form_fields.find_by(name: 'status')
          value = { custom_form_id: custom_form.id, form_field_id: form_field.id, form_value: action_value['status'] }
        elsif (action_class == 'AssignAgent')
          action_type = AutomatedTasks::ActionType.find_by(model: model, action_class: 'SetFormField')
          raise "Missing type SetFormField model #{model}" unless action_type
          custom_form = task.company.custom_forms.find_by(form_name: 'Base Ticket')
          form_field = custom_form.custom_form_fields.find_by(name: 'assigned_to')
          value = { custom_form_id: custom_form.id, form_field_id: form_field.id, form_value: action_value }
        else
          action_type = AutomatedTasks::ActionType.find_by(model: model, action_class: action_class)
          value = action_value
          raise "Missing type #{action_class} model #{model}" unless action_type
        end
        new_task.task_action = AutomatedTasks::TaskAction.new(action_type: action_type, value: value.to_json)

        if new_task.event_type_ids != task.event_type_ids
          puts "Original ID: #{task.id}"
          puts JSON.pretty_generate(AutomatedTasks::JsonOutput.new(new_task.company).json(new_task)) if new_task.event_type_ids != task.event_type_ids
          new_task.save!
        end
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "Updates target for default comment tasks"
  task update_task_target: :environment do
    AutomatedTasks::AutomatedTask.find_each do |task|
      if task.company_mailer.present?
        task.task_event.event_details.each do |detail|
          if detail.event_subject_type.key == "comment"
            new_target = JSON.parse(task.task_action.value)
            if new_target['target'].exclude?("private")
              new_target['target'] = new_target['target'] + "," + "private"
              task.task_action.update_columns(value: (new_target.to_json))
            end
          end
        end
      end
    end
  end

  desc "Removes blank recipients from tasks"
  task update_task_recipients: :environment do
    AutomatedTasks::AutomatedTask.find_each do |task|
      task_value = JSON.parse(task.task_action.value)
      original_recipients = task_value['recipients']
      task_value['recipients'] = original_recipients.split(",").compact_blank.join(',')
      task.task_action.update_columns(value: (task_value.to_json))
    end
  end

  desc "Fixed task action model form comment events"
  task update_task_comment_event: :environment do
    action_email_id = AutomatedTasks::ActionType.find_by(name: "send an [email]", module: "help_tickets", model: "HelpTicketComment")&.id
    action_sms_id = AutomatedTasks::ActionType.find_by(name: "send a [sms]", module: "help_tickets", model: "HelpTicketComment")&.id
    tasks = AutomatedTasks::AutomatedTask.includes(task_event: :event_type).where(automated_tasks_event_types: { event_class: "CommentAdded" })
    tasks.find_each do |task|
      if task.task_action.action_type.model == "HelpTicket" && task.task_action.action_type.name = "send an [email]" && action_email_id
        task.task_action.update_columns(action_type_id: action_email_id)
      elsif task.task_action.action_type.model == "HelpTicket" && task.task_action.action_type.name = "send a [sms]" && action_sms_id
        task.task_action.update_columns(action_type_id: action_sms_id)
      end
    end
  end

  desc "Updates attributes for email automated tasks"
  task update_automated_task_attributes: :environment do
    AutomatedTasks::AutomatedTask.find_each do |task|
      action_class = task.task_action.action_type.action_class
      if (action_class == 'SendEmail' || action_class == 'SendSms')
        task_value = JSON.parse(task.task_action.value)
        task_body = task_value['body'] ? task_value['body'] : task_value['message'] if task_value
        if task_body&.include?("ticket_creator")
          task_body = task_body.gsub! 'ticket_creator', 'ticket_created_by'
          task_value['body'] ? task_value['body'] = task_body : task_value['message'] = task_body
          task.task_action.update_columns(value: (task_value.to_json))
        end
        if task_body&.include?("task_assignees")
          task_body = task_body.gsub! 'task_assignees', 'ticket_assigned_to'
          task_value['body'] ? task_value['body'] = task_body : task_value['message'] = task_body
          task.task_action.update_columns(value: (task_value.to_json))
        end
      end
    end
  end

  desc "Updates event detail value have key label_name to label"
  task update_event_detail_attributes: :environment do
    AutomatedTasks::EventDetail.where.not(value: nil).find_each do |event_detail|
      value = JSON.parse(event_detail.value)
      if value["form_field"].present? && value["form_field"].keys.include?("label_name")
        value["form_field"]["label"] = value["form_field"].delete("label_name")
        event_detail.update(value: value.to_json)
      end
    end
  end

  desc "Add more keys value into event detail value have name as key in them"
  task update_event_detail_attributes_with_name: :environment do
    AutomatedTasks::EventDetail.where.not(value: nil).find_each do |event_detail|
      value = JSON.parse(event_detail.value)
      if value["form_field"].present? && value["form_field"].keys.include?("name") && value["custom_forms"].present?
        field = CustomForm.find_by(id: value["custom_forms"][0]["id"])&.custom_form_fields&.select('id, label')&.find_by(field_attribute_type: value["form_field"]["field_attribute_type"], name: value["form_field"]["name"])
        if field.present? && field.id.present? && field.label.present?
          value["form_field"]["id"]= field.id
          value["form_field"]["label"] = field.label
          value["form_field"].delete("name")
          event_detail.update_columns(value: value.to_json)
        end
      end
    end
  end

  desc "Change custom_forms key name to custom_form and it's value from array to object"
  task update_custom_form_attributes: :environment do
    AutomatedTasks::EventDetail.where.not(value: nil).find_each do |event_detail|
      value = JSON.parse(event_detail.value)
      if value["custom_forms"].present? && value["custom_forms"].is_a?(Array)
        value["custom_form"] = value["custom_forms"][0]
        value.delete("custom_forms")
        event_detail.update_columns(value: value.to_json)
      end
    end
  end

  desc "Update automated task email body to be empty string instead of nil"
  task update_automated_task_email_body: :environment do
    tasks = AutomatedTasks::AutomatedTask.includes(task_action: :action_type).where(automated_tasks_action_types: { action_class: "SendEmail" })
    tasks.find_each do |task|
      task_value = JSON.parse(task.task_action.value)
      if task_value.present? && task_value['body'].nil?
        task_value['body'] = ""
        task.task_action.update_columns(value: (task_value.to_json))
      end
    end
  end

  desc "Run timed events based on time zones"
  task timed_tasks: :environment do
    AutomatedTasks::TimedTaskExecutor.new.call
  end

  desc "Updates TicketFormField subject data to new format"
  task update_ticket_form_fields: :environment do
    AutomatedTasks::ObjectSubjectType.where(subject_class: 'TicketFormField').find_each do |subject_type|
      subject_type.event_details.find_each do |event_detail|
        json = JSON.parse(event_detail.value)
        changed = false
        if json['custom_form']
          json['custom_forms'] = [ json['custom_form'] ]
          json.delete('custom_form')
          changed = true
        end
        if json['form_field'] && json['form_field']['id']
          id = json['form_field']['id']
          form_field = CustomFormField.find_by(id: id)
          if form_field.present?
            json['form_field'] = {
              name: form_field.name,
              field_attribute_type: form_field.field_attribute_type,
            }
            changed = true
          end
        end

        if changed
          event_detail.value = json.to_json
          event_detail.save!
        end
      end
    end
  end

  desc "Changes Custom Form to Custom Forms"
  task to_custom_forms: :environment do
    types = AutomatedTasks::EventSubjectType.where(subject_class: 'TicketFormField')
    AutomatedTasks::EventDetail.where(event_subject_type: types).where("value ilike '%custom_form\"%'").find_each do |detail|
      json = JSON.parse(detail.value)
      json['custom_forms'] = [ json['custom_form'] ] if json['custom_form'] 
      json.delete('custom_form')
      detail.value = json.to_json
      detail.save!
      putc 'X'
    end
    puts
  end

  desc "Updates TicketFormField subject data to new format"
  task remove_ids: :environment do
    keys = %w{ contributors assets vendors locations contracts }
    AutomatedTasks::EventDetail.where.not(value: nil).find_each do |detail|
      json = JSON.parse(detail.value)
      keys.each do |key|
        if json[key] && json[key][0].is_a?(Hash)
          ids = json[key].map { |o| o['id'] }.compact
          if ids.present?
            json[key] = ids
            detail.value = json.to_json
            detail.save!
            putc "x"
          end
        end
      end
    end
    puts
  end

  desc "Regenerates default autoamted tasks"
  task regenerate: :environment do
    Company.find_each do |company|
      company.company_mailers.find_each do |company_mailer|
        task = company_mailer.automated_task
        if task
          disabled_at = company_mailer.automated_task.disabled_at
          task.destroy
          company_mailer.automated_task = nil
          company_mailer.update_automated_task
          company_mailer.automated_task.disabled_at = disabled_at
          company_mailer.save!
        end
      end
    end
  end

  desc "Update comment refer to vendor value to new format"
  task update_comment_refer_to_vendor: :environment do
    AutomatedTasks::EventDetail.joins(:event_subject_type).where(automated_tasks_event_subject_types: { name: "/a comment/ that references [a vendor]" }).find_each do |event_detail|
      value = JSON.parse(event_detail.value)
      if value.present? && value["vendor"].present?
        value[:vendors] = [value[:vendors] = value.delete("vendor")]
        event_detail.update_columns(value: value.to_json)
      end
    end
  end

  desc "To update detail key name to custom_forms "
  task update_key_name: :environment do
    details = []
    event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is updated")
    event_subject_type = event_type.children.find_by(name: "/a ticket/ with {a form field}")
    event_details  = AutomatedTasks::EventDetail.where(event_subject_type: event_subject_type)

    event_details.each do |obj|
      parsed_value = JSON.parse(obj.value)
      details << obj if parsed_value["custom_form"].present? && !parsed_value["custom_form"].is_a?(Array)
    end

    details.each do |d|
      parsed_value = JSON.parse(d.value)
      form_detail = parsed_value["custom_form"]
      parsed_value["custom_forms"] = parsed_value.delete "custom_form"
      parsed_value["custom_forms"] = [form_detail]
      d.value = parsed_value.to_json
      d.save!
    end
  end

  desc "update keys from year_quarter_dates to year_dates for '/a date/ that is on the [date of the year]'"
  task update_key_to_year_dates_for_date_on_the_year_tasks: :environment do
    details = []
    event_type = AutomatedTasks::EventType.find_by(name: "{a date} occurs")
    event_subject_type = event_type.children.find_by(name: "/a date/ that is on the [date of the year]")
    event_details  = AutomatedTasks::EventDetail.where(event_subject_type: event_subject_type)

    event_details.each do |obj|
      parsed_value = JSON.parse(obj.value)
      details << obj if parsed_value["year_quarter_dates"].present?
    end

    details.each do |d|
      parsed_value = JSON.parse(d.value)
      parsed_value["year_dates"] = parsed_value.delete "year_quarter_dates"
      d.value = parsed_value.to_json
      d.save!
    end
  end

  desc "Fix survey automated task"
  task fix_survey_default_automated_task: :environment do
    event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is updated", module: 'help_tickets')
    event_subject_type = event_type.children.find_by(name: "/a ticket/ with {a form field}")
    event_details  = AutomatedTasks::EventDetail.where(event_subject_type: event_subject_type)

    default_mailer = DefaultMailer.find_by(event: "survey_sent")
    event_details.each do |obj|
      if obj.task_event.automated_task.company_mailer&.default_mailer&.event == "survey_sent"
        parsed_value = JSON.parse(obj.value)
        if parsed_value["status"].nil?
          obj.value = "{\"custom_forms\":[{\"id\":\"all\",\"is_active\":true,\"name\":\"Any Form\"}],\"form_field\":{\"name\":\"status\",\"field_attribute_type\":\"status\"},\"negate\":false,\"any\":false,\"status\":\"Closed\"}"
          obj.save!
        end
      end
    end
  end

  desc "Update actions from customFormId to customForms"
  task convert_to_custom_forms: :environment do
    AutomatedTasks::ActionType.joins(:task_actions).where(module: 'help_tickets', action_class: 'SetFormField').find_each do |action_type|
      action_type.task_actions.find_each do |task_action|
        json = JSON.parse(task_action.value)
        custom_forms = json['custom_forms']
        if !custom_forms && json['custom_form_id']
          form = CustomForm.find(json['custom_form_id'])
          custom_forms = [{
            id: form.id,
            name: form.form_name,
            is_active: form.is_active,
          }]
        end
        form_field = json['form_field']
        if !form_field && json['form_field_id']
          field = CustomFormField.find(json['form_field_id'])
          form_field = {
            field_attribute_type: field.field_attribute_type,
            name: field.name,
          }
        end

        # now let's normalize the custom form value
        task = task_action.automated_task
        any_ticket_details = task.task_event.all_event_details_by_subject_class('AnyTicket')
        ticket_form_details = task.task_event.all_event_details_by_subject_class('TicketFormField').uniq
        if any_ticket_details.present?
          custom_forms = [{
            id: 'all',
            name: 'Any Form',
          }]
        elsif ticket_form_details.present? && ticket_form_details.length > 1
          exit("Task ID #{task.id} has more than one ticket_form; exiting...")
        elsif ticket_form_details.present?
          value = JSON.parse(ticket_form_details.first.value)
          custom_forms = value['custom_forms']
        end

        if custom_forms.present? && form_field.present?
          new_json = {
            custom_forms: custom_forms,
            form_field: form_field,
            form_value: json["form_value"],
          }
          task_action.value = new_json.to_json
          task_action.save!
        end
      end
    end
  end

  desc "Update event detail value format for add comment to a ticket"
  task comment_value_format: :environment do
    automated_tasks = AutomatedTasks::EventDetail.joins(:event_subject_type).where(automated_tasks_event_subject_types: { name: "/a comment/ which includes [text]" })
    automated_tasks.each do |task|
      if task.value.present?
        new_value = JSON.parse(task.value)
        if !new_value['value'].blank?
          task.value = new_value['value'].to_json
          task.save!
        end

      end
    end
  end

  desc "Update event detail value format for add project task to a ticket"
  task task_value_format: :environment do
    automated_tasks = AutomatedTasks::EventDetail.joins(:event_subject_type).where(automated_tasks_event_subject_types: { name: "/a project task/ that has a {description}" })

    automated_tasks.each do |task|
      if task.value.present?
        new_value = JSON.parse(task.value)
        if !new_value['value'].blank?
          task.value = new_value['value'].to_json
          task.save!
        end
      end
    end
  end

  desc "remove fiscal year old format subject option"
  task remove_extra_fiscal_year_option: :environment do
    AutomatedTasks::ObjectSubjectType.find_by(name: "/a date/ that is on the [date] of fiscal year").destroy
  end

  desc "Update any value status if no other value is present"
  task update_any_value_status: :environment do
    CustomForm.find_each do |form|
      if form.automated_tasks_automated_task_id.present?
        task = form.automated_task
        task_event_detail = task.task_event.event_details.first.value
        parsed_event_detail = JSON.parse(task_event_detail)
        if !parsed_event_detail['negate'].present? && !parsed_event_detail['any'].present? && !parsed_event_detail['contributors'].present?
          task.task_event.event_details.first.update_columns(value: "{\"custom_forms\":[{\"id\":#{parsed_event_detail['custom_forms'][0]['id']},\"form_name\":\"#{parsed_event_detail['custom_forms'][0]['form_name']}\"}],\"form_field\":{\"name\":\"assigned_to\",\"field_attribute_type\":\"people_list\"},\"negate\":false,\"any\":true,\"contributors\":null}")
        end
      end
    end
  end

  desc "Update/delete automated tasks if contributor doesn't exists"
  task delete_automated_task_with_no_contributor: :environment do
    AutomatedTasks::ActionType.joins(:task_actions).where(module: 'help_tickets', action_class: 'SetFormField').find_each do |action_type|
      action_type.task_actions.find_each do |task_action|
        value = task_action.value.is_a?(String) ? JSON.parse(task_action.value) : task_action.value
        field_type = value["form_field_id"].present? ? CustomFormField.find(value['form_field_id']).field_attribute_type : value["form_field"]["field_attribute_type"]
        if field_type == "people_list"
          task_value = value["form_value"].is_a?(Hash) ? value["form_value"]["contributors"] : value["form_value"]
          task_value&.each do |contributor_id|
            contributor = Contributor.find_by(id: contributor_id)
            if !(contributor && (contributor&.company_user || contributor&.group))
              if task_value.count == 1
                task_action.automated_task.destroy
              else
                task_value.delete(contributor_id)
                task_action.value = value.as_json
                task_action.save!
              end
            end
          end
        end
      end
    end
  end

  desc 'Add external users in default automated tasks'
  task add_external_users: :environment do
    CompanyMailer.where.not(automated_tasks_automated_task_id: nil).find_each do |mailer|
      automated_task = mailer.automated_task
      next unless automated_task.present?
    
      task_action = automated_task.task_action
      value = JSON.parse(task_action.value)
      if value['target'].include?('creator')
        value['target'] << ',creator_email'
        task_action.update_columns(value: value.to_json)
      end
    end
  end

  desc 'Update name for default automated tasks send mail when ticket is created'
  task default_name_ticket_create_task: :environment do
    ticket_create_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify all people and groups that are related to a ticket of the ticket being created' })
    ticket_create_tasks.update_all(name: 'Notify Ticket Created - Email')
  end

  desc 'update name for default automated tasks send mail when comment is update'
  task default_name_comment_add_task: :environment do
    comment_added_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify all people and groups that are related to a ticket of a note being added to a ticket' })
    comment_added_tasks.update_all(name: 'Notify Comment Added - Email')
  end

  desc 'Update name for default automated tasks send mail when attachment is created'
  task default_name_attachment_add_task: :environment do
    attachment_added_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify all people and groups that are related to a ticket that an attachment is added to a ticket' })
    attachment_added_tasks.update_all(name: 'Notify Attachment Added - Email')
  end

  desc 'Update name for default automated tasks send mail when status change'
  task default_name_status_change_task: :environment do
    status_change_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify all people and groups that are related to a ticket of a change to the status of a ticket' })
    status_change_tasks.update_all(name: 'Notify Status Changes - Email')
  end

  desc 'Update name for default automated tasks send mail when status is closed'
  task default_name_closed_status_task: :environment do
    closed_status_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Automatically send a survey for a ticket when a ticket is closed to the creator of a ticket' })
    closed_status_tasks.update_all(name: 'Notify Status Closed - Email')
  end

  desc 'Update name for default automated tasks send mail when priority change'
  task default_name_priority_change_task: :environment do
    priority_change_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify all people and groups that are related to a ticket of a change to the priority of a ticket' })
    priority_change_tasks.update_all(name: 'Notify Priority Changes - Email')
  end

  desc 'Update name for default automated tasks send email for related assigned to'
  task default_name_related_assigned_to: :environment do
    assinged_to_related_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify me whenever a ticket is assigned to me' })
    assinged_to_related_tasks.update_all(name: 'Notify Assigned-To Changes - Email')
  end

  desc 'Update name for default automated tasks send email for assigned to agents'
  task default_name_assigned_to_agent: :environment do
    assigned_to_agent_tasks = AutomatedTasks::AutomatedTask.joins(company_mailer: :default_mailer).where(default_mailer:  { description: 'Notify all people and groups that are related to a ticket that an agent has been assigned to the ticket' })
    assigned_to_agent_tasks.update_all(name: 'Notify Assigned-To Changes - Email')
  end

  desc 'Changed assigned to automated tasks structure against custom forms'
  task assigned_to_task_new_structure_via_cf: :environment do
    event_subject_id = AutomatedTasks::EventSubjectType.find_by(name: "any ticket").id
    automated_tasks = AutomatedTasks::AutomatedTask.joins(:custom_form)
    automated_tasks.find_each do |at|
      at.task_event.event_details.first.update_column(:value, nil)
      at.task_event.event_details.first.update_column(:event_subject_type_id, event_subject_id)
    end
  end

  desc 'update target name and key group to contributors'
  task update_group_target_name_to_contributors: :environment do
    task_actions = AutomatedTasks::TaskAction.includes(:action_type).where(action_type: { action_class: "SendEmail", module: 'help_tickets' })
    task_actions.find_each do |action|
      json_value = JSON.parse(action.value)
      targets = json_value['target']&.split(",")
      if targets.include?("groups")
        if json_value["group"] && json_value["group"]["id"]
          json_value["contributors"] = json_value.delete("group")
          targets = (targets - ["groups"]) + ["contributors"]
          json_value['target'] = targets.join(',')
          action.update_columns(value: (json_value.to_json))
        end
      end
    end
  end

  desc 'Change deleted form in automated task'
  task change_deleted_form_in_automated_tasks: :environment do
    AutomatedTasks::AutomatedTask.find_each do |task|
      form_names = CustomForm.where(workspace_id: task.workspace).pluck(:form_name)
      value = task.task_event.event_details.first.value
      if value.present?
        temp_value = JSON.parse(value)
        if temp_value['custom_forms'][0]['id'] != 'all' && !form_names.include?(temp_value['custom_forms'][0]['form_name'])
          cf = CustomForm.find_by(default: true, workspace_id: task.workspace_id)
          cff = cf.custom_form_fields.find_by(name: temp_value['form_field']['name'])
          temp_value['custom_forms'][0]['id'] = cf.id
          temp_value['custom_forms'][0]['form_name'] = cf.form_name
          temp_value['form_field']['id'] = cff.id
          value = temp_value.to_json
          task.task_event.event_details.first.update_columns(value: value)
        end
      end
    end
  end

  desc 'Removed multiple email notification setting'
  task removed_multiple_email_notification: :environment do
    Workspace.find_each do |workspace|
      default_mailer = CompanyMailer.where(workspace_id: workspace.id)
      mailer_ids = default_mailer.pluck(:default_mailer_id)
      mailer_ids.select do |e|
        if mailer_ids.count(e) > 1
          puts(workspace.id)
          multi_setting = default_mailer.where(default_mailer_id: e)
          if multi_setting.count > 1
            max_mailer = multi_setting.max
            max_mailer.update_columns(default_mailer_id: nil)
            task_id = max_mailer.automated_tasks_automated_task_id
            max_mailer.delete
            AutomatedTasks::AutomatedTask.find_by(id: task_id)&.delete
          end
        end
      end
    end
  end

  desc 'Update custom form key for default automated tasks Notify Status Closed'
  task update_cf_key_name_to_form_name: :environment do
    default_tasks = AutomatedTasks::AutomatedTask.joins(:company_mailer).where(name: 'Notify Status Closed - Email')
    default_tasks.find_each do |task|
      event_detail = task.task_events.first.event_details.first
      value = JSON.parse(event_detail.value)
      if value["custom_forms"][0]["form_name"].nil?
        value["custom_forms"][0]["form_name"] = value["custom_forms"][0].delete("name")
        value["custom_forms"][0]["form_name"] ||= "Any Form"
        event_detail.update_columns(value: value.to_json)
      end
    end
  end

  desc "Run ticket state based timed automated tasks"
  task ticket_state_based_timed_tasks: :environment do
    AutomatedTasks::StateBasedTimedTaskExecutor.new.call
  end

  desc "Run integration failed asset automated tasks"
  task intergration_failed_tasks: :environment do
    AssetAutomatedTasks::IntegrationFailedTaskExecutor.new.call
  end

  desc "Run agent not resynced asset automated tasks"
  task agent_not_resynced_tasks: :environment do
    AssetAutomatedTasks::AgentResyncTaskExecutor.new.call
  end

  desc "Run low disk space asset automated tasks"
  task low_disk_space_tasks: :environment do
    AssetAutomatedTasks::LowDiskSpaceTaskExecutor.new.call
  end

  desc "Run software not detected asset automated tasks"
  task software_not_detected_tasks: :environment do
    AssetAutomatedTasks::SoftwareNotDetectedTaskExecutor.new.call
  end

  desc "Replace Task Name with Triggers Type"
  task replace_task_name_with_triggers_type: :environment do
    AutomatedTasks::AutomatedTask.where(name: "Notify Ticket Created - Slack").find_each do |task|
      if task.task_events.last.event_type.name == "{a comment} is added to a ticket"
        task.update_columns(name: "Notify Comment Added - Slack")
      elsif task.task_events.last.event_type.name == "{a ticket} is updated"
        task.update_columns(name: "Notify Ticket Status Updated - Slack")
      elsif task.task_events.last.event_type.name == "{a ticket} is created"
        task.update_columns(name: "Notify Ticket Created - Slack")
      end
    end

    AutomatedTasks::AutomatedTask.where(name: "Notify Ticket Status Updated - Teams").find_each do |task|
      if task.task_events.last.event_type.name == "{a comment} is added to a ticket"
        task.update_columns(name: "Notify Comment Added - Teams")
      elsif task.task_events.last.event_type.name == "{a ticket} is updated"
        task.update_columns(name: "Notify Ticket Status Updated - Teams")
      elsif task.task_events.last.event_type.name == "{a ticket} is created"
        task.update_columns(name: "Notify Ticket Created - Teams")
      end
    end
  end

  desc 'Add workspace_id in AutomatedTasks ExecutionLog'
  task populate_workspace_id_for_execution_logs: :environment do
    Company.find_each do |com|
      com.execution_logs.where(workspace_id: nil).update_all(workspace_id: com.default_workspace.id)
    end
  end

  desc 'Only keep last two weeks automated execution logs'
  task delete_automated_tasks_execution_logs: :environment do
    BulkDeleteAutomatedTasksExecutionLogWorker.perform_async()
  end

  desc 'rename staff_list to people_list in task action values'
  task rename_task_action_staff_list_to_people_list: :environment do
    AutomatedTasks::TaskAction.where('value ilike ?', '%field_attribute_type\":\"staff_list"%').find_each do |action|
      action.value = action.value.gsub("field_attribute_type\":\"staff_list", "field_attribute_type\":\"people_list")
      action.save!
    end
  end

  desc 'rename staff_list to people_list in task action values'
  task rename_event_detail_staff_list_to_people_list: :environment do
    AutomatedTasks::EventDetail.where('value ilike ?', "%staff_list%").find_each do |detail|
      detail.value = detail.value.gsub("field_attribute_type\":\"staff_list", "field_attribute_type\":\"people_list")
      detail.save!
    end
  end

  desc "Update event_details impacted_users to followers for TaskEvent records with subject_class 'TicketFormField'"
  task update_task_event_event_details: :environment do
    AutomatedTasks::EventDetail.joins(:event_subject_type).where(event_subject_type: {subject_class: "TicketFormField"}).find_each do |task_event|
      value_json = JSON.parse(task_event.value)
      if value_json.dig("form_field", "name") == "impacted_users"
        value_json["form_field"]["name"] = "followers"
        task_event.update(value: value_json.to_json)
      end
    end
  end  

  desc "Update target from 'creator' or 'creator_email' to 'ticket_creator'"
  task update_target: :environment do
    actions = AutomatedTasks::TaskAction.where("value LIKE '%\"target\":\"%creator%' OR value LIKE '%\"target\":\"%creator_email%'")
    actions.find_each do |action|
      value_data = JSON.parse(action.value)  
      if value_data["target"].include?("creator") || value_data["target"].include?("creator_email")
        targets = value_data["target"].split(',')  
        targets.map! do |target|
          if target == "creator" || target == "creator_email"
            "ticket_creator"
          else
            target
          end
        end  
        value_data["target"] = targets.uniq.join(',')  
        action.update(value: value_data.to_json)
      end
    end
  end
  

  desc "Update task_action impacted_users to followers"
  task update_task_action: :environment do
    AutomatedTasks::TaskAction.joins(:action_type).where(action_type: { name: "set a [form field]"}).find_each do |task_action|
      value_json = JSON.parse(task_action.value)
      if value_json.dig("form_field", "name") == "impacted_users"
        value_json["form_field"]["name"] = "followers"
        task_action.update(value: value_json.to_json)
      end
    end
  end

  desc "Update serial numbers based on created_at"
  task update_serial_numbers: :environment do
    Workspace.includes(:automated_tasks).find_each do |workspace|
      automated_tasks = workspace.automated_tasks.order(:created_at)
      if automated_tasks.any?
        automated_tasks.each.with_index(1) do |task, index|
          task.update_column(:serial_number, index)
        end
      end
    end
  end

  desc "Update nil event_details value"
  task update_event_details: :environment do
    automated_tasks = AutomatedTasks::AutomatedTask.left_outer_joins(:company_mailer).where(company_mailers: { id: nil })
    automated_tasks.each do |task|
      task.task_events.each do |event|
        event.event_details.each do |detail|
          if detail.value.nil?
            new_value = "{\"custom_forms\":[{\"id\":\"all\",\"is_active\":true,\"form_name\":\"Any Form\"}],\"form_field\":{\"name\":\"subject\",\"field_attribute_type\":\"text\",\"label\":\"subject\",\"options\":null},\"negate\":false,\"any\":false,\"partial_match\":false,\"text\":\"sample subject\"}"
            parsed_value = JSON.parse(new_value)
            detail.update(value: parsed_value.to_json)
            task.update(disabled_at: DateTime.now)
          end
        end
      end 
    end
  end

  desc "Fix order for workspaces with automated tasks having order 0"
  task fix_order: :environment do
    workspaces_with_order_zero_tasks = Workspace.includes(:automated_tasks).select do |workspace|
      workspace.automated_tasks.any? { |task| task.order == 0 }
    end
    workspaces_with_order_zero_tasks.each do |workspace|
      zero_order_tasks = workspace.automated_tasks.where(order: 0).order(created_at: :desc)      
      zero_order_tasks.each_with_index do |task, index|
        task.update_column(:order, index +1)
      end
      remaining_tasks = workspace.automated_tasks.where.not(id: zero_order_tasks.pluck(:id)).order(order: :asc)
      last_order = zero_order_tasks.size
      remaining_tasks.each do |task|
        last_order += 1
        task.update_column(:order, last_order)
      end
    end
  end

  desc "Update options and templates for automated tasks"
  task update_automated_tasks: :environment do
    update_ticket_created_tasks
    update_comment_added_tasks
    update_survey_automated_task
    update_assigned_to_task
  end

  desc "Hide private channel names for Slack"
  task hide_private_channels: :environment do
    private_configs = Integrations::Slack::Config.where("channel_name NOT LIKE ?", '#%')
                                                 .where(authorized_config_id: nil)
    private_configs.each do |config|
      old_name = config.channel_name
      config.update_columns(
        channel_name: "private-channel",
        meta_data: { private_channel_name: old_name  }
      )
      config.update_associated_automated_tasks('private-channel', true)
      puts "Updated config ID #{config.id}: old channel name was => '#{old_name}'"
    end
  end

  # To fix those tasks which were cloned and are in same state (their order is not updated)
  desc "Fix tasks that have same serial number and same order"
  task update_order_and_serial_numbers: :environment do
    Workspace.includes(:automated_tasks).find_each do |workspace|
      puts "Iterating over workspace: #{workspace.id}"
      automated_tasks = workspace.automated_tasks
      existing_orders = automated_tasks.pluck(:order).uniq
      existing_serial_numbers = automated_tasks.pluck(:serial_number).uniq

      duplicate_order_and_serial_number_tasks = automated_tasks.group(:order, :serial_number)
                                                                .having('COUNT(*) > 1')
                                                                .pluck(:order, :serial_number)

      duplicate_order_and_serial_number_tasks.each do |order, serial_number|
        faulty_tasks = automated_tasks.where(order: order, serial_number: serial_number).order(:created_at)
        faulty_tasks.each_with_index do |task, index|
          next if index == 0

          next_order = existing_orders.max + 1
          next_serial_number = existing_serial_numbers.max + 1

          task.update_columns(order: next_order, serial_number: next_serial_number)

          existing_orders << next_order
          existing_serial_numbers << next_serial_number
        end
      end
    end
  end

  # To fix those tasks which were cloned but their order was updated, and now they have duplicate serial numbers
  desc "Fix tasks having same serial number"
  task fix_serial_numbers: :environment do
    Workspace.includes(:automated_tasks).find_each do |workspace|
      puts "Iterating over workspace: #{workspace.id}"
      automated_tasks = workspace.automated_tasks
      existing_serial_numbers = automated_tasks.pluck(:serial_number).uniq

      duplicate_serial_number_tasks = automated_tasks.group(:serial_number).having('COUNT(*) > 1').pluck(:serial_number)

      duplicate_serial_number_tasks.each do |serial_number|
        faulty_tasks = automated_tasks.where(serial_number: serial_number).order(:created_at)
        faulty_tasks.each_with_index do |task, index|
          next if index == 0

          next_serial_number = existing_serial_numbers.max + 1
          task.update_column(:serial_number, next_serial_number)
          existing_serial_numbers << next_serial_number
        end
      end
    end
  end

  # To fix those tasks which have duplicate orders
  desc "Fix tasks having same order"
  task fix_order_numbers: :environment do
    Workspace.includes(:automated_tasks).find_each do |workspace|
      puts "Iterating over workspace: #{workspace.id}"
      automated_tasks = workspace.automated_tasks
      existing_orders = automated_tasks.pluck(:order).uniq

      duplicate_order_values = automated_tasks.group(:order).having('COUNT(*) > 1').pluck(:order)
      zero_order_values = automated_tasks.where(order: 0).pluck(:order)
      duplicate_order_tasks = (duplicate_order_values + zero_order_values).uniq

      duplicate_order_tasks.each do |order|
        faulty_tasks = automated_tasks.where(order: order).order(:created_at)
        faulty_tasks.each_with_index do |task, index|
          next if index == 0 && order != 0

          next_order = existing_orders.max + 1
          task.update_column(:order, next_order)
          existing_orders << next_order
        end
      end
    end
  end

  def update_ticket_created_tasks
    tasks = AutomatedTasks::AutomatedTask.joins(task_events: :event_type)
                                        .includes(task_events: :event_type)
                                        .where(automated_tasks_event_types: { name: "{a ticket} is created", module: 'help_tickets' })

    tasks.find_each do |task|
      next unless task.task_actions.present?
      task.task_actions.find_each do |task_action|
        next unless task_action&.action_type&.name == 'send an [email]'

        update_task_actions(task_action)
      end
    end
  end

  def update_comment_added_tasks
    tasks = AutomatedTasks::AutomatedTask.joins(task_events: :event_type)
                                        .includes(task_events: :event_type)
                                        .where(automated_tasks_event_types: { name: "{a comment} is added to a ticket", module: 'help_tickets' })

    tasks.find_each do |task|
      task.task_actions&.find_each do |task_action|
        next unless task_action&.action_type&.name == 'send an [email]'

        update_task_actions(task_action)
      end
    end
  end

  def update_survey_automated_task
    event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is updated", module: 'help_tickets')
    event_subject_type = event_type.children.find_by(name: "/a ticket/ with {a form field}")
    event_details = AutomatedTasks::EventDetail.where(event_subject_type: event_subject_type)
    default_mailer = DefaultMailer.find_by(event: "survey_sent")

    event_details.find_each do |event_detail|
      parsed_value = JSON.parse(event_detail.value)
      form_field = parsed_value['form_field']
      status = parsed_value['status']

      next unless form_field['name'] == 'status' && form_field['field_attribute_type'] == 'status' && status == 'Closed' && event_detail.task_event.automated_task&.company_mailer&.default_mailer&.event == 'survey_sent'

      task_actions = event_detail.task_event.automated_task&.task_actions

      if task_actions.present?
        task_actions.find_each do |task_action|
          next unless task_action&.action_type&.name == 'send an [email]'

          update_task_actions(task_action)
        end
      end
    end
  end

  def update_assigned_to_task
    event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is updated", module: 'help_tickets')
    event_subject_type = event_type.children.find_by(name: "/a ticket/ with {a form field}")
    event_details = AutomatedTasks::EventDetail.where(event_subject_type: event_subject_type)

    event_details.find_each do |event_detail|
      parsed_value = JSON.parse(event_detail.value)
      form_field = parsed_value['form_field']
      any = parsed_value['any']

      next unless form_field['name'] == 'assigned_to' && form_field['field_attribute_type'] == 'people_list' && any == true

      task_actions = event_detail.task_event.automated_task&.task_actions

      if task_actions.present?
        task_actions.find_each do |task_action|
          next unless task_action&.action_type&.name == 'send an [email]'

          action_value = JSON.parse(task_action.value)
          update_task_actions(task_action)
          if valid_subject?(action_value['subject']) && valid_body?(action_value['body'])
            update_task_actions(task_action, "New Ticket Assignment [\#{ticket number}]: {ticket subject}")
          else
            update_task_actions(task_action)
          end
        end
      end
    end
  end

  def valid_subject?(subject)
    valid_subjects = [
      "New Ticket Assignment [\#{ticket number}]: {ticket subject}",
      "New assignment for ticket [\#{ticket_number}]: {ticket_subject}"
    ]
    valid_subjects.include?(subject)
  end

  def valid_body?(body)
    body.gsub(/\s+/, ' ').strip == assigned_to_me_message.gsub(/\s+/, ' ').strip || body.gsub(/\s+/, ' ').strip == assigned_to_anyone_message.gsub(/\s+/, ' ').strip
  end

  def update_task_actions(task_action, subject = nil)
    action_value = JSON.parse(task_action.value)
    workspace_id = task_action.automated_task.workspace_id
    email_template = subject ? EmailTemplate.find_by(workspace_id: workspace_id, subject_title: subject) : EmailTemplate.find_by(workspace_id: workspace_id, subject_title: action_value['subject'])

    is_body_matching = action_value['body'].gsub(/\s+/, ' ').strip == ticket_created_message.gsub(/\s+/, ' ').strip ||
                       (email_template&.template_name == "ticket assigned" && email_template&.is_default == true) ||
                       (email_template&.email_body&.gsub(/\s+/, ' ')&.strip == action_value['body']&.gsub(/\s+/, ' ')&.strip)

    if email_template.present? && is_body_matching
      action_value['option'] = { value: 2, name: 'Select Template' }
      action_value['template'] = email_template
    else
      action_value['option'] = { value: 1, name: 'Customize an email' }
    end

    task_action.update_column(:value, action_value.to_json)
  end

  def ticket_created_message
    """
      <div>
        <div>
          <span style=\"font-size: 1rem;\"><b>{ticket_created_by}</b> submitted a new help desk ticket on {ticket_created_at}: <b>\"{ticket_subject}\" [\#{ticket_number}]</b></span>
          <br>
          <br>
          {ticket_button}
          </div>
        </div>
      </div>
    """
  end

  def assigned_to_me_message
    """
      <div>
        <div>
          <span style=\"font-size: 0.875rem;\"><b>{ticket_assigned_to}</b> has been assigned to ticket <b>{ticket_subject} [\#{ticket_number}]</b>.</span>
          <br/>
          <br/>
          <p style=\"font-size: 0.875rem;\">{ticket_description}</p>
          <br/>
          <br/>
          {ticket_button}
        </div>
      </div>
    """
  end

  def assigned_to_anyone_message
    """
      <div>
        <div>
          <span style=\"font-size: 0.875rem;\"><b>{ticket_assigned_to}</b> has been assigned to ticket <b>{ticket_subject} [\#{ticket_number}]</b>.</span>
          <br/>
          <br>
          <p style=\"font-size: 0.875rem;\">{ticket_description}</p>
          <br/>
          <br>
          {ticket_button}
        </div>
      </div>
    """
  end
end
