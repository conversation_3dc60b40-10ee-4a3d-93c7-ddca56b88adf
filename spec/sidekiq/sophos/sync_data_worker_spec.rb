require 'rails_helper'

RSpec.describe Integrations::Sophos::SyncDataWorker, type: :worker do
  describe '#perform' do
    before do
      @company = FactoryBot.create(:company)
      Integration.find_or_create_by!(name: 'sophos')
      @config = FactoryBot.create(:sophos_config, company: @company)

      @device = {
        'id' => 'device-abc-123',
        'hostname' => 'Sophos-Laptop-1',
        'ipv4Addresses' => ['*************', '*************'],
        'serialNumber' => 'SERIAL-123',
        'macAddresses' => ['00:11:22:33:44:55', '00:11:22:33:44:56', '00:11:22:33:44:56'],
        'os' => {
          'platform' => 'windows',
          'name' => 'Windows 10 Pro',
          'majorVersion' => 10,
          'minorVersion' => 0,
          'isServer' => false
        },
        'encryption' => {
          'overallStatus' => 'encrypted'
        }
      }

      @tenant = FactoryBot.create(:sophos_tenant, sophos_config: @config)
      @config.update(sophos_tenant: @tenant, access_token: 'mock_access_token', import_type: "managed_asset")
      @config.reload

      allow_any_instance_of(Integrations::Sophos::FetchData)
        .to receive(:refresh_access_token).and_return(true)

      allow_any_instance_of(Integrations::Sophos::FetchData)
        .to receive(:get_devices) do
          {
            'items' => [@device],
            'pages' => { 'nextKey' => nil }
          }
        end
    end

    it 'syncs devices and creates a discovered asset and asset source' do
      described_class.new.perform(@config.id, true, false, nil)
      da = DiscoveredAsset.sophos.find_by(
        company_id: @company.id,
        machine_serial_no: 'SERIAL-123'
      )
      expect(da).to be_present
      expect(da.mac_addresses).to include('00:11:22:33:44:55')
      expect(da.asset_sources.where(source: 'sophos').count).to eq(1)
    end

    it 'rescues exceptions and updates company integration status on failure' do
      allow_any_instance_of(Integrations::Sophos::FetchData)
        .to receive(:get_devices)
        .and_raise("timeout")

      expect {
        described_class.new.perform(@config.id, false, false, nil)
      }.not_to raise_error

      ci = @config.company_integration.reload
      expect(ci.sync_status).to eq('failed')
      expect(ci.status).to be false
      expect(ci.error_message).to match(/timeout/)
    end
  end
end
