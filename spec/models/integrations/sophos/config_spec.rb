require 'rails_helper'

RSpec.describe Integrations::Sophos::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:company_user) { FactoryBot.create(:user) }

  let(:valid_config_attributes) do
    {
      client_id: 'test_client_id',
      client_secret: 'test_client_secret',
      access_token: 'valid_token',
      company_id: company.id,
      company_user_id: company_user.id
    }
  end

  subject { described_class.new(valid_config_attributes) }

  describe 'validations' do
    it { should validate_presence_of(:client_id) }
    it { should validate_presence_of(:client_secret) }
    it { should validate_presence_of(:access_token) }
    it { should validate_presence_of(:company_id) }
  end

  describe 'associations' do
    it { should belong_to(:company) }
    it { should belong_to(:company_user) }
    it { should have_one(:company_integration).dependent(:destroy) }
    it { should have_one(:sophos_tenant).class_name('Integrations::Sophos::Tenant').with_foreign_key('sophos_config_id').dependent(:destroy) }
  end

  describe 'enum' do
    it { should define_enum_for(:import_type).with_values([:managed_asset, :discovered_asset]) }
  end

  describe 'callbacks' do
    it { is_expected.to callback(:create_comp_intg_and_execute_job).after(:commit)}
    it { is_expected.to callback(:create_credentials_update_log).after(:update) }
    it { is_expected.to callback(:destroy_integration_data).before(:destroy) }
  end
end
