require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Sophos::ConfigsController, type: :controller do
  create_company_and_user

  let(:sophos_config) { FactoryBot.create(:sophos_config, company: company, company_user: company_user) }

  let(:valid_sophos_data) do
    {
      client_id: 'test_client_id',
      client_secret: 'test_client_secret',
      import_type: 'managed_asset'
    }
  end

  let(:invalid_sophos_data) do
    {
      client_id: '',
      client_secret: 'test_client_secret'
    }
  end

  let(:token_detail) do
    {
      'access_token' => 'mock_access_token',
      'expires_in' => DateTime.now + 30.minutes
    }
  end

  describe '#create' do
    context 'when credentials are valid' do
      it 'authenticates and saves credentials' do
        allow_any_instance_of(Integrations::Sophos::FetchData).to receive(:token).and_return(token_detail)
        allow_any_instance_of(Integrations::Sophos::FetchData).to receive(:fetch_tenant_info).and_return({ 'id' => 'abc', 'apiHosts' => { 'dataRegion' => 'some_url' } })
        post :create, params: { sophos_config: valid_sophos_data }

        config = Integrations::Sophos::Config.find_by(company_id: company.id)

        expect(response).to have_http_status(:ok)
        expect(config).not_to be_nil
        expect(config.client_id).to eq('test_client_id')
      end
    end

    context 'when credentials are invalid' do
      it 'fails to create configuration and returns an error message' do
        allow_any_instance_of(Integrations::Sophos::FetchData).to receive(:token).and_return(token_detail)
        post :create, params: { sophos_config: invalid_sophos_data }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to include("Client can't be blank")
      end
    end
  end
  describe 'PATCH #update_import_type' do
    it 'updates the import_type' do
      patch :update_import_type, params: { id: sophos_config.id, import_type: 'discovered_asset' }

      expect(response.status).to eq(200)
      expect(sophos_config.reload.import_type).to eq('discovered_asset')
    end
  end

  describe 'POST #deactivate' do
    it 'deactivates the Sophos configuration' do
        post :deactivate, params: { id: sophos_config.id }

        expect(response.status).to eq(200)
        expect(sophos_config.reload.company_integration.active).to eq(false)
    end
  end

  describe 'DELETE #destroy' do
    it 'destroys the Sophos integration config' do
      delete :destroy, params: { id: sophos_config.id }

      expect(response.status).to eq(200)
        expect(Integrations::Sophos::Config.exists?(sophos_config.id)).to eq(false)
    end
  end
end
