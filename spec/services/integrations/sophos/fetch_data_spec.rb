require 'rails_helper'
require 'httparty'
include CompanyUserHelper

RSpec.describe Integrations::Sophos::FetchData, type: :service do
  before(:all) do
    @company = FactoryBot.create(:company)
    @config = FactoryBot.create(:sophos_config, company: @company)
    @service = described_class.new(@company.id, @config)
  end

  let(:sophos_data) do
    {
      'client_id' => 'dummy_id',
      'client_secret' => 'dummy_secret'
    }
  end

  let(:valid_token_response) do
    OpenStruct.new(
      parsed_response: {
        'access_token' => 'mock_access_token',
        'expires_in' => 3600
      },
      code: 200,
      success?: true,
      body: '{"access_token":"mock_access_token","expires_in":3600}'
    )
  end

  let(:error_token_response) do
    OpenStruct.new(
      parsed_response: {
        'error' => 'invalid_client'
      },
      code: 401,
      success?: false,
      body: '{"error":"invalid_client"}'
    )
  end

  let(:mock_access_token) { 'mock_access_token' }

  let(:valid_tenant_response) do
    OpenStruct.new(
      parsed_response: {
        'id' => 'tenant_123',
        'apiHosts' => { 'dataRegion' => 'https://api.example.com' }
      },
      code: 200,
      success?: true,
      message: 'OK',
      body: '{"id":"tenant_123","apiHosts":{"global": "https://api.example.com","dataRegion":"https://api.example.com"}}'
    )
  end

  let(:error_tenant_response) do
    OpenStruct.new(
      parsed_response: { 'error' => 'Unauthorized' },
      code: 401,
      success?: false,
      message: 'Unauthorized',
      body: '{"error":"Unauthorized"}'
    )
  end

  let(:device_response) do
    OpenStruct.new(
      parsed_response: {
        'items' => [{ 'id' => 'device_1', 'hostname' => 'Device One' }],
        'pageFromKey' => 'next_page_token_123'
      },
      body: '{"items":[{"id":"device_1","hostname":"Device One"}],"pageFromKey":"next_page_token_123"}',
      code: 200,
      success?: true
    )
  end
  let(:error_response) do
    OpenStruct.new(
      parsed_response: { 'error' => 'Unauthorized' },
      body: '{"error":"Unauthorized"}',
      code: 401,
      success?: false
    )
  end

  describe '#token' do
    it 'returns token and logs success' do
      allow(HTTParty).to receive(:post).and_return(valid_token_response)
      result = @service.token(sophos_data)

      expect(result['access_token']).to eq('mock_access_token')
      expect(result['expires_in']).to be_within(1.second).of(DateTime.now + 3600.seconds)

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'token',
        class_name: described_class.name,
        status: :success
      )
      expect(event).to be_present
    end

    it 'returns error and logs failure' do
      allow(HTTParty).to receive(:post).and_return(error_token_response)
      result = @service.token(sophos_data)

      expect(result['error']).to eq('invalid_client')

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'token',
        class_name: described_class.name,
        status: :error
      )
      expect(event).to be_present
    end
  end
  describe '#fetch_tenant_info' do
    it 'returns tenant info and logs success' do
      allow(HTTParty).to receive(:get).and_return(valid_tenant_response)
      result = @service.fetch_tenant_info(mock_access_token)

      expect(result['id']).to eq('tenant_123')
      expect(result['apiHosts']['dataRegion']).to eq('https://api.example.com')

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'get_authorization',
        class_name: described_class.name,
        status: :success
      )
      expect(event).to be_present
    end

    it 'returns error response and logs failure when unauthorized' do
      allow(HTTParty).to receive(:get).and_return(error_tenant_response)
      result = @service.fetch_tenant_info(mock_access_token)

      expect(result['error']).to eq('Unauthorized')

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'get_authorization',
        class_name: described_class.name,
        status: :error
      )
      expect(event).to be_present
    end
  end

  describe '#get_devices' do
    before do
      tenant = instance_double('SophosTenant', data_region_url: 'https://api.example.com', tenant_id: 'tenant_123')
      @service.instance_variable_set(:@tenant, tenant)
      @config.update(access_token: 'mock_access_token')
    end
    it 'returns devices and logs success' do
      allow(HTTParty).to receive(:get).and_return(device_response)
      result = @service.get_devices

      expect(result['items'].first['hostname']).to eq('Device One')

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'get_devices',
        class_name: described_class.name,
        status: :success
      )
      expect(event).to be_present
    end

    it 'returns error and logs failure' do
      allow(HTTParty).to receive(:get).and_return(error_response)
      result = @service.get_devices

      expect(result['error']).to eq('Unauthorized')

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'get_devices',
        class_name: described_class.name,
        status: :error
      )
      expect(event).to be_present
    end

    it 'calls correct URL with pageFromKey if given' do
      expected_url = "https://api.example.com/endpoint/v1/endpoints?pageSize=100&view=full&pageFromKey=next_token"

      expect(@service).to receive(:make_api_call).with(
        expected_url,
        'get_devices',
        {
          'Authorization' => 'Bearer mock_access_token',
          'X-Tenant-ID' => 'tenant_123'
        }
      ).and_return(device_response)

      @service.get_devices('next_token')
    end
  end
end
