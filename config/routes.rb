require 'sidekiq-scheduler/web'
require 'sidekiq/web'

class ActionDispatch::Routing::Map<PERSON>
  def draw(routes_name)
    instance_eval(File.read(Rails.root.join("config/routes/#{routes_name}.rb")))
  end
end

Rails.application.routes.draw do
  resources :guests, except: [:show, :index, :update]
  resources :help_desk_custom_forms
  resources :articles, param: :slug, only: [:index, :create, :show, :update, :destroy] do
    collection do
      get :get_article_prvileges
    end
  end
  resources :automated_tasks, only: [:index, :create, :show, :update, :destroy]
  resources :automated_task_groups, only: [:index, :create, :show, :update, :destroy]

  resources :asset_automation_tasks, only: [:index, :create, :show, :update, :destroy] do
    collection do
      get :asset_softwares
    end
  end

  namespace :custom_forms do
    put 'orderings' => 'orderings#update'
    resources :helpdesk_custom_forms, only: [:index, :update]
  end

  resources :current_contributors, only: [:index]

  scope module: 'automated_tasks' do
    put '/orderings' => 'orderings#update'
    resources :automated_tasks
    resources :cloned_tasks, only: [:create]
    resources :automated_task_disables, only: [:update, :destroy]
    resources :action_types, only: [:index]
    resources :event_subject_types, only: [:index]
    resources :event_types, only: [:index]
    resources :string_interpolation_keys, only: [:index]
    resources :task_templates, only: [:index]
    post 'string_interpolation_keys/value' => 'string_interpolation_keys#key_interpolation'
  end

  scope module: 'assets' do
    resources :automated_tasks
    resources :asset_automation_tasks
    resources :automated_task_disables, only: [:update, :destroy]
    get 'asset_task_event_types', to: 'asset_task_event_types#index'
    get 'asset_task_action_types' => 'asset_task_action_types#index'
    get 'asset_task_event_subject_types' => 'asset_task_event_subject_types#index'
  end

  resources :workspace_sessions, only: [:index, :create]

  resources :partners
  resources :contributor_actionable_alerts
  resources :actionable_alerts
  if Rails.env.development? || Rails.env.test?
    get 'cognito' => 'cognito#destroy'
  end

  resources :task_criteria

  delete '/group/:id/group_members/:contributor_id' => 'group_members#destroy'
  resources :group_members, only: [:create]
  resources :groups
  resources :saved_tasks
  draw :status

  resources :telecom_service_locations
  resources :telecom_service_price_changes

  resources :library_documents, only: [:index, :create, :edit, :update, :destroy] do
    get :associated_tickets, on: :member
  end

  resources :api_events
  resources :plaid_event_logs

  get '/sso_company/new' => 'sso_companies#new', as: 'new_sso_company'
  post '/sso_company' => 'sso_companies#create'
  get '/no_sso_access' => 'sso_companies#no_access'
  get '/no_sso_company' => 'sso_companies#company_creation_denied'

  get '/faqs' => 'help_tickets/public_faqs#index'
  get 'faqs/workspaces/:workspace_id' => 'help_tickets/public_faqs#index'

  get '/knowledge_base' => 'help_tickets/public_articles#index'
  get '/knowledge_base/workspaces/:workspace_id' => 'help_tickets/public_articles#index'
  get '/knowledge_base/:slug/:workspace_id' => 'help_tickets/public_articles#show'

  get '/asset_preferences' => 'asset_preferences#index'
  put '/asset_preferences' => 'asset_preferences#update'
  get '/discovered_asset_preferences' => 'discovered_asset_preferences#index'
  put '/discovered_asset_preferences' => 'discovered_asset_preferences#update'
  get 'discovery_tools/validate' => 'discovery_tools#validate'

  get '/email_verify/success' => 'email_verifications#success'
  get '/email_verify/failed' => 'email_verifications#failed'
  get '/email_verify/check' => 'email_verifications#check'

  #!! START IMPORT ROUTES

  # MANAGED ASSETS IMPORT ROUTES
  get '/managed_assets/import_assets' => 'managed_assets#index'
  get '/managed_assets/import_assets'  => 'managed_assets#import'
  get '/managed_assets/sample' => 'sample_managed_assets_xls#index'

  scope module: 'imports' do
    post '/managed_assets/import' => 'managed_assets_xls#create'
    post '/managed_assets/validate_rows' => 'managed_assets_xls#validate_rows'
    post '/managed_assets/create_row' => 'managed_assets_xls#create_row'
    get '/managed_assets/xls_details' => 'managed_assets_xls#index'
    get '/managed_assets/import_xls/:id' => 'managed_assets_xls#start_importing_file'
    get '/managed_assets/import/details/:id' => 'managed_assets_xls#import_details'
    get '/managed_assets/imports/imported_sheet/:id' => "managed_assets_xls#single_sheet_ready_for_imports"
    get '/managed_assets/imports/delete_file/:id' => "managed_assets_xls#remove_file"
    post '/managed_assets/imports/just_save' => "managed_assets_xls#save_xls_row"
  end

  # CONTRACTS IMPORT ROUTES
  get '/contracts/import_contracts' => 'contracts#new'
  get '/contracts/sample' => 'sample_contracts_xls#index'

  scope module: 'imports' do
    post '/contracts/import' => 'contracts_xls#create'
    post '/contracts/validate_rows' => 'contracts_xls#validate_rows'
    post '/contracts/create_row' => 'contracts_xls#create_row'
    get '/contracts/xls_details' => 'contracts_xls#index'
    get '/contracts/import_xls/:id' => 'contracts_xls#start_importing_file'
    get '/contracts/import/details/:id' => 'contracts_xls#import_details'
    get '/contracts/imports/imported_sheet/:id' => "contracts_xls#single_sheet_ready_for_imports"
    get '/contracts/imports/delete_file/:id' => "contracts_xls#remove_file"
    post '/contracts/imports/just_save' => "contracts_xls#save_xls_row"
  end

  get '/related_companies/company_templates/:template_id' => 'related_companies#index'
  get '/related_companies/company_templates' => 'related_companies#index'
  get '/related_companies/child_company_management' => 'related_companies#index'
  get '/related_companies/child_company_management/access_groups' => 'related_companies#index'
  get '/related_companies/child_company_management/access_groups/new' => 'related_companies#index'
  get '/related_companies/child_company_management/access_groups/:id/edit' => 'related_companies#index'
  get '/related_companies/child_company_management/build_elements' => 'related_companies#index'
  get '/related_companies/child_company_management/build_elements/all_build_elements' => 'related_companies#index'
  get '/related_companies/child_company_management/build_elements/:id/edit' => 'related_companies#index'
  get '/related_companies/child_company_management/logs' => 'related_companies#index'
  resources :related_companies, only: [:index, :show], param: :subdomain do
    post :copy_items, on: :collection
    post :delete_items, on: :collection
  end
  resources :access_groups, only: [:index, :create, :update, :destroy, :show]
  resources :child_companies_management, only: [:index] do
    get :msp_permissions_options, on: :collection
    post :copy_elements, on: :collection
    post :revoke_access, on: :collection
  end
  resources :company_builds, only: [:index, :create, :update, :destroy, :show]
  resources :msp_data_counts, only: [:index]
  resources :build_data_sets, only: [:index, :create, :show, :update]
  resources :applied_build_data_sets, only: [:index, :create, :show, :update]
  get '/related_companies/custom_forms/new' => 'related_companies#new'
  get '/related_companies/custom_forms/:id/edit' => 'related_companies#index'

  get '/related_companies/automated_tasks/new' => 'related_companies#index'
  get '/related_companies/automated_tasks/:id/edit' => 'related_companies#index'
  get '/msp/templates/custom_forms/fetch_form_fields' => 'msp/templates/custom_forms#fetch_all_form_fields'
  namespace :msp do
    namespace :templates do
      template_resources = [:asset_types, :categories, :library_documents, :company_users, :snippets, :groups, :custom_forms, :field_types_for_custom_form_fields, :faqs, :blocked_keywords, :automated_tasks]
      template_resources.each do |resource|
        resources resource do
          unless [:company_users, :groups, :field_types_for_custom_form_fields].include?(resource)
            post :copy_elements, on: :collection
            post :copy_existing_item, on: :collection
          end
        end
      end
    end

    namespace :dashboard do
      get '/companies_overview' => 'dashboard#companies_overview'
      get '/alerts' => 'dashboard#alerts'
      get '/helpdesk_summary' => 'dashboard#helpdesk_summary'
      get '/assets_summary' => 'dashboard#assets_summary'
      get '/contracts_summary' => 'dashboard#contracts_summary'
      get '/vendors_and_sass_summary' => 'dashboard#vendors_and_sass_summary'
      get '/get_warranty_data' => 'dashboard#get_warranty_data'
      get '/get_vendors_spending' => 'dashboard#get_vendors_spending'
      get '/get_network_activity' => 'dashboard#get_network_activity'
      get '/get_services_spending' => 'dashboard#get_services_spending'
    end
  end
  namespace :company do
    resources :related_companies, only: [:index]
  end

  # STATE OF IT ROUTES
  resources :state_of_it, only: [:index, :create] do
    collection do
      get :report_preference
      post :export_report
      put :security_content
    end
  end

  resources :it_reports, only: [:index, :destroy]
  post '/it_reports/download_report' => 'it_reports#download_report'
  post '/helpdesk_reports/download_report' => 'help_tickets/reports#download_report'

  namespace :it_reports do
    scope '/helpdesk' do
      get '/statuses', to: 'helpdesk#statuses'
      get '/sources', to: 'helpdesk#sources'
      get '/agent_productivity', to: 'helpdesk#agent_productivity'
      get '/ticket_pipeline', to: 'helpdesk#ticket_pipeline'
    end

    scope '/assets' do
      get '/count_and_types', to: 'assets#count_and_types'
      get '/warranties_info', to: 'assets#warranties_info'
      get '/locations', to: 'assets#assets_locations'
    end

    scope '/vendors' do
      get '/spend_breakdown', to: 'vendors#spend_breakdown'
      get '/top_vendors', to: 'vendors#top_vendors'
      get '/top_apps', to: 'vendors#top_apps'
      get '/spend_by_category', to: 'vendors#spend_by_category'
    end

    scope '/contracts' do
      get '/breakdown', to: 'contracts#breakdown'
      get '/fixed', to: 'contracts#fixed'
    end
  end

  # VENDORS IMPORT ROUTES
  get '/vendors/import_vendors' => 'vendors#new'
  get '/vendors/sample' => 'sample_vendors_xls#index'

  scope module: 'imports' do
    post '/vendors/import' => 'vendors_xls#create'
    post '/vendors/validate_rows' => 'vendors_xls#validate_rows'
    post '/vendors/create_row' => 'vendors_xls#create_row'
    get '/vendors/xls_details' => 'vendors_xls#index'
    get '/vendors/import_xls/:id' => 'vendors_xls#start_importing_file'
    get '/vendors/import/details/:id' => 'vendors_xls#import_details'
    get '/vendors/imports/imported_sheet/:id' => "vendors_xls#single_sheet_ready_for_imports"
    get '/vendors/imports/delete_file/:id' => "vendors_xls#remove_file"
    post '/vendors/imports/just_save' => "vendors_xls#save_xls_row"
  end

  # COMPANY USER IMPORT ROUTES
  get '/company/import_staff' => 'companies#get_company_template'
  get '/company_users/sample' => 'sample_company_users_xls#index'
  post '/company/users/:company_user_id/permissions' => 'company_user_permissions#create'

  scope module: 'imports' do
    post '/company_users/import' => 'company_users_xls#create'
    post '/company_users/validate_rows' => 'company_users_xls#validate_rows'
    post '/company_users/create_row' => 'company_users_xls#create_row'
    get '/company_users/xls_details' => 'company_users_xls#index'
    get '/company_users/import_xls/:id' => 'company_users_xls#start_importing_file'
    get '/company_users/import/details/:id' => 'company_users_xls#import_details'
    get '/company_users/imports/imported_sheet/:id' => "company_users_xls#single_sheet_ready_for_imports"
    get '/company_users/imports/delete_file/:id' => "company_users_xls#remove_file"
    post '/company_users/imports/just_save' => "company_users_xls#save_xls_row"
  end

  # GENERAL TRANSACTIONS IMPORT ROUTES
  get '/vendors/transactions/import_transactions' => 'vendors#index'
  get '/vendors/general_transactions/sample' => 'sample_general_transactions_xls#index'

  scope module: 'imports' do
    post '/vendors/general_transactions/import' => 'general_transactions_xls#create'
    post '/vendors/general_transactions/validate_rows' => 'general_transactions_xls#validate_rows'
    post '/vendors/general_transactions/create_row' => 'general_transactions_xls#create_row'
    get '/vendors/general_transactions/xls_details' => 'general_transactions_xls#index'
    get '/vendors/general_transactions/import_xls/:id' => 'general_transactions_xls#start_importing_file'
    get '/vendors/general_transactions/import/details/:id' => 'general_transactions_xls#import_details'
    get '/vendors/general_transactions/imports/imported_sheet/:id' => "general_transactions_xls#single_sheet_ready_for_imports"
    get '/vendors/general_transactions/imports/delete_file/:id' => "general_transactions_xls#remove_file"
    post '/vendors/general_transactions/imports/just_save' => "general_transactions_xls#save_xls_row"
  end

  resources :closing_surveys, only: [:show, :update]
  # SAAS USAGE APP IMPORT ROUTES
  get '/vendors/saas_usage/import_apps' => 'vendors#index'
  get '/vendors/apps/sample' => 'sample_apps_xls#index'

  scope module: 'imports' do
    post '/vendors/apps/import' => 'apps_xls#create'
    post '/vendors/apps/validate_rows' => 'apps_xls#validate_rows'
    post '/vendors/apps/create_row' => 'apps_xls#create_row'
    get '/vendors/apps/xls_details' => 'apps_xls#index'
    get '/vendors/apps/import_xls/:id' => 'apps_xls#start_importing_file'
    get '/vendors/apps/import/details/:id' => 'apps_xls#import_details'
    get '/vendors/apps/imports/imported_sheet/:id' => "apps_xls#single_sheet_ready_for_imports"
    get '/vendors/apps/imports/delete_file/:id' => "apps_xls#remove_file"
    post '/vendors/apps/imports/just_save' => "apps_xls#save_xls_row"
  end

  # LOCATION IMPORT ROUTES
  get '/company/import_locations' => 'companies#show'
  get '/locations/sample' => 'sample_locations_xls#index'

  scope module: 'imports' do
    post '/locations/import' => 'locations_xls#create'
    post '/locations/validate_rows' => 'locations_xls#validate_rows'
    post '/locations/create_row' => 'locations_xls#create_row'
    get '/locations/xls_details' => 'locations_xls#index'
    get '/locations/import_xls/:id' => 'locations_xls#start_importing_file'
    get '/locations/import/details/:id' => 'locations_xls#import_details'
    get '/locations/imports/imported_sheet/:id' => "locations_xls#single_sheet_ready_for_imports"
    get '/locations/imports/delete_file/:id' => "locations_xls#remove_file"
    post '/locations/imports/just_save' => "locations_xls#save_xls_row"
  end

  # TELECOM SERVICES IMPORT ROUTES
  get '/telecom_providers/services/import_services' => 'telecom_providers#new'
  get '/telecom_services/sample' => 'sample_telecom_services_xls#index'

  scope module: 'imports' do
    post '/telecom_services/import' => 'telecom_services_xls#create'
    post '/telecom_services/validate_rows' => 'telecom_services_xls#validate_rows'
    post '/telecom_services/create_row' => 'telecom_services_xls#create_row'
    get '/telecom_services/xls_details' => 'telecom_services_xls#index'
    get '/telecom_services/import_xls/:id' => 'telecom_services_xls#start_importing_file'
    get '/telecom_services/import/details/:id' => 'telecom_services_xls#import_details'
    get '/telecom_services/imports/imported_sheet/:id' => "telecom_services_xls#single_sheet_ready_for_imports"
    get '/telecom_services/imports/delete_file/:id' => "telecom_services_xls#remove_file"
    post '/telecom_services/imports/just_save' => "telecom_services_xls#save_xls_row"
  end

  # PHONE NUMBERS IMPORT ROUTES
  get '/telecom_providers/phone_numbers/import_numbers' => 'telecom_providers#new'
  get '/phone_numbers/sample' => 'sample_phone_numbers_xls#index'

  scope module: 'imports' do
    post '/phone_numbers/import' => 'phone_numbers_xls#create'
    post '/phone_numbers/validate_rows' => 'phone_numbers_xls#validate_rows'
    post '/phone_numbers/create_row' => 'phone_numbers_xls#create_row'
    get '/phone_numbers/xls_details' => 'phone_numbers_xls#index'
    get '/phone_numbers/import_xls/:id' => 'phone_numbers_xls#start_importing_file'
    get '/phone_numbers/import/details/:id' => 'phone_numbers_xls#import_details'
    get '/phone_numbers/imports/imported_sheet/:id' => "phone_numbers_xls#single_sheet_ready_for_imports"
    get '/phone_numbers/imports/delete_file/:id' => "phone_numbers_xls#remove_file"
    post '/phone_numbers/imports/just_save' => "phone_numbers_xls#save_xls_row"
  end

  # IP ADDRESSES IMPORT ROUTES
  get '/telecom_providers/ip_addresses/import_ips' => 'telecom_providers#new'
  get '/ip_addresses/sample' => 'sample_ip_addresses_xls#index'

  scope module: 'imports' do
    post '/ip_addresses/import' => 'ip_addresses_xls#create'
    post '/ip_addresses/validate_rows' => 'ip_addresses_xls#validate_rows'
    post '/ip_addresses/create_row' => 'ip_addresses_xls#create_row'
    get '/ip_addresses/xls_details' => 'ip_addresses_xls#index'
    get '/ip_addresses/import_xls/:id' => 'ip_addresses_xls#start_importing_file'
    get '/ip_addresses/import/details/:id' => 'ip_addresses_xls#import_details'
    get '/ip_addresses/imports/imported_sheet/:id' => "ip_addresses_xls#single_sheet_ready_for_imports"
    get '/ip_addresses/imports/delete_file/:id' => "ip_addresses_xls#remove_file"
    post '/ip_addresses/imports/just_save' => "ip_addresses_xls#save_xls_row"
  end

  # HELP TICKETS IMPORT ROUTES
  get '/help_tickets/import_help_tickets' => 'help_tickets/tickets#new'
  get '/help_tickets/insights' => 'help_tickets/tickets#index'
  get '/help_tickets/reports/recipients', to: 'help_tickets/reports#recipients'

  namespace :help_tickets do
      get 'get_people', to: 'people#index'
      get 'people/should_display_people' => 'people#should_display_people'
    resources :reports, only: [:index] do
      collection do
        get :overview, to: 'tickets#index'
        get :analytics, to: 'tickets#index'
        get :saved, to: 'tickets#index'
        get :scheduled, to: 'tickets#index'
        get 'saved/:id', to: 'tickets#index'
      end
    end

    get '/reports', to: redirect('/help_tickets/reports/overview')
  end

  get '/help_tickets/reports/:id', to: 'help_tickets/tickets#index'
  post '/help_tickets/reports', to: 'help_tickets/reports#create'
  put '/help_tickets/reports/:id', to: 'help_tickets/reports#update'
  delete '/help_tickets/reports/:id', to: 'help_tickets/reports#destroy'
  get '/help_desk/reports/:id', to: 'help_tickets/reports#show'
  get '/help_tickets/export_report/:id', to: 'help_tickets/reports#export_report'

  get 'report_templates', to: 'report_templates#index'
  authenticate :user, lambda { |u| u.super_admin? } do
    post 'report_templates', to: 'report_templates#create'
    get 'report_templates/new', to: 'report_templates#index'
    put 'report_templates/:id', to: 'report_templates#update'
    get 'report_templates/:id', to: 'report_templates#show'
    delete 'report_templates/:id', to: 'report_templates#destroy'
  end

  get '/analytics_report_templates/recipients' => 'analytics_report_templates#recipients'
  get '/help_tickets/sample' => 'sample_help_tickets_xls#index'
  get '/export_reports/export', to: 'export_reports#export'

  scope module: 'imports' do
    post '/help_tickets/import' => 'help_tickets_xls#create'
    post '/help_tickets/validate_rows' => 'help_tickets_xls#validate_rows'
    post '/help_tickets/create_row' => 'help_tickets_xls#create_row'
    get '/help_tickets/xls_details' => 'help_tickets_xls#index'
    get '/help_tickets/import_xls/:id' => 'help_tickets_xls#start_importing_file'
    get '/help_tickets/import/details/:id' => 'help_tickets_xls#import_details'
    get '/help_tickets/imports/imported_sheet/:id' => "help_tickets_xls#single_sheet_ready_for_imports"
    get '/help_tickets/imports/delete_file/:id' => "help_tickets_xls#remove_file"
    post '/help_tickets/imports/just_save' => "help_tickets_xls#save_xls_row"
  end

  #!! END IMPORT ROUTES
  # Export routes

  get '/vendors/export' => 'vendors_xls_export#index'
  get '/locations/export' => 'locations_xls_export#index'
  get '/contracts/export' => 'contracts_xls_export#index'
  get '/contracts/calendar_export'  => 'contracts_xls_export#index'
  get '/help_tickets/export' => 'help_tickets/xls_export#export'
  get '/system_details_export' => 'system_details_xls_export#index'

  get '/contracts/:id/history' => 'contracts#show'
  get '/contract_history' => 'contract_history#index'

  get '/telecom_services/export' => 'telecom_services_xls_export#index'

  get '/ip_addresses/export' => 'ip_addresses_xls_export#index'

  get '/phone_numbers/export' => 'phone_numbers_xls_export#index'

  get '/general_transactions/export' => 'general_transactions_xls_export#index'
  get '/general_transaction_groups' => 'general_transaction_groups#index'
  post '/graphical_report' => 'reports#graphical_report'
  post '/reports/download_report' => 'reports#download_report'
  get '/download_report_from_email' => 'reports#download_report_from_email'
  resources :reports, only: [:index, :create, :destroy]
  post '/contract_reports' => 'contract_reports#create'
  get '/apps/export' => 'apps_xls_export#index'

  # resources :discounts

  post '/bulk_help_ticket_emails/bulk_delete' => 'bulk_help_ticket_emails#bulk_delete'
  post '/bulk_help_tickets/bulk_delete' => 'bulk_help_tickets#bulk_delete'
  put '/bulk_help_tickets/archive' => 'bulk_help_tickets#archive'
  put '/bulk_help_tickets/unarchive' => 'bulk_help_tickets#unarchive'
  put '/articles_order' => 'articles#reorder'
  get '/pdf_import_status' => 'articles#get_pdf_import'
  delete '/bulk_module_alerts/bulk_delete' => 'bulk_module_alerts#bulk_delete'
  post '/bulk_location/bulk_delete' => 'bulk_locations#bulk_delete'
  post '/bulk_help_tickets/move' => 'bulk_help_tickets#move'

  resources :bulk_help_tickets, only: [] do
    collection do
      put :update_status
      put :status_options
      put :update_assignment
      put :canned_responses
      put :add_canned_response
    end
  end

  resources :upcoming_events_badges

  resources :genuity_admins, only: [:index]
  resources :module_permissions, only: [:index] do
    get :top_three_module_users, on: :collection
  end

  resources :privileges do
    resources :module_members, only: [:index, :create, :destroy, :update]
    resources :group_privileges, only: [:update, :destroy]
  end
  resources :groups do
    resources :module_members, only: [:index, :create, :destroy, :update]
  end

  resources :linkables
  resources :linkable_links do
    collection do
      get :fetch_users_linkable_types
    end
  end

  get 'phone_number_lookups/show'
  resources :phone_number_lookups, only: [:show]
  get 'ip_lookups/show'
  get 'ip_lookups/new'
  get '/pusher_lookup' => 'pusher#index'

  # For details on the DSL available within this file, see http://guides.rubyonrails.org/routing.html
  get 'user_accesses', to: 'user_accesses#new'
  resources :user_accesses, only: [:new, :show]
  resources :integration_configurations, only: [:index]
  resources :mfa_status, only: [:index]
  resources :mfa, only: [:index] do
    collection do
      post :verify
      post :registration
      post :add_registered_device
      post :edit_registered_device
      post :udpate_mfa_settings
      put :reset_mfa
      get :settings
      get :send_code_via_email
      get :send_code_via_text
      get :authenticate_via_app
    end
  end

  resources :store_accesses
  resources :monitoring_accesses
  get 'monitoring_down_checks' => 'monitoring_checks#index'
  resources :app_versions, only: [:index, :new, :create]
  get '/window_scripts/:source' => 'window_scripts#get_latest_version'
  resources :vendor_history, only: [:show]

  resources :general_transactions, only: [:index, :show, :destroy, :create, :update] do
    post :matching_transactions, on: :collection
    member do
      post :update_tags
      post :delete_tag
      post :split_transaction
    end
  end
  resources :general_transaction_monthly_breakdowns, only: [:index]
  resources :general_transaction_tags, only: [:index, :create, :update, :destroy]
  get '/general_transaction_tags/fetch_vendor_tag' => 'general_transaction_tags#fetch_vendor_tag'
  resources :general_transaction_spends, only: [:index]

  put '/general_transaction_locations/:location_id' => 'general_transaction_locations#update'
  post '/general_transaction_statuses' => 'general_transaction_statuses#update'
  post '/set_sso_user_password' => 'users#set_sso_user_password'
  post '/update_pinned_company_status' => 'users#update_pinned_company_status'

  resources :plaid_account_imports, only: [:create]
  resources :plaid_accounts do
    collection do
      post :deactivate
      post :reactivate
    end
  end

  get '/help_tickets/all' => 'help_tickets/tickets#index'
  get '/select_company' => 'company_selections#index'
  get '/plaid_info' => 'plaid_info#index'
  get '/plaid_institution/:institution_id' => 'plaid_institution#show'

  resources :company_user_mailers, only: [:index, :update]
  resources :company_user_archivals, only: [:update, :destroy]

  get '/current_company_user' => 'current_company_users#show'
  get '/current_company_users' => 'current_company_users#index'
  get '/current_company_user/reseller_companies' => 'current_company_users#reseller_companies'
  put '/current_company_user' => 'current_company_users#update'

  authenticate :user, lambda { |u| u.super_admin || Rails.env.development? } do
    mount Sidekiq::Web => '/sidekiq'
  end

  # to prevent non super users to access active admin routes
  authenticate :user, lambda { |u| u.super_admin? } do
    ActiveAdmin.routes(self) rescue ActiveAdmin::DatabaseHitDuringLoad
  end

  devise_for :users, controllers: {
    sessions:       'users/sessions',
    confirmations:  'users/confirmations',
    registrations:  'users/registrations',
    passwords:      'users/passwords',
    invitations:    'users/invitations'
  }, sign_out_via: [:get, :delete]

  devise_scope :user do
    get :registration_confirmation, action: :registration_confirmation, controller: 'users/registrations', as: 'registration_confirmation'
    get "/sessions/check_session", :to => "users/sessions#check_session"
    post "/users/sync_with_hubspot", :to => "users/registrations#sync_with_hubspot"
  end

  put '/users' => 'users#update'

  resources :module_privileges, only: [:index]
  resources :help_messages, only: [:create]

  get '/profile' => 'profiles#index'
  get '/profile/edit' => 'profiles#index'
  get '/profile/:id/edit' => 'profiles#index'
  get '/profile/email_confirmation' => 'profiles#index', as: 'email_confirmation'
  get '/profile/referrals' => 'profiles#index', as: 'user_referrals'
  get '/send_email_confirmation' => 'users#send_email_confirmation'
  get '/send_new_email_confirmation' => 'users#send_new_email_confirmation'
  get '/confirm_email' => 'users#confirm_email'
  get '/confirm_new_email' => 'users#confirm_new_email'
  get '/no_access' => 'no_access#index'
  get '/denied' => 'module_access_denied#index'
  get '/unsubscribed' => 'unsubscribed_module#index'
  get '/users/sign_up/about_you' => 'onboarding#new', as: 'new_user_about_you'
  get '/users/sign_up/company_info', to: redirect('/users/sign_up/about_you', status: 302)
  get '/users/sign_up/welcome', to: redirect('/users/sign_up/about_you', status: 302)
  get '/users/validate' => 'onboarding#user_validate'
  get '/users/check_domain' => 'onboarding#check_domain'
  post '/verify_recaptcha' => 'recaptcha#captcha_validate'

  post '/send_help_message' => 'help_message#send_message'
  post '/request_permission' => 'users#request_permission'

  get '/terms_of_service' => 'terms_of_service#show'

  resources :warranty_summaries, only: [:index]
  resources :managed_asset_warranties, only: [:update]
  resources :asset_type_summaries, only: [:index]
  resources :profiles, only: [:update]
  resources :system_details, only: [:show]

  root :to => 'home#index'
  get '/home/<USER>' => 'home#index'
  get '/help'   => 'help#help'
  get '/guides' => 'help#guides'
  get '/videos' => 'help#videos'

  get '/check_for_sample_company' => 'sample_companies#has_sample_company', as: 'check_for_sample_company'
  get '/download_discovery_tool'  => 'companies#download_discovery_tool', as: 'download_discovery_tool'
  get '/download_mac_tool'        => 'companies#download_mac_tool', as: 'download_mac_tool'
  get '/download_mac_agent'       => 'managed_assets#download_mac_agent', as: 'download_mac_agent'
  get '/download_mini_helpdesk'   => 'mini_helpdesk_versions#download_mini_helpdesk', as: 'download_mini_helpdesk'
  get '/download_ms_teams_app'    => 'ms_teams_apps#index'
  get '/get_latest_windows_agent_version' => 'managed_assets#get_latest_windows_agent_version', as: 'get_latest_windows_agent_version'

  post '/beta_feedback' => 'beta_feedback#create'

  get '/discovered_assets_summary' => 'discovered_assets_summary#index'
  resources :discovered_assets, only: [:index, :show] do
    get :import, on: :member
    get :asset_count_by_type, on: :collection
    get :discovered_asset_updates, on: :collection
    get :discovered_asset_updates_all, on: :collection
    post :import_bulk, on: :collection
    post :bulk_archive, on: :collection
    post :bulk_delete, on: :collection
    post :bulk_unarchive, on: :collection
  end

  authenticate :user, lambda { |u| u.super_admin || Rails.env.development? } do
    get '/managed_assets/discovered_assets/admin', to: 'managed_assets#index'
    get 'admin_discovered_assets' => 'discovered_assets#admin_assets'
  end

  get '/consume_code' => 'oauth2#consume_code'
  get '/discovered_users_summary' => 'discovered_users_summary#index'
  get '/discovered_users_summary/count' => 'discovered_users_summary#total_unseen'

  resources :discovered_users, only: [:index, :destroy] do
    get :import, on: :member
    post '/import_bulk' => 'bulk_discovered_user_imports#create', on: :collection
    post '/bulk_ignore' => 'bulk_discovered_users#delete', on: :collection
    post '/bulk_unignore' => 'bulk_discovered_users#update', on: :collection
  end

  resources :discovered_user_histories, only: [:index, :create, :destroy] do
    delete 'destroy_all', on: :collection, to: 'discovered_user_histories#destroy_all'
  end

  resources :discovered_asset_histories, only: [:index, :destroy]
  resources :asset_connector_logs, only: [:index]
  resources :probe_locations, only: [:index, :update, :destroy] do
    get :logs, on: :member
  end
  resources :agent_locations, only: [:index, :update, :destroy] do
    get :logs, on: :member
  end
  resources :asset_lifecycles, only: [:index, :create, :destroy, :update] do
    post :apply, on: :member
    get :custom, on: :collection
    get :check_associated_assets, on: :member
  end
  resources :probe_configs, only: [:update]
  resources :default_categories, only: [:index]
  resources :default_departments, only: [:index]
  post 'add_discovered_departments' => 'departments#add_discovered_departments'
  resources :company_asset_types, only: [:index, :create, :destroy, :update]
  resources :company_asset_statuses, only: [:index, :create, :destroy, :update]
  resources :company_asset_tags, only: [:index, :create, :destroy, :update]
  resources :categories, only: [:index, :create, :destroy]
  resources :helpdesk_categories, only: [:index, :create, :destroy]
  resources :departments, only: [:index, :create, :destroy]
  resources :service_summaries, only: [:index]
  resources :managed_asset_departments, only: [:index]
  resources :managed_asset_softwares, only: [:index]
  resources :managed_asset_manufacturers, only: [:index]
  resources :managed_asset_operating_systems, only: [:index]

  get '/contracts/list_view_columns' => 'contracts#index'
  get 'contracts/old_contracts' => 'contracts#old_contracts_of_user'
  get '/apps_location_summary' => 'applications_location#index'
  get '/contracts/all' => 'contracts#index'
  get '/contracts/insights' => 'contracts#index'
  get '/contracts/calendar' => 'contracts#index'
  get '/contracts/monthly_contracts' => 'contracts#monthly_contracts'
  get '/contracts/contract_contacts' => 'contracts#contract_contacts'
  get '/contracts/contracts_metrics' => 'contracts#contracts_metrics'
  get '/contracts/should_display_contracts_calendar', to: 'contracts#should_display_contracts_calendar'
  get '/contracts_list_columns' => 'contract_preferences#index'
  put '/contracts_list_columns' => 'contract_preferences#update'
  resources :contracts do
    member do
      post :archive
      post :unarchive
      post 'assign_parent_contract' => 'contract_hierarchy#assign_parent_contract'
      post 'assign_as_parent_contract' => 'contract_hierarchy#assign_as_parent_contract'
      post 'unlink_contract' => 'contract_hierarchy#unlink_contract'
      post 'bulk_link_contracts' => 'contract_hierarchy#bulk_link_contracts'
      post 'update_hierarchy' => 'contract_hierarchy#update_hierarchy'
      get 'hierarchy' => 'contract_hierarchy#index'
      get 'descendant_contracts' => 'contract_hierarchy#descendant_contracts'
      get 'ancestor_contracts' => 'contract_hierarchy#ancestor_contracts'
    end

    collection do
      delete :remove_file
      delete :remove_alert
      delete :remove_contact
    end
  end

  put '/contract_alert_dates/:id' => 'contract_alert_dates#update'
  put '/managed_asset_alert_dates/:id' => 'managed_asset_alert_dates#update'
  get '/managed_asset_alert_dates/get_recipients' => 'managed_asset_alert_dates#get_recipients'
  put '/managed_asset_alert_dates/:id/update_is_seen_alert' => 'managed_asset_alert_dates#update_is_seen_alert'
  put '/managed_asset_alert_dates/:id/update_smart_alert' => 'managed_asset_alert_dates#update_smart_alert'

  resources :software_types, only: [:index]

  resources :company_user_locks, only: [:update, :destroy]
  resources :user_activities, only: [:index]
  resources :analytics_report_templates, only: [:index, :create, :update, :destroy]
  resources :scheduled_reports, only: [:index, :create, :update, :destroy]

  get '/help_tickets/settings/event_log' => 'help_tickets/tickets#index'
  get '/company/event_logs' => 'companies#show'
  get '/company/user_activities' => 'companies#show'
  get '/company/groups' => 'groups#index'
  get '/company/groups/new' => 'groups#index'
  get '/company/groups/admins_group' => 'groups#admins_group'
  get '/company/groups/:id' => 'groups#index'
  get '/company/groups/:id/group_dependent_automated_task' => 'groups#group_dependent_automated_task'
  get '/company/groups/:id/edit' => 'groups#index'
  get '/preview_help_tickets/:guid' => 'preview_help_tickets#show'
  get '/help_desk_connectors' => 'help_desk_connectors#index'

  resources :managed_asset_images, only: [:show]
  resources :contributors, only: [:show]

  resources :ssl_lookups, only: [:index, :show, :create]
  resources :domain_lookups, only: [:index, :show, :create]
  resources :company_user_invites, only: [:index, :show]
  resources :company_invites, only: [:new, :show]
  resources :notice_periods, only: [:index]
  resources :email_formats, only: [:index, :create, :update]
  resources :email_templates, only: [:index, :create, :update, :destroy]
  resources :managed_assets_notes, only: [:update, :destroy]
  resources :managed_assets_details, only: [:update]

  get '/check_template_linkage/:id', to: 'email_templates#check_linkage'
  get '/email_formats/preview', to: 'email_formats#preview'
  get '/company/email_format' => 'companies#show'
  get '/company/customize/' => 'companies#show'

  get '/managed_assets/dashboard', to: 'managed_assets#index'
  get '/managed_assets/insights/details' => 'managed_assets#index'
  get '/managed_assets/insights', to: 'managed_assets#index'
  get '/managed_assets/analytics', to: 'managed_assets#index'
  get '/managed_assets/risk_center', to: 'managed_assets#index'
  get '/managed_assets/assets', to: 'managed_assets#index'
  get '/managed_assets/people_assets/assigned' => 'managed_assets#index'
  get '/managed_assets/people_assets/unassigned' => 'managed_assets#index'
  get '/managed_assets/people_assets/archived' => 'managed_assets#index'
  get '/managed_assets/should_display_people_tab' => 'managed_assets#should_display_people_tab'
  put '/managed_assets/associated_information', to: 'associated_information#update_information'
  get '/managed_assets/discovered_assets', to: 'managed_assets#index'
  get '/managed_assets/discovered_assets/ready_for_import', to: 'managed_assets#index'
  get '/managed_assets/discovered_assets/unrecognized', to: 'managed_assets#index'
  get '/managed_assets/discovered_assets/incomplete', to: 'managed_assets#index'
  get '/managed_assets/discovered_assets/imported', to: 'managed_assets#index'
  get '/managed_assets/discovered_assets/ignored', to: 'managed_assets#index'
  get '/managed_assets/discovered_assets/:id', to: 'managed_assets#index'
  get '/managed_assets/staff_tools' => 'managed_assets#new'
  get '/managed_assets/discovery' => 'managed_assets#index'
  get '/managed_assets/network_probe' => 'managed_assets#index'
  get '/managed_assets/agent' => 'managed_assets#index'
  get '/managed_assets/self_onboarding' => 'managed_assets#index'
  get '/managed_assets/discovery_tools' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/agents' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/probes' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/logs' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/connections' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/connectors' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/connectors/agent' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/connectors/network_probe' => 'managed_assets#index'
  get '/managed_assets/discovery_tools/connectors/self_onboarding' => 'managed_assets#index'
  get '/managed_assets/agents' => 'managed_assets#index'
  get "/managed_assets/settings" => redirect("/managed_assets/settings/asset_types")
  get '/managed_assets/settings/lifecycle_management' => 'managed_assets#index'
  get '/managed_assets/settings/qr_code' => 'managed_assets#index'
  get '/managed_assets/settings/asset_types' => 'managed_assets#index'
  get '/managed_assets/settings/table_data' => 'managed_assets#index'
  get '/managed_assets/settings/card_data' => 'managed_assets#index'
  get '/managed_assets/settings/statuses' => 'managed_assets#index'
  get '/managed_assets/settings/tags' => 'managed_assets#index'
  get '/managed_assets/export' => 'managed_assets_xls_export#index'
  post '/managed_assets/import' => 'managed_assets_xls#create'
  get '/products' => 'products#company_products'
  get '/managed_assets/discovery_tools/agents/export' => 'agent_locations_xls_export#index'
  get '/managed_assets/assets/insights' => 'managed_asset_insights#index'
  get '/managed_assets/assets/insights/location' => 'managed_asset_insights#location'
  get '/managed_assets/assets/insights/search_field_filter_options' => 'managed_asset_insights#search_field_filter_options'
  get '/asset_insight_widgets' => 'asset_insight_widgets#index'
  put '/asset_insight_widgets' => 'asset_insight_widgets#update'
  get '/managed_assets/insights/export' => 'managed_assets_insights_xls_export#index'
  get 'managed_assets/used_assets' => 'managed_assets#previously_used_assets_by_user'
  get 'managed_assets/managed_assets' => 'managed_assets#previously_managed_assets_by_user'
  get '/managed_assets/automated_tasks/new' => 'managed_asset#index'
  get '/managed_assets/automated_tasks/:id/edit' => 'managed_assets#index'

  namespace :managed_assets do
    get 'risk_center/export', to: 'risk_center_xls_export#index'

    get 'risk_center_summaries', to: 'asset_risk_center_widgets#summary'
    get 'assets/risk_center_widgets', to: 'asset_risk_center_widgets#index'
    get 'risk_center_widget', to: 'asset_risk_center_widgets#widget_data'
    get 'risk_center_widgets_summary', to: 'asset_risk_center_widgets#widgets_summary_data'
    get 'should_display_asset_risk_center', to: 'asset_risk_center_widgets#should_display_asset_risk_center'
    get 'has_agent_installed', to: 'asset_risk_center_widgets#has_agent_installed'
    get 'custom_widget_options', to: 'asset_risk_center_widgets#custom_widget_options'

    post 'risk_center_widgets', to: 'asset_risk_center_widgets#create'
    put 'risk_center_widgets', to: 'asset_risk_center_widgets#update'
    delete 'risk_center_widgets', to: 'asset_risk_center_widgets#destroy'
    post 'risk_center_widget/widget_custom_option', to: 'asset_risk_center_widgets#create_widget_custom_option'
  end

  get '/managed_assets/:id/software' => 'managed_assets#show'
  get '/managed_assets/:id/software/:software_id' => 'managed_assets#show'
  get '/managed_assets/:id/hardware_details' => 'managed_assets#show'
  get '/managed_assets/:id/system_details' => 'managed_assets#show'
  get '/managed_assets/:id/history' => 'managed_assets#show'
  get '/managed_assets/:id/notes' => 'managed_assets#show'
  get '/managed_assets/:id/details' => 'managed_assets#show'
  get '/managed_assets/:id/sources' => 'managed_assets#show'
  get '/managed_assets/:id/cloud_asset_attributes' => 'managed_assets#show'
  get '/managed_assets/:id/asset_usage_history' => 'managed_assets#usage_history'
  get '/managed_assets/:id/usage_history' => 'managed_assets#asset_usage_history'
  get '/managed_assets/:id/asset_latency_history' => 'managed_assets#latency_history'
  get '/managed_asset_history' => 'managed_asset_history#index'
  post '/managed_assets/update_asset_type' => 'managed_assets#bulk_asset_types_update'
  put '/managed_assets/update_status' => 'managed_assets#bulk_status_update'

  post '/bulk_managed_assets/bulk_archive' => 'bulk_managed_assets#bulk_archive'
  post '/bulk_managed_assets/bulk_unarchive' => 'bulk_managed_assets#bulk_unarchive'
  post '/bulk_managed_assets/bulk_delete' => 'bulk_managed_assets#bulk_delete'
  post '/bulk_managed_assets/bulk_add_locations' => 'bulk_managed_assets#add_locations'
  post '/bulk_managed_assets/bulk_assign_users' => 'bulk_managed_assets#assign_users'
  post '/bulk_managed_assets/inline_assign_users' => 'bulk_managed_assets#inline_assign_users'
  post '/bulk_managed_assets/inline_remove_users' => 'bulk_managed_assets#inline_remove_users'
  post '/bulk_managed_assets/bulk_update' => 'bulk_managed_assets#bulk_update'

  get '/managed_assets/discovered_assets/:id/software' => 'managed_assets#index'
  get '/managed_assets/discovered_assets/:id/sources' => 'managed_assets#index'
  get '/managed_assets/discovered_assets/:id/details' => 'managed_assets#index'
  get '/managed_assets/discovered_assets/:id/asset_discovery_xml_scans' => 'managed_assets#index'
  get '/managed_assets/discovered_assets/:id/discovery_details' => 'managed_assets#index'
  get '/managed_assets/discovered_assets/:id/cloud_asset_attributes' => 'managed_assets#index'
  get '/discovered_asset_table_data' => 'discovered_asset_preferences#index'
  get '/asset_discovery_xml_scans/:id' => 'discovered_asset_xml_details#index'
  post '/managed_assets/save_imported_records' => 'managed_assets#save_imported_records'

  get '/iframes/software_migration' => 'iframes#software_migration'
  post '/software_migration_messages' => 'software_migration_messages#create'

  resources :qr_codes, only: :index
  resources :managed_asset_statuses, only: [:index, :update]

  resources :managed_assets do
    member do
      post :archive
      post :unarchive
      get :sources_summary
      get :asset_usage_history
    end

    resources :asset_softwares, only: [:index, :update, :create, :destroy]
    resources :archived_asset_softwares, only: [:update, :destroy]
    resources :cloud_asset_attributes, only: [:create, :update, :destroy]

    collection do
      get :os_list
      get :impacts
      post :send_app_to_all
      get :windows_exe_available
      delete :remove_alert
      get :get_associated_tickets_history
      get :get_asset_report_record
      post :reset_download_link
    end
  end

  resources :merge_managed_assets, only: [:create] do
    collection do
      get :assets_info
      post :undo_merge
    end
  end

  post '/entity_reassignments' => 'entity_reassignments#update_entity'
  resources :ticket_creators, only: [:index]
  resources :company_user_related_entities, only: [:index]

  resources :subdomain_checks, only: [:show]
  resources :asset_types
  resources :asset_statuses, only: [:index]
  post '/bulk_company_users/bulk_archive' => 'bulk_company_users#bulk_archive'
  post '/bulk_company_users/bulk_delete' => 'bulk_company_users#bulk_delete'
  post '/bulk_company_users/bulk_unarchive' => 'bulk_company_users#bulk_unarchive'
  post '/bulk_company_users/bulk_assign_location' => 'bulk_company_users#bulk_assign_location'
  post '/bulk_company_users/bulk_assign_group' => 'bulk_company_users#bulk_assign_group'

  get '/asset_fields/:company_asset_type_id/all_fields', to: 'asset_fields#all_fields'

  gem 'activeadmin', github: 'activeadmin'
  get 'version/debug', to: 'version#debug'

  get '/dashboard' => 'companies#dashboard', as: 'dashboard'
  get '/dashboard/network_activity' => 'monitoring_devices#dashboard_data', as: 'dashboard_network_activity'
  put '/check_out_my_sample_onboarding' => 'companies#check_out_my_sample_onboarding'
  put '/check_out_my_module_onboarding' => 'users#check_out_my_module_onboarding'
  put '/selected_first_module' => 'users#selected_first_module'

  get '/company/edit' => 'companies#edit'
  get '/company/overview' => 'companies#show'
  get '/company/password' => 'companies#show'
  get '/company/locations' => 'companies#show'
  get '/company/locations/new' => 'companies#get_company_template'
  get '/company/locations/:id' => 'companies#show'
  get '/company/partners' => 'companies#show'
  get '/company/partners/:id/edit' => 'companies#show'
  get '/company/partners/new' => 'companies#show'
  get '/company/locations/:id/edit' => 'companies#show'
  get '/company/users/upload' => 'companies#show'
  get '/company/users/new' => 'companies#get_company_template'
  get '/company/users/sync_and_download' => 'companies#show'
  get '/company/users/ready_for_import' => 'companies#show'
  get '/company/users/archived' => 'companies#show'
  get '/company/users/imported' => 'companies#show'
  get '/company/users/:id' => 'companies#show'
  get '/company/users/:id/edit' => 'companies#show'
  get '/company/users' => 'companies#show'
  get '/company/locations/:id' => 'companies#show'
  get '/company/permissions/genuity_admins' => 'companies#show'
  get '/company/permissions' => 'companies#show'
  get '/company/permissions/assets' => 'companies#show'
  get '/company/permissions/contracts' => 'companies#show'
  get '/company/permissions/vendors' => 'companies#show'
  get '/company/permissions/telecom' => 'companies#show'
  get '/company/permissions/helpdesk' => 'companies#show'
  get '/company/permissions/network' => 'companies#show'
  get '/company/categories' => 'companies#show'
  get '/company/departments' => 'companies#show'
  get '/company/security' => 'companies#get_company_template'
  get '/company/business_settings' => 'companies#show'
  get '/company/company_domains_settings' => 'companies#show'
  get '/company/current_company' => 'companies#get_current_company'
  get '/reports' => 'reports#index'
  post '/categories/update_categories_position' => 'categories#update_categories_position'
  post '/departments/update_departments_position' => 'departments#update_departments_position'

  scope :company do
    get '/custom_forms' => 'companies#get_company_template'
    get '/business_hours'  => 'business_hours#index'
    post '/update_business_hours' => 'business_hours#update_business_hours'
    get '/company_domains' => 'company_domains#index'
    post '/update_company_domain' => 'company_domains#update_company_domain'
    get '/send_invitation' => 'existing_companies_invitations#send_invitation'
    get '/logo_url' => 'companies#logo_url'
    scope :feature_requests do
      get '/' => 'companies#show'
      get '/recent/:id/edit' => 'companies#show'
      get '/new' => 'companies#show'
      get '/pending' => 'companies#show'
      get '/requested' => 'companies#show'
      get '/upcoming' => 'companies#show'
      get '/recent' => 'companies#show'
      get '/pending/:id' => 'companies#show'
      get '/requested/:id' => 'companies#show'
      get '/upcoming/:id' => 'companies#show'
      get '/recent/:id' => 'companies#show'
      get '/pending/:id/edit' => 'companies#show'
      get '/requested/:id/edit' => 'companies#show'
      get '/upcoming/:id/edit' => 'companies#show'
    end
    scope :custom_forms do
      get '/locations' => 'companies#show'
      get '/locations/:id' => 'companies#get_company_template'
      get '/locations/:id/edit' => 'companies#get_company_template'
      get '/company_users' => 'companies#show'
      get '/company_users/:id' => 'companies#get_company_template'
      get '/company_users/:id/edit' => 'companies#get_company_template'
    end
  end

  get '/reconfirm_company_linking/:id' => 'existing_companies_invitations#reconfirm_company_linking', as: 'invitation_reconfirmation'
  get '/link_company' => 'existing_companies_invitations#link_company', as: 'link_company'
  get '/feature_request_summary' => 'feature_request_summary#index'
  get '/company/billing/pricing' => 'companies#show'
  get '/company/billing' => 'companies#show', as: 'company_billing'
  get '/company/billing/history' => 'companies#show'
  get '/company/billing/payment_methods' => 'companies#show'
  get '/company/billing/payment' => 'companies#show'
  get '/company/discover' => 'companies#show', as: 'user_discovery'
  get '/company/fiscal_year' => 'companies#fiscal_year', as: 'company_fiscal_year'
  get '/company/new' => 'companies#new', as: 'new_company'
  get '/company/new/:id' => 'companies#new'
  get '/company/new_child_company' => 'companies#new', as: 'new_child_company'
  get '/company/invite_existing_company' => 'companies#new', as: 'invite_company'
  get '/company' => 'companies#show', as: 'company'

  post '/company_users/update_password' => 'company_users#update_password'
  get '/company_users/:id/user_dependent_automated_task' => 'company_users#user_dependent_automated_task'
  put '/company_users/:id/move_teammate' => 'company_users#move_teammate'
  put '/company_users/:id/reset_authenticator_app' => 'company_users#reset_authenticator_app'
  get '/company_users/export' => 'company_users_xls_export#index'
  get '/report-columns'      =>   'report_columns#index'
  put '/company_users/:id/update_user_office_status' => 'company_users#update_user_office_status'
  get 'company_users/fetch_company_user_vendors', to: 'company_users#fetch_company_user_vendors'
  put '/tickets/:id/update_notification_status' => 'help_tickets/tickets#update_notification_status'
  put '/tickets/:id/update_ticket_seen_status' => 'help_tickets/tickets#update_ticket_seen_status'
  put '/company_users/:id/update_user_active_status' => 'company_users#update_user_active_status'

  resources :review_companies, only: [:index]
  resources :companies, only: [:create, :update]

  # Filter options
  scope module: :options do
    resources :department_options, only: [:index]
    resources :assigned_to_options, only: [:index]
    resources :category_options, only: [:index]
    resources :company_options, only: [:index]
    resources :company_user_options, only: :index
    resources :contract_options, only: [:index]
    resources :contributor_options, only: [:index]
    get '/contributor_options_with_current' => 'contributor_options_with_current#index'
    get '/contributor_options_with_guests' => 'contributor_options_with_guests#index'
    resources :created_by_options, only: [:index]
    resources :company_and_default_vendor_options, only: [:index]
    resources :custom_form_field_options, only: [:index]
    resources :custom_form_options, only: [:index]
    resources :custom_form_template_options, only: [:index]
    resources :entity_options, only: [:index]
    resources :external_user_options, only: [:index]
    resources :field_options, only: [:index]
    resources :group_options, only: [:index, :show]
    resources :helpdesk_agent_options, only: [:index]
    resources :linkable_options, only: [:index]
    resources :location_options, only: [:index]
    resources :managed_asset_options, only: [:index]
    resources :os_options, only: [:index]
    resources :priority_options, only: [:index]
    resources :smart_list_field_options, only: [:index]
    resources :smart_lists_for_custom_forms, only: [:index]
    resources :status_options, only: [:index]
    resources :source_options, only: [:index]
    resources :telecom_provider_options, only: [:index]
    resources :telecom_service_options, only: [:index]
    resources :uninvited_company_user_options, only: [:index]
    resources :vendor_options, only: [:index]
    resources :workspace_options, only: [:index]
    resources :tag_options, only: [:index]
  end

  scope module: :help_tickets do
    resources :email_confirmation_emails, only: [:update]
  end

  resources :custom_forms
  resources :abbreviated_custom_forms, only: [:index]
  resources :workspaces
  resources :expanded_permissions, only: [:index]
  resources :company_dissociators, only: [:index, :update]

  resources :workspace_custom_forms, only: [:destroy]

  resources :companies, only: [:create, :update]

  resources :help_desk_custom_emails, only: [:index]

  resources :feature_requests do
    resources :feature_request_comments, only: [:index]
    resources :feature_request_attachments, only: [:index]
    collection do
      post '/pending/approve_feature' => 'feature_requests#approve_feature'
      post '/requested/update_vote' => 'feature_requests#update_total_votes'
    end
  end

  resources :feature_request_comments, only: [:create, :update]
  resources :feature_request_attachments, only: [:create, :destroy]

  resources :company_users, only: [:show, :index, :create, :update, :destroy] do
    collection do
      get :contact_users
    end
    resources :custom_form_values, only: [:update, :create, :destroy, :show, :index]
  end
  resources :accept_user_invites, only: [:show, :update]
  resources :invited_company_users, only: [:show]
  resources :dashboard_preferences, only: [:show, :create, :update]
  get '/current_company_users/curr_user_dept' => 'dashboard_preferences#current_user_department'

  resources :lookups, only: [] do
    get :index, on: :collection
    get :ip_lookup, on: :collection
    get :number_lookup, on: :collection
  end

  # valid email extensions
  resources :valid_email_extensions, only: [:index]
  put '/valid_email_extensions/update' => 'valid_email_extensions#update'

  resources :registration_emails, only: [:index, :create, :update, :destroy]
  get '/registration_emails/export' => 'registration_emails#export'
  resources :registration_email_restores, only: [:update]
  resources :module_members, only: [:index]

  resources :company_trackings, only: [:index]

  authenticate :user, lambda { |u| u.super_admin? } do
    resources :azure_licenses, only: [:index]
  end

  authenticate :user, lambda { |u| u.super_admin? } do
    resources :report_templates, only: [:index]
  end

  resources :azure_reports, only: [:index, :create, :destroy]
  post '/azure_reports/download_report' => 'azure_reports#download_report'

  resources :subnet_calculator, only: [:index] do
    get :subnet_details, on: :collection
  end

  resources :general_transaction_summaries, only: [:index]
  resources :validate_emails, only: [:index, :show]

  resources :users, only:[:index] do
    member do
      get :subdomain_session
    end
    collection do
      get :validate_email
      get :search
    end
    resources :monitoring_devices, only: [:index, :show] do
      get  :edit_monitoring, on: :collection
      get  :request_info, on: :collection
      post :update_monitoring_info, on: :collection
      post :request_monitoring, on: :collection
      get  :info, on: :collection
    end
  end
  get 'network_monitoring' => 'monitoring_devices#index'

  resources :user_files, only: [:show, :create]

  namespace :api, defaults: { format: :json }, path: '/' do
    scope module: :v1, constraints: ApiConstraints.new(version: 1, default: true) do
      # We are going to list our resources here
      resources :monitoring_devices, only: [] do
        collection do
          get :devices_list
          get :device_sensors_list
          get :sensor_details
          get :sensor_graph_data
          get :get_sensor_data
          get :historical_graph_data
        end
      end
    end

    scope module: :v2, constraints: ApiConstraints.new(version: 2, default: true) do
      resources :sessions, only: [] do
        get :retrieve_token, on: :collection
        get :token_verified, on: :collection
      end
      resources :app_versions, only: [] do
        get :download_latest_installer, on: :collection
        get :get_latest_version, on: :collection
      end
      resources :asset_collections, only: [] do
        post :remote_asset, on: :collection
        post :host_asset, on: :collection
        post :discovery_asset, on: :collection
      end
    end
  end

  namespace :api do
    namespace :v1 do
      get '/helpdesk_categories' => 'helpdesk_categories#index'
      get '/knowledge_base' => 'public_articles#index'
      get '/show_knowledge_base' => 'public_articles#show'
      get '/faqs' => 'public_faqs#index'
      get 'department_options' => 'departments#index'
      resources :tasks
      resources :time_spents
      resources :ticket_comments
      resources :task_checklists
      resources :attachment_uploads
      resources :contributor_actionable_alerts
      resources :activities, only: [:index]
      resources :categories, only: [:index]
      resources :app_versions, only: [:index]
      resources :linkable_links, only: [:create]
      resources :company_selections, only: [:index]
      resources :abbreviated_tickets, only: [:index]
      resources :expanded_permissions, only: [:index]
      resources :workspace_selections, only: [:index]
      resources :ticket_dashboard_graphs, only: [:index]
      resources :closing_surveys, only: [:show, :update]
      resources :user_devices, only: [:create, :destroy, :update]
      resources :current_actionable_alerts, only: [:index] do
        get :actionable_alerts_count , on: :collection
      end
      resources :custom_form_attachments, only: [:create, :destroy] do
        get :fetch_attachments, on: :collection
      end
      resources :project_tasks do
        get :project_task_options , on: :collection
      end

      resources :tickets do
        member do
          post :archive
          post :unarchive
          put :update_ticket_seen_status
          put :update_notification_status
        end

        resources :stop_watch_timers
        resources :parent_tasks, only: [:create, :destroy]
        resources :custom_form_values, only: [:update, :create, :destroy, :show, :index]
      end

      resources :custom_forms do
        resources :custom_form_tickets, only: [:create, :index, :update]

        get 'vendor_options' => 'options/vendor_options#index'
        get 'contract_options' => 'options/contract_options#index'
        get 'location_options' => 'options/location_options#index'
        get 'linkable_options' => 'options/linkable_options#index'
        get 'contributor_options' => 'options/contributor_options#index'
        get 'managed_asset_options' => 'options/managed_asset_options#index'
        get '/external_user_options' => 'options/external_user_options#index'
        get 'telecom_service_options' => 'options/telecom_service_options#index'
        get '/contributor_options_with_guests' => 'options/contributor_options_with_guests#index'
      end

      scope module: :options do
        resources :assigned_to_options, only: [:index]
        resources :company_options, only: [:index]
        resources :contract_options, only: [:index]
        resources :contributor_options, only: [:index]
        get '/contributor_options_with_guests' => 'contributor_options_with_guests#index'
        resources :created_by_options, only: [:index]
        resources :custom_form_options, only: [:index]
        resources :linkable_options, only: [:index]
        resources :location_options, only: [:index]
         resources :managed_asset_options, only: [:index]
        resources :priority_options, only: [:index]
        resources :smart_lists_for_custom_forms, only: [:index]
        resources :status_options, only: [:index]
        resources :source_options, only: [:index]
        resources :telecom_service_options, only: [:index]
        resources :vendor_options, only: [:index]
        resources :workspace_options, only: [:index]
      end

      get '/user_detail' => 'users#user_detail'
      get '/ticket_list_columns' => 'ticket_list_columns#index'
      post '/renew_tokens' => 'authenticate_users#renew_tokens'
      get '/authenticate_user' => 'authenticate_users#authenticate'
      get '/user_subdomains' => 'authenticate_users#user_subdomains'
      get '/custom_form_fields/search' => 'custom_form_fields#search'
      get '/send_email_confirmation' => 'users#send_email_confirmation'
      get '/authenticate_sso_user' => 'authenticate_users#authenticate_sso_user'
      post '/update_pinned_company_status' => 'users#update_pinned_company_status'
      post '/create_project_tasks' => 'task_checklists#create_project_task_in_ticket'
      post '/project_task_positions/:help_ticket_id' => 'project_task_positions#update_positions'

      resources :mfa, only: [:index] do
        post :verify, on: :collection
        post :registration, on: :collection
        post :add_registered_device, on: :collection
        post :edit_registered_device, on: :collection
        post :udpate_mfa_settings, on: :collection
        get :settings, on: :collection
        get :send_code_via_email, on: :collection
        get :send_code_via_text, on: :collection
      end

      post '/confirm_new_password' => 'forgot_passwords#update_password'
      post '/send_reset_password_code' => 'forgot_passwords#send_reset_password_code'
    end

    namespace :v3 do
      resources :discovered_assets, only: [:create]
      resources :managed_assets, only: [:create]
      resources :company_users, only: [:create]
      resources :discovered_users, only: [:create]
      resources :probe_locations, only: [:create]
      resources :asset_discovery_logs, only: [:create]
      resources :staff_import_service_logs, only: [:create]
      resources :mac_app_versions, only: [:index]
    end
  end

  # Expand as necessary (FYI, we expect everything but index to be json, not a view)
  resources :impacts, only: [:index]

  get '/telecom_provider_summary' => 'telecom_provider_summaries#index'

  resources :locations, only: [:show, :index, :create, :update, :destroy] do
    post '/import_bulk_locations' => 'locations#import_bulk_locations', on: :collection
    resources :custom_form_values, only: [:update, :create, :destroy, :show, :index]
  end

  resources :user_locations, only: [:index, :create, :update, :destroy]

  post '/import_integration_location' => 'integration_locations#import_locations'
  get '/vendor_spend_by_category' => 'vendor_spend_by_category#index'
  get '/vendor_spend_by_source' => 'vendor_spend_by_source#index'
  get '/vendor_spend_by_tag' => 'vendor_spend_by_tag#index'
  get '/vendor_spend_by_vendor' => 'vendor_spend_by_vendor#index'

  get '/telecom_providers/:telecom_provider_id/ip_addresses'       => 'ip_addresses#index'
  get '/telecom_providers/:telecom_provider_id/phone_numbers'       => 'phone_numbers#index'
  get '/telecom_providers/all' => 'telecom_providers#index'
  get '/telecom_providers/services' => 'telecom_providers#index'
  get '/telecom_providers/phone_numbers' => 'telecom_providers#index'
  get '/telecom_provide_newrs/:id/phone_numbers' => 'telecom_providers#index'
  get '/telecom_providers/ip_addresses' => 'telecom_providers#index'
  get '/telecom_providers/:service_id/ip_addresses' => 'telecom_providers#index'
  get '/telecom_providers/case_study' => 'telecom_providers#index'
  get '/telecom_providers/:provider_id/services' => 'telecom_providers#index'
  get '/telecom_providers/:provider_id/services/:service_id' => 'telecom_providers#index'
  get '/telecom_providers/:provider_id/services/:service_id/edit' => 'telecom_providers#index'
  get '/telecom_providers/:provider_id/phone_numbers' => 'telecom_providers#index'
  get '/telecom_providers/:provider_id/ip_addresses' => 'telecom_providers#index'

  resources :telecom_services do
    member do
      post :archive
      post :unarchive
    end
    collection do
      get :export_data
      get :dont_show_again
    end
  end

  resources :help_center_logos, only: [:create, :update, :index, :destroy] do
    collection do
      delete 'delete_image_attachment', to: 'help_center_logos#destroy'
    end
  end

  resources :xls_imports, only: [:show]
  resources :custom_surveys
  resources :custom_surveys_responses
  resources :custom_domains, only: [:create, :update, :index]
  resources :telecom_vendors, only: [:update]
  resources :managed_asset_vendors, only: [:update]
  resources :contract_vendors, only: [:index, :update]
  resources :general_transaction_vendors, only: [:update]
  resources :discovered_vendors, only: [:index] do
    get :general_transactions, on: :member
    post :import_bulk, on: :collection
    post :bulk_archive, on: :collection
    post :bulk_delete, on: :collection
    post :bulk_unarchive, on: :collection
    get :summary, on: :collection
    put :update_category, on: :member
  end
  get '/custom_domains/:id/verify_name', to: 'custom_domains#verify_name'
  get '/custom_domains/:id/resend_email', to: 'custom_domains#resend_email'
  put '/custom_domains/:id/toggle_active', to: 'custom_domains#toggle_active'
  put '/custom_surveys/:id/toggle_active', to: 'custom_surveys#toggle_active'
  get '/custom_survey/should_display_custom_surveys', to: 'custom_surveys#should_display_custom_surveys'
  get '/should_display_survey_settings', to: 'workspaces#should_display_survey_settings'
  get '/custom_surveys_response/get_unopened_response', to: 'custom_surveys_responses#get_unopened_response'
  get '/custom_surveys_response/resend_email', to: 'custom_surveys_responses#resend_email'
  get '/vendors/discovered_vendors', to: 'vendors#index'
  get '/vendors/discovered_vendors/ready_for_import', to: 'vendors#index'
  get '/vendors/discovered_vendors/imported', to: 'vendors#index'
  get '/vendors/discovered_vendors/ignored', to: 'vendors#index'

  get '/vendors/vendors-products/:id'          => 'vendors_based_insights#vendors_products'
  get '/vendors/vendors-insights'              => 'vendors_based_insights#index'
  get '/vendors/tags-insights'                 => 'tags_based_insights#index'
  get '/vendors/categories-insights'           => 'categories_based_insights#index'
  get '/vendors/products-insights'             => 'products_based_insights#index'
  get '/vendors/subscriptions-summary'         => 'vendor_insights#subscriptions_summary'
  get '/vendors/apps-insights-summary'         => 'vendor_insights#apps_insights_summary'
  get '/vendors/cloud-usage-insights'          => 'vendor_insights#cloud_usage_graph_data'
  get '/vendors/all'                           => 'vendors#index'
  get '/vendors/alerts'                        => 'vendors#index'
  get '/vendors/:id/usage/:app'                => 'vendors#index'
  get '/vendors/:id/usage'                     => 'vendors#index'
  get '/vendors/:id/products/new'              => 'vendors#new'
  get 'vendors/:id/products/:product_id'       => 'vendors#index'
  get '/vendors/:id/products/:product_id/edit' => 'vendors#index'
  get '/vendors/:id/cloud_usage'               => 'vendors#index'
  get '/vendors/:id/basic_information'         => 'vendors#index'
  get '/vendors/:id/billing'                   => 'vendors#index'
  get '/vendors/:id/contacts'                  => 'vendors#index'
  get '/vendors/:id/related_items'             => 'vendors#index'
  get '/vendors/:id/contacts/:contact_id'      => 'vendors#index'
  get '/vendors/:id/managers'                  => 'vendors#index'
  get '/vendors/:id/contracts'                 => 'vendors#index'
  get '/vendors/:id/telecom'                   => 'vendors#index'
  get '/vendors/:id/alerts'                    => 'vendors#index'
  get '/vendors/sync_accounts'                 => 'vendors#index', as: 'integrations'
  get '/vendors/insights'                      => 'vendors#index'
  get '/vendors/insights/overview'             => 'vendors#index'
  get '/vendors/insights'                      => 'vendors#index'
  get '/vendors/insights/product_subscriptions'=> 'vendors#index'
  get '/vendors/dashboard_transactions'        => 'vendors#index'
  get '/vendors/dashboard_cloud'               => 'vendors#index'
  get '/vendors/cloud'                         => redirect('/vendors/saas_usage', status: 302)
  get '/vendors/cloud/:status'                 => redirect('/vendors/saas_usage', status: 302)
  get '/vendors/cloud/ignored'                 => redirect('/vendors/saas_usage/ignored', status: 302)
  get '/vendors/saas_usage'                    => 'vendors#index'
  get '/vendors/saas_usage/ignored'            => 'vendors#index'
  get '/vendors/saas_usage/new'                => 'vendors#index'
  get '/vendors/saas_usage/:id/edit'           => 'vendors#index'
  get '/vendors/saas_usage/:id/users_info'     => 'vendors#index'
  get '/vendors/users'                         => 'vendors#index'
  get '/vendors/transactions/:status'          => 'vendors#index' # recognized transactions
  get '/vendors/transactions'                  => 'vendors#index'
  get '/products/:id'                          => 'products#show'
  get '/products/:id/transactions'             => 'products#transactions'
  get '/products/:id/assets'                   => 'products#assets'
  get '/products/:id/usage_transactions'       => 'products#usage_transactions'
  post '/vendors/:id/base_product'             => 'vendors#create_base_product'
  get '/vendors/telecom_recommendations'       => 'vendors#telecom_recommendations'
  post '/vendors/bulk_update'                  => 'vendors#bulk_update'
  resources :archived_vendors, only: :index

  scope '/vendors' do
    resources :module_alert_notifications, only: [:index]
  end

  resources :download_windows_agents, only: [:create]
  resources :download_windows_probes, only: [:create]
  resources :download_user_import_apps, only: [:create]

  resources :default_vendors do
    scope module: 'options' do
      resources :product_options, only: [:index]
    end
  end

  resources :telecom_providers
  post '/telecom_providers/:telecom_provider_id/archive'       => 'telecom_providers#archive'
  get '/telecom_providers/:telecom_provider_id/disable_insights'  => 'telecom_providers#disable_insights_box'

  resources :vendors do
    member do
      put :archive
      get '/transactions/:status' => 'vendors#show'
    end
    resources :general_transactions, only: [:index, :create, :update]
    resources :module_alerts, only: [:index, :create]
    resources :module_alert_notifications, only: [:index]
    resources :vendor_contacts
    scope module: 'options' do
      resources :product_options, only: [:index]
    end
    resources :products, only: [:index, :create, :show, :update]
  end
  get 'vendors/:id/apps' => 'vendors#apps'

  resources :products, only: [:destroy]
  resources :product_spends, only: [:index]
  get 'product_spends/cloud_spend_breakdown' => 'product_spends#cloud_spend_breakdown'

  resources :alert_defaults, only: [:index]
  put '/alert_defaults' => 'alert_defaults#update'

  resources :module_alerts
  resources :module_alert_notifications

  resources :phone_numbers do
    collection do
      post :bulk_delete
      get :validate_phone_number
    end
  end

  resources :ip_addresses do
    collection do
      post :bulk_delete
    end
  end

  resources :locations, only: [:index]
  resources :discovered_voice_services, only: [:index, :create]
  resources :discovered_data_services, only: [:index, :create]
  resources :gamification_notification_limits, only: [:index, :create]
  resources :managed_asset_tags, only: [:create, :update, :destroy] do
    collection do
      post :add_tags
    end
  end

  resources :contract_tags do
    collection do
      post :add_tags
    end
  end

  resources :article_tags, only: [:index]

  get 'devtools' => 'devtools#index'
  get 'devtools/colors' => 'devtools#index'
  get 'devtools/theming' => 'devtools#index'
  get 'devtools/icons' => 'devtools#index'
  get 'devtools/css' => 'devtools#index'
  get 'devtools/ruby' => 'devtools#index'
  get 'devtools/vue' => 'devtools#index'
  get 'devtools/css-components' => 'devtools#index'
  get 'devtools/vue-components' => 'devtools#index'
  get 'devtools/nulodgicons' => 'devtools#nulodgicons'

  # Gamification API routes
  get '/gamification' => 'gamification#index'
  get '/gamification/upcoming_rewards' => 'gamification#upcoming_rewards'
  get '/gamification/rewards_history' => 'gamification#rewards_history'
  resources :current_company_user_level, only: [:index]
  resources :badges, only: [:index]

  resources :payment_sources, only: [:index, :create, :destroy, :update]
  resources :subscriptions, only: [:index, :create, :update]
  resources :subscription_subscribers, only: [:update]
  resources :subscription_activities, only: [:index]
  resources :subscription_stripe_transactions, only: [:index]
  resources :subscription_plans, only: [:index]

  resources :recommended_products, only: [:show]

  post '/stripe_webhook' => 'stripe_webhooks#index'

  get '/static_map' => 'google_maps#static_map'

  resources :company_integrations, only: [:index]
  resources :integrated_vendors, only: [:index, :update]
  resources :new_integrated_vendors_count, param: :slug, only: [:index, :update]

  post 'managed_assets/discovery_tools/company_integrations/:id/dismiss_delete_alert', to: 'company_integrations#dismiss_delete_alert'
  get 'managed_assets/discovery_tools/company_integrations/dismissed_alerts', to: 'company_integrations#dismissed_alerts' 
  get 'company_integrations/display_google_workspace_integration' => 'company_integrations#display_google_workspace_integration'

  namespace :integrations do
    resources :apps, only: [:index, :create, :update, :destroy]
    get 'apps/old_apps_of_user', to: 'apps#old_apps_of_user'
    resources :users, only: [:index, :create, :update, :destroy]

    put 'app/update_licenses_info' => 'apps#update_license_info'
    get 'apps/services' => 'apps#services'
    get 'users/fetch_users' => 'users#fetch_users'
    put 'app/bulk_update' => 'apps#bulk_update'
    put 'apps/update' => 'apps#update'
    get 'app/:id/users/:type' => 'users#index'

    get 'check_access' => 'apps#check_access'

    namespace :slack do
      resources :configs, only: [:index, :destroy, :update]
      get '/oauth2callback' => 'configs#create'
      get '/authorize' => 'consent#show'
    end

    namespace :ms_teams do
      resources :configs, only: [:index, :destroy]
      post 'configs/send_connector_status_update' => 'configs#send_connector_status_update'
    end

    namespace :gsuite do
      resources :configs, only: [:destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      get '/authorize' => 'configs#authorize'
      get '/oauth2callback' => 'configs#consume'
    end

    namespace :google_assets do
      resources :configs, only: [:create, :destroy, :show] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/resync' => 'configs#resync'
    end

    namespace :google_drive, path: 'google_drive' do
      get '/authorize' => 'configs#authorize'
      get '/oauth2callback' => 'configs#consume'
      get '/credentials' => 'configs#get_credentials'
      get '/should_display_drive_import' => 'configs#should_display_drive_import'
    end

    namespace :google_workspace do
      resources :configs, only: [:destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      get '/google_auth', to: 'configs#consent'
      get '/oauth2callback', to: 'configs#callback'
      post '/resync' => 'configs#resync'
    end

    namespace :one_drive, path: 'one_drive' do
      get '/credentials' => 'configs#get_credentials'
      get '/should_display_drive_import' => 'configs#should_display_drive_import'
    end

    namespace :gsuite_ad, path: 'gsuite_directory' do
      resources :configs, only: [:destroy]
      get '/authorize' => 'configs#authorize'
      get '/oauth2callback' => 'configs#consume'

      # Because of ad blockers, we need to keep standalone "ad" from the routes (e.g., no '-ad', '_ad', '/ad/')
      post '/sync_gsuite_directory' => "configs#sync_gsuite_ad"
      get '/sync_gsuite_directory_data' => "configs#sync_gsuite_ad_data"
      get '/gsuite_directory_integration' => "configs#gsuite_ad_integration"
      get '/company_channel_key' => "configs#company_channel_key"
    end

    namespace :sage_accounting do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      get '/authorize' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
    end

    namespace :microsoft do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
      get '/microsoft_integration' => "configs#microsoft_integration"
    end

    namespace :azure_ad do
      resources :configs, only: [:destroy]
      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
      get '/sync_azure_ad_data' => 'configs#sync_azure_ad_data'
      get '/azure_integration' => 'configs#azure_ad_integration'
      get '/sync_azure_ad' => 'configs#sync_azure_ad'
    end

    namespace :azure_ad_assets do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/resync' => 'configs#resync'
      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
    end

    namespace :jamf_pro do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/resync' => 'configs#resync'
    end

    namespace :mosyle do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/resync' => 'configs#resync'
    end

    namespace :sophos do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
          put '/import_type' => 'configs#update_import_type'
        end
      end
      post '/resync' => 'configs#resync'
    end

    namespace :azure do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
    end

    namespace :azure_assets do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/resync' => 'configs#resync'
      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
    end

    namespace :salesforce do
      resources :configs, only: [:destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      get '/authentication' => 'configs#authenticate'
      get '/authentication/callback' => 'configs#oauth_callback'
    end

    namespace :one_login do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
    end

    namespace :okta do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
    end

    namespace :ubiquiti do
      resources :configs, only: :destroy do
        member do
          post '/deactivate' => 'configs#deactivate'
          put '/import_type' => 'configs#update_import_type'
        end
      end
      post '/resync' => 'configs#resync'
      post '/fetch_sites' => "configs#fetch_sites"
      get '/fetch_sites' => "configs#fetch_sites"
      post '/save_config' => "configs#integrate_ubiquiti"
      post '/delete_controller' => "configs#delete_controller"
      get  '/activate_config/:id' => "configs#activate_config"
      get  '/deactivated_controllers' => "configs#deactivated_controllers"
    end

    namespace :kaseya do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
          put '/import_type' => 'configs#update_import_type'
        end
      end
      get '/oauth2callback' => 'configs#consent_callback'
      post '/resync' => 'configs#resync'
    end

    namespace :meraki do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
          put '/import_type' => 'configs#update_import_type'
        end
      end
      post '/resync' => 'configs#resync'
      post '/fetch_locations' => "configs#fetch_locations"
      get '/fetch_locations' => "configs#fetch_locations"
      get '/fetch_meraki_data' => "configs#fetch_meraki_data"
      post '/save_config' => 'configs#save_config'
      post '/customize' => 'configs#customize'
    end

    namespace :aws do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
    end

    namespace :quickbooks do
      resources :configs, only: [:destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end

      get '/authenticate' => 'configs#authenticate'
      get '/oauth_callback' => 'configs#oauth_callback'
      post '/fetch_transactions' => 'configs#fetch_transactions'
      get '/quickbooks_data' => 'configs#quickbooks_data'
    end

    namespace :xero do
      resources :configs, only: [:destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end

      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
    end

    namespace :expensify do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
    end

    namespace :bill do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/fetch_transactions' => 'configs#fetch_transactions'
      post '/fetch_orgs' => "configs#fetch_orgs"
      post '/fetch_integrated_vendors/:org_id'=> "configs#fetch_integrated_vendors"
    end

    namespace :aws_assets do
      resources :configs, only: [:show, :create, :destroy] do
        member do
          post "/deactivate" => "configs#deactivate"
        end
      end
      get "regions" => "configs#aws_regions"
      post '/resync' => 'configs#resync'
    end

    namespace :kandji do
      resources :configs, only: [:show, :create, :destroy] do
        member do
          post "/deactivate" => "configs#deactivate"
        end
      end
      post '/resync' => 'configs#resync'
    end

    namespace :netsuite do
      resources :configs, only: [:create, :destroy] do
        member do
          post "/deactivate" => "configs#deactivate"
        end
      end
    end

    namespace :sage_intacct do
      resources :configs, only: [:create, :destroy] do
        member do
          post "/deactivate" => "configs#deactivate"
        end
      end
    end

    namespace :ms_intune_assets do
      resources :configs, only: [:create, :destroy] do
        member do
          post '/deactivate' => 'configs#deactivate'
        end
      end
      post '/resync' => 'configs#resync'
      get '/adminconsent' => 'configs#consent'
      get '/oauth2callback' => 'configs#consent_callback'
    end
  end

  post '/referral_mailer' => 'referral_mailer#create'
  resources :referrals, only: [:index] do
    collection do
      put '/mark_as_paid' => 'referrals#mark_as_paid'
      put '/mark_as_pending' => 'referrals#mark_as_pending'
    end
  end

  post '/partner_form' => 'partner_form#create'

  resources :depreciations
  resources :depreciation_types, only: :index

  get '/asset_depreciation_breakdown/:asset_id' => 'asset_depreciation_breakdowns#show'

  get '/note_links' => 'note_links#index'

  get '/invoice_downloads' => 'invoice_downloads#index'

  resources :current_actionable_alerts, only: [:index]

  scope module: :webhooks, path: 'webhooks' do
    scope module: :monitoring, path: 'monitoring' do
      post '/company_users' => 'company_users#create'
      put '/companies' => 'companies#update'
    end

    scope module: :integrations, path: 'integrations' do
      scope module: :helpdesk, path: 'helpdesk' do
        scope module: :ms_teams, path: 'ms_teams' do
          resources :configs, only: [:create, :destroy, :update]
          resources :help_tickets, only: [:create, :update], as: 'ms_teams_help_tickets'
          resources :help_ticket_comments, only: [:create]
          resources :help_ticket_options, only: [:index]
          resources :help_ticket_message_ids, only: [:update]
          get '/configs/show' => 'configs#show'
          get '/contributor_options' => 'contributor_options#index'
        end
        scope module: :slack, path: 'slack' do
          post '/new_help_ticket' => 'help_tickets#new'
          post '/interactivity' => 'interactivity#index'
          post '/events' => 'events#index'
          post '/contributor_options' => 'contributor_options#index'
        end
      end

      post '/sage_intacct' => 'sage_intacct#create'
      post '/net_suite' => 'net_suite#create'
      post '/plaid' => 'plaid#create'
    end
  end

  resources :attachment_uploads do
    collection do
      delete 'bulk_delete_attachments'
    end
  end

  resources :custom_form_attachments, only: [:create, :destroy] do
    get :fetch_attachments, on: :collection
  end
  resources :custom_form_attributes
  resources :attachments, only: [:show], constraints: { id: /.+/ }

  scope :helpdesk do
    resources :custom_forms
  end

  resources :default_custom_form_templates, only: [:index]

  resources :custom_forms do
    get 'contract_options' => "options/contract_options#index"
    get 'contributor_options' => "options/contributor_options#index"
    get 'managed_asset_options' => "options/managed_asset_options#index"
    get 'location_options' => "options/location_options#index"
    get 'telecom_service_options' => "options/telecom_service_options#index"
    get 'vendor_options' => "options/vendor_options#index"
    get 'linkable_options' => "options/linkable_options#index"
    get '/external_user_options' => 'options/external_user_options#index'
    scope module: 'options' do
      resources :custom_form_field_options, only: [:index]
      resources :field_options, only: [:index]
    end

    resources :custom_form_tickets, only: [:create, :index]
    resources :custom_form_locations, only: [:create, :index, :update]
    resources :custom_form_users, only: [:create, :index, :update]
  end

  get '/module_walkthrough_bypass' => 'module_walkthrough_bypass#index'

  resources :custom_form_templates, only: [:index, :show]
  get '/custom_form_fields/search' => 'custom_form_fields#search'
  get '/custom_form_fields/fetch_form_fields' => 'custom_form_fields#fetch_all_form_fields'
  get '/custom_form_fields/fetch_people_list_fields' => 'custom_form_fields#fetch_people_list_fields'
  resources :custom_form_field_permissions, only: [:destroy]
  resources :custom_form_fields
  post 'custom_form_fields/:id', to: 'custom_form_fields#show'
  resources :automated_task_suggestions, only: [:update, :index]

  resources :vendor_alert_summaries, only: [:index, :show]

  resources :invoices, only: [:destroy]

  ## Help Ticket Routes
  get '/help_tickets/denied' => 'help_tickets/tickets#index'
  get '/help_tickets/ticket_activities' => 'help_tickets/tickets#index'
  get '/help_tickets/workspaces' => 'help_tickets/tickets#index'
  get '/help_tickets/workspaces/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/report' => 'help_tickets/tickets#index'
  get '/help_tickets/people' => 'help_tickets/tickets#index'
  get '/help_tickets/dashboard' => 'help_tickets/tickets#index'
  get '/help_tickets/initial' => 'help_tickets/tickets#index'
  get '/help_tickets/request' => 'help_tickets/tickets#index'
  get '/help_tickets/incoming' => 'help_tickets/tickets#index'
  get '/help_tickets/articles' => 'help_tickets/tickets#index'
  get '/help_tickets/automated_tasks/new' => 'help_tickets/tickets#index'
  get '/help_tickets/automated_tasks/:id/edit' => 'help_tickets/tickets#index'
  get '/help_tickets/articles/:slug' => 'help_tickets/tickets#index'
  get '/help_tickets/articles/actions/new' => 'help_tickets/tickets#new'
  get '/help_tickets/articles/:slug/edit' => 'help_tickets/tickets#index'
  get '/help_tickets/snippets' => 'help_tickets/tickets#index'
  get '/help_tickets/snippets/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/snippets/new' => 'help_tickets/tickets#new'
  get '/help_tickets/documents' => 'help_tickets/tickets#index'
  get '/help_tickets/responses/new' => 'help_tickets/tickets#index'
  get '/help_tickets/responses/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/tasks/new' => 'help_tickets/tickets#index'
  get '/help_tickets/tasks/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/task_checklists/new' => 'help_tickets/tickets#index'
  get '/help_tickets/task_checklists/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/surveys' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/edit' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/comments' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/history' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/time' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/tasks' => 'help_tickets/tickets#index'
  get '/help_tickets/:help_ticket_id/tasks/:id/edit' => 'help_tickets/tickets#index'
  get '/help_tickets/:help_ticket_id/tasks/new' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/time/:time_spent_id/edit' => 'help_tickets/tickets#index'
  get '/help_tickets/:id/general' => 'help_tickets/tickets#index'
  get '/help_tickets/settings' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/email_notifications' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/email_setting' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/email_templates/end_users' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/valid_email_extensions' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/blocked_mails' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/blocked_keywords' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/email_templates/agents' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/email_templates/surveys' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/help_center' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/help_center/display' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/mobile_app_store' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/desktop_app' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/connectors' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/connectors/slack' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/connectors/ms-teams' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/general' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/help_center' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/help_center/custom_domain' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/open_ticket' => 'help_tickets/tickets#index'
  get '/help_tickets/blocked' => 'help_tickets/tickets#index'
  get '/help_tickets/faqs' => 'help_tickets/tickets#index'
  get '/help_tickets/faqs/new' => 'help_tickets/tickets#new'
  get '/help_tickets/faqs/:id/edit' => 'help_tickets/tickets#index'
  get '/help_tickets/faqs/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/list_view_columns' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/sla/policies' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/sla/policies/new' => 'help_tickets/tickets#new'
  get '/help_tickets/settings/sla/policies/:id' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/business_hour_settings' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/surveys' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/categories' => 'help_tickets/tickets#index'
  get '/help_tickets/ticket_emails/:id' => 'help_tickets/tickets#get_ticket_emails'
  post '/help_tickets/settings/helpdesk_email_format' => 'helpdesk_email_formats#create'
  put '/help_tickets/settings/helpdesk_email_format/:id' => 'helpdesk_email_formats#update'
  get '/help_tickets/settings/email_format' => 'helpdesk_email_formats#index'
  get '/help_tickets/settings/helpdesk_email_format' => 'help_tickets/tickets#index'
  get '/app-store-redirect', to: 'app_store_redirect#index'

  # Redirect old routes to corresponding new routes under help_center
  get 'open_ticket/workspaces/:workspace_id', to: redirect('help_center/workspaces/%{workspace_id}')
  get 'open_ticket/forms/:id', to: redirect('help_center/forms/%{id}')
  get 'open_ticket/workspaces/:workspace_id/forms/:id', to: redirect('help_center/workspaces/%{workspace_id}/forms/%{id}')

  get 'help_center', to: 'help_tickets/help_center#index'
  get 'help_center/workspaces/:workspace_id', to: 'help_tickets/help_center#index'
  get 'help_center/forms/:id/', to: 'help_tickets/help_center#index'
  get 'help_center/workspaces/:workspace_id/forms/:id', to: 'help_tickets/help_center#index'
  get '/help_tickets/workspaces/:workspace_id/forms/:id', to: 'help_tickets/tickets#index'
  get '/help_tickets/faqs/workspaces/:workspace_id', to: 'help_tickets/tickets#index'
  get '/help_tickets/knowledge/workspaces/:workspace_id', to: 'help_tickets/tickets#index'
  get '/help_tickets/end_user_tickets', to: 'help_tickets/tickets#index'
  get '/help_tickets/end_user_dashboard', to: 'help_tickets/tickets#index'
  get '/help_tickets/knowledge/:slug/:workspace_id', to: 'help_tickets/tickets#index'
  get '/help_tickets/ticket_summaries', to: 'help_tickets/ticket_summaries#summarize'
  get '/help_tickets/should_display_ai_summary', to: 'help_tickets/ticket_summaries#should_display_summary_feature'
  get 'help_center/submit_request/', to: 'help_tickets/help_center#index'
  get 'help_center/knowledge_base', to: 'help_tickets/help_center#index'
  get 'help_center/knowledge_base/workspaces/:workspace_id', to: 'help_tickets/help_center#index'
  get 'help_center/knowledge_base/:slug/:workspace_id', to: 'help_tickets/help_center#index'
  get 'help_center/faqs', to: 'help_tickets/help_center#index'
  get 'help_center/faqs/workspaces/:workspace_id', to: 'help_tickets/help_center#index'

  get 'open_ticket', to: redirect('help_center')

  get '/request_email' => 'request_emails#index'

  # Custom forms routes
  get '/help_tickets/settings/custom_forms' => 'help_tickets/tickets#index'
  get '/help_tickets/settings/custom_forms/new' => 'help_tickets/tickets#new'
  get '/help_tickets/settings/custom_forms/:id/edit' => 'help_tickets/tickets#edit'
  get '/help_tickets/settings/custom_forms/:id/*form_name/edit' => 'help_tickets/tickets#index'

  resources :custom_forms

  scope :help_tickets do
    resources :custom_forms do
      resources :default_emails, only: [:index]
    end
  end
  resources :event_logs, only: [:index]
  resources :msp_event_logs, only: [:index]

  get '/ticket_list_columns' => 'help_tickets/ticket_list_columns#index'
  put '/ticket_list_columns' => 'help_tickets/ticket_list_columns#update'
  scope module: 'help_tickets' do
    resources :activities, only: [:index]
    resources :abbreviated_tickets, only: [:index]
    get '/abbreviated_tickets/should_display_kanban_view', to: 'abbreviated_tickets#should_display_kanban_view'
    get '/abbreviated_tickets/is_modern_help_ticket_view_enabled', to: 'abbreviated_tickets#is_modern_help_ticket_view_enabled'
    get '/quick_filters_preview_data' => 'abbreviated_tickets#quick_filters_preview_data'
    get '/kanban_columns' => "kanban_columns#index"
    resources :blocked_entities, only: [:index, :create, :destroy]
    resources :company_mailers
    resources :faqs, only: [:index, :new, :create, :show, :update, :destroy], path: 'helpdesk_faqs'
    resources :snippets
    resources :tasks
    resources :task_checklists
    resources :scheduled_tasks
    resources :ticket_emails, only: [:index, :create, :destroy]
    resources :ticket_email_counts, only: [:index]
    resources :activity_types, only: [:index]
    resources :custom_email_checks, only: [:index]
    resources :ticket_sessions, only: [:destroy, :index]
    resources :insights, only: [:index]                               # Ticket Reports routes

    scope module: 'dashboard' do
      put 'dashboard/admin_overview_customization' => "admin_overview_customization#update"
      get 'dashboard/admin_overview_customization' => "admin_overview_customization#show"
      get 'dashboard/admin_overview' => "admin_overview#index"
      get 'dashboard/closed_times' => "closed_times#index"
      get 'dashboard/priorities' => "priorities#index"
      get 'dashboard/response_times' => "response_times#index"
      get 'dashboard/satisfaction' => "satisfaction#index"
      get 'dashboard/statuses' => "statuses#index"
      get 'dashboard/assignments' => "assignments#index"
      get 'dashboard/tickets' => "tickets#index"
      get 'dashboard/sources' => 'sources#index'

      get 'dashboard/msp/companies_overview' => "msp_dashboard#companies_overview"
      get 'dashboard/msp/statuses' => "msp_dashboard#statuses"
      get 'dashboard/msp/sources' => "msp_dashboard#sources"
      get 'dashboard/msp/assignments' => "msp_dashboard#assignments"
      get 'dashboard/msp/priorities' => "msp_dashboard#priorities"
      get 'dashboard/msp/satisfaction' => "msp_dashboard#satisfaction"
    end

    resources :abbreviated_tickets, only: [:index] do
      collection do
        get "smart_list_options" => "options/smart_list_options#index"
      end
    end
    resources :ordered_faqs, only: [:create]
    resources :company_mailers

    post '/project_task_positions/:help_ticket_id' => 'project_task_positions#update_positions'
    post '/create_project_tasks' => 'task_checklists#create_project_task_in_ticket'
    put '/order_checklists' => 'task_checklists#order_checklists'
    put '/order_tasks' => 'tasks#order_tasks'

    get '/fetch_assignee_groups' => 'helpdesk_settings#fetch_assignee_groups'
    resources :helpdesk_settings, only: [:index, :update] do
      member do
        get :send_confirmation_email
      end
    end

    resources :ticket_emails, only: [:index, :create, :destroy]
    resources :inbound_emails, only: [:create]
    resources :help_ticket_slack, only: [:create]
    resources :help_ticket_slack_comments, only: [:create]
    resources :ticket_comments, only: [:create, :update, :destroy] do
      collection do
        post :change_comment_tone
        post :shorten_reply
        get :should_display_comments_feature
      end
    end
    resources :ticket_drafts, only: [:index, :create, :update, :destroy] do
      collection do
        get :check_draft
        get :should_enable_ticket_drafts
      end
    end

    resources :ticket_portal_url, only: [:index]
    resources :custom_attributes, only: [:index, :create, :update, :destroy] do
      post :update_positions, on: :collection
    end

    resources :merged_tickets, only: [:create]
    resources :unmerged_tickets, only: [:create]

    resources :staff_from_ticket_emails, only: [:create]
    resources :tickets_from_emails, only: [:create]
    resources :help_center, only: [:index]

    resources :tickets do
      resources :attachments, only: [:create, :destroy]
      put '/ticket_location/:id' => 'tickets#update_location'
      resources :activities, only: [:index]
      resources :time_spents
      resources :stop_watch_timers
      resources :ticket_comments, only: [:index, :show] do
        collection do
          get :get_scheduled_comments
          get :get_scheduled_comment
          post :add_scheduled_comment
        end
      end
      resources :project_tasks
      resources :parent_tasks, only: [:create, :destroy]

      member do
        post :archive
        post :unarchive
      end
      collection do
        delete :remove_file
      end
    end

    resources :ticket_emails, only: [:create]
    resources :non_helpdesk_agents, only: [:index]
    resources :assigned_duration_summaries, only: [:index]
    resources :closed_duration_summaries, only: [:index]
    resources :open_duration_summaries, only: [:index]
    resources :response_time_ticket_summaries, only: [:index]
    resources :help_center_summaries, only: [:index]
    resources :in_progress_ticket_summaries, only: [:index]
  end

  namespace :sla do
    resources :policies, only: [:update, :create, :destroy, :show, :index]
  end

  resources :tickets do
    resources :custom_form_values, only: [:update, :create, :destroy, :show, :index]
  end
  get '/help_tickets' => 'help_tickets/tickets#index', as: 'help_tickets'
  get '/help_tickets/new' => 'help_tickets/tickets#new'
  get '/help_tickets/:id' => 'help_tickets/tickets#show'
  get '/workspace_tickets_count' => 'workspaces#workspace_tickets_count'
  put '/set_default_workspace' => 'workspaces#set_default_workspace'
  put '/unset_default_workspace' => 'workspaces#unset_default_workspace'
  get '/workspace_quick_settings' => 'workspaces#workspace_quick_settings'
  post '/tickets/move_ticket' => 'help_tickets/tickets#move_ticket'
  post '/tickets/clone_ticket' => 'help_tickets/tickets#clone_ticket'

  get '/forgot_password' => 'forgot_passwords#new'
  post '/send_reset_password_code' => 'forgot_passwords#send_reset_password_code'
  put '/confirm_new_password' => 'forgot_passwords#update_password'
  get '/microsoft_sso_configuration' => 'integration_configurations#get_microsoft_sso_configuration'
  put '/sla/orderings' => 'sla/policies#ordering'
  get 'automated_tasks/:id/reset_task' => 'automated_tasks#reset_task'
  put 'automated_tasks/:id/update_at_assign_value' => 'automated_tasks#update_at_assign_value'
  get '/sla/has_base_policy' => 'sla/policies#has_base_policy?'
  get '/time_spents_insights' => 'help_tickets/time_spents_insights#insights_data'
  get '/asset_analytics_preferences' => 'asset_analytics_preferences#index'
  put '/asset_analytics_preferences' => 'asset_analytics_preferences#update'
  get '/quick_view_filters' => 'quick_view_filters#index'
  put '/quick_view_filters' => 'quick_view_filters#update'
  delete '/quick_view_filters' => 'quick_view_filters#destroy'
  put '/reorder_quick_view_filters' => 'quick_view_filters#reorder'

  get '/preview_email' => 'previews#index'

  mount ActionCable.server => '/cable'

  if Rails.env.production? || Rails.env.staging?
    match "(errors)/:status", to: 'errors#internal_error', constraints: {status: /\d{3}/}, via: :all
  end

  if Rails.env.production?
    get '/sidekiq_processes_status' => proc { [Sidekiq::ProcessSet.new.size >= 8 ? 200 : 406, {}, []] }
  end

  get '/health' => proc { [200, {}, ['OK']] }

  match "*missing", :to => "not_found#index", via: :all
end
