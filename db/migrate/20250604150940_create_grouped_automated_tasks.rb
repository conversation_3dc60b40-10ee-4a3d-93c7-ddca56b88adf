class CreateGroupedAutomatedTasks < ActiveRecord::Migration[7.0]
  def change
    create_table :automated_task_groups do |t|
      t.string :name
      t.bigint :company_id
      t.bigint :workspace_id
      t.bigint :task_template_id

      t.timestamps
    end

    add_index :automated_task_groups, :workspace_id
    add_index :automated_task_groups, :company_id

    add_reference :automated_tasks_automated_tasks, :automated_task_group, index: { name: "idx_auto_tasks_on_grouped_task_id" }, foreign_key: true
  end
end
