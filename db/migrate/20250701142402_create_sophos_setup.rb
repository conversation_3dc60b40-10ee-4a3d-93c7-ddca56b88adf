class CreateSophosSetup < ActiveRecord::Migration[7.0]
  def change
    create_table :sophos_configs do |t|
      t.string :client_id
      t.string :client_secret
      t.string :access_token
      t.datetime :expires_in
      t.integer :import_type, null: false, default: 1
      t.references :company, index: true, foreign_key: true
      t.references :company_user, foreign_key: true

      t.timestamps
    end

    create_table :sophos_tenants do |t|
      t.string :tenant_id
      t.string :data_region_url
      t.references :sophos_config, null: false, foreign_key: true

      t.timestamps
    end
  end
end
