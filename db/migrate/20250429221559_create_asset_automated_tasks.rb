class CreateAssetAutomatedTasks < ActiveRecord::Migration[7.0]
  def change
    create_table :assets_automated_tasks do |t|
      t.string :name
      t.datetime :disabled_at, null: true
      t.boolean :force_disabled, default: false
      t.integer :order, default: 0
      t.integer :serial_number, default: 1, null: false
      t.references :company, foreign_key: true, index: true
      t.references :contributor, foreign_key: true, index: true

      t.timestamps
    end

    create_table :assets_event_types do |t|
      t.string :event_class
      t.string :icon
      t.string :model
      t.string :module
      t.string :name

      t.timestamps
    end

    create_table :assets_action_types do |t|
      t.string :action_class
      t.string :icon
      t.string :model
      t.string :module
      t.string :name

      t.timestamps
    end

    create_table :assets_task_actions do |t|
      t.references :automated_task, foreign_key: { to_table: :assets_automated_tasks }, index: true
      t.references :action_type, foreign_key: { to_table: :assets_action_types }, index: true
      t.text :value

      t.timestamps
    end

    create_table :assets_task_events do |t|
      t.references :automated_task, foreign_key: { to_table: :assets_automated_tasks }, index: true
      t.references :event_type, foreign_key: { to_table: :assets_event_types }, index: true
      t.hstore :value

      t.timestamps
    end

    create_table :assets_event_subject_types do |t|
      t.string :icon
      t.string :key
      t.string :name
      t.references :parent, polymorphic: true, index: false
      t.string :subject_class
      t.string :type

      t.timestamps
    end

    create_table :assets_event_details do |t|
      t.references :task_event, foreign_key: { to_table: :assets_task_events }, index: true
      t.references :event_subject_type, foreign_key: { to_table: :assets_event_subject_types }, index: true
      t.text :value

      t.timestamps
    end

    add_index :assets_event_subject_types, [:parent_id, :parent_type], name: 'idx_assets_event_subject_types_parent'

  end
end
