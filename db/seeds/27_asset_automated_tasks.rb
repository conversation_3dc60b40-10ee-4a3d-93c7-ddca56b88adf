if Rails.env.development?
  Assets::AutomatedTask.destroy_all
  Assets::EventType.destroy_all
  Assets::ObjectSubjectType.destroy_all
  Assets::TaskAction.destroy_all
  Assets::ActionType.destroy_all
end

######################### Software wasn't detected ##################################
## Event type
Assets::EventType.find_or_create_by(
    name: "{a software} wasn't detected",
    module: 'managed_assets',
    model: 'ManagedAsset',
    event_class: 'AssetSoftware',
    icon: 'nulodgicon-ios-copy-outline'
)
parent = Assets::EventType.find_by(name: "{a software} wasn't detected")
## Object subject type
Assets::ObjectSubjectType.find_or_create_by(
    name: "/a/ {specific software}",
    key: 'assets_software',
    subject_class: 'SpecificSoftware',
    icon: 'genuicon-envelope-o',
    parent: parent
)

######################### Integration failed continuously ##################################
## Event Type
Assets::EventType.find_or_create_by(
    name: "{an integration} failed continuously",
    module: 'managed_assets',
    model: 'ManagedAsset',
    event_class: 'Integration',
    icon: 'nulodgicon-ios-copy-outline'
)
parent = Assets::EventType.find_by(name: "{an integration} failed continuously")
## Object subject type
Assets::ObjectSubjectType.find_or_create_by(
    name: "any integration",
    key: 'assets_integration',
    subject_class: 'AnyIntegration',
    icon: 'genuicon-envelope-o',
    parent: parent
)
## Object subject type
Assets::ObjectSubjectType.find_or_create_by(
    name: "/a/ {specific integration}",
    key: 'assets_integration',
    subject_class: 'SpecificIntegration',
    icon: 'genuicon-envelope-o',
    parent: parent
)

######################### Agent didn't sync ##################################
## Event Type
Assets::EventType.find_or_create_by(
    name: "{an agent} didn't resync",
    module: 'managed_assets',
    model: 'ManagedAsset',
    event_class: 'AgentLocation',
    icon: 'nulodgicon-ios-copy-outline'
)
parent = Assets::EventType.find_by(name: "{an agent} didn't resync")
## Object subject type
Assets::ObjectSubjectType.find_or_create_by(
    name: "any agent",
    key: 'agents_integration',
    subject_class: 'AnyAgentIntegration',
    icon: 'genuicon-envelope-o',
    parent: parent
)

######################### Disk Space is low ##################################
## Event type
Assets::EventType.find_or_create_by(
    name: "{an asset's} disk space is low",
    module: 'managed_assets',
    model: 'ManagedAsset',
    event_class: 'AssetDiskSpace',
    icon: 'nulodgicon-ios-copy-outline'
)
parent = Assets::EventType.find_by(name: "{an asset's} disk space is low")
## Object subject type
Assets::ObjectSubjectType.find_or_create_by(
    name: "any asset",
    key: 'disk_free_space',
    subject_class: 'AnyAsset',
    icon: 'genuicon-envelope-o',
    parent: parent
)

####################### Assets Automation Actions ##################################
Assets::ActionType.find_or_create_by(name: 'send an [email]',
                                     module: 'managed_assets',
                                     model: 'ManagedAsset',
                                     action_class: 'SendEmail',
                                     icon: 'genuicon-envelope-o')
                                             
Assets::ActionType.find_or_create_by(name: 'create an [alert]',
                                     module: 'managed_assets',
                                     model: 'ManagedAsset',
                                     action_class: 'AddAlert',
                                     icon: 'genuicon-alerts')
