class TaskActionMailer < ApplicationMailer
  include Validations

  layout 'nulodgic_mailer'

  attr_accessor :root, :email_data

  EMAIL_SIZE_LIMIT = 40

  def action_email(action, root, recipients, email_data)
    @root = root
    @email_data = email_data
    data = JSON.parse(action.value)
    email_body = email_data['email_body']

    if email_data['should_add_attachments'] && email_data['custom_form_attachments']
      if is_size_limit_exceeded?
        recipients.each do |recipient|
          Logs::EmailLog.create(
            company_id: object.company_id,
            subject: data['subject'],
            body: email_body,
            sender_email: email_data['from_email'],
            receiver_emails: [recipient],
            email_type: 'custom_email',
            status: 'size_restriction'
          )
        end
        return
      else
        email_data['custom_form_attachments'].each do |attachment|
          attachments[attachment['file_name']] = attachment['file']
        end
      end
    end
    
    body = email_data['sanitize_email_body']
    return if body.blank?

    if TaskActionMailer.class_variable_defined?(:@@size_greater_than_limit)
      TaskActionMailer.remove_class_variable(:@@size_greater_than_limit)
    end
    subject = email_data['subject']

    helpdesk_recipients = recipients.select { |recipient| email_data['helpdesk_custom_emails'].include?(recipient) }
    if helpdesk_recipients.present?
      create_custom_email_log(subject, recipients, email_body)
    end

    deliver_email_to_recipients(subject, recipients, body, email_body)
  end

  def deliver_email_to_recipients(subject, recipients, body, email_body)
    valid_recipients = recipients.select { |recipient| recipient.match(email_regex) }
    return if valid_recipients.empty?

    teams_recipients = valid_recipients.select { |recipient| recipient.split('@')[1].include?('teams.ms') }
    non_teams_recipients = valid_recipients - teams_recipients

    if teams_recipients.present?
      teams_body = body.gsub(/<style> .* <\/style>/xm, '')
                       .gsub('<div><!--block-->', '')
                       .gsub('</div>', '')
      teams_body = HtmlToMarkdownConverter.new(parse_type: 'teams', content: teams_body).call

      create_custom_email_log(subject, teams_recipients, email_body)
      send_email_with_configuration_set(subject, teams_recipients, teams_body)
    end

    if non_teams_recipients.present?
      @workspace = object.workspace unless root.is_a?(Assets::AutomatedTask)
      @company = object.company
      send_email_with_layout(subject, non_teams_recipients, body)
    end
  end

  def is_size_limit_exceeded?
    @@size_greater_than_limit ||= (email_data['attachment_size'] / 1048576) > EMAIL_SIZE_LIMIT
  end

  def create_custom_email_log(subject, recipients, email_body)
    recipients.each do |recipient|
      Logs::EmailLog.create(
        company_id: object.company_id,
        subject: subject,
        body: email_body,
        sender_email: email_data['from_email'],
        receiver_emails: [recipient],
        created_at: Date.today,
        email_type: "custom_email"
      )
    end
  end

  def send_email_with_configuration_set(subject, recipients, body)
    RawEmailMailer.send_email(email_data['from_email'], recipients, subject, object, body, false).deliver_now!
  end

  def object
    @object ||= if root.is_a?(HelpTicket) || root.is_a?(AutomatedTasks::ExecutionDate) || root.is_a?(Assets::AutomatedTask)
                  root
                else
                  root.help_ticket
                end
  end

  def send_email_with_layout(subject, recipients, body)
    @is_helpdesk_email = true
    mail(
      from: email_data['from_email'],
      to: recipients,
      subject: subject,
      body: body
    ) do |format|
      format.html { render locals: { object: object, body: body } }
    end
  end
end
