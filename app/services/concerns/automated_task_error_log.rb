module AutomatedTaskErrorLog
  extend ActiveSupport::Concern

  included do
    def handle_task_error(error, task, entity_name, type_name, company_id, retry_params = {})
      if Rails.env.staging? || Rails.env.production?
        Bugsnag.notify(error)
        NotifySlackFailedTaskWorker.perform_async(company_id, task.id) if Rails.env.production? && !execution_log_exists?(task, company_id)
      end
      create_error_execution_log(error, task, entity_name, type_name, company_id, retry_params)
      decrease_trigger_count(task.id)
    end

    def create_error_execution_log(msg, task, entity_name, class_name, company_id, retry_params)
      AutomatedTasks::ExecutionLog.create!(
        message: msg,
        entity_name: entity_name,
        automated_tasks_automated_task_id: task.id,
        company_id: company_id,
        workspace_id: task.workspace_id,
        entity_attributes: { entity_class: class_name, retry_params: retry_params }.to_json
      )
    end
  
    def execution_log_exists?(task, company_id)
      AutomatedTasks::ExecutionLog.find_by(company_id: company_id, automated_tasks_automated_task_id: task.id, created_at: DateTime.now.all_day)
    end

    def decrease_trigger_count(executed_task_id)
      return if executed_task_id.blank?
    
      task = AutomatedTasks::AutomatedTask.find_by(id: executed_task_id)
      if task.present? && task.trigger_count.to_i > 0
        task.decrement!(:trigger_count)
      end
    end
  end
end
