class BulkAssetsUpdate
  include Asset<PERSON>ifecycleHelper

  def initialize(company_id, assets = [])
    @assets = assets
    @company_id = company_id
  end

  def add_tags(tags, user_id, request_ip, request_uuid)
    ManagedAssetTag.transaction do
      create_company_asset_tags(tags)
      tags_lookup = company_asset_tags_lookup(tags)
      @assets.each do |asset|
        new_tags = tags - asset.managed_asset_tags.joins(:company_asset_tag).pluck(:name)
        new_tags.each do |tag|
          tag = asset.managed_asset_tags.find_or_create_by!(company_asset_tag_id: tags_lookup[tag])
        end
      end

      apply_lifecycle_to_assets(tags_lookup.values, user_id, request_ip, request_uuid)
    rescue Exception => e
      ManagedAssetTag.connection.execute "ROLLBACK"
      push_notification('unsuccessful')
      raise e
    end
  end

  def remove_tags(remove_tags)
    ManagedAssetTag.transaction do
      if remove_tags.present?
        @assets.each do |asset|
          asset.managed_asset_tags.joins(:company_asset_tag).where(company_asset_tag: { name: remove_tags }).destroy_all
        end
      end
    rescue Exception => e
      ManagedAssetTag.connection.execute "ROLLBACK"
      push_notification('unsuccessful')
      raise e
    end
  end

  def update_tags(tags_to_add, tags_to_remove, user_id, request_ip, request_uuid)
    add_tags(tags_to_add, user_id, request_ip, request_uuid) if tags_to_add.present?
    remove_tags(tags_to_remove) if tags_to_remove.present?
    push_notification('successful')
  end

  def add_locations(location_id)
    @assets.find_each do |asset|
      asset.location_id = location_id
      asset.save!
    end
    push_notification('successful')
  rescue
    push_notification('unsuccessful')
  end

  def assign_users(used_by_contributor_id, managed_by_contributor_id)
    @assets.each do |selected_managed_asset|
      selected_managed_asset.assignment_information.update_columns(used_by_contributor_id: used_by_contributor_id, managed_by_contributor_id: managed_by_contributor_id)
    end
    push_notification('successful')
  rescue
    push_notification('unsuccessful')
  end

  def inline_assign_users(contributor_id, type)
    @assets.each do |selected_managed_asset|
      if type == 'used_by_contributor_id'
        selected_managed_asset.assignment_information.update_column(:used_by_contributor_id, contributor_id)
      elsif type == 'managed_by_contributor_id'
        selected_managed_asset.assignment_information.update_column(:managed_by_contributor_id, contributor_id)
      end
    end
    push_notification('successful')
  rescue
    push_notification('unsuccessful')
  end

  def inline_remove_users(contributor_id, type)
    @assets.each do |selected_managed_asset|
      if type == 'used_by_contributor_id'
        selected_managed_asset.assignment_information.update_column(:used_by_contributor_id, nil)
      elsif type == 'managed_by_contributor_id'
        selected_managed_asset.assignment_information.update_column(:managed_by_contributor_id, nil)
      end
    end
    push_notification('successful')
  rescue
    push_notification('unsuccessful')
  end

  def bulk_update(used_by_contributor_id, managed_by_contributor_id)
    errors = []
    asset_ids = @assets.pluck(:id)
    @assets_with_associations = ManagedAsset.includes(:asset_type, :hardware_detail, :assignment_information).where(id: asset_ids)
    assets_to_update = @assets_with_associations.index_by(&:id)
    managed_asset_update_params = []
    assignment_info_update_params = []

    @assets.each_with_index do |item, index|
      asset = assets_to_update[item[:id]]
      next unless asset

      assignment_information_update_params = {}
      update_params = item.except(:id, :custom_status_id).as_json
      assignment_information_update_params["used_by_contributor_id"] = update_params.delete("used_by_contributor_id")
      assignment_information_update_params["managed_by_contributor_id"] = update_params.delete("managed_by_contributor_id")
      if update_params["department_id"]
        assignment_information_update_params["department_id"] = update_params.delete("department_id")
      elsif update_params["department"].present?
        department = Department.find_by(company_id: @company_id, name: update_params.delete("department"))
        assignment_information_update_params["department_id"] = department.id
      else
        assignment_information_update_params["department_id"] = nil
      end
      asset_type_id = update_params.delete('asset_type_id')
      if asset_type_id
        asset.hardware_detail&.destroy if asset.hardware_detail.present?
        asset.reload
        asset_type = CompanyAssetType.find_by(id: asset_type_id.to_i)
        asset.asset_type = asset_type
        asset.hardware_detail = asset_type&.hardware_detail_class&.create(managed_asset_id: asset.id)
      end
      if assignment_information_update_params.present?
        assignment_info_update_params << { id: asset.assignment_information.id, **assignment_information_update_params }
      end

      asset.assign_attributes(update_params)
      asset.company_asset_status_id = item['custom_status_id'].to_i if item['custom_status_id'].present?

      unless asset.valid?
        error = {
          :message => asset.errors.full_messages.to_sentence,
          :columns => asset.errors.details.keys,
          :asset_number => index
        }
        unless errors.include? error
          errors.push(error)
        end
      end
      managed_asset_update_params << asset.attributes.except('updated_at').merge(update_params)
    end

    if errors.empty?
      begin
        unless assignment_info_update_params.empty?
          AssignmentInformation.upsert_all(assignment_info_update_params)
        end
        ManagedAsset.upsert_all(managed_asset_update_params)
        bulk_create_usage_history(@assets_with_associations)
        push_notification('successful')
      rescue => e
        push_notification('unsuccessful')
      end
    else
      push_notification('unsuccessful')
    end
    [true, errors]
  end

  def bulk_archive(ids, company, scoped_company_user_id, request_ip, request_user_id )
    ids.each do |id|
      create_audit_object(true, id, company, scoped_company_user_id, request_ip, request_user_id )
    end
    company.managed_assets.where(id: ids).update_all(archived: true)
    push_notification('successful')
  rescue
    push_notification('unsuccessful')
  end

  def bulk_unarchive(ids, company, scoped_company_user_id, request_ip, request_user_id )
    assets = company.managed_assets.where(id: ids).update_all(archived: false)
    ids.each do |id|
      create_audit_object(false, id, company, scoped_company_user_id, request_ip, request_user_id)
    end
    push_notification('successful')
  rescue
    push_notification('unsuccessful')
  end

  def bulk_delete(ids, scoped_company)
    scoped_company.discovered_assets.where(managed_asset_id: ids).destroy_all
    scoped_company.managed_assets.where(id: ids, archived: true).destroy_all
    push_notification('successful', true)
  rescue
    push_notification('unsuccessful', true)
  end

  def create_audit_object(archived, id, company, scoped_company_user_id, request_ip, request_uuid )
    a = Audited::Audit.new
    a[:auditable_id] = id
    a[:auditable_type] = "ManagedAsset"
    a[:user_id] = scoped_company_user_id
    a[:user_type] = 'CompanyUser'
    a[:action] = 'update'
    a[:audited_changes] = {"archived" => [!archived, archived]}
    a[:remote_address] = request_ip
    a[:request_uuid] = request_uuid
    a.save!
  end

  private

  def push_notification(status, is_delete = false)
    action = is_delete ? 'delete' : 'update'
    Pusher.trigger("#{@company_id}_managed_assets","#{status}-assets-update", { action: action })
  end

  def company_asset_tags_lookup(tags)
    CompanyAssetTag.where(company_id: @company_id, name: tags).pluck(:name, :id).to_h
  end

  def existing_tags
    @existing_tags ||= CompanyAssetTag.where(company_id: @company_id).pluck(:id, :name).to_h
  end

  def create_company_asset_tags(tags)
    new_company_asset_tags = tags - existing_tags.values

    if new_company_asset_tags.present?
      company_asset_tags = new_company_asset_tags.map { |tag| { name: tag.downcase, company_id: @company_id } }
      CompanyAssetTag.upsert_all(company_asset_tags)
    end
  end

  def get_lifecycle_data(tags_ids)
    lifecycle = fetch_latest_tag_lifecycle(tags_ids)
    return {} unless lifecycle

    {
      asset_lifecycle_id: lifecycle.id,
      lifecycle_type: 'by_tag',
      purchase_price: lifecycle.purchase_price,
      replacement_cost: lifecycle.replacement_cost,
      salvage: lifecycle.salvage,
      useful_life: lifecycle.useful_life,
      approaching_end_of_life: lifecycle.approaching_end_of_life,
      po: lifecycle.po
    }
  end

  def fetch_latest_tag_lifecycle(tags_ids)
    @lifecycle ||= AssetLifecycle.where(
      company_id: @company_id,
      linked_item_type: 'CompanyAssetTag',
      linked_item_id: tags_ids
    ).last
  end

  def apply_lifecycle_to_assets(tags_ids, user_id, request_ip, request_uuid)
    lifecycle_data = get_lifecycle_data(tags_ids)
    new_lifecycle_id = fetch_latest_tag_lifecycle(tags_ids)&.id
    return unless lifecycle_data.present?

    costs_data = []
    audits = []
    @assets.includes(:cost).where(costs: { lifecycle_type: [nil, 'by_tag', 'by_type'] }).each do |asset|
      cost = asset.cost
      if cost.blank?
        new_cost = asset.create_cost(lifecycle_data)
        audits << lifecycle_audits(new_cost.id, asset.id, user_id, [nil, new_lifecycle_id], request_ip, request_uuid)
      elsif cost.asset_lifecycle_id != new_lifecycle_id
        costs_data << asset.cost.attributes.merge(lifecycle_data)
        audits << lifecycle_audits(cost.id, asset.id, user_id, [cost.asset_lifecycle_id, new_lifecycle_id], request_ip, request_uuid)
      end
    end

    Audited::Audit.upsert_all(audits) if audits.present?
    Cost.upsert_all(costs_data, unique_by: [:managed_asset_id]) if costs_data.present?
  end

  def bulk_create_usage_history(assets)
    return if assets.empty?
    usage_history_records = []
  
    assets.each do |asset|
      usage_history_records << asset.assignment_information.attributes.slice(
        'location_id',
        'managed_by_contributor_id',
        'department_id',
        'used_by_contributor_id'
      ).merge(
        company_asset_status_id: asset.company_asset_status_id,
        managed_asset_id: asset.id
      )
    end
    AssetUsageHistory.insert_all(usage_history_records) unless usage_history_records.empty?
  end
end
