class EventLogService
  include HandleCompanyCache<PERSON>eys

  def initialize(form, user_id, activity_type, default_form = nil, field = nil, position = nil, incoming_email_info = nil, ticket_assignment = nil)
    @form = form
    @user_id = user_id
    @activity_type = field.present? ? 'updated' : activity_type
    @field_deleted = activity_type == 'deleted'
    @default_form = default_form
    @field = field
    @field_position = position
    @incoming_email_info = incoming_email_info
    @ticket_assignment = ticket_assignment
  end

  def create_activity
    if @current_data.present? || @previous_data.present? || activity_type != "updated"
      EventLog.create!(activity_params)
    end
  end

  def activity_params
    {
      entity_id: @activity_type != 'deleted'? @form['id'] : nil,
      company_id: @form['company_id'],
      module_name:  @form.company_module,
      owner_id: @user_id,
      activity_type: activity_type,
      data: activity_data,
      workspace_id: @form.workspace_id ? @form.workspace_id : nil,
      entity_type: "CustomForm"
    }
  end

  def activity_data
    {
      form_id: @form['id'],
      form_name: @form['form_name'],
      field_activity_type: @field_deleted == true ? 'Deleted' : @form_activity_type,
      previous_value: @previous_data,
      current_value: @current_data,
      field_label: @current_field_label,
      field_type: @current_field_type
    }
  end

  def create_form_activity
    current_form_values = ['created_at', 'updated_at', :permissions, :field_position]
    form_changes = @form.saved_changes.except(*current_form_values)
    form_changes&.keys&.each do |key|
      if key == 'default'
        @previous_data = { "#{key.to_sym}": @default_form }
        @current_data = { "#{key.to_sym}": @form.form_name }
      else
        @previous_data = { "#{key.to_sym}": form_changes[key.to_sym][0] }
        @current_data = { "#{key.to_sym}": form_changes[key.to_sym][1] }
      end
      create_activity
    end
  end

  def incoming_email_activity
    @previous_data = {}
    @current_data = {}
    current_form_values = ['created_at', 'updated_at', 'id', 'entity_id', 'helpdesk_custom_email_id', 'company_id', :permissions, :field_position]
    form_changes = @incoming_email_info.saved_changes.except(*current_form_values)
    form_changes&.keys&.each do |key|
      if form_changes[key.to_sym][0].present? || form_changes[key.to_sym][1].present?
        if key == 'email'
          @previous_data = { "#{key.to_sym}": form_changes[key.to_sym][0] } if form_changes[key.to_sym][0].present? && form_changes[key.to_sym][0].split('@').first != 'null'
          @current_data = { "#{key.to_sym}": form_changes[key.to_sym][1] } if form_changes[key.to_sym][1].present? && form_changes[key.to_sym][1].split('@').first != 'null'
          if @previous_data.present? && @current_data.blank?
            @current_data = { "#{key.to_sym}": nil }
          end
        else
          @previous_data = { "#{key.to_sym}": form_changes[key.to_sym][0] }
          @current_data = { "#{key.to_sym}": form_changes[key.to_sym][1] }
        end
        create_activity if @current_data.present?
      end
    end
  end

  def form_automated_task_activity
    if @ticket_assignment  && @ticket_assignment  == 'disabled'
      @previous_data = { "Ticket assignment": 'Enabled' }
      @current_data = { "Ticket assignment": 'Disabled' }
    else
      @previous_data = { "Ticket assignment": 'Disabled' }
      @current_data = { "Ticket assignment": 'Enabled' }
    end
    create_activity
  end

  def create_field_activity
    current_form_values = ['id', 'custom_form_id', 'custom_form_field_id', 'created_at', 'updated_at', :permissions, :field_position]
    @previous_data = {}
    @current_data = {}
    @current_field_label = @field.present? ? @field.label : @field_position.custom_form_field.label
    @current_field_type =  @field.present? ? @field.field_attribute_type : @field_position.custom_form_field.field_attribute_type
    if @field && @field.previous_changes.length > 0
      field_changes = @field.previous_changes.except(*current_form_values)
      field_changes&.keys&.each do |key|
        next unless key != 'order_position'
        smart_class = smart_list_classes[@field.field_attribute_type.to_sym]
        if smart_class && key == 'default_value' && field_changes['default_value'].present?
          current_data = []
          previous_data = []
          default_current_values = JSON.parse(field_changes[key.to_sym][1])
          default_previous_values = JSON.parse(field_changes[key.to_sym][0]) if field_changes[key.to_sym][0].present?
          if smart_class.name == 'Location'
            current_data << get_location_data(default_current_values)
            previous_data << get_location_data(default_previous_values) if default_previous_values.present?
          elsif smart_class.name == 'Contributor'
            current_data << get_user_data(default_current_values)
            previous_data << get_user_data(default_previous_values) if default_previous_values.present?
          else
            current_data << smart_class.where(id: default_current_values).pluck(:name)
            previous_data << smart_class.where(id: default_previous_values).pluck(:name) if default_previous_values.present?
          end
          @current_data["#{key.to_sym}"] = current_data.flatten
          @previous_data["#{key.to_sym}"] = previous_data.flatten
        else
          @previous_data["#{key.to_sym}"] = field_changes[key.to_sym][0]
          @current_data["#{key.to_sym}"] = field_changes[key.to_sym][1]        
        end
      end
    elsif @field_deleted
      @current_data = { label: @field.label }
    elsif @field_position && @field_position.previous_changes.length > 0
      field_changes = @field_position.previous_changes.except(*current_form_values)
      key = field_changes.keys.first
      @previous_data["#{key.to_sym}"] = field_changes[key.to_sym][0]
      @current_data["#{key.to_sym}"] = field_changes[key.to_sym][1]
    end
    create_activity if @current_data.present?
  end 

  def create_field_permissions_activity(updated_permissions)
    existing_permissions_by_contributor = @field.custom_form_field_permissions.index_by(&:contributor_id)
    updated_contributor_ids = updated_permissions.map { |p| p['contributor_id'] }
    @current_field_label = @field.label
    @current_field_type = @field.field_attribute_type
    updated_permissions.each do |updated_permission|
      @previous_data = {}
      @current_data = {}
      contributor_name = updated_permission['name']
      contributor_id = updated_permission['contributor_id']
      new_permission = updated_permission['permissions']&.include?('write') ? 'write' : 'read'
  
      existing_permission = existing_permissions_by_contributor[contributor_id]
      old_permission = existing_permission.can_edit? ? 'write' : 'read' if existing_permission.present?
  
      if old_permission != new_permission
        if existing_permission.present?
          @previous_data = { 'field_permissions' => "#{contributor_name} (#{old_permission})" }
        end
        @current_data = { 'field_permissions' => "#{contributor_name} (#{new_permission})" }
  
        create_activity if @current_data.present?
      end
    end

    existing_permissions_by_contributor.each do |contributor_id, existing_permission|
      next if updated_contributor_ids.include?(contributor_id)
      @current_data = {}
      @previous_data = {}
      contributor_name = existing_permission.contributor&.name
      old_permission = existing_permission.can_edit? ? 'write' : 'read'
      @previous_data = { 'field_permissions' => "#{contributor_name} (#{old_permission})" }
      @current_data = { 'field_permissions' => "" }
      create_activity if @previous_data.present?
    end
  end

  def activity_type
    if @activity_type == 'created'
      @form_activity_type = 'Create'
    elsif @activity_type == 'updated'
      @form_activity_type = 'Updates'
    elsif @activity_type == 'deleted'
      @form_activity_type = 'Delete'
    end
    @activity_type
  end

  def smart_list_classes
    @smart_list_classes ||= {
      asset_list: ManagedAsset,
      contract_list: Contract,
      vendor_list: Vendor,
      telecom_list: TelecomService,
      people_list: Contributor,
      external_user: Contributor,
      location_list: Location,
    }
  end

  def get_location_data(ids)
    CustomFormValue.includes(:custom_form_field).where(custom_form_fields: { name: "name" }, custom_form_values: { module_id: ids }).pluck(:value_str)
  end

  def get_user_data(ids)
    user_data = ids.map do |id|
      if (id == 'current')
        CompanyUser.find(@user_id).name
      else
        contributor = find_contributor_by_id(id)
        contributor&.name
      end
    end
    user_data.compact
  end

  ######## General Settings Logs ########

  def create_general_settings_activity
    helpdesk_settings_values = ['created_at', 'updated_at']
    settings_changes = @form.saved_changes.except(*helpdesk_settings_values)
    @key = @form.default_helpdesk_setting.title
    @previous_data = settings_changes.values.flatten[0]
    @current_data = settings_changes.values.flatten[1]
    create_logs_activity
  end

  def create_logs_activity
    if @current_data.present? || @previous_data.present?
      EventLog.create!(settings_activity_params)
    end
  end

  def settings_activity_params
    {
      entity_id: @form[:id],
      company_id: @form.company_id ? @form.company_id : @form.workspace.company_id,
      module_name: 'helpdesk',
      owner_id: @user_id,
      activity_type: @activity_type == 'update' ? 'updated' : nil,
      data: { key: @key, previous_value: @previous_data, current_value: @current_data },
      workspace_id: @form.workspace_id ? @form.workspace_id : nil,
      entity_type: @form.company_id ? "HelpdeskSetting" : "CompanyMailer"
    }
  end

  ######## Email Notifications Logs ########

  def create_email_notifications_activity
    @key = "Notify when #{@form.default_mailer.description.downcase}"
    @current_data = @form.params[:opted_in]
    @previous_data = !@form.params[:opted_in]
    create_logs_activity
  end

  ######## Workspaces Logs ########

  def workspace_activity_data
    workspace_settings_values = ['created_at', 'updated_at']
    workspace_changes = @form.saved_changes.except(*workspace_settings_values)
    @key = workspace_changes.keys.flatten[0]
    @previous_data = workspace_changes.values.flatten[0]
    @current_data = workspace_changes.values.flatten[1]
    create_workspace_activity
  end

  def group_activity_data
    @key = "This group has been unlinked from the #{@form['workspace_name']} workspace."
    @previous_data = "linked"
    @current_data = "unlinked"
    create_group_activity
  end

  def create_workspace_member_activity
    contributor = find_contributor_by_id(@form[:id])
    @key = "Member was #{@default_form}"
    @current_data = contributor&.company_user ? contributor&.company_user&.name : contributor&.group&.name
    @previous_data = nil
    @company_id = contributor&.company_id
    create_workspace_activity
  end

  def create_new_workspace_activity
    @key = "New workspace #{@activity_type}"
    @current_data = @form[:name]
    @previous_data = nil
    @company_id = @form[:company_id]
    create_workspace_activity
  end

  def create_workspace_custom_form_activity
    @key = "Custom Form was #{@default_form}"
    @current_data = @form[:name]
    @previous_data = nil
    @company_id = Workspace.find(@form[:workspace_id]).company
    create_workspace_activity
  end

  def create_workspace_activity
    if @current_data.present? || @previous_data.present?
      EventLog.create!(workspace_activity_params)
    end
  end

  def create_group_activity
    if @current_data.present? || @previous_data.present?
      EventLog.create!(group_activity_params)
    end
  end

  def workspace_activity_params
    {
      entity_id: @form[:workspace_id] ? @form[:workspace_id] : @form[:id],
      company_id: @form.respond_to?(:company_id) ?  @form.company_id : @company_id,
      module_name: 'helpdesk',
      owner_id: @user_id,
      activity_type: @activity_type == 'update' ? 'updated' : @activity_type,
      data: workspace_data,
      workspace_id: @form[:workspace_id] ? @form[:workspace_id] : @form.id,
      entity_type: "Workspace"
    }
  end

  def group_activity_params
    {
      entity_id: @form['id'],
      company_id: @form['company_id'],
      module_name: 'company_user',
      owner_id: @user_id,
      activity_type: @activity_type,
      data: group_data,
      workspace_id: nil,
      entity_type: "Group"
    }
  end

  def group_data
    {
      key: @key,
      previous_value: @previous_data,
      current_value: @current_data,
      workspace_name: @previous_data,
      group_name: @form['name']
    }
  end

  def workspace_data
    {
      key: @key,
      previous_value: @previous_data,
      current_value: @current_data,
      workspace_name: @form[:workspace_id] ? Workspace.find(@form[:workspace_id]).name : @current_data
    }
  end

  ######## Sla Policy Logs ########

  def policy_activity
    @key = "Sla policy #{policy_activity_type}"
    @previous_data = nil
    @current_data = @form.name
    create_policy_activity
  end

  def policy_updated_activity
    policy_settings_values = ['created_at', 'updated_at']
    @policy_changes = @form.saved_changes.except(*policy_settings_values)

    @policy_changes.each do
      if @policy_changes[:name].present?
        @key = "Policy name"
        @previous_data = @policy_changes[:name].first
        @current_data = @policy_changes[:name].second
        @policy_changes = @policy_changes.except('name')
        create_policy_activity
      elsif @policy_changes[:custom_form_id].present?
        @key = "Custom Form"
        previous_form = CustomForm.find(@policy_changes[:custom_form_id].first)
        old_form = CustomForm.find(@policy_changes[:custom_form_id].second)
        @previous_data = previous_form.form_name
        @current_data = old_form.form_name
        @policy_changes = @policy_changes.except('custom_form_id')
        create_policy_activity
      elsif @policy_changes[:description].present?
        @key = "Description"
        @previous_data = @policy_changes[:description].first
        @current_data = @policy_changes[:description].second
        @policy_changes = @policy_changes.except('description')
        create_policy_activity
      end
    end
  end

  def policy_details_updated_activity
    policy_details_values = ['created_at', 'updated_at']
    @policy_changes = @form.saved_changes.except(*policy_details_values)
    @priority = @form.priority

    @policy_changes.each do
      if @policy_changes[:first_response_time].present?
        @key = "Priority #{@priority}: First response time"
        prev_data = @policy_changes[:first_response_time].first.split("-")
        curr_data = @policy_changes[:first_response_time].second.split("-")

        @previous_data = prev_data.first.concat("d")
        @previous_data << prev_data.second.concat("h")
        @previous_data << prev_data.third.concat("m")
        @current_data = curr_data.first.concat("d")
        @current_data << curr_data.second.concat("h")
        @current_data << curr_data.third.concat("m")

        @policy_changes = @policy_changes.except('first_response_time')
        create_policy_activity
      elsif @policy_changes[:resolution_time].present?
        @key = "Priority #{@priority}: Resolution time"
        prev_data = @policy_changes[:resolution_time].first.split("-")
        curr_data = @policy_changes[:resolution_time].second.split("-")

        @previous_data = prev_data.first.concat("d")
        @previous_data << prev_data.second.concat("h")
        @previous_data << prev_data.third.concat("m")
        @current_data = curr_data.first.concat("d")
        @current_data << curr_data.second.concat("h")
        @current_data << curr_data.third.concat("m")

        @policy_changes = @policy_changes.except('resolution_time')
        create_policy_activity
      elsif @policy_changes[:operational_hours].present?
        @key = "Priority #{@priority}: Operational hours"
        @previous_data = @policy_changes[:operational_hours].first
        @current_data = @policy_changes[:operational_hours].second
        @policy_changes = @policy_changes.except('operational_hours')
        create_policy_activity
      elsif @policy_changes[:escalation].present?
        @key = "Priority #{@priority}: Escalation"
        @previous_data = @policy_changes[:escalation].first
        @current_data = @policy_changes[:escalation].second
        @policy_changes = @policy_changes.except('escalation')
        create_policy_activity
      end
    end
  end

  def policy_emails_updated_activity
    policy_details_values = ['created_at', 'updated_at']
    @policy_changes = @form.saved_changes.except(*policy_details_values)

    @policy_changes.each do
      if @policy_changes[:email_type].present?
        @key = 'Reminder/Escalation: Time approaches'
        @previous_data = @policy_changes[:email_type].first
        @current_data = @policy_changes[:email_type].second
        @policy_changes = @policy_changes.except('email_type')
        create_policy_activity
      elsif @policy_changes[:subject].present?
        @key = 'Reminder/Escalation: Subject'
        @previous_data = @policy_changes[:subject].first
        @current_data = @policy_changes[:subject].second
        @policy_changes = @policy_changes.except('subject')
        create_policy_activity
      elsif @policy_changes[:approaches_in].present?
        @key = 'Reminder/Escalation: Approaches in '
        prev_data = @policy_changes[:approaches_in].first.split(":")
        curr_data = @policy_changes[:approaches_in].second.split(":")
        @previous_data = prev_data.first.concat("h")
        @previous_data << prev_data.second.concat("m")
        @current_data = curr_data.first.concat("h")
        @current_data << curr_data.second.concat("m")
        @policy_changes = @policy_changes.except('approaches_in')
        create_policy_activity
      elsif @policy_changes[:target].present?
        previous_emails = @policy_changes[:target].first
        current_emails = @policy_changes[:target].second
        to_add = current_emails - previous_emails
        to_remove = previous_emails - to_add - (previous_emails & current_emails)

        to_add&.map do |id|
          @key = 'Reminder/Escalation: Target person added'
          @previous_data = nil
          if id != "assigned"
            contributor = find_contributor_by_id(id)
            @current_data = contributor&.group ? contributor&.group&.name : contributor&.company_user&.name
          else
            @current_data = "Agent Assigned"
          end
          create_policy_activity
        end
        to_remove&.map do |id|
          @key = 'Reminder/Escalation: Target person removed'
          @previous_data = nil
          if id != "assigned"
            contributor = find_contributor_by_id(id)
            @current_data = contributor&.group ? contributor&.group&.name : contributor&.company_user&.name
          else
            @current_data = "Agent Assigned"
          end
          create_policy_activity
        end
        @policy_changes = @policy_changes.except('target')
      end
    end
  end

  def policy_emails_created_activity
    @key = "New reminder/escalation added"
    @previous_data = nil
    @current_data = @form.subject
    create_policy_activity
  end

  def policy_emails_deleted_activity
    @key = "Reminder/escalation deleted"
    @previous_data = nil
    @current_data = @form.subject
    create_policy_activity
  end

  def create_policy_activity
    if @current_data.present? || @previous_data.present?
      EventLog.create!(sla_policy_activity_params)
    end
  end

  def sla_policy_activity_params
    {
      entity_id: @form.respond_to?(:sla_policy_id) ? @form.sla_policy_id : @form.id,
      company_id: @form.respond_to?(:company_id) ? @form.company_id : Sla::Policy.find(@form.sla_policy_id).company.id,
      module_name: 'helpdesk',
      owner_id: @user_id,
      activity_type: policy_activity_type,
      data: sla_policy_data,
      workspace_id: @form.respond_to?('custom_form') ? @form.custom_form.workspace.id : Sla::Policy.find(@form.sla_policy_id).custom_form.workspace.id,
      entity_type: "Policy"
    }
  end

  def sla_policy_data
    {
      key: @key,
      previous_value: @previous_data,
      current_value: @current_data,
      policy_name: sla_policy_name
    }
  end

  def sla_policy_name
    return @form.name if @form.respond_to?('name') ##when name is updated

    return Sla::Policy.find(@form.sla_policy_id).name if @form.respond_to?(:sla_policy_id) ##when details are updated

    return @current_data if @current_data.present? ##when policy is created
  end

  def policy_activity_type
    if @activity_type == 'create'
      return 'created'
    elsif @activity_type == 'destroy'
      return 'deleted'
    elsif @activity_type == 'update'
      return 'updated'
    end
  end

  ############ Automated Tasks Event Logs #################

  def create_automated_task_activity
    EventLog.create!(automated_task_activity_params)
  end

  def automated_task_activity_params
    {
      entity_id: @form.id,
      company_id: @form.company.id,
      module_name: 'helpdesk',
      owner_id: @user_id,
      activity_type: automated_task_activity_type,
      data: automated_task_activity_type == 'updated' ? task_updated_data : automated_task_data,
      workspace_id: @form.workspace_id,
      entity_type: "AutomatedTask"
    }
  end
  
  def create_asset_automated_task_activity
    EventLog.create!(asset_automated_task_activity_params)
  end

  def asset_automated_task_activity_params
    {
      entity_id: @form.id,
      company_id: @form.company.id,
      module_name: 'asset',
      owner_id: @user_id,
      activity_type: automated_task_activity_type,
      data: automated_task_activity_type == 'updated' ? task_updated_data : automated_task_data,
      entity_type: "AssetAutomatedTask"
    }
  end

  def automated_task_activity_type
    types = {
      "create" => 'created',
      "destroy" => 'deleted',
      "enable" => 'enabled',
      "disable" => 'disabled',
      "update" => 'updated',
    }

    types[@activity_type]
  end

  def automated_task_data
    {
      key: "Automated Task #{automated_task_activity_type}",
      prev_data: nil,
      current_data: nil,
      task_name: @form.name,
    }
  end

  def automated_task_updated_activity
    automated_task_values = @form.saved_changes.except('updated_at')
    create_automated_task_activity if automated_task_values.blank?
    automated_task_values.each do
      if automated_task_values[:name].present?
        @key = "Task name"
        @previous_data = automated_task_values[:name].first
        @current_data = automated_task_values[:name].second
        @value = automated_task_values.except('name')
        create_task_activity
      end
    end
  end

  def asset_automated_task_updated_activity
    automated_task_values = @form.saved_changes.except('updated_at')
    create_asset_automated_task_activity if automated_task_values.blank?
    automated_task_values.each do
      if automated_task_values[:name].present?
        @key = "Task name"
        @previous_data = automated_task_values[:name].first
        @current_data = automated_task_values[:name].second
        @value = automated_task_values.except('name')
        EventLog.create!(asset_automated_task_activity_params) if @current_data.present? || @previous_data.present?
      end
    end
  end

  def create_task_activity
    if @current_data.present? || @previous_data.present?
      EventLog.create!(automated_task_activity_params)
    end
  end

  def create_asset_task_activity
    if @current_data.present? || @previous_data.present?
      EventLog.create!(asset_automated_task_activity_params)
    end
  end

  def task_updated_data
    {
      key: @key,
      previous_value: @previous_data,
      current_value: @current_data,
      task_name: @form.name
    }
  end
end
