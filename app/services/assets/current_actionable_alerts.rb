class Assets::CurrentActionableAlerts
  def initialize(scoped_company_user)
    @scoped_company_user = scoped_company_user
  end

  def resources
    format_alerts(actionable_alerts)
  end

  def paginated_resources(page, per_page)
    paginated_alerts = actionable_alerts&.limit(per_page)&.offset((page - 1) * per_page) || []
    format_alerts(paginated_alerts)
  end

  def total_count
    actionable_alerts.size
  end

  private

  def actionable_alerts
    @actionable_alerts ||= begin
      contributor_actionable_alerts = @scoped_company_user.contributor&.contributor_actionable_alerts

      if contributor_actionable_alerts.present?
        contributor_actionable_alerts.joins(:actionable_alert)
                                     .where(dismissed_at: nil, actionable_alerts: { workspace_id: nil, module: 'asset' })
                                     .select('actionable_alerts.id', 'actionable_alerts.message', 'actionable_alerts.link', 'actionable_alerts.created_at')
      else
        ActionableAlert.none
      end
    end
  end

  def format_alerts(alerts)
    return [] unless alerts.present?

    alerts.map do |alert_data|
      {
        id: alert_data['id'],
        message: alert_data['message'],
        link: alert_data['link'],
        created_at: alert_data['created_at']
      }
    end
  end
end
