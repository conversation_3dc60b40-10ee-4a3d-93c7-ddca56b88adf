# frozen_string_literal: true

class ManagedAssetService < BaseAssetService
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include ReadReplicaDb

  def update(agent_location)
    @agent_location = agent_location
    # params company, serial_number, mac_addresses, display_name, manufacturer
    @managed_asset = find_managed_asset(@company, pc_serial_no, mac_addresses, display_name, manufacturer)

    if @managed_asset.blank?
      return if combined_asset.present?
      
      new_managed_asset
      set_main_attributes
    end

    @is_lower_precedence = !is_higher_precedence?(**precedence_data)
    @managed_asset.lower_precedence = @is_lower_precedence
    @managed_asset.assignment_information = AssignmentInformation.new if @managed_asset.assignment_information.blank?
    @company_user.update(self_onboarding: :onboard) if @company_user.present?
    
    add_usage_and_location
    set_optional_attributes
    set_asset_attributes
    @managed_asset.save!
    @managed_asset.assignment_information.save!
    @managed_asset.hardware_detail&.save!
    add_to_discovered_assets
    add_applications
    add_user_accounts
    add_system_details
  end

  def new_managed_asset
    if params["source"] == 'agent'
      @managed_asset = ManagedAsset.agent.new(company_asset_type_id: asset_type)
    else
      @managed_asset = ManagedAsset.selfonboarding.new(company_asset_type_id: asset_type)
    end
    @managed_asset.hardware_detail = @managed_asset.asset_type.hardware_detail_class.new
  end

  def add_to_discovered_assets
    discovered_asset = nil
    #params company, is_probe, serial_number, mac_addresses, display_name, ip_address, manufacturer, is_mac_duplicate
    linked_da = set_read_replica_db do
      @managed_asset.discovered_asset
    end
    already_existing_discovered_asset = find_discovered_asset(@company, false, pc_serial_no, mac_addresses, display_name, ip_address, manufacturer, false)

    # If the managed asset is already linked to a discovered asset with empty machine_serial_no and the existing discovered asset is present with the same serial no as of managed asset,
    # update and transfer its asset sources to the linked discovered asset, then remove the existing discovered asset
    if linked_da && already_existing_discovered_asset.present? && already_existing_discovered_asset.status != "imported"
      already_existing_discovered_asset.update_column(:machine_serial_no, nil)

      already_existing_discovered_asset.asset_sources.each do |asset_source|
        unless linked_da.asset_sources.exists?(source: asset_source.source)
          asset_source_record = set_read_replica_db do
            AssetSource.find(asset_source.id)
          end
          asset_source_record&.update_columns(discovered_asset_id: linked_da.id, managed_asset_id: managed_asset.id)
        end
      end

      already_existing_discovered_asset.reload
      already_existing_discovered_asset.destroy!
    end

    discovered_asset = linked_da
    discovered_asset ||= already_existing_discovered_asset
    discovered_asset ||= DiscoveredAsset.new(company_id: @company.id)

    display_name_data = display_name.present? ? display_name : discovered_asset.display_name
    mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
    sec_mac_addresses_data = sec_mac_addresses.present? ? sec_mac_addresses : discovered_asset.secondary_mac_addresses
    mac_addresses_info_data = mac_addresses_info.present? ? mac_addresses_info : discovered_asset.mac_addresses_info
    ip_address_data = ip_address.present? ? ip_address : discovered_asset.ip_address
    manufacturer_data = manufacturer.present? ? manufacturer : discovered_asset.manufacturer
    pc_serial_no_data = pc_serial_no.present? ? pc_serial_no : discovered_asset.machine_serial_no
    os_name_data = os_name.present? ? os_name : discovered_asset.os_name
    os_version_data = os_version.present? ? os_version : discovered_asset.os_version
    os_serial_no_data = os_serial_no.present? ? os_serial_no : discovered_asset.os_serial_no
    asset_source_data = asset_source.present? ? asset_source : discovered_asset.source
    system_uuid_data = system_uuid.present? ? system_uuid : discovered_asset.system_uuid
    system_up_time_data = system_up_time.present? ? system_up_time : discovered_asset.system_up_time

    discovered_asset.assign_attributes(
      display_name: display_name_data,
      asset_type: @managed_asset.asset_type.name,
      mac_addresses: mac_addresses_data,
      secondary_mac_addresses: sec_mac_addresses_data,
      mac_addresses_info: mac_addresses_info_data,
      ip_address: ip_address_data,
      manufacturer: manufacturer_data,
      machine_serial_no: pc_serial_no_data,
      os_name: os_name_data,
      os_version: os_version_data,
      os_serial_no: os_serial_no_data,
      system_uuid: system_uuid_data,
      system_up_time: system_up_time_data,
      source: asset_source_data,
      managed_asset_id: @managed_asset.id,
      status: "imported",
      last_synced_at: DateTime.now,
      lower_precedence: @is_lower_precedence
    )

    discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if discovered_asset.discovered_assets_hardware_detail.blank?
    discovered_asset.discovered_assets_hardware_detail.assign_attributes(hardware_attributes)

    discovered_asset.save!

    @managed_asset.reload
    
    add_source(discovered_asset, asset_source)
  end

  def add_usage_and_location
    if params['assignedTo'].present?
      c_user = CompanyUser.find_by_cache(id: assigned_to)
      contributor_id = c_user&.contributor_id
      @managed_asset.assignment_information.used_by_contributor_id = contributor_id
      @managed_asset.assignment_information.lower_precedence = @is_lower_precedence
  
      unless has_manually_updated_location?
        location_id = c_user&.location_id
        @managed_asset.location_id = location_id
      end
    end
  end
  
  def has_manually_updated_location?
    audits = set_read_replica_db do
      @managed_asset.audits.where(associated_type: "ComputerDetail")
    end

    audits.any? do |audit|
      audit.audited_changes["location_id"] && audit.audited_changes["source"] == "manually_added"
    end
  end 

  def set_optional_attributes
    os_data = os.present? ? os : @managed_asset.operating_system
    os_version_data = os_version.present? ? os_version : @managed_asset.os_version
    manufacturer_data = manufacturer.present? ? manufacturer : @managed_asset.manufacturer
    model_data = model.present? ? model : @managed_asset.model
    pc_serial_no_data = pc_serial_no.present? ? pc_serial_no : @managed_asset.machine_serial_number
    ip_address_data = ip_address.present? ? ip_address : @managed_asset.ip_address
    mac_addresses_data = mac_addresses.present? ? mac_addresses : @managed_asset.mac_addresses
    asset_source_data = asset_source.present? ? asset_source : @managed_asset.source
    asset_type_data = asset_type.present? ? asset_type : @managed_asset.company_asset_type_id
    system_up_time_data = system_up_time.present? ? system_up_time : @managed_asset.system_up_time
    system_uuid_data = system_uuid.present? ? system_uuid : @managed_asset.system_uuid

    @managed_asset.assign_attributes(
      operating_system: os_data,
      os_version: os_version_data,
      name: asset_name,
      manufacturer: manufacturer_data,
      model: model_data,
      machine_serial_number: pc_serial_no_data,
      ip_address: ip_address_data,
      mac_addresses: mac_addresses_data,
      source: asset_source_data,
      system_up_time: system_up_time_data,
      system_uuid: system_uuid_data,
      company_asset_type_id: asset_type_data,
      agent_location_id: @agent_location&.id,
      update_by_source_at: DateTime.now,
      lower_precedence: @is_lower_precedence
    )
  end

  def set_main_attributes
    contributor_id = CompanyUser.find_by_cache(id: assigned_to)&.contributor_id
    @managed_asset.assign_attributes(
      name:           display_name,
      company_id:     @company.id,
      assignment_information_attributes: {
        used_by_contributor_id: contributor_id,
      }
    )
  end

  def asset_name
    if @managed_asset && manually_added_attributes(@managed_asset, "name")
      @managed_asset.name
    else
      display_name.present? ? display_name : @managed_asset.name
    end
  end

  def asset_type
    if @managed_asset && manually_added_attributes(@managed_asset, "company_asset_type_id")
      @managed_asset.asset_type.id
    else
      type_id = set_read_replica_db do
        if params["assetType"].present?
          type = @company.asset_types.find_by(name: params["assetType"])
          type&.id || @company.asset_types.find_by(name: "Desktop").id
        else
          @company.asset_types.find_by(name: "Desktop").id
        end
      end
    end
  end

  def asset_source
    params["source"] == "agent" ? "agent" : "selfonboarding"
  end

  def set_asset_attributes
    if @managed_asset.hardware_detail.blank? || @managed_asset.hardware_detail_type != @managed_asset.asset_type.hardware_detail_class&.to_s
      @managed_asset.hardware_detail = @managed_asset.asset_type.hardware_detail_class&.new
    end

    if @managed_asset.hardware_detail
      @managed_asset.hardware_detail.lower_precedence = @is_lower_precedence
      valid_keys = @managed_asset.hardware_detail.attributes.keys - ["id", "created_at", "updated_at"]
      valid_keys.each do |key|
        next if hardware_attributes[key.to_sym].blank? || (key === "hostname" && is_value_same(key))
        @managed_asset.hardware_detail.send("#{key}=", hardware_attributes[key.to_sym])
      end
    end
  end

  def is_value_same(key)
    hardware_attributes[key.to_sym]&.downcase&.gsub(/[._-]/ ,"") === @managed_asset.hardware_detail[key]&.downcase&.gsub(/[._-]/ ,"")
  end

  def add_applications
    softwares = []
    asset_softwares = []

    if is_applications_present?
      managed_asset_softwares = set_read_replica_db do
        @managed_asset.asset_softwares
      end

      unless @is_lower_precedence && managed_asset_softwares.present?
        managed_asset_softwares.where(is_manual: false).delete_all if managed_asset_softwares.present?

        discovered_asset = @managed_asset.discovered_asset
        managed_discovered_asset_softwares = set_read_replica_db do
          discovered_asset&.asset_softwares
        end 
        managed_discovered_asset_softwares ||= discovered_asset.asset_softwares

        managed_discovered_asset_softwares.where(is_manual: false).delete_all if managed_discovered_asset_softwares.present?
        asset_softwares = set_read_replica_db do
          @managed_asset.asset_softwares.where(is_manual: true).pluck(:name)
        end
        
        applications.each do |app|
          app["displayName"] = app["displayName"].remove_unicode_control_codes if app["displayName"].include_unicode_control_codes?
          if app["displayName"] != "" && !asset_softwares.include?(app["displayName"])
            software = @managed_asset.asset_softwares.new(application_params(app))
            software.assign_attributes(discovered_asset_id: @managed_asset.discovered_asset.id)
            softwares << software.as_json.compact
          end
        end
      end
    end

    if !asset_softwares.include?(operating_system_attributes[:name])
      unless @is_lower_precedence && @managed_asset.asset_softwares&.operating_systems&.present?
        @managed_asset.asset_softwares.operating_systems.delete_all
        os = set_read_replica_db do
          @managed_asset.asset_softwares.find_or_initialize_by(operating_system_attributes)
        end
        os.assign_attributes(discovered_asset_id: @managed_asset.discovered_asset.id)
        softwares << os.as_json.compact
      end
    end
    batched_hashes = softwares.group_by { |hash| hash.keys.sort }
    batched_hashes.each do |key_structure, batch|
      filtered_batch = batch.map { |hash| hash.compact }
      AssetSoftware.upsert_all filtered_batch if filtered_batch.present?
    end
  end

  def application_params app
    install_date = app["installDate"]&.gsub("\u0000", "")
    {
      name: app["displayName"].gsub("\u0000", ""), 
      software_type: "Application",
      install_date: install_date === "" ?  nil : install_date
    }
  end

  def add_user_accounts
    user_accounts = []
    required_keys = %w[ name full_name last_login managed_asset_id discovered_asset_id ]

    if user_accounts_info.present?
      unless @is_lower_precedence && @managed_asset.asset_user_accounts.present?
        @managed_asset.asset_user_accounts.delete_all if @managed_asset.asset_user_accounts.present?

        user_accounts_info.each do |acc|
          user_account = @managed_asset.asset_user_accounts.new(user_account_params(acc))
          user_account.assign_attributes(discovered_asset_id: @managed_asset.discovered_asset.id)
          user_accounts << user_account.as_json(only: required_keys)
        end
        AssetUserAccount.upsert_all user_accounts if user_accounts.present?
      end
    end
  end

  def user_account_params acc
    {
      name: acc["name"], 
      full_name: acc["fullName"],
      last_login: acc["lastLogin"]
    }
  end

  def add_system_details
    system_detail_categories&.each do |detail|
      detail_data = send(detail)
      if detail_data.present?
        asset_detail = set_read_replica_db do
          @managed_asset.system_details.find_or_initialize_by(detail_category: detail)
        end
        asset_detail.assign_attributes(
          discovered_asset_id: @managed_asset.discovered_asset.id,
          detail_category: detail,
          detail_data: get_detail_data(detail, detail_data)
        )
        asset_detail.save!
      end
    end
  end

  def remove_null_characters(detail_data)
    detail_data.each do |data|
      data.each do |key, value|
        data[key] = value.gsub("\u0000", "") if value.is_a?(String)
      end
    end
  end

  def is_applications_present?
    applications.present? && applications.count > 0
  end

  def precedence_data
    { 
      asset_sources: @managed_asset.sources,
      current_source: asset_source,
      discovered_asset: @managed_asset.discovered_asset,
      incoming_discovered_asset: @managed_asset.discovered_asset
    }
  end

  def system_detail_categories
    common_categories = [
      'chrome_extensions_info',
      'network_interfaces_info',
      'etc_hosts_info',
      'startup_items_info',
      'certificates_info',
      'kernel_info'
    ]

    windows_categories = [
      'disk_info',
      'logical_disk_info',
      'drive_encryption_info',
      'patch_info',
      'scheduled_tasks_info',
      'system_services_info',
      'shared_resources_info',
      'system_tpm_info',
      'system_info',
      'secure_boot_status',
      'firewall_info'
    ]

    mac_categories = [
      'crash_reports',
      'shared_folders',
      'mount_details',
      'printer_details',
      'usb_devices',
      'cpu_brand_name',
      'battery_info',
      'launchd_info',
      'system_controls_info',
      'mac_disk_info',
      'apple_system_integrity_protection',
      'firefox_extensions_info',
      'safari_extensions_info',
      'application_layer_firewall_status',
      'user_ssh_keys_state'
    ]

    case @managed_asset.operating_system
    when 'Windows'
      common_categories.concat(windows_categories)
    when 'MAC OS'
      common_categories.concat(mac_categories)
    end
  end

  def combined_asset
    ManagedAsset.unscoped.find_by(
      company_id: @company.id,
      machine_serial_number: pc_serial_no,
      source: "agent",
      is_combined_asset: true
    )
  end
end
