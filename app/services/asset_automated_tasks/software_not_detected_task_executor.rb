class AssetAutomatedTasks::SoftwareNotDetectedTaskExecutor
  def call
    software_not_detected_tasks = Assets::AutomatedTask
                                    .joins(task_events: :event_type)
                                    .where(assets_event_types: { name: "{a software} wasn't detected" }, disabled_at: nil, force_disabled: false)
                                    .distinct
  
    software_not_detected_tasks.find_each do |task|
      task.task_events.find_each do |event|
        next unless event.event_type.name == "{a software} wasn't detected"
        event_detail = Assets::EventDetail.find_by(task_event_id: event.id)
        parsed_value = JSON.parse(event_detail.value)
        softwares = parsed_value["asset_softwares"].map { |i| i["name"] }
        company = task.company
        software_not_detected_assets = company.managed_assets
                                        .where(archived: false)
                                        .where.not(id: company.managed_assets
                                          .joins(:asset_softwares)
                                          .where(asset_softwares: { name: softwares, archived_at: nil })
                                          .select(:id)
                                        )
                                        .select(:id, :name)
                                        .map { |asset| { "id" => asset.id, "name" => asset.name } }

        if software_not_detected_assets.present?
          event_data = {
            "event_type" => event.event_type.name,
            "software_not_detected_assets" => software_not_detected_assets,
            "softwares" => softwares
          }
          AssetAutomatedTasks::TaskExecutor.new(task, event_data).call
        end
      end
    end
  end
end
