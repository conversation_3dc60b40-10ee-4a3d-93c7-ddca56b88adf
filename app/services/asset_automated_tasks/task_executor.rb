class AssetAutomatedTasks::TaskExecutor
  
  def initialize(task, event_data)
    @task = task
    @event_data = event_data
  end

  def call
    execute_task(@task)
  end

  def execute_task(task)
    task.task_actions.each do |action|
      type_name = action.action_type.action_class
      AssetAutomatedTasks::TaskActionsWorker.perform_async(action.id, @task.id, class_name, type_name, @event_data)
    end
  rescue Exception => e
    if Rails.env.staging? || Rails.env.production?
      Bugsnag.notify(e)
    end
  end

  def class_name
    @class_name ||= @task.class.name
  end
end
