module AssetAutomatedTasks
  class JsonOutput
    attr_accessor :company

    def initialize(company)
      self.company = company
    end

    def json(task)
      {
        id: task.id,
        name: task.name,
        order: task.order,
        serial_number: task.serial_number,
        events: task.task_events.map { |event| event_json(event) },
        actions: task.task_actions.sort_by(&:id).map { |action| action_json(action) },
        disabled_at: task.disabled_at,
        company_id: task.company_id,
        force_disabled: task.force_disabled
      }
    end

    def event_json(detail)
      return nil unless detail
      {
        id: detail.id,
        class: detail.class.name.demodulize,
        node_type: detail.event_type.as_json,
        nodes: detail.event_details.map {|d| event_detail_json(d) },
        value: value_detail_json(detail),
      }
    end

    def value_detail_json(detail)
      return nil unless detail.value

      return JSON.parse(detail.value)
    end

    def event_detail_json(detail)
      {
        id: detail.id,
        class: detail.class.name.demodulize,
        value: value_detail_json(detail),
        node_type: detail.event_subject_type.as_json,
      }
    end

    def action_json(task_action)
      return nil unless task_action
      {
        node_type: task_action.action_type.as_json,
        value: JSON.parse(task_action.value),
      }
    end

    def automated_data
      @automated_data ||= params[:task].as_json
    end
  end
end
