module AssetAutomatedTasks
  class Update
    include ContributorHelper
    include HandleCompanyCacheKeys

    attr_accessor :task, :data

    TYPE_MAPPING = {
      'TextDetail' => 'text',
    }

    def initialize(task)
      self.task = task
    end

    def update(data)
      self.data = data
      ActiveRecord::Base.transaction do
        task.task_events.destroy_all
        task.task_actions.destroy_all
        task.name = data["name"]
        task.task_events = events
        task.task_actions = actions
        task.force_disabled = false if valid_contributors
        task.save!
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      end
      task
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
      { error_message: "Transaction aborted" }
    end

    def events
      @events = []
      self.data['events'].each do |event|
        @event = Assets::TaskEvent.new(event_type_id: event['node_type']['id'])
        node_type = event['nodes'][0]['node_type']
        event['nodes'].each do |node|
          eee = event_detail(node, node_type)
          @event.event_details << eee
        end
        @events << @event
      end
      @events
    end

    def event_detail(node, node_type)
      node = node['value'] if node['value'].present?
      detail = Assets::EventDetail.new
      if ['Object', 'Type'].include?(node_type['type'])
        detail.event_subject_type_id = node_type['id']
        detail.value = node.to_json
      end
      detail
    end

    def actions
      @actions = []
      @contributor_values = []
      # update_child_company_default_task if task.company_mailer.present?
      self.data['actions'].each do |action|
        type_id = action['node_type']['id']
        @contributor_values << extract_contributor_values(action['value'])
        value = action['value'].to_json
        template_id = action['value'].dig('template', 'id') if action['value'].key?('template')
        @actions << Assets::TaskAction.new(action_type_id: type_id, value: value)
      end
      @actions
    end

    def valid_contributors
      contributor_values = @contributor_values.flatten
      Contributor.where(id: contributor_values).count == contributor_values.count
    end

    def action_detail(node)
      detail = Assets::ActionDetail.new
      detail.action_type_id = node['node_type']['id']
      detail.value = node['value']
      detail
    end

    def task
      @task ||= Assets::AutomatedTask.new(company: @company, contributor: @contributor)
    end
  end
end
