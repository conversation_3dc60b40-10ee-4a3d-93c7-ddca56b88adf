module AssetAutomatedTasks
  class Create
    attr_accessor :task, :data

    TYPE_MAPPING = {
      'TextDetail' => 'text',
    }

    def initialize(task)
      self.task = task
    end

    def create(data)
      current_comapny = self.task.company
      new_order = (Assets::AutomatedTask.where(company: current_comapny).maximum(:order) || 0) + 1
      new_serial = (Assets::AutomatedTask.where(company: current_comapny).maximum(:serial_number) || 0) + 1
      self.data = data
      ActiveRecord::Base.transaction do
        self.task.task_events = events
        self.task.task_actions = actions
        self.task.order = new_order
        self.task.serial_number = new_serial
        self.task.save!
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
      self.task
    end

    def events
      @events = []
      self.data['events'].each do |event|
        @event = Assets::TaskEvent.new(event_type_id: event['node_type']['id'])
        node_type = event['nodes'][0]['node_type']
        event['nodes'].each do |node|
          @event.event_details << event_detail(node, node_type)
        end
        @events << @event
      end
      @events
    end

    def event_detail(node, node_type)
      node = node['value'] if node['value'].present?
      detail = Assets::EventDetail.new
      if ['Object', 'Type'].include?(node_type['type'])
        detail.event_subject_type_id = node_type['id']
        detail.value = node.to_json
      end
      detail
    end

    def actions
      @actions = []
      self.data['actions'].each do |action|
        type_id = action['node_type']['id']
        value = (action['value'] || "").to_json
        template_id = action['value'].dig('template', 'id') if action['value']&.key?('template')
        @actions << Assets::TaskAction.new(action_type_id: type_id, value: value)
      end
      @actions
    end

    def action_detail(node)
      detail = AutomatedTasks::ActionDetail.new
      detail.action_type_id = node['node_type']['id']
      detail.value = node['value'].to_json
      detail
    end

    def task
      @task ||= Assets::AutomatedTask.new(company: @company, contributor: @contributor)
    end
  end
end
