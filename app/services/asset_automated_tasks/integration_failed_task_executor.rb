class AssetAutomatedTasks::IntegrationFailedTaskExecutor
  def call
    integration_failed_tasks = Assets::AutomatedTask
                                .joins(task_events: :event_type)
                                .where(assets_event_types: { name: "{an integration} failed continuously" }, disabled_at: nil, force_disabled: false)
                                .distinct

    integration_failed_tasks.find_each do |task|
      task.task_events.find_each do |event|
        next unless event.event_type.name == "{an integration} failed continuously"
        event_detail = Assets::EventDetail.find_by(task_event_id: event.id)
        parsed_value = JSON.parse(event_detail.value)
        number_of_days = parsed_value["number_of_days"].to_i
        company = task.company
        failed_integrations = []

        if event_detail.event_subject_type.name === "any integration"
          failed_integrations = company.company_integrations
                                  .includes(:integration)
                                  .where('failure_count >= ?', number_of_days)
                                  .map { |ci| ci.integration.name.titleize }
        else
          integration_names = parsed_value["integrations"].map { |i| i["searchable_name"] }
          company_integrations = company.company_integrations
                                  .includes(:integration)
                                  .where(integrations: { name: integration_names })
                                  .where('company_integrations.failure_count >= ?', number_of_days)

          failed_integrations = company_integrations.map { |ci| ci.integration.name.titleize }
        end

        
        if failed_integrations.present?
          event_data = {
            "event_type" => event.event_type.name,
            "failed_integrations" => failed_integrations
          }
          AssetAutomatedTasks::TaskExecutor.new(task, event_data).call
        end
      end
    end
  end
end
