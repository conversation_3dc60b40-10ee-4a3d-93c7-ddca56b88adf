class AssetAutomatedTasks::AgentResyncT<PERSON>Executor
  def call
    agent_not_synced_tasks = Assets::AutomatedTask
                              .joins(task_events: :event_type)
                              .where(assets_event_types: { name: "{an agent} didn't resync" }, disabled_at: nil, force_disabled: false)
                              .distinct
    
    agent_not_synced_tasks.find_each do |task|
      task.task_events.find_each do |event|
        next unless event.event_type.name == "{an agent} didn't resync"
        event_detail = Assets::EventDetail.find_by(task_event_id: event.id)
        parsed_value = JSON.parse(event_detail.value)
        number_of_days = parsed_value["number_of_days"].to_i
        company = task.company
        not_resynced_agents = company.agent_locations.where.not(last_scanned_at: nil)
                                .where("DATE(?) - DATE(last_scanned_at) >= ?", Date.current, number_of_days)
                                .select(:os, :computer_name)
                                .map { |agent| { "os" => agent.os, "computer_name" => agent.computer_name } }
       
        if not_resynced_agents.present?
          event_data = {
            "event_type" => event.event_type.name,
            "not_resynced_agents" => not_resynced_agents, 
          }
          AssetAutomatedTasks::TaskExecutor.new(task, event_data).call
        end
      end
    end
  end
end
