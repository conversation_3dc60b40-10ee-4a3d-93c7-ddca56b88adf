class AssetAutomatedTasks::LowDiskSpaceTaskExecutor
  def call
    low_disk_space_tasks = Assets::AutomatedTask
                            .joins(task_events: :event_type)
                            .where(assets_event_types: { name: "{an asset's} disk space is low" }, disabled_at: nil, force_disabled: false)
                            .distinct

    low_disk_space_tasks.find_each do |task|
      task.task_events.find_each do |event|
        next unless event.event_type.name == "{an asset's} disk space is low"
        event_detail = Assets::EventDetail.find_by(task_event_id: event.id)
        parsed_value = JSON.parse(event_detail.value)
        free_space = parsed_value["free_space"].to_i
        free_space_unit = parsed_value["free_space_unit"]["name"].downcase

        company = task.company

        base_query = company.managed_assets.joins("INNER JOIN computer_details ON computer_details.id = managed_assets.hardware_detail_id")
          .where(archived: false, hardware_detail_type: "ComputerDetail")
          .where.not("computer_details.disk_free_space": nil)

        low_disk_space_assets =
          if free_space_unit == "gb"
            base_query
              .where("computer_details.disk_free_space ~* '\\d+(\\.\\d+)?\\s*GB'")
              .where("CAST(REGEXP_REPLACE(computer_details.disk_free_space, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) < ?", free_space)
              .select(:id, :name)
              .map { |asset| { "id" => asset.id, "name" => asset.name } }

          elsif free_space_unit == "%"
            base_query
              .where("computer_details.disk_free_space ~* '\\d+(\\.\\d+)?\\s*GB'")
              .where("computer_details.hard_drive ~* '\\d+(\\.\\d+)?\\s*GB'")
              .where(<<~SQL.squish, free_space)
                (
                  (CAST(REGEXP_REPLACE(computer_details.disk_free_space, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) /
                   NULLIF(CAST(REGEXP_REPLACE(computer_details.hard_drive, '[^0-9.]', '', 'g') AS DOUBLE PRECISION), 0)
                  ) * 100
                ) < ?
              SQL
              .select(:id, :name)
              .map { |asset| { "id" => asset.id, "name" => asset.name } }
          end

        if low_disk_space_assets.present?
          event_data = {
            "event_type" => event.event_type.name,
            "low_disk_space_assets" => low_disk_space_assets,
          }
          AssetAutomatedTasks::TaskExecutor.new(task, event_data).call
        end
      end
    end
  end
end
