# frozen_string_literal: true

class Integrations::Meraki::FetchData
  include ReadReplicaDb

  attr_accessor :api_logs, :dynamic_meraki_host

  MERAKI_HOST = 'https://api.meraki.com'
  GOOGLE_MAP_HOST = 'https://maps.googleapis.com/maps'
  MERAKI_LOCATION_KEY = Rails.application.credentials.meraki_google_maps_key
  def initialize(meraki_config, flag=true)
    @config = meraki_config
    @intg_id = set_read_replica_db do
      Integration.find_by_name("meraki").id
    end
    @api_logs = []
    @first_time = flag
  end

  def authenticate?
    url ="/api/v1/organizations"
    response = make_api_call(url, "authentication")
    if response.success? && valid_organization_name(response.parsed_response).present?
      return true
    else
      error_message = response.parsed_response.is_a?(Hash) ? response.parsed_response["errors"] : []
      @config.errors.add(:base, error_message.first) if error_message.present? && @config.respond_to?(:errors)
      return false
    end
  end

  def organizations
    url = "/api/v1/organizations"
    response = make_api_call(url, "organizations")
    if response.code == 401
      log_event(response, "organizations", { code: response.code })
      return
    end
    response.parsed_response
  end

  def networks(organization_id)
    url = "/api/v1/organizations/#{organization_id}/networks"
    response = make_api_call(url, "networks")
    response.parsed_response
  end

  def organization_devices_statuses(organization_id)
    url = "/api/v1/organizations/#{organization_id}/devices/statuses"
    response = make_api_call(url, "organization_devices")
    response.parsed_response
  end

  def switch_ports(serial_number)
    url = "/api/v1/devices/#{serial_number}/switch/ports"
    response = make_api_call(url, "switch_ports_statuses")
    response.parsed_response
  end

  def ap_device_usage_history(network_id, device_serial_no, from, resolution, date_format)
    url = "/api/v1/networks/#{network_id}/wireless/usageHistory?t0=#{from}&resolution=#{resolution}&deviceSerial=#{device_serial_no}"
    response = make_api_call(url, "wireless_devices_usage_history")
    if response&.code == 200
      response = response.parsed_response
      return {
        filtered_response: {
          graph_labels: response.pluck("endTs").map { |t| t.to_datetime.strftime(date_format) },
          graph_data: response.pluck("totalKbps").map { |t| t.to_f / 1000 }
        },
        status: :ok
      }
    else
      response
    end
  end

  def firewall_usage_history(network_id, from, resolution, date_format)
    to = DateTime.now
    url = "/api/v1/networks/#{network_id}/appliance/uplinks/usageHistory?t0=#{from}&t1=#{to}&resolution=#{resolution}"
    response = make_api_call(url, "firewall_usage_history")
    if response&.code == 200
      response = response.parsed_response
      return {
        filtered_response: {
          graph_labels: response.pluck("endTime").map {|res| res.to_datetime.strftime(date_format)},
          wan1_graph_data: response.pluck("byInterface").map { |res| res.map { |i| (i["sent"] + i["received"]) * 2.2222e-9 if i["interface"] == "wan1" }}.flatten.compact_blank,
          wan2_graph_data: response.pluck("byInterface").map { |res| res.map { |i| (i["sent"] + i["received"]) * 2.2222e-9 if i["interface"] == "wan2" }}.flatten.compact_blank
        },
        status: :ok
      }
    else
      response
    end
  end

  def ap_ssids(device_serial)
    url = "/api/v1/devices/#{device_serial}/wireless/status"
    response = make_api_call(url, "ap ssids")
    return unless response['basicServiceSets'].present?

    if response.code == 400
      log_event(response, "ap ssids", { code: response.code })
      return
    end

    response['basicServiceSets'].pluck('ssidName').reject{ |e| e.include?('Unconfigured')}.uniq
  end

  def switch_client_usage_history(network_id, client_ids, from, resolution, date_format)
    to = DateTime.now
    client_data = []
    response = nil
    client_ids.each do |client|
      url = "/api/v1/networks/#{network_id}/clients/usageHistories?t0=#{from}&t1=#{to}&clients=#{client}"
      response = make_api_call(url, "switch_client_usage_history")
      data = response.parsed_response.is_a?(Array) ? response.parsed_response.pluck("usageHistory") : nil
      client_data << data if data
    end
    if client_data.present?
      response = client_data.flatten.each_with_object({}) { |g,h| h.update(g["ts"]=>g.dup) { |_,oh,nh| oh.update(nh) { |k, ov, nv| k=="ts" ? ov : ov + nv }}}.values
      refine_dates = response.map { |res| res['ts'].to_datetime.strftime(date_format) }
      refine_data = response.map { |i| (i['sent']+ i['received']) }

      return {
        filtered_response: {
          graph_labels: (0).step(refine_dates.size - 1, 3).map { |index| refine_dates[index] },
          graph_data: refine_data.each_slice(3).map { |slice| slice.inject(:+) * 2.22224e-6 }
        },
        status: :ok
      }
    else
      response
    end
  end

  def device_latency_history(device_serial, timespan)
    timespan = 14400 unless timespan.present?
    url = "/api/v1/devices/#{device_serial}/wireless/latencyStats?timespan=#{timespan}"
    response = make_api_call(url, "wirless_latency_history")
    if response&.code == 200
      raw_distribution = response.parsed_response["latencyStats"].map { |t| t.map { |i| i["rawDistribution"] }}.flatten.compact_blank
      filter_raw_data = filter_data(raw_distribution)
      return {
        filtered_response: {
          latency_labels: ['latency more than 64 ms', 'latency less than 64 ms', 'latency equals to 64 ms'],
          latency_data: filter_raw_data
        },
        status: :ok
      }
    else
      response
    end
  end

  def device_clients(device_serial_no, from)
    url = "/api/v1/devices/#{device_serial_no}/clients?t0=#{from}"
    response = make_api_call(url, "device_clients")
    response.parsed_response
  end

  def network_devices(network_id)
    url = "/api/v1/networks/#{network_id}/devices"
    response = make_api_call(url, "network_devices")
    if response.code == 404
      log_event(response, "network devices", { code: response.code })
      return
    end

    response.parsed_response
  end

  def network_sm_devices(network_id)
    url = "/api/v1/networks/#{network_id}/sm/devices"
    response = make_api_call(url, "network_sm_devices")
    response.parsed_response
  end

  def organization_devices(organization_id, next_device_id=nil)
    url = "/api/v1/organizations/#{organization_id}/devices?perPage=500&startingAfter=#{next_device_id}"
    response = make_api_call(url, "organization_devices")
    response
  end

  def network_clients(network_id, next_client_id = nil)
    url = "/api/v1/networks/#{network_id}/clients?perPage=200&startingAfter=#{next_client_id}"
    response = make_api_call(url, "network_clients")
    response
  end

  def meraki_location_data(addresses)
    url = "/api/geocode/json?address=#{addresses}&key=#{MERAKI_LOCATION_KEY}"
    response = make_google_api_call(url, "meraki_location_data")
    response.parsed_response
  end

  def body
    from_date = nil
    to_date = nil

    if @first_time
      from_date = ( Time.now - 30.days ).to_i
      to_date = Time.now.to_i
      { perPage: 1000, t0: from_date, t1: to_date }
    else
      from_date = ( Time.now - 2.days ).to_i
      to_date = Time.now.to_i
      { perPage: 1000, t0: from_date, t1: to_date }
    end
  end

  def headers
    {'Accept': "application/json", 'X-Cisco-Meraki-API-Key': @config.token}
  end

  def valid_organization_name organizations
    organizations.find { |org| org["name"].downcase == @config.name.downcase }
  end

  def log_event response, api_type, fur_detail={}, excep = nil
    status = response.present? && response.code == 200 ? :success : :error
    api_response = {code: response.code, message: response.message, body: response.body}.to_s if response.present?
    log_params = {
      api_type: api_type,
      class_name: self.class.name,
      integration_id: @intg_id,
      company_id:  @config&.company_id,
      status: status,
      detail: details.merge(fur_detail || {}),
      activity: :action,
      response: api_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail:  excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil }
    @api_logs << log_params
    LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
  end

  def make_api_call(endpoint, api_type, retries=0)
    detail_params = { endpoint: endpoint, api_type: api_type }
    no_body_apis = ["network_sm_devices", "wirless_latency_history"]
    if no_body_apis.include?(api_type)
      response = HTTParty.get("#{current_meraki_host}#{endpoint}", headers: headers)
    else
      response = HTTParty.get("#{current_meraki_host}#{endpoint}", headers: headers, body: body)
    end
    log_event response, api_type, detail_params
    if api_type == 'organizations' && response.code == 200
      org_url = response.parsed_response[0]['url'] rescue nil
      if org_url.present?
        subdomain = URI.parse(org_url).host.split('.').first
        self.dynamic_meraki_host = "https://#{subdomain}.meraki.com"
      end
    end

    if success_status(response.code, api_type)
      return response
    else
      if response.code == 429 || response.code == 502
        sleep 3
        return response if retries >= 5

        make_api_call(endpoint, api_type, retries + 1)
      elsif !@first_time && response.code == 403
        raise Exception.new "403 - Forbidden"
      else
        runtime_api_keys = ['firewall_usage_history', 'switch_client_usage_history', 'wireless_devices_usage_history', 'wirless_latency_history', 'network_devices']
        return response if api_type == "authentication"
        if runtime_api_keys.include?(api_type)
          return response
        else
          raise Exception.new "#{response.code} - #{response.message}"
        end
      end
    end
  rescue Exception => e
    error_messages = ['SSL_connect SYSCALL', 'Net::OpenTimeout', 'Net::ReadTimeout', "Failed to open TCP connection"]
    if error_messages.find{|em| e.message.downcase.include?(em.downcase)} && retries < 5
      sleep 3
      retries_count = retries + 1
      return make_api_call(endpoint, api_type, retries_count)
    end

    Rails.logger.error e.message
    log_event response, api_type, detail_params, e
    raise e
  end

  def make_google_api_call(endpoint, api_type)
    detail_params = { endpoint: endpoint, api_type: api_type }
    response = HTTParty.get "#{GOOGLE_MAP_HOST}#{URI.parse(URI::Parser.new.escape(endpoint.strip))}"
    log_event response, api_type, detail_params
    response
  rescue => e
    Rails.logger.error e.message
    log_event response, api_type, detail_params, e
    raise e
  end

  def details
    @config ? { token: @config.token, name: @config.name } : {}
  end

  def success_status code, api_type
    return true if code == 200
    return code == 400 ? ['network_clients', 'network_sm_devices', 'ap ssids'].include?(api_type) : false
  end

  def filter_data array
    sum = Hash.new(0)
    array.each_with_object(sum) do
      |hash, sum| hash.each { |key, value| sum[key] += value }
    end

    total = sum.values.sum
    less_than_64 = ((sum.to_a[0,7]).to_h.values.sum.to_f / total) * 100
    less_than_128 = ((sum.to_a[7,1]).to_h.values.sum.to_f / total ) * 100
    less_than_2048 = ((sum.to_a[8,5]).to_h.values.sum.to_f / total) * 100

    filter = [less_than_2048, less_than_64, less_than_128]
  end

  def current_meraki_host
    dynamic_meraki_host || MERAKI_HOST
  end
end
