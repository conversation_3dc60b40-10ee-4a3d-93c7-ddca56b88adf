class Integrations::Sophos::FetchData
  include ReadReplicaDb

  attr_accessor :client, :error

  def initialize(company_id, config = nil, tenant = nil)
    @company_id = company_id
    @config = config
    @tenant = tenant
    @intg_id = set_read_replica_db do
      Integration.find_by_name('sophos').id
    end
    @requests_count = 0
  end

  def token(sophos_data)
    url = "https://id.sophos.com/api/v2/oauth2/token"
    auth_params = {
      grant_type: 'client_credentials',
      client_id: sophos_data['client_id'],
      client_secret: sophos_data['client_secret'],
      scope: 'token'
    }
    headers = {
      'Content-Type' => 'application/x-www-form-urlencoded'
    }
    response = HTTParty.post(url, headers: headers, body: auth_params)
    log_event(response, 'token', { sophos_data: sophos_data })
    parsed = response.parsed_response
    if parsed['expires_in'].present?
      parsed['expires_in'] = Time.current + parsed['expires_in'].to_i
    end
    parsed
  end

  def fetch_tenant_info(access_token)
    url = "https://api.central.sophos.com/whoami/v1"
    headers = {
      'Authorization' => "Bearer #{access_token}"
    }
    response = make_api_call(url, 'get_authorization', headers)
    response.parsed_response
  end

  def refresh_access_token
    response = token(@config)
    if response && response['access_token']
      @config.update(
        access_token: response['access_token'],
        expires_in: response['expires_in'],
        skip_callbacks: true
      )
      @config.reload
    end
    @config
  end

  def check_token_expiry
    if @requests_count == 30 || (@config.expires_in.present? && Time.now > (@config.expires_in - 5.minutes))
      refresh_access_token
      @requests_count = 0
    end
    @requests_count += 1
  end

  def get_devices(next_page_token = nil)
    base_url = "#{@tenant.data_region_url}/endpoint/v1/endpoints?pageSize=100&view=full"
    url = next_page_token ? "#{base_url}&pageFromKey=#{next_page_token}" : base_url
    headers = {
      'Authorization' => "Bearer #{@config.access_token}",
      'X-Tenant-ID' => @tenant.tenant_id
    }
    response = make_api_call(url, 'get_devices', headers)
    response.parsed_response
  end

  def make_api_call(url, api_type, headers)
    check_token_expiry
    response = HTTParty.get(url, headers:headers)

    if response['error']
      self.error = response['error']
    end
    log_event(response, api_type, { endpoint: url })
    response
  end

  def log_event(response, api_type, additional_details = {}, excep = nil)
    status = response.present? && response.code == 200 ? :success : :error
    api_response = { code: response.code, message: response.message, body: response.body }.to_s if response
    log_params = {
      api_type: api_type,
      class_name: self.class,
      integration_id: @intg_id,
      company_id: @company_id,
      status: status,
      detail: details.merge(additional_details || {}),
      activity: 1,
      response: api_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil,
    }
    if Rails.env.test?
      Logs::ApiEvent.create(log_params)
    else
      LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
    end
  end

  def ok?
    error.blank?
  end

  def details
    @config ? { token: @config.access_token, expires_in: @config.expires_in } : {}
  end
end
