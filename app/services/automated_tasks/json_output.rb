module AutomatedTasks
  class JsonOutput
    attr_accessor :company

    def initialize(company)
      self.company = company
    end

    def json(task)
      {
        id: task.id,
        name: task.name,
        order: task.order,
        serial_number: task.serial_number,
        assign_values: task.assign_values,
        events: task.task_events.map { |event| event_json(event) },
        actions: task.task_actions.sort_by(&:id).map { |action| action_json(action) },
        disabled_at: task.disabled_at,
        company_mailer: task.company_mailer&.event,
        workspace_id: task.workspace_id,
        company_id: task.workspace.company_id,
        custom_form_id: task.custom_form&.id,
        force_disabled: task.force_disabled,
        trigger_count: task.trigger_count,
        selected_task_group: task.automated_task_group,
        grouped_automated_task_id: task.automated_task_group_id,
      }
    end

    def event_json(detail)
      return nil unless detail
      {
        id: detail.id,
        class: detail.class.name.demodulize,
        node_type: detail.event_type.as_json,
        nodes: detail.event_details.map {|d| event_detail_json(d) },
        value: value_detail_json(detail),
      }
    end

    def value_detail_json(detail)
      return nil unless detail.value
      if detail.event_subject_type.name == "contract"
        assets = JSON.parse(detail.value)['contracts']
        ids = assets.map{ |a| a['id'] }
        json = Contract.where(id: ids).select(:id, :name).map{ |contract| { id: contract.id, name: contract.name } }
        return { contracts: json }
      elsif detail.event_subject_type.name == "user or group"
        return { contributors: contributors_json(detail) }
      elsif detail.event_subject_type.name == "asset"
        return { assets: assets_json(detail) }
      elsif detail.event_subject_type.name == "vendor"
        return { vendor: vendor_json(detail) }
      elsif detail.event_subject_type.name == "telecom servive"
        return { vendor: telecom_service_json(detail) }
      else
        return JSON.parse(detail.value)
      end
    end

    def contributors_json(detail)
      contributors = JSON.parse(detail.value)['contributors']
      ids = contributors.map{ |a| a['id'] }
      groups = company.groups.where(contributor_id: ids)
      groups_hash = { }
      groups.find_each do |group|
        groups_hash[group.contributor_id] = group.name
      end
      company_users = company.company_users.where(contributor_id: ids).includes(:user)
      company_users_hash = { }
      company_users.each do |company_user|
        company_users_hash[company_user.contributor_id] = company_user.full_name
      end

      json = contributors.map do |contributor|
        id = contributor['id']
        name = groups_hash[id] || company_users_hash[id]
        {
          id: id,
          name: name,
        }
      end
    end

    def vendor_json(detail)
      json = JSON.parse(detail.value)['vendor']
      vendor = Vendor.find_by(id: json['id'])
      if vendor
        { id: vendor.id, name: vendor.name }
      else
        nil
      end
    end

    def assets_json(detail)
      assets = JSON.parse(detail.value)['assets']
      ids = assets.map{ |a| a['id'] }
      assets = ManagedAsset.where(id: ids).select(:id, :name)
      assets.map do |asset|
        {
          id: asset.id,
          name: asset.name,
        }
      end
    end

    def telecom_service_json(detail)
      telecom_services = JSON.parse(detail.value)['telecoms']
      ids = telecom_services.map{ |a| a['id'] }
      telecom_services = TelecomService.where(id: ids).select(:id, :name)
      telecom_services.map do |ts|
        {
          id: ts.id,
          name: ts.name,
        }
      end
    end

    def event_detail_json(detail)
      {
        id: detail.id,
        class: detail.class.name.demodulize,
        value: value_detail_json(detail),
        nodes: detail.children.map{ |d| event_detail_json(d) },
        node_type: detail.event_subject_type.as_json,
      }
    end

    def action_json(task_action)
      return nil unless task_action
      {
        node_type: task_action.action_type.as_json,
        value: JSON.parse(task_action.value),
      }
    end

    def automated_data
      @automated_data ||= params[:task].as_json
    end
  end
end