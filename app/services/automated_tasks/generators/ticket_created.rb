module AutomatedTasks
  module Generators
    class TicketCreated < AutomatedTasks::Generators::Base
      def build_task
        order = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:order) || 0) + 1
        new_serial = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:serial_number) || 0) + 1
        task = AutomatedTasks::AutomatedTask.new(workspace: workspace, order: order, name: 'Notify Ticket Created - Email', serial_number: new_serial)
        event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is created",
                                                       module: 'help_tickets',
                                                       model: 'HelpTicket',
                                                       event_class: 'TicketAdded')
        task.task_events << AutomatedTasks::TaskEvent.new(event_type: event_type)
        subject_type = event_type.children&.find_by(name: "any ticket")
        task.task_events.first.event_details << AutomatedTasks::EventDetail.new(event_subject_type: subject_type)
        action_type = AutomatedTasks::ActionType.find_by(module: "help_tickets",
                                                         name: "send an [email]")
        task.task_actions << AutomatedTasks::TaskAction.new(action_type: action_type, value: value_as_json.to_json, email_template_id: template.id)
        task.company_mailer = company_mailer
        task.automated_task_group_id = workspace.automated_task_groups.find_by(name: "Notify Ticket Created Group").id
        task
      end

      def value_as_json
        {
          target: 'assigned,agents,ticket_creator',
          subject: subject,
          body: message,
          option: { value: 2, name: "Select Template" },
          template: template,
        }
      end

      def message
        """
<div>
  <div>
    <span style=\"font-size: 1rem;\"><b>{ticket_created_by}</b> submitted a new help desk ticket on {ticket_created_at}: <b>\"{ticket_subject}\" [\#{ticket_number}]</b></span>
    <br>
    <br>
    {ticket_button}
    </div>
  </div>
</div>
        """
      end

      def template
        workspace.email_templates.find_by(subject_title: subject, is_default: true)
      end

      def subject
        "New Ticket [\#{ticket number}]: {ticket subject}"
      end
    end
  end
end
