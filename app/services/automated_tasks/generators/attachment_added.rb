module AutomatedTasks
  module Generators
    class AttachmentAdded < AutomatedTasks::Generators::Base
      def build_task
        order = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:order) || 0) + 1
        new_serial = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:serial_number) || 0) + 1
        task = AutomatedTasks::AutomatedTask.new(workspace: workspace, order: order, name: 'Notify Attachment Added - Email', serial_number: new_serial)
        event_type = AutomatedTasks::EventType.find_by(name: "{an attachment} is added to a ticket",
                                                       module: 'help_tickets')
        task.task_events << AutomatedTasks::TaskEvent.new(event_type: event_type)
        subject_type = event_type&.children&.find_by(name: "any attachment")
        task.task_events.first.event_details << AutomatedTasks::EventDetail.new(event_subject_type: subject_type)
        action_type = AutomatedTasks::ActionType.find_by(module: "help_tickets",
                                                         name: "send an [email]")
        task.task_actions << AutomatedTasks::TaskAction.new(action_type: action_type, value: value_as_json.to_json)
        task.company_mailer = company_mailer
        task.automated_task_group_id = workspace.automated_task_groups.find_by(name: "Notify Attachment Added Group").id
        task
      end

      def value_as_json
        {
          target: 'assigned,agents,ticket_creator',
          subject: "New Attachment [\#{ticket_number}]: {attachment_filename}",
          body: message,
        }
      end

      def message
        """
          <div>
            <div>
              <span style=\"font-size: 0.875rem;\">You have a new attachment for Help Desk ticket <b>&quot;{ticket_subject}&quot; [\#{ticket_number}]</b>.</span>
              <br/>
              <br/>
              <p style=\"font-size: 0.875rem;\">{attachment_filename}</p>
              <br/>
              <br/>
              {ticket_button}
              </div>
            </div>
          </div>
        """
      end
    end
  end
end
