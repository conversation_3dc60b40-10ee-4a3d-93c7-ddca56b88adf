module AutomatedTasks
  module Generators
    class CommentAdded < AutomatedTasks::Generators::Base
      def build_task
        order = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:order) || 0) + 1
        new_serial = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:serial_number) || 0) + 1
        task = AutomatedTasks::AutomatedTask.new(workspace: workspace, order: order, name: 'Notify Comment Added - Email', serial_number: new_serial)
        event_type = AutomatedTasks::EventType.find_by(name: "{a comment} is added to a ticket",
                                                       module: 'help_tickets')
        task.task_events << AutomatedTasks::TaskEvent.new(event_type: event_type)
        subject_type = event_type&.children&.find_by(name: "any comment")
        task.task_events.first.event_details << AutomatedTasks::EventDetail.new(event_subject_type: subject_type)
        action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]",
                                                         module: "help_tickets",
                                                         model: "HelpTicketComment")
        task.task_actions << AutomatedTasks::TaskAction.new(action_type: action_type, value: value_as_json.to_json, email_template_id: template.id)
        task.company_mailer = company_mailer
        task.automated_task_group_id = workspace.automated_task_groups.find_by(name: "Notify on New Comment Group").id
        task
      end

      def value_as_json
        {
          target: 'assigned,agents,private,ticket_creator',
          subject: subject,
          body: message,
          option: { value: 2, name: "Select Template" },
          template: template,
        }
      end

      def message
        """
<div>
  <div>
    <span style=\"font-size: 0.875rem;\">A new comment has been added by <b>{commenter}</b> to ticket <b>{ticket subject} [\#{ticket_number}]</b>.</span>
    <br/>
    <br/>
    <p style=\"font-size: 0.875rem;\">{comment body}</p>
    <br/>
    <br/>
    {ticket_button}
  </div>
</div>
        """
      end

      def template
        workspace.email_templates.find_by(subject_title: subject, is_default: true)
      end

      def subject
        "Ticket [\#{ticket_number}] has new comment added"
      end
    end
  end
end
