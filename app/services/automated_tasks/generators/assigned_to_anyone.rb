module AutomatedTasks
  module Generators
    class AssignedToAnyone < AutomatedTasks::Generators::Base
      def build_task
        return nil unless custom_form
        form_field = custom_form.custom_form_fields.find_by(name: 'assigned_to')
        return nil unless form_field
        order = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:order) || 0) + 1
        new_serial = (AutomatedTasks::AutomatedTask.where(workspace: workspace).maximum(:serial_number) || 0) + 1
        task = AutomatedTasks::AutomatedTask.new(workspace: workspace, order: order, name: 'Notify Assigned-To Changes - Email', serial_number: new_serial)
        task_event = AutomatedTasks::TaskEvent.new
        event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is updated")
        task_event.event_type = event_type
        object = AutomatedTasks::EventDetail.new
        object.event_subject_type = event_type.children.find_by(name: "/a ticket/ with {a form field}")
        object.value = {
          custom_forms: [{
            id: custom_form.id,
            form_name: custom_form.form_name,
          }],
          form_field: {
            id: form_field.id,
            name: form_field.name,
            field_attribute_type: form_field.field_attribute_type,
          },
          any: true,
        }.to_json
        task_event.event_details << object
        task.task_events << task_event

        action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]",
                                                         module: "help_tickets")
        task.task_actions << AutomatedTasks::TaskAction.new(action_type: action_type, value: value_as_json.to_json, email_template_id: template.id)
        task.company_mailer = company_mailer
        task.disabled_at = company_mailer
        task.automated_task_group_id = workspace.automated_task_groups.find_by(name: "Notify Assigned-To Changes Group").id
        task
      end

      def value_as_json
        {
          target: 'related',
          subject: subject,
          body: message,
          option: { value: 2, name: "Select Template" },
          template: template,
        }
      end

      def message
        """
<div>
  <div>
    <span style=\"font-size: 0.875rem;\"><b>{ticket_assigned_to}</b> has been assigned to ticket <b>{ticket_subject} [\#{ticket_number}]</b>.</span>
    <br/>
    <br>
    <p style=\"font-size: 0.875rem;\">{ticket_description}</p>
    <br/>
    <br>
    {ticket_button}
  </div>
</div>
        """
      end

      def template
        workspace.email_templates.find_by(subject_title: subject, is_default: true)
      end

      def subject
        "New Ticket Assignment [\#{ticket number}]: {ticket subject}"
      end
    end
  end
end
