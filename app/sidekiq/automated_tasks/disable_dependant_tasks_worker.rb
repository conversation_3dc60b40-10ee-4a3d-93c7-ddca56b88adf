class AutomatedTasks::DisableDependantTasksWorker
  include Sidekiq::Job

  sidekiq_options queue: 'automated_tasks'

  def perform(workspace_ids, contributor_ids, company_users, is_archive_modal, is_bulk_delete = false, company_id = nil)
    AutomatedTasks::DependentAutomatedTasksService.new(workspace_ids, contributor_ids, company_users, is_archive_modal, true, is_bulk_delete, false, company_id).call
  end
end
