class ExpiredCompaniesSlackAlertWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'low_intensity_schedule'

  HEADERS = [
    'Name', 'Subdomain', 'Email', 'CreatedAt', 'ManagedAssets',
    'Contracts', 'Vendors', 'TelecomServices', 'TelecomProviders', 'HelpTickets'
  ].freeze

  def perform
    if Rails.env.production?
      process_production
    elsif Rails.env.staging?
      process_staging
    end
  end

  private

  def expired_companies
    Company.joins(:subscriptions)
           .group("companies.id")
           .having(
             "COUNT(subscriptions.id) = SUM(
                CASE 
                  WHEN subscriptions.status = 2 AND subscriptions.updated_at <= :six_months_ago THEN 1
                  WHEN subscriptions.status = 1 AND subscriptions.updated_at <= :six_months_ago 
                    AND subscriptions.end_date IS NOT NULL 
                    AND subscriptions.end_date <= :today 
                  THEN 1 
                  ELSE 0 
                END
              )",
             six_months_ago: 6.months.ago,
             today: Date.today
           )
  end

  def process_production
    companies = expired_companies

    return if companies.empty?

    generate_and_send_report(companies, '#support', 'Production')
  end

  def process_staging
    low_object_companies = Company.find_each.select do |company|
     total_count = company.managed_assets.count +
                   company.contracts.count +
                   company.vendors.count +
                   company.telecom_services.count +
                   company.telecom_providers.count +
                   company.help_tickets.count
      total_count < 10 && !company.has_current_free_trial?
    end
    companies = (expired_companies + low_object_companies).uniq
    return if companies.empty?

    generate_and_send_report(companies, '#dev-support', 'Staging')
  end

  def generate_and_send_report(companies, channel, environment)
    report = generate_excel_file(companies, environment)
    s3_url = upload_to_s3(report, environment)
    if s3_url
      send_slack_alert(s3_url, companies.size, channel)
    end
  end

  def generate_excel_file(companies, environment)
    workbook = RubyXL::Workbook.new
    workbook.worksheets.shift
    worksheet = workbook.add_worksheet(environment)
    
    HEADERS.each_with_index do |header, index|
      cell = worksheet.add_cell(0, index, header)
      cell.change_font_bold(true)
      worksheet.change_column_width(index, 25)
    end

    companies.each_with_index do |company, row_index|
      row_data = [
        company.name, company.subdomain, company.email, company.created_at.strftime('%Y-%m-%d'),
        company.managed_assets.count, company.contracts.count, company.vendors.count,
        company.telecom_services.count, company.telecom_providers.count, company.help_tickets.count
      ]
    
      row_data.each_with_index do |value, col_index|
        worksheet.add_cell(row_index + 1, col_index, value)
      end
    end
    
    tempfile = Tempfile.new(["expired_companies", ".xlsx"])
    workbook.write(tempfile.path)
    tempfile.rewind
    tempfile.read
  ensure
    if tempfile
      tempfile.close
      tempfile.unlink
    end
  end

  def upload_to_s3(report, environment)
    s3 = Aws::S3::Resource.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )
    
    filename = "expired_companies_#{environment}_#{Time.now.to_i}.xlsx"
    obj = s3.bucket(Rails.application.credentials.aws[:s3][:bucket]).object(filename)
    
    begin
      obj.put(body: report, acl: 'public-read')
      obj.public_url
    rescue StandardError => e
      Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    end
  end
  

  def send_slack_alert(url, count, channel)
    client = Slack::Web::Client.new(token: Rails.application.credentials.slack[:api_token])
    client.auth_test
    formatted_url = "<#{url}|Download Excel Report>"

    begin
      if channel == '#support'
        message = ":warning: *Production Alert*: Found #{count} companies expired for 6+ months. "\
                  "Report ready: #{formatted_url}"
      else
        message = ":hammer_and_wrench: *Staging Alert*: #{count} companies expired for 6+ months or with low records. "\
                  "Report available: #{formatted_url}"
      end

      client.chat_postMessage(
        channel: channel,
        text: message,
        unfurl_links: true,
        unfurl_media: true,
        mrkdwn: true
      )
    rescue Slack::Web::Api::Errors::SlackError => e
      Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    end
  end
end
