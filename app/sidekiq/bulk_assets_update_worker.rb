class BulkAssetsUpdateWorker
  include Sidekiq::Job
  sidekiq_options queue: 'bulk_operations'

  def perform(company_id, status_filter, params = {}, service_type = nil, select_all_assets = [])
    @status_filter = status_filter
    company = Company.find_by_cache(id: company_id)
    return unless company.present?
    if @status_filter["current_tag"].present?
      assets = company.managed_assets.joins(:managed_asset_tags).where(where_clause, managed_asset_tags: {tag: @status_filter["current_tag"]}).distinct
    else
      assets = company.managed_assets.where(where_clause)
    end

    if @status_filter["search"].present?
      search_params = { search: @status_filter["search"] }
      assets = ManagedAssets::AssetsQuery::SearchClause.new(search_params, nil, nil, [company_id], nil, nil ).call(assets)
    end

    if service_type == "tag"
      BulkAssetsUpdate.new(company_id, assets).update_tags(params["tags"], params["remove_tags"], params["cu_id"], params["req_ip"], params["req_uuid"])
    elsif service_type == "location"
      BulkAssetsUpdate.new(company_id, assets).add_locations(params["location_id"])
    elsif service_type == "users"
      used_by_contributor_id = params["used_by_contributor_id"]
      managed_by_contributor_id = params["managed_by_contributor_id"]
      BulkAssetsUpdate.new(company_id, assets).assign_users(used_by_contributor_id, managed_by_contributor_id)
    elsif service_type == "bulk_update"
      used_by_contributor_id = params["used_by_contributor_id"]
      managed_by_contributor_id = params["managed_by_contributor_id"]
      bulk_update_assets = select_all_assets.empty? ? assets : select_all_assets.map(&:symbolize_keys)
      BulkAssetsUpdate.new(company_id, bulk_update_assets).bulk_update(used_by_contributor_id, managed_by_contributor_id)
    elsif service_type == "archive"
      assets_ids = assets.pluck(:id)
      BulkAssetsUpdate.new(company_id).bulk_archive(assets_ids, company, params["cu_id"], params["req_ip"], params["req_uuid"])
    elsif service_type == "unarchive"
      assets_ids = assets.pluck(:id)
      BulkAssetsUpdate.new(company_id).bulk_unarchive(assets_ids, company, params["cu_id"], params["req_ip"], params["req_uuid"])
    elsif service_type == "bulk_delete"
      assets_ids = assets.pluck(:id)
      BulkAssetsUpdate.new(company_id).bulk_delete(assets_ids, company)
    end
  end

  def where_clause
    query = {}

    query["archived"] = @status_filter["asset_status"] == ["archived"] if @status_filter["asset_status"].present?
    query["company_asset_type_id"] = @status_filter["asset_type"] if @status_filter["asset_type"].present?
    query["source"] = @status_filter["asset_source"] if @status_filter["asset_source"].present?
    query["location_id"] = @status_filter["current_location"] if @status_filter["current_location"].present?
    query["status"] = @status_filter["asset_available_status"] if @status_filter["asset_available_status"].present?
    query["warranty_expiration"] = get_warranty_date if @status_filter["warranty_status"].present?
    query
  end

  def get_warranty_date
    warranty_query = {
      "expired_warranty": ...Date.today,
      "expiring_warranty": Date.today..(Date.today + 3.months),
      "in_warranty": Date.today...,
      "no_warranty": nil  
    }
    warranty_query[@status_filter["warranty_status"].to_sym]
  end
end