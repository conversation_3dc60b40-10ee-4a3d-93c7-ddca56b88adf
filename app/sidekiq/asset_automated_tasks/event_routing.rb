require 'malloc_trim'

module AssetAutomatedTasks
  class EventRouting
    include Sidekiq::Job
    sidekiq_options queue: 'automated_tasks'

    # We want to make sure we have the automated tasks prioritized
    # since AT handle help ticket notifications
    attr_accessor :id, :class_name

    def perform(id, class_name, force = false)
      self.id = id
      self.class_name = class_name
      return if GlobalEmailBlocking.last&.company_ids&.include?(object.company.id)

      raise 'Missing entity' unless object

      execution_log.entity_attributes = json_body
      execution_log.company = object.company

      previous_log = Assets::ExecutionLog
                      .where("entity_name = ? AND entity_attributes = ? AND created_at > ? AND completed_at IS NOT NULL", class_name, json_body, 1.minute.ago)
                      .order(id: :desc).limit(1).first
      raise "Circular execution detected. Created at (#{previous_log.created_at})" if previous_log.present? && !force

      Assets::TaskExecutor.new(object).call
      execution_log.message = "success"
      execution_log.completed_at = Time.now
    rescue => e
      execution_log.message = e.message
      raise e if Rails.env.development?
    ensure
      execution_log.save
      exec_lock.destroy if exec_lock&.id

      GC.start
      # Call malloc_trim to release unused memory
      MallocTrim.trim
    end

    def json_body
      @json_body ||= serialize(class_name).serialize(object)
    end

    def object
      @object ||= class_name.constantize.find_by(id: id)
    end

    def execution_log
      @execution_log ||= Assets::ExecutionLog.new(entity_id: id, entity_name: class_name)
    end

    def serialize(class_name)
      "AutomatedTasks::Serializers::#{class_name}".constantize
    end
  end
end
