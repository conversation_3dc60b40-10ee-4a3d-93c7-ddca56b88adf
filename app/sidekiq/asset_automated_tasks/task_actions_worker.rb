module AssetAutomatedTasks
  class TaskActionsWorker
    include Sidekiq::Job
    sidekiq_options queue: 'at_actions'
    
    def perform(action_id, object_id, object_class_name, type_name, event_data)
      @action = Assets::TaskAction.find_by(id: action_id)
      @object = object_class_name.constantize.find_by(id: object_id)
      @type_name = type_name
      @event_data = event_data
      action&.call if @object.present? && @action.present?
    end

    def action
      "AssetAutomatedTasks::Actions::#{@type_name}".constantize.new(@action, @object, @event_data)
    end
  end
end
