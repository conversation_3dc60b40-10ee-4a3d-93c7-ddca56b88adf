class Integrations::Aws::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  attr_accessor :aws_config, :retries, :first_time_flag

  def perform aws_config_id, first_time_flag, retries = 0
    @starting_time = Time.current
    set_read_replica_db do
      @aws_config = Integrations::Aws::Config.find(aws_config_id)
      @company_integration = @aws_config.company_integration
      @company = @company_integration.company
    end
    @client = Integrations::Aws::FetchData.new(@aws_config)
    @first_time_flag = first_time_flag
    @retries = retries

    if first_time_flag
      start_date = (Date.today - 6.month).beginning_of_month.strftime('%F')
    else
      start_date = (Date.today - 1.month).beginning_of_month.strftime('%F')
    end
    pusher(@aws_config, true, 0.2)
    save_services start_date
    pusher(@aws_config, true, 0.6)

    save_projected_services_cost if @client.ok?
    pusher(@aws_config, true, 0.8)
    update_company_integration

    pusher(@aws_config, true, 1) if first_time_flag
    intg_duration_analysis(@company.id, first_time_flag)
  rescue Exception => e
    company_integration_failed

    Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?)
    pusher(@aws_config, false, 0) if first_time_flag
  end

  def user_error_message(message)
    if message.to_s.include?("not authorized to perform:")
      return "Unauthorized to perform actions. Please follow steps provided in Help Center."
    end
  end

  private

  def save_projected_services_cost
    start_date = (Date.today + 1.day).strftime('%F')
    end_date = (Date.today.end_of_month + 1.day).strftime('%F')
    end_date = (end_date.to_date().end_of_month).strftime('%F') if start_date == end_date
    response = @client.get_projections start_date, end_date, "SERVICE"
    return unless @client.ok?
    save_projections response
  end

  def save_projections projections
    vendor = get_vendor
    projections.forecast_results_by_time.each do |projection|
      start_date = projection.time_period.start
      value = projection.mean_value

      predicted_cloud_transaction = PredictedCloudTransaction.find_or_initialize_by(
              name: "Forecasted Cost",
              date: start_date,
              company_id: @company.id,
              company_integration_id: @company_integration.id)

      predicted_cloud_transaction.assign_attributes(vendor_id: vendor.id,
                                                    amount: value.to_f,
                                                    product_id: get_product_id(predicted_cloud_transaction, vendor))
      predicted_cloud_transaction.save!
    end
  end

  def save_services start_date, next_page_token = nil
    end_date = (Date.today).strftime('%F')
    response = @client.get_transactions start_date, end_date, "SERVICE", next_page_token
    return unless @client.ok?
    pusher(@aws_config, true, 0.4)
    sync_services response

    if response.next_page_token.present?
      save_services(start_date, response.next_page_token)
    end
  end

  def sync_services service_transactions
    service_transactions.results_by_time.each do |result|
      start_date = result.time_period.start
      end_date = result.time_period.end
      save_transactions result.groups, start_date
    end
  end

  def save_transactions transactions, date
    vendor = get_vendor
    company_cloud_usage_transactions = @company.cloud_usage_transactions
    company_integration_id = @company_integration.id
    transactions.each do |transaction|
      next if transaction.metrics["UnblendedCost"].amount.to_f == 0.0

      cloud_usage_transaction = {}
      set_read_replica_db do
        cloud_usage_transaction = company_cloud_usage_transactions.find_or_initialize_by(
          transaction_date: date,
          name: transaction.keys.join,
          company_integration_id: company_integration_id,
          is_manual: false)
      end

      cloud_usage_transaction.assign_attributes(vendor_id: vendor.id,
                                                amount: transaction.metrics["UnblendedCost"].amount.to_f,
                                                product_id: get_product_id(cloud_usage_transaction, vendor))

      cloud_usage_transaction.save!
    end
  end

  def get_vendor
    vendor = nil
    default_vendor = nil
    category = nil
    set_read_replica_db do
      vendor = @company.vendors.find_by_name("Amazon Web Services")
      default_vendor = DefaultVendor.find_by_name("Amazon Web Services")
      category = @company.categories.find_by(name: default_vendor.default_category.name)
    end
    if vendor.present?
      vendor.update!(default_tags: [default_vendor.tag],
                     category_id: category&.id,
                     logo_url: default_vendor.logo_url,
                     is_cloud_platform: default_vendor.is_cloud_platform,
                     friendly_name: default_vendor.friendly_name)
      vendor
    else
      @company.vendors.create!(name: default_vendor.name,
                               default_tags: [default_vendor.tag],
                               category_id: category&.id,
                               logo_url: default_vendor.logo_url,
                               is_cloud_platform: default_vendor.is_cloud_platform,
                               friendly_name: default_vendor.friendly_name)
    end
  end

  def get_product_id(transaction, vendor)
    product = set_read_replica_db { vendor.products.find_by_name("Amazon Web Services") }
    if product.nil?
      default_product = set_read_replica_db { DefaultProduct.find_by_name("Amazon Web Services") }
      product = Product.create!(name: default_product.name,
                                default_product_id: default_product.id,
                                url: default_product.url,
                                vendor_id: vendor.id,
                                company_id: @company.id)
    end
    product.id
  end

  def update_company_integration
    if is_sync_successfull
      @company_integration.update!(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        failure_count: 0
      )
    else
      company_integration_failed
      error_messages = [
        "The security token included in the request is invalid.",
        "The request signature we calculated does not match the signature you provided. Check your AWS Secret Access Key and signing method. Consult the service documentation for details.",
        "not authorized to perform:"
      ]

      send_email_notification(error_messages)
      raise_bugsnag_issue(error_messages)
      pusher(@aws_config, false, 0) if first_time_flag
    end
  end

  def company_integration_failed
    @company_integration.update(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: @client.error,
      user_error_message: user_error_message(@client.error),
      failure_count: @company_integration.failure_count + 1
    )
  end

  def send_email_notification(error_messages)
    return if @first_time_flag
    return unless error_messages.find{ |em| @client.error.include?(em) }

    send_notification(@company_integration)
  end

  def raise_bugsnag_issue(error_messages)
    return if error_messages.find{ |em| @client.error.include?(em) }
    return unless Rails.env.production? || Rails.env.staging?

    Bugsnag.notify(@client.error)
  end

  def is_sync_successfull
    @client.ok? || @client.error.downcase.include?("insufficient amount of historical data")
  end
end
