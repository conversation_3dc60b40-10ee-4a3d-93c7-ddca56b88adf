class Integrations::GsuiteAd::FetchGroupsAndOrg
  include Sidekiq::Job
  include ReadReplicaDb

  attr_accessor :gsuite

  sidekiq_options queue: 'critical'

  def perform gsuite_config_id
    @gsuite = set_read_replica_db do 
      Integrations::GsuiteAd::Config.find(gsuite_config_id)
    end
    @groups = []
    @org_units = []
    data = { config_id: gsuite_config_id }
    @company_guid = set_read_replica_db do 
      @gsuite.company.guid
    end

    refresh_access_token

    @org_units = get_org_units
    org_units_data = { org_units: @org_units }
    @gsuite.update_column('data', org_units_data)
    Pusher.trigger(@company_guid, "gsuite_ad-config", data)

    @groups = get_groups
    delete_all_unwanted_groups
    groups_data = { groups: @groups }
    data = data.merge({ groups_synced: true })
    @gsuite.update_column('data', @gsuite.data.merge(groups_data))
    Pusher.trigger(@company_guid, "gsuite_ad-config", data)

    gsuite_ad_data = {
      selected_groups: @gsuite.group_ids,
      selected_orgs: @gsuite.organization_paths,
      sync_all_users: @gsuite.sync_all_users,
    }
    @gsuite.update_column('data', @gsuite.data.merge(gsuite_ad_data))
    Pusher.trigger(@company_guid, "gsuite_ad-config", data)
  rescue StandardError => e
    error_message = ''
    if e.message.start_with?('forbidden') && e.status_code == 403
      error_message = 'Please ensure you have access to Google Workspace.'
    elsif e.message.include?('Request had insufficient authentication scopes.')
      error_message = 'Please authorize your Google Workspace with the necessary scopes.'
    end
    @gsuite.company_integration.assign_attributes(sync_status: :failed,
                                                  status: false,
                                                  last_synced_at: DateTime.now,
                                                  active: true,
                                                  error_message: e.message)

    @gsuite.company_integration.save!
    Pusher.trigger(@company_guid, "gsuite_ad-config", { value: 0, error_message: error_message })
  end

  def delete_all_unwanted_groups
    latest_groups_ids = @groups.pluck(:id)
    deleted_groups_ids = @gsuite.group_ids - latest_groups_ids
    @gsuite.groups.where(external_id: deleted_groups_ids).destroy_all if deleted_groups_ids.present?
  end

  def get_groups
    response = client.get_groups
    groups_array = response&.groups

    return [] unless groups_array
    groups_array.map do |group|
      {
       name: group&.name,
       id: group&.id,
       email: group&.email,
       direct_members_count: group&.direct_members_count,
       description: group&.description
      }
    end 
  end

  def get_org_units(org_unit_path=nil, visited_paths=[])
    return [] if visited_paths.include?(org_unit_path)
    response = client.get_org_units org_unit_path
    org_units_array = response&.organization_units

    return [] unless org_units_array
    org_units_array.map do |org_unit|
      org_dup = {
        name: org_unit&.name,
        id: org_unit&.org_unit_id,
        description: org_unit&.description,
        org_unit_path: org_unit&.org_unit_path
      }
      childern_org_units = get_org_units(org_unit&.org_unit_path, visited_paths + [org_unit_path])
      org_dup["childern_org_units"] = childern_org_units
      org_dup
    end
  end

  def refresh_access_token
    current_company = set_read_replica_db do 
      @gsuite.company
    end
    gsuite_service = Integrations::GsuiteAd::FetchData.new(current_company.id, "read")
    refresh_token_detail = gsuite_service.refresh_token
    if refresh_token_detail["access_token"]

      expiry_time = (Time.now + refresh_token_detail["expires_in"]).to_i
      @gsuite.update_columns(
        token: refresh_token_detail["access_token"],
        expiration_time: expiry_time)

      file_path = Rails.root.join("tmp/#{current_company.id}_token.yaml")
      File.delete(file_path) if File.exist?(file_path)
      @gsuite.scope = @gsuite.scope.gsub(' ', '')
      refresh_token = @gsuite.refresh_token.nil? ? "null" : "#{@gsuite.refresh_token}"

      content = "---\n '#{@gsuite.company_id.to_s}': "
      content << "'{\"client_id\":\"#{@gsuite.client_id.to_s}"
      content << "\",\"access_token\":\"#{@gsuite.token}\","
      content << "\"refresh_token\":\"#{refresh_token}\","
      content << "\"scope\":#{@gsuite.scope},"
      content << "\"expiration_time_millis\":#{@gsuite.expiration_time}}'"

      File.open(file_path, "w+") do |f|
       f.write(content)
      end
      @gsuite.reload
    end
  end

  private
  def client
    return if @gsuite.nil?
    
    company_integration_id = set_read_replica_db do 
      @gsuite.company_integration.company.id
    end
    @client ||= Integrations::GsuiteAd::FetchData.new(company_integration_id, "read")
  end
end
