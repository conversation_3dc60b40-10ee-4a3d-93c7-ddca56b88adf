class Integrations::GsuiteAd::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include SyncCompanyUserFields
  include ReadReplicaDb
  include GsuiteAdCommonMethods

  sidekiq_options queue: 'long_integrations'

  attr_accessor :gsuite, :source_id

  def perform gsuite_config_id, first_time_flag, groups=[], orgs=[], syncAllUsers=false, excluded_attributes=[], is_resync=false
    @starting_time = Time.current
    set_read_replica_db do
      @gsuite = Integrations::GsuiteAd::Config.find(gsuite_config_id)
      @source_id = Integration.find_by(name: 'gsuite_ad').id
      @company_integration = @gsuite.company_integration
    end
    @first_time_flag = first_time_flag
    @groups = groups
    @orgs = orgs
    @syncAllUsers = syncAllUsers
    @excluded_attributes = excluded_attributes
    @gsuite.update(excluded_attributes: @excluded_attributes)
    @is_resync = is_resync
    refresh_access_token
    pusher(@gsuite, true, 0.2)
    primary_details = save_primary_details gsuite_config_id
    intg_duration_analysis(@gsuite.company_id, first_time_flag)
  end

  def save_primary_details gsuite_config_id
    pusher(@gsuite, true, 0.2)
    fetch_groups_and_org unless @first_time_flag
    @group_members_ids = []
    @group_members_ids = fetchGroupsMembers if @groups.present? && !@syncAllUsers

    params = {
      "gsuite_config_id" => gsuite_config_id,
      "first_time_flag" => @first_time_flag,
      "starting_time" => @starting_time.to_json,
      "is_resync" => @is_resync,
      "sync_all_users" => @syncAllUsers,
      "excluded_attributes" => @excluded_attributes,
      "group_members_ids" => @group_members_ids,
      "orgs" => @orgs,
      "next_page_token" => nil
    }

    Integrations::GsuiteAd::SaveUsersWorker.perform_async(params)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "SSL_connect",
      "Unauthorized",
      "forbidden: Caller does not have access to the customers reporting data.",
      "Authorization failed",
      "invalid_grant",
      "Request had insufficient authentication scopes.",
      "forbidden: Not Authorized to access this resource/api",
      "Invalid Input"
    ]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    client.api_logs.each do |log_param|
      if Rails.env.test?
        Logs::ApiEvent.create(log_param)
      else
        LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
      end
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@gsuite, false, 0)
  end

  def fetch_groups_and_org
    Integrations::GsuiteAd::FetchGroupsAndOrg.new.perform(@gsuite.id)
    @groups = @gsuite.group_ids
  end

  def fetchGroupsMembers
    group_members_ids = []

    @groups.each do |group|
      res = client.get_group_members(group)
      group_members_ids = group_members_ids + res.members.map {|member| member.id} if res.members.present?
    end
    group_members_ids.uniq
  end

  private

  def client
    return if @gsuite.nil?
    
    company_integration_id = set_read_replica_db do 
      @gsuite.company_integration.company.id
    end
    @client ||= Integrations::GsuiteAd::FetchData.new(company_integration_id, "read")
  end
end
