class Integrations::GsuiteAd::SaveUsersWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include SyncCompanyUserFields
  include ReadReplicaDb
  include NotifyAdminsHelper
  include GsuiteAdCommonMethods

  sidekiq_options queue: 'long_integrations'

  def perform(params)
    @starting_time = JSON.parse(params["starting_time"]).to_time
    set_read_replica_db do
      @gsuite = Integrations::GsuiteAd::Config.find(params["gsuite_config_id"])
      @company_integration = @gsuite.company_integration
    end
    @current_company_id = @gsuite.company_id
  
    @sync_all_users = params["sync_all_users"]
    @first_time_flag = params["first_time_flag"]
    @group_members_ids = params["group_members_ids"]
    @orgs = params["orgs"]
    @excluded_attributes = params["excluded_attributes"]
    @is_resync = params["is_resync"]
    @disabled_users = []
    refresh_access_token
    response = client.get_users(params["next_page_token"])
    @building_response = fetch_buildings(client) if @excluded_attributes.exclude?('location')
    users_array = response.users
    save_users(users_array)

    if response.next_page_token.present?
      Integrations::GsuiteAd::SaveUsersWorker.perform_async({
        "gsuite_config_id" => params["gsuite_config_id"],
        "first_time_flag" => @first_time_flag,
        "starting_time" => @starting_time.to_json,
        "is_resync" => @is_resync,
        "sync_all_users" => params["sync_all_users"],
        "excluded_attributes" => params["excluded_attributes"],
        "group_members_ids" => params["group_members_ids"],
        "orgs" => params["orgs"],
        "next_page_token" => response.next_page_token
      })
    else
      pusher(@gsuite, true, 0.6)

      @company_integration.assign_attributes(sync_status: :successful,
                                             status: true,
                                             last_synced_at: DateTime.now,
                                             active: true,
                                             error_message: nil,
                                             notified: nil)
      @company_integration.save!
      pusher(@gsuite, true, 1)
      path = Rails.root.join("tmp/#{@gsuite.company.id}_token.yaml")
      File.delete(path) if File.exist?(path)
      send_email_to_admins(@current_company_id, 'google', @disabled_users) if @disabled_users.present?
      intg_duration_analysis(@gsuite.company_id, @first_time_flag)
    end
  rescue Exception => e
    error_messages = [
      'forbidden: Not Authorized to access this resource/api',
      'authorization failed',
      'unauthorized'
    ]

    @retries = defined?(@retries) ? @retries + 1 : 1
    if error_messages.any? { |msg| e.message.downcase.include?(msg) } && @retries <= 3
      sleep(5)
      retry
    end

    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!
    client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em) }
    end
    pusher(@gsuite, false, 0)
  end

  private

  def fetch_buildings(client, next_page_token = nil)
    @building_response ||= client.get_buildings(next_page_token)
    
    is_next_page = @building_response.next_page_token

    while is_next_page.present?
      next_response = client.get_buildings(is_next_page)
      @building_response.buildings.concat(next_response.buildings) if next_response.buildings.present?
      is_next_page = next_response.next_page_token
    end

    @building_response
  end

  def save_users(users)
    users&.each do |gsuite_user|
      next if gsuite_user.primary_email.blank?
      discovered_user = set_read_replica_db do
        DiscoveredUser.where(company_id: @gsuite.company_id).where('lower(email) = ?', gsuite_user.primary_email.downcase).first
      end
      next if discovered_user.blank? && (gsuite_user.archived || gsuite_user.suspended)

      if check_if_can_add_user gsuite_user
        if discovered_user.present?
          user_data_mappings(gsuite_user, discovered_user).each do |attribute, new_value|
            old_value = discovered_user.send(attribute)
            if old_value != new_value
              DiscoveredUserHistory.create!(
                discovered_user: discovered_user,
                company_id: @current_company_id,
                values: {
                  value_type: attribute,
                  new_value: new_value,
                  old_value: old_value,
                  first_name: discovered_user&.first_name,
                  last_name: discovered_user&.last_name,
                  email: discovered_user&.email
                }
              )
            end
          end
        else
          discovered_user = DiscoveredUser.new(company_id: @gsuite.company_id, email: gsuite_user.primary_email.downcase)
        end
        
        discovered_user.assign_attributes(user_data_mappings(gsuite_user, discovered_user))
        staff_user = set_read_replica_db do 
          discovered_user.company.users.find_by_cache(email: gsuite_user.primary_email.downcase)
        end
        if staff_user.present?
          if is_access_revoked?(gsuite_user)
            company_user = set_read_replica_db do 
              CompanyUser.find_by_cache(user_id: staff_user.id, company_id: @gsuite.company_id)
            end
            if (company_user&.granted_access_at.present?)
              company_user.update(granted_access_at: nil)
              disabled_user = staff_user
              @disabled_users << { id: company_user.id, full_name: disabled_user.full_name, email: disabled_user.email }
            end
          end
          is_location_update = @excluded_attributes.exclude?('location')
          update_fields(discovered_user, staff_user, @gsuite.company_id, is_location_update)
          discovered_user.status = :imported
        else
          discovered_user.status = :ignored if is_access_revoked?(gsuite_user)
        end

        discovered_user.save!

        DiscoveredUserSource.find_or_create_by!(discovered_user_id: discovered_user.id, source: :gsuite_active_directory)
      elsif discovered_user.present? && discovered_user.status != "imported"
        discovered_user.destroy
      end
    end
  end

  def check_if_can_add_user gsuite_user
    @sync_all_users || @group_members_ids.include?(gsuite_user.id) || @orgs.include?(gsuite_user.org_unit_path)
  end

  def client
    return if @gsuite.nil?
    
    company_integration_id = set_read_replica_db do
      @gsuite.company_integration.company.id
    end
    @client ||= Integrations::GsuiteAd::FetchData.new(company_integration_id, "read")
  end

  def gsuite_user_location_id(user)
    return unless user.locations&.first&.dig("buildingId").present?

    matched_building = @building_response.buildings.find { |building| building.building_id == user.locations.first["buildingId"] }
    if matched_building.present?
      address = matched_building&.building_name
      gsuite_user_location = Integrations::Location.find_or_create_by!(address: address, company_id: @gsuite.company_id) do |location|
        location.source = set_read_replica_db do
          @gsuite.company_integration.integration.name
        end
      end
      gsuite_user_location.id
    end
  end

  def is_access_revoked?(gsuite_user)
    gsuite_user.archived || gsuite_user.suspended
  end

  def user_data_mappings(gsuite_user, discovered_user)
    {
      first_name: gsuite_user.name&.given_name || discovered_user.first_name,
      last_name: gsuite_user.name&.family_name || discovered_user.last_name,
      department: @excluded_attributes.include?('department') ? nil : (gsuite_user.organizations&.first&.dig("department") || discovered_user.department),
      location_id: @excluded_attributes.include?('location') ? nil : (gsuite_user_location_id(gsuite_user) || discovered_user.location_id),
      title: @excluded_attributes.include?('title') ? nil : (gsuite_user.organizations&.first&.dig("title") || discovered_user.title),
      work_phone: @excluded_attributes.include?('work_phone') ? nil : (gsuite_user.phones&.find { |phone| phone["type"] == "work" }&.dig("value") || discovered_user.work_phone),
      supervisor: @excluded_attributes.include?('supervisor') ? nil : (gsuite_user.relations&.find { |rel| rel["type"] == "manager" }&.dig("value") || discovered_user.supervisor),
      mobile_phone: @excluded_attributes.include?('mobile_phone') ? nil : (gsuite_user.phones&.find { |phone| phone["type"] == "mobile" }&.dig("value") || discovered_user.mobile_phone),
      principal_name: @excluded_attributes.include?('principal_name') ? nil : (gsuite_user.emails&.find { |email| email.keys == ["address"] }&.dig("address") || discovered_user.principal_name)
    }
  end
end
