class Integrations::Mosyle::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs
  include MosyleCommonMethods

  attr_accessor :config, :first_time_flag, :page

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    self.config = set_read_replica_db do
      Integrations::Mosyle::Config.find_by(id: config_id)
    end
    return if self.config.nil?

    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    self.page = 1
    pusher(config, true, 0.1)
    refresh_access_token
    pusher(config, true, 0.2)
    save_devices('mac')
    pusher(config, true, 0.4)
    Integrations::Mosyle::SaveIosDevicesWorker.perform_async(config.id, first_time_flag, @asset_count, @is_resyncing, @company_user_id)
  rescue Exception => e
    send_notification_with_updating_status(e)
    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em) }
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'mosyle_integration').to_json)
    pusher(config, false, 0)
  end

  def error_messages
    [
     'accessToken Required',
     'Invalid accessToken',
     'API Token or Bearer Token are incorrect'
    ]
  end

  def save_devices(type)
    devices = client.get_devices(self.page, type)
    if !devices['response'].present? || !devices['response'][0]['devices'].present?
      return
    end

    devices['response'][0]['devices'].each do |device|
      retries = 0
      begin
        @mac_addresses = []
        @device_info = device
        display_name = @device_info['device_name']
        ip_address = @device_info['last_ip_beat']
        serial_number = @device_info['serial_number']
        manufacturer = "Apple"
        @mac_addresses << @device_info['wifi_mac_address'] rescue nil
        @mac_addresses << @device_info['bluetooth_mac_address'] rescue nil
        @discovered_asset = find_discovered_asset(company, false, serial_number, @mac_addresses, display_name, ip_address, manufacturer, false)
        @discovered_asset ||= DiscoveredAsset.mosyle.ready_for_import.new(company_id: company.id)
        assign_discovered_asset_attributes(device)
        create_asset_software
        managed_asset = find_managed_asset(company, serial_number, @mac_addresses, display_name, manufacturer)
        if managed_asset.present?
          @discovered_asset.status = :imported
          @discovered_asset.managed_asset_id = managed_asset.id
        end

        assign_discovered_assets_hardware_details(device)
        is_new = @discovered_asset.new_record?
        @discovered_asset.save!
        @asset_count += 1 if is_new

        create_asset_source(device)
      rescue PG::ConnectionBad => e
        retries += 1
        if retries <= 3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, @discovered_asset)
          break
        end
      rescue StandardError => e
        log_exception(e, @discovered_asset)
      end
    end
    rows = devices['response'][0]['rows']
    page_size = devices['response'][0]['page_size']
    page = devices['response'][0]['page']
    next_page_call(type, rows, page_size, page)
  end

  def next_page_call(type, rows, page_size, page)
    total_pages = (rows / page_size.to_f).ceil

    if page < total_pages
      self.page += 1
      save_devices(type)
    end
  end

  def client
    @mosyle_service ||= Integrations::Mosyle::FetchData.new(company.id, config)
  end

  def send_notification_with_updating_status(error)
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      error_message: error.message,
      failure_count: company_integration.failure_count + 1
    )
    company_integration.save
    send_notification(company_integration) unless @first_time_flag
  end
end
