class Integrations::Mosyle::SaveIosDevicesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs
  include MosyleCommonMethods

  attr_accessor :config, :first_time_flag, :page

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, asset_count, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = asset_count
    @starting_time = Time.current
    self.config = set_read_replica_db do
      Integrations::Mosyle::Config.find_by(id: config_id)
    end
    return if self.config.nil?
    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    self.page = 1
    pusher(config, true, 0.6)
    refresh_access_token
    pusher(config, true, 0.7)
    save_devices('ios')
    pusher(config, true, 0.9)
    update_company_integration
    pusher(config, true, 1)
    create_logs
    intg_duration_analysis(company.id, first_time_flag)
  rescue Exception => e
    create_logs
    send_notification_with_updating_status(e)
    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em) }
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'mosyle_integration').to_json)
    pusher(config, false, 0)
  end

  def error_messages
    [
     'accessToken Required',
     'Invalid accessToken',
     'API Token or Bearer Token are incorrect'
    ]
  end

  def refresh_access_token
    client.refresh_access_token
  end

  def save_devices(type)
    devices = client.get_devices(self.page, type)
    if !devices['response'].present? || !devices['response'][0]['devices'].present?
      return
    end

    devices['response'][0]['devices'].each do |device|
      retries = 0
      begin
        @mac_addresses = []
        @device_info = device
        display_name = @device_info['device_name']
        ip_address = @device_info['last_ip_beat']
        serial_number = @device_info['serial_number']
        manufacturer = "Apple"
        @mac_addresses << @device_info['wifi_mac_address'] rescue nil
        @mac_addresses << @device_info['bluetooth_mac_address'] rescue nil
        @discovered_asset = find_discovered_asset(company, false, serial_number, @mac_addresses, display_name, ip_address, manufacturer, false)
        @discovered_asset ||= DiscoveredAsset.mosyle.ready_for_import.new(company_id: company.id)
        assign_discovered_asset_attributes(device)
        create_asset_software
        managed_asset = find_managed_asset(company, serial_number, @mac_addresses, display_name, manufacturer)
        if managed_asset.present?
          @discovered_asset.status = :imported
          @discovered_asset.managed_asset_id = managed_asset.id
        end

        assign_discovered_assets_hardware_details(device)
        is_new = @discovered_asset.new_record?
        @discovered_asset.save!
        @asset_count += 1 if is_new

        create_asset_source(device)
      rescue PG::ConnectionBad => e
        retries += 1
        if retries <= 3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, @discovered_asset)
          break
        end
      rescue StandardError => e
        log_exception(e, @discovered_asset)
      end
    end
    rows = devices['response'][0]['rows']
    page_size = devices['response'][0]['page_size']
    page = devices['response'][0]['page']
    next_page_call(type, rows, page_size, page)
  end

  def next_page_call(type, rows, page_size, page)
    total_pages = (rows / page_size.to_f).ceil

    if page < total_pages
      self.page += 1
      save_devices(type)
    else
      self.page = 1
    end
  end

  def client
    @mosyle_service ||= Integrations::Mosyle::FetchData.new(company.id, config)
  end

  def update_company_integration
    if client.ok?
      company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: 0
      )
    else
      company_integration.assign_attributes(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        active: true,
        error_message: client.error,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: company_integration.failure_count + 1
      )
    end
    company_integration.save
  end

  def send_notification_with_updating_status(error)
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      error_message: error.message
    )
    company_integration.save
    send_notification(company_integration) unless @first_time_flag
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Mosyle', @company_user_id, company.id)
    end
  end
end
