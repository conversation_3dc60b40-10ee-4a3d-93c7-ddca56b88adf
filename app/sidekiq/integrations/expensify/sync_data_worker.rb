class Integrations::Expensify::SyncDataWorker
  include Sidekiq::Job
  include PrimaryIntegration
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  attr_accessor :service

  def perform(company_id, first_time_flag)
    @starting_time = Time.current
    @company_id = company_id
    set_read_replica_db do
      @company = Company.find_cache(id: @company_id)
      @company_integration = CompanyIntegration.find_by(integrable_type: "Integrations::Expensify::Config", company_id: @company_id)
    end
    @primary_integration_flag = set_primary_integration_flag "Integrations::Expensify::Config", @company
    @first_time_flag = first_time_flag

    if @company_integration
      @service = Integrations::Expensify::FetchData.new(@company.expensify_config)
      pusher(@company.expensify_config, true, 0.3)
      create_transactions
    end
    intg_duration_analysis(@company_id, first_time_flag)
  rescue Exception => e
    failed_company_integration(e)
    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless e.message.downcase.include?("authentication error")
    end
  end

  private

  def create_transactions
    templates = service.fetch_expensify_templates(@first_time_flag)
    return failed_company_integration if templates.nil?
    pusher(@company.expensify_config, true, 0.8)

    templates.each do |response|
      payments_response = service.download_expensify_payments(@first_time_flag, response.parsed_response)
      next if payments_response && payments_response == "error"

      payments_response&.each do |pay|
        next if pay[4].to_date > Date.today

        category_id = nil
        transaction = nil
        set_read_replica_db do
          category_id = @company.categories.where(name: pay[3])&.first&.id
          transaction = GeneralTransaction.find_or_initialize_by(
              transaction_id: pay[0],
              transaction_type: "expenses",
              transaction_date: pay[4].to_date
            )
        end

        if !transaction.manually_edited
          transaction.assign_attributes(
            product_name: pay[1],
            amount: (pay[2].to_f / 100),
            company_id: @company_id,
            company_integration_id: @company_integration.id,
            category_id: category_id
          )
          map_vendor(transaction, @company_integration.company)
        end
      end
      service.log_event(templates[0], "create_transactions")
    end

    @company_integration.update(
      sync_status: :successful,
      status: true,
      last_synced_at: DateTime.now,
      active: true,
      error_message: nil,
      notified: nil
    )
    pusher(@company.expensify_config ,true, 1)
  rescue => e
    Rails.logger.info "Payments Exception: #{e}"
    service.log_event(nil, "exception_fetch_expensify_payments", {}, e)
  end

  def map_vendor(transaction, company)
    TransactionMapping.new({
      transaction: transaction,
      company: company,
      current_company_user: company.admin_company_users.first,
      primary_integration_flag: @primary_integration_flag},
                           source: 'Expensify'
    ).match_vendor
  end
  
  def failed_company_integration(e=nil)
    @company_integration.update!(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: e&.message || "Sorry, there was an error, please try again later."
    )

    if !@first_time_flag && e&.message&.include?("Authentication error")
      send_notification(@company_integration)
    end

    @service.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    pusher(@company.expensify_config ,false, 0)
  end
end
