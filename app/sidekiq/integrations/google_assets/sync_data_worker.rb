class Integrations::GoogleAssets::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include Regions
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    @config = set_read_replica_db do
      Integrations::GoogleAssets::Config.find_by(id: config_id)
    end
    return if @config.nil?
    set_read_replica_db do
      @source_id = Integration.find_by(name: 'google_assets').id
      @company_integration = @config.company_integration
      @company = @company_integration.company
    end
    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    @worker_start_time = DateTime.current
    pusher(@config, true, 0.2)
    save_gc_assets
    pusher(@config, true, 0.8)
    save_success_company_integration
    intg_duration_analysis(@company.id, first_time_flag)
  rescue Exception => e
    create_logs
    save_failed_company_integration e
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @company_integration.to_json, "google_assets_integration").to_json)
  end

  def save_success_company_integration
    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil, 
                                           failure_count: 0,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag)
    @company_integration.save!
    pusher(@config, true, 1)
    create_logs
  end

  def save_failed_company_integration error
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           error_message: error.message, 
                                           failure_count: @company_integration.failure_count + 1,
                                           active: true,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag)

    @company_integration.save!

    error_messages = [
      "Unauthorized",
      "forbidden: Caller does not have access to the customers reporting data.",
      "Request had insufficient authentication scopes."
    ]

    if !@first_time_flag && error_messages.find {|em| error.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    pusher(@config, false, 0)
  end

  def gc_instances
    instances = []
    gc_projects.each do |project|
      instances += client.list_instances project.project_id
    end
    pusher(@config, true, 0.4)
    instances
  end

  def instance_public_ip
    @instance[:network_interfaces]&.first[:access_configs]&.first[:nat_ip]
  end

  def instance_type
    @instance[:machine_type].split('/').last
  end

  def instance_hard_drive_size
    hd_size = client.get_disk @instance[:project_id], @instance[:zone], @instance[:name]
    "#{hd_size}GB"
  end

  def instance_location
    loc_hash = GCP_LOCATIONS.find{ |loc| loc[:zone] == @instance[:zone] } || {}
    loc_hash[:location] || @instance[:zone]
  end

  def instance_machine_info
    client.get_machine_info @instance[:project_id], @instance[:zone], instance_type
  end

  def discovered_asset_attributes
    { 
      manufacturer: 'Google Cloud Platform',
      asset_type: 'Virtual Machine',
      source: 'google',
      display_name: @instance[:name],
      ip_address: instance_public_ip,
      model: instance_type,
      discovered_asset_type: :device
    }
  end

  def discovered_assets_hardware_detail_attributes
    { 
      hard_drive: instance_hard_drive_size,
      memory: instance_machine_info[:memory],
      processor: @instance[:cpu_platform],
      processor_cores: instance_machine_info[:cores]
    }
  end

  def asset_source_data
    {
      asset_type: 'Virtual Machine',
      display_name: @instance[:name],
      ip_address: instance_public_ip,
      manufacturer: @discovered_asset.manufacturer,
      source: @discovered_asset.source
    }
  end

  def save_discovered_asset
    set_read_replica_db do
      @discovered_asset.assign_attributes(discovered_asset_attributes)
      @discovered_asset.discovered_assets_hardware_detail ||= DiscoveredAssetsHardwareDetail.new
      @discovered_asset.discovered_assets_hardware_detail.assign_attributes(discovered_assets_hardware_detail_attributes)
      @discovered_asset.google_project = gc_projects.find_by_project_id(@instance[:project_id])
    end
    @discovered_asset.integration_location = get_integration_location(instance_location)
    @discovered_asset.lower_precedence = @is_lower_precedence
    is_new = @discovered_asset.new_record?
    @discovered_asset.save!
    @asset_count += 1 if is_new
  end

  def save_gc_assets
    gc_instances.each do |instance|
      @instance = instance
      @discovered_asset = nil
      @managed_asset = nil
      is_instance_running = @instance[:status].downcase.include?('running')
      next unless is_instance_running

      @discovered_asset = find_discovered_asset_for_cloud(@company, @instance[:name], 'google', @worker_start_time)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: @company.id)
      @is_lower_precedence = !is_higher_precedence?(**precedence_data)

      save_discovered_asset

      create_cloud_asset_attributes
      add_source(asset_source_data)
    end
  end

  def cloud_attributes
    {
      project_id: @instance[:project_id],
      zone: @instance[:zone],
      location: instance_location,
      creation_timestamp: @instance[:creation_timestamp],
      status: @instance[:status],
      **discovered_asset_attributes,
      description: @instance[:description],
      private_ip: @instance[:network_interfaces].first[:network_ip],
      deletion_protection: @instance[:deletion_protection],
      can_ip_forward: @instance[:can_ip_forward],
      start_restricted: @instance[:start_restricted],
      is_shared_cpu: instance_machine_info[:is_shared_cpu]
    }
  end

  def create_cloud_asset_attributes
    cloud_attributes.each do |attribute|
      cloud_attr = set_read_replica_db do
        @discovered_asset.cloud_asset_attributes.find_or_initialize_by(key: attribute[0].to_s.gsub(/\W+/, ' ').gsub('_', ' ').titleize)
      end
      cloud_attr.value = attribute[1].to_s
      cloud_attr.save!
    end
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :google)
    end
    das.asset_data = asset_source_data
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.company_integration_id = @company_integration.id
    das.updated_at = DateTime.now
    das.save!
  end

  def get_integration_location address
    location = set_read_replica_db do
      @company.integrations_locations.find_by(address: address, source: 'google')
    end
    location ||= @company.integrations_locations.create!(address: address, source: 'google')
  end

  private

  def client
    @client ||= Integrations::GoogleAssets::FetchData.new(@config.company_integration.company.id) unless @config.nil?
  end

  def event_params error, detail, api_type
    {
      company_id: @company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: @source_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end

  def gc_projects
    set_read_replica_db do
      @config.projects
    end
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end

    {
      asset_sources: discovered_managed_asset_sources,
      current_source: "google",
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.google.new
    }
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Google', @company_user_id, @company.id)
    end
  end
end
