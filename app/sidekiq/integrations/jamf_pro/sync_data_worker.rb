class Integrations::JamfPro::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs

  attr_accessor :config, :first_time_flag, :page

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    self.config = set_read_replica_db do
      Integrations::JamfPro::Config.find_by(id: config_id)
    end
    return if self.config.nil?
    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    self.page = 0

    pusher(config, true, 0.2)
    refresh_access_token
    pusher(config, true, 0.4)
    save_devices(:computers)
    save_devices(:devices)
    pusher(config, true, 0.8)
    update_company_integration
    pusher(config, true, 1)
    create_logs
    intg_duration_analysis(company.id, first_time_flag)
  rescue Exception => e
    create_logs
    send_notification_with_updating_status(e)
    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em) }
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'jamf_pro_integration').to_json)
    pusher(config, false, 0)
  end

  def error_messages
    ['forbidden']
  end

  def company
    set_read_replica_db do
      config.company
    end
  end

  def company_integration
    set_read_replica_db do
      config.company_integration
    end
  end

  def refresh_access_token
    client.refresh_access_token
  end

  def jamf_pro_computers
    @jamf_pro_computers = client.get_computers_inventory(self.page)
    @total_computers ||= @jamf_pro_computers['totalCount']
    @jamf_pro_computers['results']
  end

  def jamf_pro_devices
    @jamf_pro_devices = client.get_mobile_devices_inventory(self.page)
    @total_devices ||= @jamf_pro_devices['totalCount']
    @jamf_pro_devices['results']
  end

  def next_page_call(api_call_type)
    @total_count = api_call_type == :computers ? @total_computers : @total_devices
    @last_api_call_type = api_call_type
  
    if @total_count.present?
      total_pages = (@total_count / 100.to_f).floor
      self.page = self.page + 1
      if self.page == total_pages + 1
        self.page = 0
        return
      end
      save_devices(api_call_type)
    end
  end

  def save_devices(api_call_type)
    assets = api_call_type == :computers ? jamf_pro_computers : jamf_pro_devices
    assets&.each do |asset|
      retries = 0
      begin
        @mac_addresses = []
        @secondary_mac_addresses = []
        @device_info = asset
        display_name = @device_info['general'] && api_call_type == :computers ? @device_info['general']['name'] : @device_info['general']['displayName']
        ip_address = @device_info['general'] && api_call_type == :computers ? @device_info['general']['lastIpAddress'] : @device_info['general']['ipAddress']
        serial_number = @device_info['hardware'] && @device_info['hardware']['serialNumber']
        manufacturer = @device_info['hardware'] && api_call_type == :computers ? @device_info['hardware']['make'] : "Apple"
        @mac_addresses << @device_info['hardware']['macAddress'] rescue nil
        @secondary_mac_addresses << @device_info['hardware']['altMacAddress'] rescue nil

        @discovered_asset = find_discovered_asset(company, false, serial_number, @mac_addresses, display_name, ip_address, manufacturer, false)
        @discovered_asset ||= DiscoveredAsset.jamf_pro.ready_for_import.new(company_id: company.id)

        assign_discovered_asset_attributes(api_call_type)
        create_asset_software

        managed_asset = find_managed_asset(company, serial_number, @mac_addresses, display_name, manufacturer)

        if managed_asset.present?
          @discovered_asset.status = :imported
          @discovered_asset.managed_asset_id = managed_asset.id
        end

        assign_discovered_assets_hardware_details
        is_new = @discovered_asset.new_record?
        @discovered_asset.save!
        @asset_count += 1 if is_new

        create_asset_source(api_call_type)
      rescue PG::ConnectionBad => e
        retries += 1
        if retries <= 3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, @discovered_asset)
          break
        end
      rescue StandardError => e
        log_exception(e, @discovered_asset)
      end
    end
    next_page_call(api_call_type)
  end

  def create_asset_source(api_call_type)
    asset_source_data = 
      {
        display_name: @device_info['general'] && api_call_type == :compmuter ? @device_info['general']['name'] : @device_info['general']['displayName'],
        machine_serial_no: @device_info['hardware'] && api_call_type == :computers ? @device_info['hardware']['serialNumber'] : @device_info['hardware']['serialNumber'],
        manufacturer: @device_info['hardware'] && api_call_type == :computers ? @device_info['hardware']['make'] : "Apple",
        os_name: @device_info['operatingSystem'] && api_call_type == :computers ? @device_info['operatingSystem']['name'] : "",
        source: 'jamf_pro',
      }
    add_source(asset_source_data)
  end

  def client
    @jamf_pro_service ||= Integrations::JamfPro::FetchData.new(company.id, config)
  end

  def memory_data
    "#{@device_info['hardware']['totalRamMegabytes']/1024}GB" if @device_info['hardware'] && @device_info['hardware']['totalRamMegabytes'].present?
  end

  def asset_type(platform)
    if platform == 'Mac'
      'Laptop'
    elsif platform == 'iMac'
      'Desktop'
    elsif platform&.downcase.include?("appletv")
      'Tv'
    elsif platform&.downcase.include?("iphone")
      'iPhone'
    elsif platform&.downcase.include?("ipad")
      'iPad'
    else
      'Apple Device'
    end
  end

  def assign_discovered_asset_attributes(api_call_type)
    @discovered_asset.assign_attributes(
      display_name: @device_info['general'] && api_call_type == :computers ? @device_info['general']['name'] : @device_info['general']['displayName'],
      mac_addresses: @mac_addresses,
      secondary_mac_addresses: @secondary_mac_addresses,
      ip_address: @device_info['general'] && api_call_type == :computers ? @device_info['general']['lastIpAddress'] : @device_info['general']['ipAddress'],
      manufacturer: @device_info['hardware'] && api_call_type == :computers ? @device_info['hardware']['make'] : "Apple",
      machine_serial_no: @device_info['hardware'] && @device_info['hardware']['serialNumber'],
      os_name: @device_info['operatingSystem'] && @device_info['operatingSystem']['name'],
      os_version: @device_info['operatingSystem'] && api_call_type == :computers ? @device_info['operatingSystem']['version'] : @device_info['general']['osVersion'],
      source: 'jamf_pro',
      system_uuid: @device_info['udid'],
      last_synced_at: DateTime.now,
      model: @device_info['hardware'] && @device_info['hardware']['model'],
      lower_precedence: @is_lower_precedence,
      used_by: @device_info['userAndLocation'] && api_call_type == :computers ? @device_info['userAndLocation']['email'] : @device_info['userAndLocation']['emailAddress'],
      asset_type: asset_type(@device_info['general'] && api_call_type == :computers ? @device_info['general']['platform'] : @device_info['hardware']['modelIdentifier'])
    )
  end

  def assign_discovered_assets_hardware_details
    @discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if @discovered_asset.discovered_assets_hardware_detail.blank?
    @discovered_asset.discovered_assets_hardware_detail.assign_attributes(
      memory: memory_data,
      processor: @device_info['hardware'] && @device_info['hardware']['processorType'],
      processor_cores: @device_info['hardware'] && @device_info['hardware']['coreCount'],
      processor_architecture: @device_info['hardware'] && @device_info['hardware']['processorArchitecture']
    )
  end

  def create_asset_software
    discovered_asset_softwares = set_read_replica_db do
      @discovered_asset.asset_softwares
    end
    return if is_lower_precedence && discovered_asset_softwares.present?

    if @discovered_asset.os_name.present?
      discovered_asset_softwares.find_or_initialize_by({
        software_type: 'Operating System',
        name: @discovered_asset.os_name
      })
    end
    @device_info['applications']&.each do |app|
      discovered_asset_softwares.find_or_initialize_by({
        software_type: 'Application',
        name: app['name']
      })
    end
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end

    {
      asset_sources: discovered_managed_asset_sources,
      current_source: 'jamf_pro',
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.jamf_pro.new
    }
  end

  def is_lower_precedence
    !is_higher_precedence?(**precedence_data)
  end

  def send_notification_with_updating_status(error)
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      error_message: error.message,
      failure_count: company_integration.failure_count + 1,
      active: true
    )
    company_integration.save
    send_notification(company_integration) unless @first_time_flag
  end

  def update_company_integration
    if client.ok?
      company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: 0
      )
    else
      company_integration.assign_attributes(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        active: true,
        error_message: client.error,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: company_integration.failure_count + 1
      )
    end
    company_integration.save
  end

  def event_params(error, detail, api_type)
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace.join('\n'),
      class_name: self.class.name,
      integration_id: intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: '422',
      created_at: DateTime.now
    }
  end

  def intg_id
    @intg_id ||= set_read_replica_db do
      Integration.find_by_name('jamf_pro').id
    end
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :jamf_pro)
    end
    das.updated_at = DateTime.now
    das.asset_data = asset_source_data
    das.company_integration_id = company_integration.id
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.save
  end

  def log_exception(exception, discovered_asset)
    Rails.logger.error(exception)
    Bugsnag.notify(exception) if (Rails.env.production? || Rails.env.staging?) && !exception.message.include?('Managed asset has already been taken')
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(exception, discovered_asset.to_json, 'save_devices').to_json)
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Jamf Pro', @company_user_id, company.id)
    end
  end
end
