class Integrations::Microsoft::SaveUserLicensesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb
  include MicrosoftCommonMethods

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, next_page_token = nil)
    @starting_time = Time.current
    @config = set_read_replica_db { Integrations::Microsoft::Config.find_by(id: config_id) }
    @next_page_token = next_page_token
    @first_time_flag = first_time_flag
    refresh_access_token
    save_user_licenses(@next_page_token)
    pusher(@config, true, 0.6)
    save_usage

    if client.ok?
      company_integration.assign_attributes(sync_status: :successful,
                                            status: true,
                                            last_synced_at: DateTime.now,
                                            active: true,
                                            error_message: nil,
                                            notified: nil)
    end

    company_integration.save!
    pusher(@config, true, 1)
    intg_duration_analysis(company.id, @first_time_flag)
  rescue Exception => e
    company_integration.assign_attributes(sync_status: :failed,
                                          status: false,
                                          last_synced_at: DateTime.now,
                                          active: true,
                                          error_message: client.error)

    error_messages = [
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request."
    ]

    company_integration.save

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(company_integration)
    end

    detail_params = {error: company_integration.errors.full_messages}
    client.log_event({}, "perform", detail_params)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@config, false, 0)
  end

  def company_integration
    @company_integration ||= @config.company_integration
  end

  def company
    @config.company
  end

  def source
    set_read_replica_db { Integration.find_by(name: 'microsoft') }
  end

  def client
    @microsoft_service ||= Integrations::Microsoft::FetchData.new(company.id)
  end

  def save_user_licenses(next_page_token)
    response = client.get_users next_page_token
    return unless client.ok?

    users = response.parsed_response["value"]
    return unless users.present?

    user_emails = users.map { |user| user["userPrincipalName"] }

    @microsoft_users = set_read_replica_db do
      Integrations::Microsoft::User.where(email: user_emails, company_id: company.id)
                                   .pluck(:email, :id)
                                   .to_h
    end
    valid_app_users = users.reject { |user| user["userType"] == "Guest" && !@config.allow_guest_users }

    bulk_save_app_users(valid_app_users)
    if response.parsed_response["@odata.nextLink"]
      next_page_token = response.parsed_response["@odata.nextLink"].split("https://graph.microsoft.com/beta/users?$top=100")[1]
      Integrations::Microsoft::SaveUserLicensesWorker.perform_async(@config.id, @first_time_flag, next_page_token)
    end
  end

  def save_usage
    licensed_apps = set_read_replica_db { Integrations::Microsoft::App.where(company_id: company.id, app_type: "licensed") }

    app_display_names = licensed_apps.pluck(:app_display_name)
    integrations_apps = set_read_replica_db { Integrations::App.where(name: app_display_names, company_id: company.id).index_by(&:name) }

    usage_records = []

    licensed_apps.find_each do |licensed_app|
      intg_app = integrations_apps[licensed_app.app_display_name]
      next unless intg_app.present?

      usage_records << {
        app_id: intg_app.id,
        month: Time.now.at_beginning_of_month,
        source_id: source.id,
        company_integration_id: company_integration.id,
        num_of_users: intg_app.used,
        total_users: intg_app.total_users
      }
    end
    Integrations::AppUsage.insert_all(usage_records) if usage_records.any?
  end

  def bulk_save_app_users(users)
    return unless users.present?

    microsoft_app_users = []
    integration_app_users = []

    sku_ids = users.flat_map { |user| user["assignedLicenses"].map { |license| license["skuId"] } }.uniq

    microsoft_apps = Integrations::Microsoft::App
                      .where(company_id: company.id, app_type: "licensed")
                      .where("meta_data->>'sku_id' IN (?)", sku_ids)
                      .index_by { |app| app.meta_data["sku_id"] }

    users.each do |user|
      assigned_licenses = user["assignedLicenses"]
      next unless assigned_licenses.present?

      microsoft_user_id = @microsoft_users[user["userPrincipalName"]]
      next unless microsoft_user_id

      assigned_licenses.each do |assigned_license|
        microsoft_app = microsoft_apps[assigned_license["skuId"]]
        next unless microsoft_app

        app_user = nil
        integrated_app = nil
        integrated_user = nil
        intg_app_user = nil

        set_read_replica_db do
          app_user = Integrations::Microsoft::AppUser.find_by(
            microsoft_user_id: microsoft_user_id,
            microsoft_app_id: microsoft_app.id
          )

          integrated_app = Integrations::App.find_by(
            name: microsoft_app.app_display_name,
            company_id: company.id
          )

          integrated_user = Integrations::User.find_by(email: user["userPrincipalName"])

          intg_app_user = Integrations::AppUser.find_by(app_id: integrated_app.id, user_id: integrated_user.id) if integrated_app && integrated_user

          unless intg_app_user
            integration_app_users << {
              app_id: integrated_app.id,
              user_id: integrated_user.id
            }
          end
        end

        unless app_user
          microsoft_app_users << {
            microsoft_user_id: microsoft_user_id,
            microsoft_app_id: microsoft_app.id,
          }
        end
      end
    end
    if microsoft_app_users.any?
      Integrations::Microsoft::AppUser.insert_all(microsoft_app_users)
    end

    if integration_app_users.any?
      Integrations::AppUser.insert_all(integration_app_users)
    end
  end
end
