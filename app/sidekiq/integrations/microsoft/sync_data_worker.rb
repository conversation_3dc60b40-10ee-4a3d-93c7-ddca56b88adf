class Integrations::Microsoft::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include IntegrationAppSourceImporter
  include MicrosoftCommonMethods
  include ReadReplicaDb

  attr_accessor :config, :first_time_flag, :app_ids

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag)
    @starting_time = Time.current
    self.config = set_read_replica_db { Integrations::Microsoft::Config.find(config_id) }
    self.first_time_flag = first_time_flag
    self.app_ids = []

    refresh_access_token

    pusher(config, true, 0.2)
    save_licenses

    if client.ok?
      Integrations::Microsoft::SaveUsersWorker.perform_async(config.id, first_time_flag)
    else
      company_integration_failed
    end

    intg_duration_analysis(company.id, first_time_flag)
  rescue Exception => e
    company_integration_failed

    error_messages = [
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request."
    ]

    if !@first_time_flag && error_messages.find { |em| e.message.downcase.include?(em.downcase) }
      send_notification(company_integration)
    end

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em.downcase) }
    end
    pusher(config, false, 0)
  end

  def company
    set_read_replica_db { config.company }
  end

  def company_integration
    @company_integration ||= set_read_replica_db { config.company_integration }
  end

  def create_app(license)
    ms_license = @existing_apps_data[[company.id, license["id"]]] || 
                 Integrations::Microsoft::App.new(company_id: company.id, microsoft_id: license["id"])
    
    ms_license.assign_attributes(
      app_display_name: product_friendly_name(license["skuPartNumber"], license["skuId"], ms_license),
      logo_url: product_logo_url(license["skuPartNumber"], license["skuId"]),
      config_id: config.id,
      meta_data: {
        sku_id: license["skuId"],
        consumed_licenses: license["consumedUnits"],
        total_licenses: license["prepaidUnits"]["enabled"],
        suspended_licenses: license["prepaidUnits"]["suspended"]
      },
      app_type: "licensed"
    )

    begin
      ms_license.save!
      app_ids << ms_license.integration_app_id if ms_license.integration_app_id.present?
    rescue ActiveRecord::RecordNotUnique => e
      Rails.logger.warn("Microsoft: Duplicate app #{ms_license.inspect}")
    end

    ms_license
  end

  def create_license_service(microsoft_license, service)
    assigned_plan_id = assigned_plans.find { |k| k["servicePlanId"] == service["servicePlanId"] }
    service_name = service_friendly_name(service["servicePlanName"], service["servicePlanId"])
    ms_service = @existing_services_data[[microsoft_license.id, service["servicePlanId"]]] ||
                 set_read_replica_db { Integrations::Microsoft::Service.find_or_initialize_by(
                   microsoft_apps_id: microsoft_license.id,
                   name: service_name
                 )}
    ms_service.external_id = service["servicePlanId"]
    ms_service.friendly_name = assigned_plan_id ? assigned_plan_id["service"] : nil
    ms_service.company_id = company.id
    ms_service.save!
    ms_service
  end

  def product_logo_url(string_id, guid)
    processed_string_id = string_id.downcase.gsub(/\W/, '')
    product_info = @product_info_data[[processed_string_id, guid]]
    product_info.logo_url if product_info.present?
  end

  def product_friendly_name(string_id, guid, ms_license)
    processed_string_id = string_id.downcase.gsub(/\W/, '')
    product_info = @product_info_data[[processed_string_id, guid]]
    ms_license.is_microsoft_product = true if product_info.present?
    product_info.present? ? product_info.product_name : string_id
  end

  def service_friendly_name(string_id, guid)
    service_info = @service_info_data[[string_id, guid]]
    service_info.present? ? service_info.friendly_name : string_id
  end

  def organization
    @organization ||= client.get_organization
    return unless @organization

    @organization.parsed_response["value"]
  end

  def licenses
    @licenses ||= client.get_licenses.parsed_response["value"]
  end

  def assigned_plans
    return [] unless organization

    organization[0]["assignedPlans"]
  end

  def save_licenses
    return unless organization && client.ok? && licenses

    product_keys = Set.new
    service_keys = Set.new
    microsoft_app_ids = Set.new
    @existing_apps = []
    @existing_services = []
    @product_info_data = {}
    @service_info_data = {}
    @existing_apps_data = {}
    @existing_services_data = {}
  
    licenses.each do |license|
      next if license["skuPartNumber"].blank?

      string_id = license["skuPartNumber"].downcase.gsub(/\W/, '')
      guid = license["skuId"]
      product_keys.add([string_id, guid])

      microsoft_app_ids.add(license["id"])

      license["servicePlans"].each do |service|
        service_keys.add([service["servicePlanName"], service["servicePlanId"]])
      end
    end

    if product_keys.any?
      query_clauses = product_keys.map { "lower(string_id) = ? AND guid = ?" }.join(" OR ")
      query_values = product_keys.flat_map { |string_id, guid| [string_id, guid] }
      @product_info_data = set_read_replica_db do
        Integrations::ProductInformation.where(query_clauses, *query_values)
                                        .index_by { |pi| [pi.string_id.downcase.gsub(/\W/, ''), pi.guid] }
      end
    end

    if service_keys.any?
      query_clauses = service_keys.map { "string_id = ? AND guid = ?" }.join(" OR ")
      query_values = service_keys.flat_map { |string_id, guid| [string_id, guid] }
      @service_info_data = set_read_replica_db do
        Integrations::Microsoft::MicrosoftServiceInformation.where(query_clauses, *query_values)
                                                          .index_by { |si| [si.string_id, si.guid] }
      end
    end

    if microsoft_app_ids.any?
      @existing_apps = set_read_replica_db do
          Integrations::Microsoft::App.where(company_id: company.id, microsoft_id: microsoft_app_ids)
      end
    end
    @existing_apps_data = @existing_apps.index_by { |app| [app.company_id, app.microsoft_id] }

    existing_service_app_ids = @existing_apps.pluck(:id)
    if existing_service_app_ids.any?
      @existing_services = set_read_replica_db do
        Integrations::Microsoft::Service.where(microsoft_apps_id: existing_service_app_ids)
      end             
    end
    @existing_services_data = @existing_services.index_by { |s| [s.microsoft_apps_id, s.external_id] }

    licenses.each do |license|
      next if license["skuPartNumber"].blank?
      microsoft_license = create_app(license)
      next if microsoft_license.id.nil?
      license["servicePlans"].each do |service|
        create_license_service(microsoft_license, service)
      end
    end

    create_bulk_app_source(config.id, app_ids, 'microsoft')
  end

  def source
    source ||= set_read_replica_db { Integration.find_by(name: 'microsoft') }
  end

  def save_logs
    client.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def client
    @microsoft_service ||= Integrations::Microsoft::FetchData.new(company.id)
  end

  def company_integration_failed
    company_integration.update(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: client.error
    )
  end
end
