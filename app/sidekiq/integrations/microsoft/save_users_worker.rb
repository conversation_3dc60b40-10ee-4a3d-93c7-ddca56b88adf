class Integrations::Microsoft::SaveUsersWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include DestroyIntegrationData
  include ReadReplicaDb
  include MicrosoftCommonMethods

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, next_page_token = nil, fetched_users_emails = [])
    @starting_time = Time.current
    @config = set_read_replica_db { Integrations::Microsoft::Config.find_by(id: config_id) }
    @first_time_flag = first_time_flag
    @fetched_users_emails = fetched_users_emails

    refresh_access_token
    save_users(next_page_token)
    
    if @next_page_link
      Integrations::Microsoft::SaveUsersWorker.perform_async(@config.id, @first_time_flag, @next_page_link, @fetched_users_emails)
    else
      if !first_time_flag
        config_users = @config.users
        deleted_users_emails = config_users.pluck(:email) - @fetched_users_emails.flatten.compact if @fetched_users_emails.present?
        remove_deleted_users(config_users, deleted_users_emails, @config.user_sources, company) if deleted_users_emails.present?
      end
      
      pusher(@config, true, 0.3)
  
      if client.ok?
        Integrations::Microsoft::SaveUserLicensesWorker.perform_async(config_id, first_time_flag)
        company_integration.assign_attributes(
          sync_status: :successful,
          status: true,
          last_synced_at: DateTime.now,
          active: true,
          error_message: nil,
          notified: nil
        )
  
        company_integration.save!
        pusher(@config, true, 1)
      else
        company_integration_failed
      end
  
      intg_duration_analysis(company.id, first_time_flag)
    end
  rescue Exception => e
    company_integration_failed

    error_messages = [
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request."
    ]

    if !first_time_flag && error_messages.find { |em| e.message.downcase.include?(em.downcase) }
      send_notification(company_integration)
    end

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em.downcase) }
    end
    pusher(@config, false, 0)
  end

  def company
    @config.company
  end

  def company_integration
    @company_integration ||= @config.company_integration
  end

  def bulk_save_users(users)
    return if users.blank?

    emails = users.map { |user| user["userPrincipalName"] }.compact
    existing_users = Integrations::Microsoft::User.where(email: emails, company_id: company.id).index_by(&:email)

    new_users = []
    users.each do |user|
      next if user["userPrincipalName"].blank?

      microsoft_user = existing_users[user["userPrincipalName"]] || Integrations::Microsoft::User.new(company_id: company.id, email: user["userPrincipalName"])

      microsoft_user.assign_attributes(
        display_name: user["displayName"],
        given_name: user["givenName"],
        job_title: user["jobTitle"],
        mobile_phone: user["mobilePhone"],
        office_location: user["officeLocation"],
        surname: user["surname"],
        user_created_at: user["createdDateTime"],
        first_login_at: user["createdDateTime"],
        user_principal_name: user["userPrincipalName"],
        microsoft_id: user["id"],
        department: user["department"],
        config_id: @config.id,
        location_id: azure_location_id(user["officeLocation"]),
        supervisor: azure_supervisor(user["userPrincipalName"])
      )

      new_users << microsoft_user
    end

    new_users.each do |user|
      unless user.save
        detail_params = { error: user.errors.full_messages, email: user.email }
        client.log_event({}, "save_users", detail_params)
      end
    end
  end

  def save_users(next_page_token)
    response = client.get_users next_page_token
    return unless client.ok?

    users = response.parsed_response["value"]
    return unless users.present?

    @fetched_users_emails << users.map { |user| user["userPrincipalName"] } unless @first_time_flag

    valid_users = users.reject { |user| user["userType"] == "Guest" && !@config.allow_guest_users }

    bulk_save_users(valid_users)

    if response.parsed_response["@odata.nextLink"]
      @next_page_link = response.parsed_response["@odata.nextLink"].split("https://graph.microsoft.com/beta/users?$top=100")[1]
    else
      @next_page_link = nil
    end
  end

  def client
    @microsoft_service ||= Integrations::Microsoft::FetchData.new(company.id)
  end

  def azure_location_id(location_name)
    if location_name.present?
      azure_user_location = Integrations::Location.find_or_create_by!(address: location_name, company_id: @config.company_id) do |location|
        location.source = set_read_replica_db do
          @config.company_integration.integration.name
        end
      end
      azure_user_location.id
    end
  end

  def azure_supervisor(email)
    response = client.get_manager(email)
    response&.parsed_response["mail"] rescue nil
  end

  def company_integration_failed
    company_integration.update(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: client.error
    )
  end
end
