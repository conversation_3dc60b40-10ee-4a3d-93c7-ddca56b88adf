require 'ostruct'

class Integrations::Bill::SyncDataWorker
  include Sidekiq::Job
  include PrimaryIntegration
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(company_id, first_time_flag = true)
    @starting_time = Time.current
    @company_id = company_id
    @first_time_flag = first_time_flag
    set_read_replica_db do
      @company = Company.find_cache(id: @company_id)
      @company_integration = CompanyIntegration.find_by(integrable_type: "Integrations::Bill::Config", company_id: @company_id)
    end
    return if @company_integration.nil?
    @bill_config = set_read_replica_db { @company_integration.integrable }
    @primary_integration_flag = set_primary_integration_flag "Integrations::Bill::Config", @company

    pusher(@company.bill_config, true, 0.3)
    @service = Integrations::Bill::FetchData.new(@bill_config)

    if @company_integration && @service.authenticate?
      get_payments "3"
      pusher(@company.bill_config, true, 0.6)
      get_payments "0"
      sync_success
    end
    intg_duration_analysis(@company_id, first_time_flag)
  rescue Exception => e
    sync_fail(e)
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    pusher(@company.bill_config, false, 0)
  end

  private

  def get_payments(disbursement_status)
    vendors_ids = get_vendor_ids
    @start_date = determine_start_date

    vendors_ids.each do |vendor_id|
      start = 0
      next_page = true

      while next_page
        response = @service.fetch_payments(start, disbursement_status, vendor_id, @start_date)
        next if response.nil?
        if response["response_status"] == 0
          payments = response["response_data"]["payments"]
          process_valid_payments(payments, disbursement_status)
          start += @service.max_payments
          next_page = payments.any?
        else
          error_message = OpenStruct.new(message: response["response_data"]["error_message"])
          sync_fail(error_message)
          next_page = false
        end
      end
    end
  end

  def determine_start_date
    if @first_time_flag && @bill_config.start_date.present?
      @bill_config.start_date
    elsif @first_time_flag
      "2017-01-01"
    else
      (Date.today - 31.days).strftime('%F')
    end
  end

  def process_valid_payments(payments, disbursement_status)
    payments.each do |payment|
      next unless is_date_valid?(payment["processDate"])
      save_payment(payment, disbursement_status)
    end
  end

  def get_vendor_ids
    if @bill_config.select_all
      all_vendors_data = Integrations::Bill::FetchVendorsData.new(@company_id).fetch_vendors_data
      if all_vendors_data.present?
        all_vendors_data.map { |vendor| vendor['id'] }
      end
    else
      set_read_replica_db { @bill_config.integrated_vendors.where(sync_status: true).pluck(:vendor_id) }
    end
  end

  def save_payment(pay, disbursement_status)
    transaction = set_read_replica_db { @company.general_transactions.find_or_initialize_by(payment_params(pay)) }
    if !transaction.manually_edited
      transaction.assign_attributes(is_manual: false,
                                    description: pay["description"],
                                    amount: pay["amount"].to_f,
                                    product_name: pay["disbursement"]["vendorName"] )

      transaction.scheduled = true if disbursement_status == "0"

      map_vendor(transaction, @company)
    end
  end

  def payment_params(payment)
    {
      transaction_id: payment["id"],
      transaction_type: "payments",
      transaction_date: Date.parse(payment["processDate"]),
      company_integration_id: @company_integration.id,
    }
  end

  def map_vendor(transaction, company)
    TransactionMapping.new({transaction: transaction,
                            company: company,
                            current_company_user: company.admin_company_users.first,
                            primary_integration_flag: @primary_integration_flag},
                           source: 'Bill')
                      .match_vendor
  end

  def sync_fail(e)
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    error_messages = [
      "User name and/or password do not match our records.",
      "Your account is locked because it reached the maximum number of failed login attempts.  To reset your password, please use the \"Reset your password\" link below."
    ]

    @company_integration.save!

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    @service.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end
  end

  def sync_success
    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil)
    @company_integration.save!
    pusher(@company.bill_config, true, 1)
    Integrations::Bill::FetchVendorsData.new(@bill_config.company_id).call
  end

  def is_date_valid?(date)
    return Date.current >= Date.parse(date)
  end
end
