class Integrations::Salesforce::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include DestroyIntegrationData
  include IntegrationAppSourceImporter
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag)
    @starting_time = Time.current
    @salesforce_config = set_read_replica_db { Integrations::Salesforce::Config.find(config_id) }
    @current_company = @salesforce_config.company
    @first_time_flag = first_time_flag
    @company_integration = @salesforce_config.company_integration
    @fetched_users_emails = []
    @app_ids = []

    pusher(@salesforce_config, true, 0.2)
    save_apps
    pusher(@salesforce_config, true, 0.3)
    save_users

    unless @first_time_flag
      config_users = set_read_replica_db { @salesforce_config.users }
      deleted_users_emails = set_read_replica_db { config_users.pluck(:email) } - @fetched_users_emails.flatten.compact if @fetched_users_emails.present?
      remove_deleted_users(config_users, deleted_users_emails, @salesforce_config.user_sources, @salesforce_config.company) if deleted_users_emails.present?
    end
    
    pusher(@salesforce_config, true, 0.6)

    save_licenses
    create_bulk_app_source(config_id, @app_ids.uniq, 'salesforce')

    pusher(@salesforce_config, true, 0.7)

    save_license_user
    pusher(@salesforce_config, true, 0.8)
    save_usage

    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil)
    @company_integration.save!

    pusher(@salesforce_config, true, 1)
    intg_duration_analysis(@current_company.id, first_time_flag)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message
                                          )

    @company_integration.save!

    error_messages = [
      "FUNCTIONALITY_NOT_ENABLED",
      "INVALID_SESSION_ID"
    ]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@salesforce_config, false, 0)
    save_logs
  end

  def save_apps
    salesforce_service.get_apps.parsed_response["apps"].each do |app|
      next unless app["appId"].present?
      salesforce_app = set_read_replica_db { Integrations::Salesforce::App.find_or_initialize_by(company_id: @current_company.id, sales_id: app["appId"]) }
      salesforce_app.assign_attributes(
                      description: app["description"],
                      developer_name: app["developerName"],
                      icon_url: app["iconUrl"],
                      logo_url: app["logoUrl"],
                      app_type: 0,
                      app_name: product_name(app["label"], salesforce_app),
                      config_id: @salesforce_config.id)
      begin
        salesforce_app.save!
        @app_ids << salesforce_app.integration_app_id if salesforce_app.integration_app_id.present?
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.warn("Salesforce: Duplicate app #{salesforce_app.inspect}")
      end
    end
  end

  def save_licenses
    salesforce_service.get_licenses.parsed_response["records"].each do |license|
      salesforce_license = set_read_replica_db { Integrations::Salesforce::App.find_or_initialize_by(company_id: @current_company.id, sales_id: license["Id"]) }
      salesforce_license.assign_attributes(
                      app_name: product_name(license["Name"], salesforce_license),
                      app_type: 1,
                      meta_data: {
                        consumed_licenses: license["UsedLicenses"],
                        total_licenses: license["TotalLicenses"],
                        status: license["Status"]},
                      config_id: @salesforce_config.id)

      begin
        salesforce_license.save!
        @app_ids << salesforce_license.integration_app_id if salesforce_license.integration_app_id.present?
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.warn("Salesforce: Duplicate app #{salesforce_license.inspect}")
      end
    end
  end

  def save_users
    users_response = salesforce_service.get_users.parsed_response["records"]

    @fetched_users_emails << users_response.map { |user| user["Email"] } unless @first_time_flag

    users_response.each do |user|
      salesforce_user = set_read_replica_db { Integrations::Salesforce::User.find_or_initialize_by(company_id: @current_company.id, email: user["Email"]) }
      salesforce_user.assign_attributes(
                      sales_id: user["Id"],
                      contact_id: user["ContactId"],
                      account_id: user["AccountId"],
                      call_center_id: user["CallCenterId"],
                      created_date: user["CreatedDate"],
                      name: user["Name"],
                      first_name: user["FirstName"],
                      last_name: user["LastName"],
                      user_name: user["UserName"],
                      company_name: user["CompanyName"],
                      country: user["Country"],
                      last_login_date: user["LastLoginDate"],
                      is_active: user["IsActive"],
                      config_id: @salesforce_config.id)

      begin
        salesforce_user.save!
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.warn("Salesforce: Duplicate user #{salesforce_user.inspect}")
      end
    end
  end

  def save_usage
    source = set_read_replica_db { Integration.find_by(name: 'salesforce') }
    company_integration = set_read_replica_db { CompanyIntegration.find_by(company_id: @current_company, integration_id: source.id) }
    licensed_apps = set_read_replica_db { Integrations::Salesforce::App.where(company_id: @current_company.id, app_type: 1) }

    licensed_apps.each do |licensed_app|
      intg_app = set_read_replica_db { Integrations::App.find_by(name: licensed_app.app_name, company_id: @current_company.id, app_type: :licensed) }
      licensed_app_usage = Integrations::AppUsage.find_or_initialize_by(app_id: intg_app.id, month: Time.now.at_beginning_of_month, source_id: source.id)
      licensed_app_usage.assign_attributes(company_integration_id: company_integration.id, num_of_users: intg_app.used, total_users: intg_app.total_users)
      licensed_app_usage.save!
    end
  end

  def save_license_user
    source = set_read_replica_db { Integration.find_by(name: 'salesforce') }
    company_integration = set_read_replica_db { CompanyIntegration.find_by(company_id: @current_company, integration_id: source.id) }

    salesforce_service.get_license_users.parsed_response["records"].each do |licensed_user|
      user = set_read_replica_db { Integrations::Salesforce::User.where(company_id: @current_company.id, sales_id: licensed_user["Id"]).first }
      app = set_read_replica_db { Integrations::Salesforce::App.where(company_id: @current_company.id, sales_id: licensed_user["Profile"]["UserLicense"]["Id"]).first } if licensed_user["Profile"] && licensed_user["Profile"]["UserLicense"]
      if user && app
        Integrations::Salesforce::AppUser.find_or_create_by!(salesforce_user_id: user.id, salesforce_app_id: app.id)
      end
    end
  end

  def salesforce_service
    @salesforce_service ||= Integrations::Salesforce::FetchData.new(@current_company.id)
  end

  def save_logs
    salesforce_service.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def product_name(prod_name, salesforce)
    product_info = set_read_replica_db { Integrations::ProductInformation.find_by("lower(product_name) = ?", prod_name.downcase) }
    salesforce.app_source = product_info.source if product_info.present?
    product_info.present? ? product_info.product_name : prod_name
  end
end
