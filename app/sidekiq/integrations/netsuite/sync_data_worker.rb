class Integrations::Netsuite::SyncDataWorker
  include Sidekiq::Job
  include PrimaryIntegration
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  attr_accessor :netsuite_config, :first_time_flag, :retries, :company_integration, :service, :monthly_sync

  sidekiq_options queue: 'integrations'

  def perform(netsuite_config_id, first_time_flag=false, retries = 0, monthly_sync = false)
    @starting_time = Time.current
    @netsuite_config = set_read_replica_db { ::Integrations::Netsuite::Config.find_by_id(netsuite_config_id) }
    return unless @netsuite_config

    set_read_replica_db do
      @company_integration = @netsuite_config.company_integration
      @company = company_integration.company
    end
    @primary_integration_flag = set_primary_integration_flag("Integrations::Netsuite::Config", @company)
    @service = Integrations::Netsuite::FetchData.new(@netsuite_config)
    @first_time_flag = first_time_flag
    @retries = retries
    @monthly_sync = monthly_sync

    pusher(netsuite_config, true, 0.3)
    perform_etl
    intg_duration_analysis(@company.id, first_time_flag)
  rescue => e
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    company_integration_failed(e)
  end

  private

  def perform_etl
    netsuite_data = extract_data
    pusher(netsuite_config, true, 0.4)
    if netsuite_data.present?
      transform_data(netsuite_data)
      pusher(netsuite_config, true, 0.6)
      load_data
      pusher(netsuite_config, true, 0.8)
      company_integration_succeeded
      pusher(netsuite_config, true, 1)
      service.log_event(event_response, "perform_etl", :success)
      delete_snapshot_and_trigger_full_sync_job if monthly_sync
    else
      first_time_flag ? reschedule_sync : halt_etl
    end
  end

  def delete_snapshot_and_trigger_full_sync_job
    # Before performing a full sync to upload data to S3 for this particular tenant, it is necessary to delete the snapshot first.
    @netsuite_config.delete_snapshot_and_execute_full_sync_job
  end

  def extract_data
    first_time_flag ? service.get_data_from_s3 : service.get_data_from_webhook
  end

  def reschedule_sync
    return halt_etl if @retries >= 15
    service.log_event({retries_count: @retries, monthly_sync: monthly_sync }, "reschedule_sync", :success)
    self.class.perform_in(5.minutes, @netsuite_config.id, true, @retries + 1, monthly_sync)
  end

  def halt_etl
    service.log_event({ message: "Data not found", monthly_sync: monthly_sync }, "halt_etl", :error)
    company_integration_failed
  end

  def transform_data(data)
    vendors = data["vendors"]
    vendor_bills = data["vendorBills"]
    @vendors_names = vendors.map { |vendor| vendor["EntityId"] }
    @bill_data = vendor_bills.reduce([]) do |bills, bill|
      bills.push({
        id: bill["Id"],
        transaction_date: bill["TranDate"].to_date,
        amount: bill["Total"],
        product_name: JSON.parse(bill["EntityRef"])["name"],
      })
    end
  end

  def load_data
    create_discovered_vendors
    create_transactions
  end

  def create_discovered_vendors
    @vendors_names.each do |name|
      vendor = set_read_replica_db do
        @company.discovered_vendors.find_or_initialize_by(name: name)
      end
      vendor.save!
    end
  end

  def create_transactions
    company_transactions = @company.general_transactions
    @bill_data.each do |bill|
      transaction = find_or_init_transaction(bill, company_transactions)
      unless transaction.manually_edited
        assign_transaction_attributes(bill, transaction)
        map_vendor(transaction)
      end
    end
  end

  def find_or_init_transaction(bill, company_transactions)
    transaction = set_read_replica_db do 
      company_transactions.find_or_initialize_by({
      transaction_id: bill[:id],
      transaction_type: 'expenses',
      transaction_date: bill[:transaction_date],
      company_integration_id: company_integration.id,
    })
    end
  end

  def assign_transaction_attributes(bill, transaction)
    transaction.assign_attributes({
      product_name: bill[:product_name],
      amount: bill[:amount],
    })
  end

  def map_vendor(transaction)
    trans_mapping_params = {
      transaction: transaction,
      company: @company,
      current_company_user: @company.admin_company_users.first,
      primary_integration_flag: @primary_integration_flag
    }
    TransactionMapping.new(trans_mapping_params, 'Netsuite').match_vendor
  end

  def company_integration_succeeded
    company_integration.update(
      sync_status: :successful,
      status: true,
      last_synced_at: DateTime.now,
      active: true,
      error_message: nil,
      notified: nil
    )
  end

  def company_integration_failed(error = nil)
    company_integration.update(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: error&.message || "Sorry, we are unable to retrieve data from Netsuite at this time."
    )

    if error.present?
      service.log_event(error, "netsuite_integration", :error)
    end

    pusher(netsuite_config, false, 0)
  end

  def event_response
    first_time_flag ? uploaded_file_path : { vendors: @vendors_names, bills: @bill_data }.to_s
  end

  def uploaded_file_path
    client = Aws::S3::Client.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )

    file_path = "#{Rails.env}/#{@company.subdomain}-netsuite-#{Time.now.to_i}.json"
    s3_resource = Aws::S3::Resource.new(client: client)
    object = s3_resource.bucket(Rails.application.credentials.hotglue[:s3][:logs_bucket]).object(file_path)
    data = { vendors: @vendors_names, bills: @bill_data }.to_json
    object.put({body: data, acl: 'public-read'})
    "http://s3.amazonaws.com/#{ Rails.application.credentials.hotglue[:s3][:logs_bucket] }/#{file_path}"
  end
end
