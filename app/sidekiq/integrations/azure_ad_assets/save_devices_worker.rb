class Integrations::AzureAdAssets::SaveDevicesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include DiscoveredManagedAssetFinder
  include IntegrationsAnalysis
  include ReadReplicaDb
  include AzureAdAssetsCommonMethods
  include DiscoveredAssetLogs
  
  sidekiq_options queue: 'long_integrations'

  def perform(config_id, first_time_flag, starting_time, next_page_token, company_user_id = nil, asset_count = 0)
    @company_user_id = company_user_id
    @asset_count = asset_count
    @starting_time = JSON.parse(starting_time).to_time
    set_read_replica_db do
      @config = Integrations::AzureAdAssets::Config.find(config_id)
      @intg_id = Integration.find_by_name("azure_ad_assets").id
    end
    @first_time_flag = first_time_flag
    refresh_access_token
    devices = client.get_devices(next_page_token)
    pusher(@config, true, 0.5)

    devices_data = devices['value']
    save_devices(devices_data)

    if devices["@odata.nextLink"].present?
      next_page_token = devices["@odata.nextLink"].split("https://graph.microsoft.com/v1.0/devices?$top=100")[1]
      Integrations::AzureAdAssets::SaveDevicesWorker.perform_async(config_id, @first_time_flag, @starting_time.to_json, next_page_token, @company_user_id, @asset_count)
    else
      pusher(@config, true, 0.8)
      company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: 0)
      company_integration.save!
      pusher(@config, true, 1)
      create_logs
      intg_duration_analysis(company.id, @first_time_flag)
    end
  rescue Exception => e
    create_logs
    company_integration.assign_attributes(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        active: true,
        error_message: client.error,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag, 
        failure_count: company_integration.failure_count + 1)
    company_integration.save!
    
    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e)
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, "azure_ad_assets_integration").to_json)
    pusher(@config, false, 0)
  end

  def get_registered_user(id)
    device_data = client.get_registered_user(id)
    device_data['value'].last['mail'] if device_data.present? && device_data['value'].present?
  end

  def get_registered_owner(id)
    device_data = client.get_registered_owner(id)
    device_data['value'].last['mail'] if device_data.present? && device_data['value'].present?
  end

  def company
    @company ||= set_read_replica_db do 
      @config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do 
      @config.company_integration
    end
  end

  def client
    @azure_ad_assets_service ||= Integrations::AzureAdAssets::FetchData.new(company.id)
  end

  def save_devices(devices)
    discovered_assets = preload_discovered_assets(company, devices) if devices.present?
    devices&.each do |machine|
      os_name = machine['operatingSystem']
      os_version = machine['operatingSystemVersion']
      display_name = machine['displayName']
      manufacturer = machine['manufacturer']
      source = 'azure_ad_devices'
      model = machine['model']
      used_by = get_registered_user(machine['id'])
      managed_by = get_registered_owner(machine['id'])

      if [display_name, manufacturer].all?(&:blank?)
        next       # Skip processing devices having missing fields
      end

      @discovered_asset = discovered_assets[display_name.to_s.downcase]
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: company.id)

      display_name_data = display_name.present? ? display_name : @discovered_asset.display_name
      mac_addresses_data = @discovered_asset.mac_addresses
      ip_address_data = @discovered_asset.ip_address

      @is_lower_precedence = !is_higher_precedence?(**precedence_data)

      @discovered_asset.assign_attributes(
        display_name: display_name_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        os_name: os_name,
        os_version: os_version,
        model: model,
        source: source,
        discovered_asset_type: :device,
        lower_precedence: @is_lower_precedence,
        used_by: used_by,
        managed_by: managed_by
      )

      set_read_replica_db do
        @discovered_asset.asset_softwares.find_or_initialize_by(
          software_type: 'Operating System',
          name: os_name || ""
        )
      end

      managed_asset = @discovered_asset.managed_asset || find_managed_asset(company, nil, nil, display_name_data, manufacturer)

      if managed_asset.present?
        @discovered_asset.status = :imported
        @discovered_asset.managed_asset_id = managed_asset.id
      end

      is_new = @discovered_asset.new_record?
      @discovered_asset.save!
      @asset_count += 1 if is_new

      asset_source_data = {
        display_name: display_name,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        source: source
      }

      add_source(asset_source_data)
    end
  end

  def preload_discovered_assets(company, devices)
    display_names = devices.map { |d| d['displayName'].to_s.downcase }.uniq
    company.discovered_assets.where("machine_serial_no = ? AND mac_addresses = ? AND lower(display_name) IN (?)", "", [], display_names)
                             .includes(:managed_asset)
                             .order(:id).group_by { |da| da.display_name.downcase }
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end

    {
      asset_sources: discovered_managed_asset_sources,
      current_source: 'azure_ad_devices',
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.azure_ad_devices.new
    }
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do 
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :azure_ad_devices)
    end
    das.asset_data = asset_source_data
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.company_integration_id = company_integration.id
    das.updated_at = DateTime.now
    das.save!
  end

  def event_params(error, detail, api_type)
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace&.join("\n"),
      class_name: self.class.name,
      integration_id: @intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Azure AD', @company_user_id, company.id)
    end
  end
end
