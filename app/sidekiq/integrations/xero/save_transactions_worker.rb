class Integrations::Xero::SaveTransactionsWorker
  include Sidekiq::Job
  include PrimaryIntegration
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(tenant_ids, company_id, first_time_flag, starting_time, current_tenant_index = 0)
    @starting_time = JSON.parse(starting_time).to_time
    set_read_replica_db do
      @company = Company.find_cache(id: company_id)
      @company_integration = CompanyIntegration.find_by(integrable_type: "Integrations::Xero::Config", company_id: company_id)
      @config = @company_integration.integrable
    end
    @primary_integration_flag = set_primary_integration_flag "Integrations::Xero::Config", @company
    @first_time_flag = first_time_flag
    
    tenant_id = tenant_ids[current_tenant_index]
    @service = Integrations::Xero::FetchData.new(@config, { tenant_id: tenant_id })
    @service.refresh_token
    
    save_transactions

    if current_tenant_index < tenant_ids.length - 1
      Integrations::Xero::SaveTransactionsWorker.perform_async(tenant_ids, company_id, first_time_flag, @starting_time.to_json, current_tenant_index + 1)
    else
      pusher(@company.xero_config, true, 0.8)
      @company_integration.assign_attributes(sync_status: :successful,
                                             status: true,
                                             last_synced_at: DateTime.now,
                                             active: true,
                                             error_message: nil,
                                             notified: nil)
      @company_integration.save!
      pusher(@company.xero_config ,true, 1)
      intg_duration_analysis(@company_id, first_time_flag)
    end
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "The access token has expired",
      "The organisation for this access token is not active",
      "Unknown response code: 403"
    ]
    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    @service.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    pusher(@company.xero_config ,false, 0)
  end

  private

  def save_transactions
    payments = @service.fetch_xero_payments(@first_time_flag)
    company_integration_id = @company_integration.id
    admin_company_user = @company.admin_company_users.first
    company_transactions = @company.general_transactions

    payments.each do |pay|
      next if pay.date.to_date > Date.today

      transaction = set_read_replica_db do
        company_transactions.find_or_initialize_by(transaction_id: pay.payment_id,
                                                   product_name: pay.invoice.contact_name,
                                                   transaction_type: "payments",
                                                   transaction_date: pay.date.to_date)
      end

      if !transaction.manually_edited
        transaction.assign_attributes(
          company_integration_id: company_integration_id,
          amount: pay.amount.to_f
        )

        map_vendor(transaction, @company, admin_company_user)
      end
    end
  end

  def map_vendor(transaction, company, admin_company_user)
    TransactionMapping.new({transaction: transaction,
                           company: company,
                           current_company_user: admin_company_user,
                           primary_integration_flag: @primary_integration_flag},
                           source: 'Xero')
                      .match_vendor
  end
end
