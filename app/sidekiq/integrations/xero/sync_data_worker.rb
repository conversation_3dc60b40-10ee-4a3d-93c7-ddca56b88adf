class Integrations::Xero::SyncDataWorker
  include Sidekiq::Job
  include PrimaryIntegration
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(company_id, first_time_flag)
    @starting_time = Time.current
    @company_id = company_id
    set_read_replica_db do
      @company = Company.find_cache(id: @company_id)
      @company_integration = CompanyIntegration.find_by(integrable_type: "Integrations::Xero::Config", company_id: @company_id)
      @config = @company_integration.integrable
    end
    pusher(@company.xero_config, true, 0.3)

    if @company_integration && @config
      tenant_ids = @config.tenant_ids
      if tenant_ids.present?
        Integrations::Xero::SaveTransactionsWorker.perform_async(tenant_ids, @company_id, first_time_flag, @starting_time.to_json)
      end
    end
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)
    @company_integration.save!
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    pusher(@company.xero_config ,false, 0)
  end
end
