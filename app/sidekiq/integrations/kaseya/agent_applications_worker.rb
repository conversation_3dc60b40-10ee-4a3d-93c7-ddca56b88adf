class Integrations::Kaseya::AgentApplicationsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsEventParam
  include KaseyaCommonMethods
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, agent, retries = 0, is_resyncing = false)
    @config_id = config_id
    @kaseya_config = set_read_replica_db do
      Integrations::Kaseya::Config.find_by(id: config_id)
    end
    return if @kaseya_config.blank?

    set_read_replica_db do
      @company_integration = @kaseya_config.company_integration
      @intg_id = Integration.find_by_name("kaseya").id
    end
    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    @agent = agent
    @retries = retries

    refresh_access_token

    save_agent_installed_applications
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)
    @company_integration.save!

    send_notification(@company_integration) unless @first_time_flag

    Rails.logger.error(e)
    Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?)
    pusher(@kaseya_config, false, 0)
  end

  def save_agent_installed_applications
    begin
      installed_applications = []
      discovered_asset = nil
      top = 100
      total_records = client.get_installed_applications(@agent['AgentGuid'].to_i, top, 0).parsed_response['TotalRecords']
      total_call = total_records / 100 if total_records.present?

      if total_call.present?
        (0..total_call).step(1) do |tc|
          skip = 100 * tc
          parsed_response = client.get_installed_applications(@agent['AgentGuid'].to_i, top, skip).parsed_response
          if parsed_response.nil?
            Integrations::Kaseya::AgentApplicationsWorker.perform_in(30.seconds, @config_id, @first_time_flag, @agent, nil, @is_resyncing)
          else
            installed_applications << parsed_response['Result']
          end
        end

        installed_applications = installed_applications.flatten.compact
        discovered_asset = set_read_replica_db do 
          DiscoveredAsset.find_by(mac_address: @agent['MacAddress'], company_id: company.id) if @agent['MacAddress'].present?
        end
        if discovered_asset.present?
          installed_applications.each do |software|
            software_type = ['SystemApps', '$WINDOWS', '\\Windows\\'].any? { |path| software['DirectoryPath'] && software['DirectoryPath'].include?(path) } ? 'Operating System' : 'Application'
            if software['ProductName'].present?
              asset_software = set_read_replica_db do
                discovered_asset.asset_softwares.find_or_initialize_by(
                  software_type: software_type,
                  name: software['ProductName']
                )
              end
              asset_software.save!
            end
          end
        end
      end
    rescue => e
      error_messages = [
        'Managed asset has already been taken',
        'Net::ReadTimeout',
        'open TCP connection'
      ]

      if (Rails.env.production? || Rails.env.staging?)
        Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      end

      LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, discovered_asset.to_json, "save_agent_installed_applications").to_json)
    end
  end
end
