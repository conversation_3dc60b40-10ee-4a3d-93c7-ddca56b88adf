class Integrations::Kaseya::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsEventParam
  include KaseyaCommonMethods
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil,  sync_application = nil)
    begin
      @asset_count = 0
      @company_user_id = company_user_id
      @config_id = config_id
      @kaseya_config = set_read_replica_db do 
        Integrations::Kaseya::Config.find_by(id: config_id)
      end
      return if @kaseya_config.blank?
      @import_target = @kaseya_config.import_type
      @new_discovered_assets_count = 0
      @new_managed_assets_count = 0
      @first_time_flag = first_time_flag
      @is_resyncing = is_resyncing
      @current_company = @kaseya_config.company
      set_read_replica_db do
        @company = @kaseya_config.company
        @company_integration = @kaseya_config.company_integration
        @intg_id = Integration.find_by_name("kaseya").id
      end
      pusher(@kaseya_config, true, 0.2)
      refresh_access_token

      asset_types
      audit_asset_types

      if @asset_types.present?
        save_assets
        pusher(@kaseya_config, true, 0.4)
        @log_id = create_or_update_asset_history_log('Kaseya')
        time_to_perform = @first_time_flag ? 1.minute : 1.hour
        Integrations::Kaseya::SyncAllAgentsWorker.perform_in(time_to_perform, config_id, first_time_flag, @is_resyncing, @company_user_id, @log_id, @new_discovered_assets_count, @new_managed_assets_count)
      end
    rescue Exception => e
      @company_integration.assign_attributes(sync_status: :failed,
                                             status: false,
                                             last_synced_at: DateTime.now,
                                             active: true,
                                             error_message: e.message,
                                             failure_count: @company_integration.failure_count + 1,
                                             company_user_id: @company_user_id,
                                             first_time_flag: @first_time_flag)
      @company_integration.save!

      error_messages = [
        'SSL_connect',
        'Net::OpenTimeout',
        'Net::ReadTimeout',
        'Failed to open TCP connection',
        'Unauthorized',
        'Authentication failed'
      ]

      send_notification(@company_integration) unless @first_time_flag

      Rails.logger.error(e)

      if Rails.env.production? || Rails.env.staging?
        Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      end
      pusher(@kaseya_config, false, 0)
    end

    if @first_time_flag || (!@first_time_flag && sync_application)
      @agent_assets&.each do |agent|
        Integrations::Kaseya::AgentApplicationsWorker.perform_in(30.seconds, config_id, first_time_flag, agent, nil, @is_resyncing)
      end
    elsif !@first_time_flag && @new_agents.present?
      @new_agents.each do |new_agent|
        Integrations::Kaseya::AgentApplicationsWorker.perform_in(30.seconds, config_id, first_time_flag, new_agent, nil, @is_resyncing)
      end
    end
  end

  def assets
    assets = []
    top = 100
    total_records = client.get_kaseya_assets(top, 0).parsed_response['TotalRecords']
    total_call = total_records / 100 if total_records.present?

    if total_call.present?
      (0..total_call).step(1) do |tc|
        skip = 100 * tc
        assets << client.get_kaseya_assets(top, skip).parsed_response['Result']
      end
      assets = assets.flatten()
    end
    assets
  end

  def save_assets
    assets&.each do |device_info|
      begin
        discovered_asset = nil
        managed_asset = nil
        display_name = device_info['HostName']
        serial_number = device_info["SysSerialNum"]
        ip_address = device_info["IPAddresses"]
        mac_addresses = []
        optional_data = {}
        mac_address = (device_info["MACAddresses"])
        mac_addresses << mac_address.gsub('-', ':')  if mac_address_valid? mac_address
        device_manufacturer = device_info['DeviceManufacturer']
        asset_type = device_info['AgentId'].present? ? new_asset_type(device_info) : asset_type(device_info)
        
        discovered_asset = find_discovered_asset(@company, false, serial_number, mac_addresses, display_name, ip_address, device_manufacturer, false)
        if discovered_asset.blank? && ip_address.present? && serial_number.blank? && mac_addresses.blank?
          next if company_discovered_asset_by_ip = set_read_replica_db do 
                    @company.discovered_assets.find_by(ip_address: ip_address)
          end
        end

        if discovered_asset.blank?
          discovered_asset = DiscoveredAsset.ready_for_import.new(company_id: @company.id)
        end

        display_name_data = display_name.present? ? display_name : discovered_asset.display_name
        mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
        ip_address_data = ip_address.present? ? ip_address : discovered_asset.ip_address
        serial_data = serial_number.present? ? serial_number : discovered_asset.machine_serial_no

        is_lower_precedence = !is_higher_precedence?(**precedence_data(discovered_asset))

        discovered_asset.assign_attributes(
          display_name: display_name_data,
          asset_type: asset_type,
          mac_addresses: mac_addresses_data,
          ip_address: ip_address_data,
          manufacturer: device_manufacturer,
          source: "kaseya",
          os: device_info['OSFamily'],
          os_name: device_info['OSName'],
          lower_precedence: is_lower_precedence,
          machine_serial_no: serial_number,
          last_synced_at: Time.current
        )

        managed_asset = find_managed_asset(@company, serial_data, mac_addresses_data, display_name_data, device_manufacturer)

        if managed_asset.present?
          discovered_asset.status = :imported
          discovered_asset.managed_asset_id = managed_asset.id
        elsif @import_target == "managed_asset"
          discovered_asset.status = :imported
        end

        discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if discovered_asset.discovered_assets_hardware_detail.blank?

        is_new = discovered_asset.new_record?
        discovered_asset.save!
        @asset_count += 1 if is_new
        @new_discovered_assets_count += 1 if is_new

        save_asset_software(discovered_asset, device_info)

        asset_source_data = {
          name: display_name,
          asset_type: asset_type,
          mac_addresses: mac_addresses,
          ip_address: ip_address,
          manufacturer: device_manufacturer,
          machine_serial_no: serial_number,
          source: "kaseya",
          os: device_info['OSFamily'],
          os_name: device_info["OSName"],
          firmware: device_info["firmware"]
        }
        add_source(discovered_asset, asset_source_data)
        discovered_asset.reload

        if @import_target == "managed_asset" && discovered_asset.managed_asset_id.blank? 
          assets = Array.wrap(discovered_asset)
          BulkDiscoveredAssetsUpdate.new(assets, discovered_asset.company, nil).import_assets
          @new_managed_assets_count += 1
        end
      rescue => e
        Rails.logger.error(e)
        Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
        LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, discovered_asset.to_json, "save_assets").to_json)
      end
    end
  end

  def asset_types
    parsed_response = client.get_kaseya_asset_types.parsed_response
    if parsed_response.nil?
      Integrations::Kaseya::SyncDataWorker.perform_in(20.minutes, @config_id, true)
    else
      @asset_types = parsed_response['Result']
    end
  end

  def asset_type(device_info)
    mobiles = ['iphone', 'mobile', 'android', 'ios', 'raspberry', 'phone']
    laptops = ['mac', 'laptop', 'windows', 'microsoft', 'linux']
    desktops = ['desktop', 'computer']
    printers = ['printer']
    router = ['router']
    firewall = ['firewall']
    server = 'Server'
    mob = lap = desk = prin = rout = 0
    asset_type_name = @asset_types.find { |id| id['AssetTypeId'] == device_info['AssetTypeId']}['AssetTypeName'].downcase
    mob = mobiles.select{ |m| asset_type_name.include?(m) }
    lap = laptops.select{ |l| asset_type_name.include?(l) }
    desk = desktops.select{ |d| asset_type_name.include?(d) }
    prin = printers.select{ |p| asset_type_name.include?(p) }
    rout = router.select{ |r| asset_type_name.include?(r) || device_info['OSName']&.include?(r) }
    fire = firewall.select{ |f| asset_type_name.include?(f) || device_info['OSName']&.include?(f) }

    if prin.count > 0
      asset_type_name = get_asset_type("Printer")
    elsif mob.count > 0
      asset_type_name = get_asset_type("Mobile")
    elsif lap.count > 0
      if device_info['OSName'] && device_info['OSName'].include?(server)
        asset_type_name = get_asset_type(server)
      elsif device_info['OSName'] && device_info['OSName'].include?('phone')
        asset_type_name = get_asset_type('Mobile')
      else 
        asset_type_name = get_asset_type("Laptop")
      end
    elsif desk.count > 0
      asset_type_name = get_asset_type("Desktop")
    elsif rout.count > 0
      asset_type_name = get_asset_type("Router")
    elsif fire.count > 0
      asset_type_name = get_asset_type("Firewall")
    else
      if mobiles.include?(device_info['OSType'])
        asset_type_name = get_asset_type("Mobile")
      else
        asset_type_name = get_asset_type("Other")
      end
    end

    return asset_type_name
  end

  def save_asset_software discovered_asset, params
    if params['OSName'].present?
      asset_software = set_read_replica_db do
        discovered_asset.asset_softwares.find_or_initialize_by(
          software_type: 'Operating System',
          name: params['OSName'],
          discovered_asset_id: discovered_asset.id)
      end
      asset_software.save!
    end
  end
end
