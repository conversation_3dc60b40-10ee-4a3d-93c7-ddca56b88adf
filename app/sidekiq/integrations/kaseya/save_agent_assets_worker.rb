class Integrations::Kaseya::SaveAgentAssetsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsEventParam
  include IntegrationsAnalysis
  include KaseyaCommonMethods
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'integrations'

  def perform(args)
    @clients_info = args['clients_info']
    @first_time_flag = args['first_time_flag']
    @is_resyncing = args['is_resyncing']
    @company_user_id = args['company_user_id']
    @log_id = args['log_id']
    @asset_count = 0
    @kaseya_config = set_read_replica_db do
      Integrations::Kaseya::Config.find_by(id: args['config_id'])
    end
    @starting_time = Time.current
    return if @kaseya_config.blank?

    set_read_replica_db do
      @company_integration = @kaseya_config.company_integration
      @intg_id = Integration.find_by_name("kaseya").id
      @company = @kaseya_config.company
      @current_company = @kaseya_config.company
    end

    refresh_access_token

    audit_asset_types
    save_agent_assets
    pusher(@kaseya_config, true, 0.9)
    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil,
                                           failure_count: 0,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag,
                                           alert_info: {
                                              "failed"=>false,
                                              "new_asset"=>false,
                                              "out_of_sync"=>false,
                                              "not_reporting"=>false
                                            }
                                           )
    @company_integration.save!
    intg_duration_analysis(@company.id, @first_time_flag)
    if args['is_last_batch']
      pusher(@kaseya_config, true, 1, {
        new_kaseya_discovered_assets: args['new_discovered_assets_count'],
        new_kaseya_managed_assets: args['new_managed_assets_count']
      })
      create_or_update_asset_history_log('Kaseya')
    end
  rescue Exception => e
    create_or_update_asset_history_log('Kaseya')
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: client.error || e.message,
                                           failure_count: @company_integration.failure_count + 1,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag)
    @company_integration.save!

    error_messages = [
      'SSL_connect',
      'Net::OpenTimeout',
      'Net::ReadTimeout',
      'Failed to open TCP connection',
      'Unauthorized'
    ]
    
    send_notification(@company_integration) unless @first_time_flag

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@kaseya_config, false, 0)
  end

  def save_agent_assets
    @new_agents = []
    @clients_info&.each do |agent_device_info|
      begin
        discovered_asset = nil
        managed_asset = nil
        mac_addresses = []
        bios_version = ''
        host_name = ''
        memory = ''
        disk_storage = ''
        dns = device_dns(agent_device_info)
        gateway = device_gateway(agent_device_info)

        display_name =  agent_device_info['ComputerName'].present? ? agent_device_info['ComputerName'] : agent_device_info["DisplayName"]
        serial_number = agent_device_info['SystemSerialNumber']
        ip_address = agent_device_info['IpAddress']
        mac_address = (agent_device_info['MacAddr']) if (agent_device_info['MacAddr']) && mac_address_valid?((agent_device_info['MacAddr']))
        mac_address = mac_address.gsub('-', ':') if mac_address
        mac_addresses << mac_address if mac_address
        host_name = agent_device_info['ComputerName'] + ' ' + agent_device_info['DomainWorkgroup'] if agent_device_info['ComputerName'] && agent_device_info['DomainWorkgroup']
        processor_cores = agent_device_info['CpuCount'].to_i / 2 if agent_device_info['CpuCount'] && agent_device_info['CpuCount'].to_i >= 4
        asset_type = new_asset_type(agent_device_info) if agent_device_info['AgentId'].present?
        last_logged_in_user =  agent_device_info['LastLoggedInUser']
        last_check_in_time =  agent_device_info['LastCheckInTime']
        device_machine_group_id =  agent_device_info['MachineGroupId']
        asset_product_name = get_asset_product(agent_device_info) if agent_device_info['AgentId'].present?

        discovered_asset = find_discovered_asset(@company, false, serial_number, mac_addresses, display_name, ip_address, nil, false)
        if discovered_asset.blank? && ip_address.present? && serial_number.blank? && mac_addresses.blank?
          next if company_discovered_asset_by_ip = set_read_replica_db do
                    @company.discovered_assets.find_by(ip_address: ip_address)
          end
        end

        if discovered_asset.blank?
          discovered_asset = DiscoveredAsset.ready_for_import.new(company_id: @company.id)
        end

        display_name_data = display_name.present? ? display_name : discovered_asset.display_name
        mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
        mac_address_data = mac_address.present? ? mac_address : discovered_asset.mac_address
        ip_address_data = ip_address.present? ? ip_address : discovered_asset.ip_address
        serial_data = serial_number.present? ? serial_number : discovered_asset.machine_serial_no
        memory = agent_device_info['RamMBytes']/1000 if agent_device_info['RamMBytes'].present?
        dns_data = dns.present? ? dns : discovered_asset.discovered_assets_hardware_detail&.dns
        gateway_data = gateway.present? ? gateway : discovered_asset.discovered_assets_hardware_detail&.gateway
        is_lower_precedence = !is_higher_precedence?(**precedence_data(discovered_asset))

        discovered_asset.assign_attributes(
          display_name: display_name_data,
          asset_type: asset_type,
          mac_addresses: mac_addresses_data,
          mac_address: mac_address_data,
          ip_address: ip_address_data,
          machine_serial_no: serial_data,
          source: "kaseya",
          os: 'Windows',
          os_name: agent_device_info['OsInfo'],
          os_version: agent_device_info['OsType'],
          lower_precedence: is_lower_precedence,
          optional_details: {
            Ipv6Address: agent_device_info['Ipv6Address'],
            PrimaryKServer: agent_device_info['PrimaryKServer'],
            SecondaryKServer: agent_device_info['SecondaryKServer']
          }
        )

        managed_asset = find_managed_asset(@company, serial_data, mac_addresses_data, display_name_data, nil)

        if managed_asset.present?
          discovered_asset.status = :imported
          discovered_asset.managed_asset_id = managed_asset.id
        end

        discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if discovered_asset.discovered_assets_hardware_detail.blank?
        discovered_asset.discovered_assets_hardware_detail.assign_attributes(
          dns: dns_data,
          memory: memory,
          hostname: host_name,
          gateway: gateway_data,
          disk_free_space: disk_storage,
          hardware_version: bios_version,
          processor_cores: processor_cores,
          processor_architecture: agent_device_info['CpuType'],
          processor_logical_cores: agent_device_info['CpuCount']
        )

        is_new = discovered_asset.new_record?
        discovered_asset.save!
        @asset_count += 1 if is_new

        @new_agents << agent_device_info if discovered_asset.created_at.to_date == Date.today

        save_asset_user_account(discovered_asset, agent_device_info)

        asset_source_data = {
          name: display_name_data,
          asset_type: asset_type,
          mac_addresses: mac_addresses,
          ip_address: ip_address,
          machine_serial_no: serial_number,
          last_logged_in_user: last_logged_in_user,
          last_check_in_time: last_check_in_time,
          machine_group_id: device_machine_group_id,
          product_name: asset_product_name,
          source: "kaseya",
          os_name: agent_device_info['OsInfo'],
          hostname: host_name
        }
        add_source(discovered_asset, asset_source_data)
      rescue => e
        LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, discovered_asset.to_json, "save_agent_assets").to_json)
        raise e if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
      end
    end
  end

  def save_asset_user_account(discovered_asset, agent)
    begin
      last_login = agent['LastCheckinTime']&.in_time_zone&.strftime('%a, %d %b %Y %H:%M:%S')
      discovered_asset_user_accounts = set_read_replica_db do
        discovered_asset.asset_user_accounts
      end
      if discovered_asset_user_accounts.present?
        asset_user_account = discovered_asset_user_accounts.first
        discovered_managed_asset = set_read_replica_db do
          discovered_asset.managed_asset
        end
        asset_user_account.assign_attributes(
          last_login: last_login,
          name: agent['LastLoggedInUser'],
          managed_asset_id: discovered_managed_asset&.id
        )
        discovered_managed_asset&.id.present? ? asset_user_account.save! : asset_user_account.save_without_auditing
      else
        discovered_asset_user_accounts.create!(last_login: last_login, name: agent['LastLoggedInUser'])
      end
    rescue => e
      Rails.logger.error(e)
      Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
      LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, discovered_asset.to_json, "save_asset_user_account").to_json)
    end
  end

  def device_dns(device_info)
    dns = []
    dns << "Primary Dns: #{device_info["DnsServer1"]}" if device_info["DnsServer1"].present?
    dns << "Secondary Dns: #{device_info["DnsServer2"]}" if device_info["DnsServer2"].present?
    dns
  end

  def device_gateway(device_info)
    gateway = []
    gateway << device_info["DefaultGateway"] if device_info["DefaultGateway"].present?
    gateway << "Connection Gateway Ip: #{device_info["ConnectionGatewayIp"]}" if device_info["ConnectionGatewayIp"].present?
    gateway
  end
end
