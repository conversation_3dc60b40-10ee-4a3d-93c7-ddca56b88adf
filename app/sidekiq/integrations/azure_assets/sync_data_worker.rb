class Integrations::AzureAssets::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    @azure_assets_config = set_read_replica_db do
      Integrations::AzureAssets::Config.find_by(id: config_id)
    end
    return if @azure_assets_config.nil?

    set_read_replica_db do
      @company = @azure_assets_config.company
      @company_integration = @azure_assets_config.company_integration
      @intg_id = Integration.find_by_name("azure_assets").id
    end

    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing

    refresh_access_token
    fetch_azure_assets
    pusher(@azure_assets_config, true, 0.8)
    save_success_company_integration
    intg_duration_analysis(@company.id, first_time_flag)
  rescue Exception => e
    create_logs
    save_failed_company_integration e
    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @company_integration.to_json, "azure_assets_integration").to_json)
    pusher(@azure_assets_config, false, 0)
  end

  def save_success_company_integration
    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil,
                                           failure_count: 0,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag)
    @company_integration.save!
    pusher(@azure_assets_config, true, 1)
    create_logs
  end

  def save_failed_company_integration error
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: error.message,
                                           user_error_message: user_error_message(error.message),
                                           failure_count: @company_integration.failure_count + 1,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag)

    @company_integration.save!

    if !@first_time_flag && error_messages.find{ |em| error.message.downcase.include?(em.downcase) }
      send_notification(@company_integration)
    end
  end

  def error_messages
    [
      "invalid_grant",
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request.",
      "The security token included in the request is invalid.",
      "The current subscription type is not permitted to perform operations on any provider namespace.",
      "AuthorizationFailed"
    ]
  end

  def user_error_message(error)
    if error.to_s.include?("The current subscription type is not permitted to perform operations")
      return "Subscription not permitted. Please follow steps provided in Help Center."
    end
  end

  def refresh_access_token
    azure_assets_service = Integrations::AzureAssets::FetchData.new(@company.id, @first_time_flag)
    response = azure_assets_service.refresh_token

    if response && response["access_token"] && response["refresh_token"]
      expiry_time = Time.now + (response["expires_in"].to_i - 600)

      @azure_assets_config.update(
        token: response["access_token"],
        expires_in: expiry_time,
        refresh_token: response["refresh_token"],
        skip_callbacks: true)

      @azure_assets_config.reload
    end
  end

  def fetch_azure_assets
    virtual_machines = []
    pusher(@azure_assets_config, true, 0.2)
    subscriptions = azure_assets_client.get_subscriptions

    subscriptions.each do |subscription|
      vms = azure_assets_client.get_virtual_machines(subscription['subscriptionId'])
      virtual_machines += vms if vms.present? && vms.is_a?(Array)
    end
    pusher(@azure_assets_config, true, 0.4)
    if virtual_machines.size == 0 && azure_assets_client.errors.size > 0
      raise Exception.new azure_assets_client.errors.uniq.to_sentence
    else
      save_azure_assets virtual_machines
    end
  end

  def get_memory machine
    vm_size = azure_assets_client.find_vm_size machine
    vm_size.present? ? "#{vm_size[:memoryInMB].to_f / 1024}GB" : vm_size
  end

  def get_cores machine
    vm_size = azure_assets_client.find_vm_size machine
    vm_size.present? ? vm_size[:numberOfCores] : vm_size
  end

  def get_disk_info machine
    azure_assets_client.get_disk_info machine
  end

  def get_network_info machine
    azure_assets_client.get_network_info(machine)
  end

  def get_location machine
    vm_location = azure_assets_client.find_vm_location(machine)
    vm_location.present? ? vm_location[:displayName] : vm_location
  end

  def get_integration_location address
    @company.integrations_locations.find_or_create_by!(address: address, source: 'azure')
  end

  def save_azure_assets virtual_machines
    virtual_machines.each do |machine|
      @discovered_asset = nil
      @machine_info = nil
      managed_asset = nil
      instance_view = azure_assets_client.get_instance_view machine
      next if instance_view.blank?
      power_state = instance_view[:statuses].detect { |status| status[:code].downcase.include?('powerstate') }
      is_machine_running = power_state[:displayStatus].downcase.include?('running') if power_state.present?
      next unless is_machine_running

      os_name = instance_view[:osName]
      os_version = instance_view[:osVersion]
      display_name = machine[:name] if machine[:name].present?
      ip_address = nil
      mac_addresses = []
      manufacturer = 'Microsoft Azure'
      hard_drive_size = get_hard_drive_size machine
      operating_system = get_os machine
      source = 'azure'
      memory = get_memory machine
      location = get_location machine
      cores = get_cores machine
      os_disk_info = get_disk_info machine
      network_info = get_network_info machine
      machine[:location] = location
      machine.merge!(os_disk_info: os_disk_info,
                     network_info: network_info,
                     is_machine_running: is_machine_running,
                     os_name: os_name,
                     os_version: os_version)

      @machine_info = machine
      mac_addresses = network_info.map { |net_interface| net_interface[:properties][:macAddress] }

      network_info.each do |net_interface|
        ip_address_info = net_interface[:properties][:ipConfigurations].detect { |conf| conf[:publicIPAddress] && conf[:publicIPAddress][:properties][:ipAddress].present? }
        ip_address = ip_address_info[:publicIPAddress][:properties][:ipAddress] if ip_address_info.present?
        break if ip_address.present?
      end

      @discovered_asset = find_discovered_asset(@company, false, nil, mac_addresses, display_name, ip_address, manufacturer, false)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: @company.id)

      display_name_data = display_name.present? ? display_name : @discovered_asset.display_name
      mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses
      ip_address_data = ip_address.present? ? ip_address : @discovered_asset.ip_address

      @is_lower_precedence = !is_higher_precedence?(**precedence_data)

      @discovered_asset.assign_attributes(
        display_name: display_name_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        os: operating_system,
        os_name: os_name,
        os_version: os_version,
        asset_type: 'Virtual Machine',
        source: source,
        integration_location: get_integration_location(location),
        discovered_asset_type: :device,
        lower_precedence: @is_lower_precedence
      )

      @discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if @discovered_asset.discovered_assets_hardware_detail.blank?
      @discovered_asset.discovered_assets_hardware_detail.assign_attributes(
        processor: "#{cores} Core(s)",
        memory: memory,
        hard_drive: hard_drive_size
      )

      set_read_replica_db do
        @discovered_asset.asset_softwares.find_or_initialize_by(
          software_type: "Operating System",
          name: os_name || "",
          install_date: os_disk_info[:timeCreated]
        )
      end

      managed_asset = find_managed_asset(@company, nil, mac_addresses_data, display_name_data, manufacturer)

      # TODO not yet decided
      # if managed_asset.blank?
      #   asset_type = @company.asset_types.find_by_name('Virtual Machine')
      #   managed_asset = ManagedAsset.new(company_id: @company.id, asset_type: asset_type)
      #   managed_asset.hardware_detail = ComputerDetail.new
      # end

      if managed_asset
        @discovered_asset.status = :imported
        @discovered_asset.managed_asset = managed_asset
      end

      create_cloud_asset_attributes
      is_new = @discovered_asset.new_record?
      @discovered_asset.save!
      @asset_count += 1 if is_new

      asset_source_data = {
        display_name: display_name,
        mac_addresses: mac_addresses,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        asset_type: 'Virtual Machine',
        source: source
      }

      add_source(asset_source_data)
    rescue Exception => e
      LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @machine_info, "save_azure_assets").to_json)
    end
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :azure)
    end
    das.asset_data = asset_source_data
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.company_integration_id = @company_integration.id
    das.updated_at = DateTime.now
    das.save!
  end

  def machine_info
    @machine_info
  end

  def create_cloud_asset_attributes
    cloud_attributes.each do |key, value|
      cloud_attr = set_read_replica_db do
        @discovered_asset.cloud_asset_attributes.find_or_initialize_by(key: key.to_s.gsub(/\W+/, ' ').titleize)
      end
      cloud_attr.value = value
    end
  end

  def cloud_attributes
    resource_group_name = machine_info[:id].split('/')[4] if machine_info[:id].present?
    machine_properties = machine_info[:properties] if machine_info.present?
    machine_os_profile = machine_properties[:osProfile] if machine_properties.present?
    machine_storage_profile = machine_properties[:storageProfile] if machine_properties.present?
    machine_os_disk = machine_storage_profile[:osDisk] if machine_storage_profile.present?
    machine_managed_disk = machine_os_disk[:managedDisk] if machine_os_disk.present?
    machine_image_reference = machine_storage_profile[:imageReference] if machine_storage_profile.present?
    memory = get_memory machine_info
    cores = get_cores machine_info
    time_created = DateTime.parse(machine_info[:os_disk_info][:timeCreated]) if machine_info[:os_disk_info][:timeCreated]
    network_info = machine_info[:network_info] if machine_info.present?
    private_ip_info = {}
    public_ip_info = {}

    if network_info.present?
      mac_addresses = network_info.map { |net_interface| net_interface[:properties][:macAddress] }

      network_info.each do |net_interface|
        private_ip_info = net_interface[:properties][:ipConfigurations].detect { |conf| conf[:properties][:privateIPAddress].present? }[:properties]
        break if private_ip_info[:privateIPAddress].present?
      end

      network_info.each do |net_interface|
        ip_address_info = net_interface[:properties][:ipConfigurations].detect { |conf| conf[:publicIPAddress] && conf[:publicIPAddress][:properties][:ipAddress].present? }
        public_ip_info = ip_address_info[:publicIPAddress][:properties] if ip_address_info.present?
        break if public_ip_info.present?
      end
    end

    @cloud_attributes = {}
    
    if machine_info.present?
      @cloud_attributes["Name"] = "#{machine_info[:name]}"
      @cloud_attributes["Location"] = "#{machine_info[:location]}"
      @cloud_attributes["OS Name"] = "#{machine_info[:os_name]}"
      @cloud_attributes["OS Version"] = "#{machine_info[:os_version]}"
      @cloud_attributes["VM Generation"] = get_vm_generation machine_info
      @cloud_attributes["Power State(Running)"] = "#{machine_info[:is_machine_running]}"
      @cloud_attributes["License Type"] = "#{machine_info[:licenseType]}"
      @cloud_attributes["Provisioning State"] = "#{machine_info[:provisioningState]}"
    end

    if machine_properties.present?
      @cloud_attributes["VM Id"] = "#{machine_properties[:vmId]}"
      @cloud_attributes["VM Size"] = get_vm_size machine_properties
    end

    if machine_os_disk.present?
      @cloud_attributes["OS Type"] = "#{machine_os_disk[:osType]}"
      @cloud_attributes["OS Disk Name"] = "#{machine_os_disk[:name]}"
      @cloud_attributes["Storage Disk Size"] = "#{machine_os_disk[:diskSizeGB]}GB"
    end

    if public_ip_info.present?
      @cloud_attributes["Public IP Address"] = "#{public_ip_info[:ipAddress]}"
      @cloud_attributes["Public IP Address Version"] = "#{public_ip_info[:publicIPAddressVersion]}"
      @cloud_attributes["Public IP Allocation Method"] = "#{public_ip_info[:publicIPAllocationMethod]}"
    end

    if private_ip_info.present?
      @cloud_attributes["Private IP Address"] = "#{private_ip_info[:privateIPAddress]}"
      @cloud_attributes["Private IP Address Version"] = "#{private_ip_info[:privateIPAddressVersion]}"
      @cloud_attributes["Private IP Allocation Method"] = "#{private_ip_info[:privateIPAllocationMethod]}"
    end

    if machine_image_reference.present?
      @cloud_attributes["Image Publisher"] = "#{machine_image_reference[:publisher]}"
      @cloud_attributes["Image Offer"] = "#{machine_image_reference[:offer]}"
      @cloud_attributes["Image SKU"] = "#{machine_image_reference[:sku]}"
      @cloud_attributes["Image Version"] = "#{machine_image_reference[:version]}"
      @cloud_attributes["Image Exact Version"] = "#{machine_image_reference[:exactVersion]}"
    end

    @cloud_attributes["Memory"] = "#{memory}"
    @cloud_attributes["No. of Cores"] = "#{cores}"
    @cloud_attributes["Resource Group"] = "#{resource_group_name}" if resource_group_name.present?
    @cloud_attributes["Time Created"] = "#{time_created}"
    @cloud_attributes["Storage Account Type"] = "#{machine_managed_disk[:storageAccountType]}" if machine_managed_disk.present?
    @cloud_attributes["Admin Username"] = "#{machine_os_profile[:adminUsername]}" if machine_os_profile.present?
    @cloud_attributes["Mac Addresses"] = "#{mac_addresses.to_sentence}" if mac_addresses.present?
    
    @cloud_attributes
  end

  def azure_assets_client
    @azure_service ||= Integrations::AzureAssets::FetchData.new(@company.id, @first_time_flag)
  end

  def event_params error, detail, api_type
    {
      company_id: @company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: @intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end

    {
      asset_sources: discovered_managed_asset_sources,
      current_source: "azure",
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.azure.new
    }
  end

  def get_hard_drive_size machine
    "#{machine[:properties][:storageProfile][:osDisk][:diskSizeGB]}GB"
  rescue Exception => e
    ""
  end

  def get_os machine
    machine[:properties][:storageProfile][:osDisk][:osType]
  rescue Exception => e
    ""
  end

  def get_vm_size machine_properties
    "#{machine_properties[:hardwareProfile][:vmSize]}"
  rescue Exception => e
    ""
  end

  def get_vm_generation machine_info
    "#{machine_info[:os_disk_info][:hyperVGeneration]}"
  rescue Exception => e
    ""
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Azure', @company_user_id, @company.id)
    end
  end
end
