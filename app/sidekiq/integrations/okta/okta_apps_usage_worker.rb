class Integrations::Okta::OktaAppsUsageWorker
  require 'oktakit'
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(okta_config_id, first_time_flag)
    set_read_replica_db do
      @okta_comp_config = Integrations::Okta::Config.find okta_config_id
      @company_integration = CompanyIntegration.find_by(integrable_type: "Integrations::Okta::Config", integrable_id: @okta_comp_config.id)
    end
    @first_time_flag = first_time_flag
    get_apps_usage
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           last_synced_at: DateTime.now,
                                           error_message: e.message,
                                           active: true,
                                           status: false)

    if !@first_time_flag &&  e.message.include?("Invalid token provided")
      send_notification(@company_integration)
    end

    @company_integration.save!

    client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end
    Rails.logger.error(e)
    Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Invalid token provided")
    pusher(@okta_comp_config, false, 0)
  end

  private
  def client
    client ||= Integrations::Okta::FetchData.new(token: @okta_comp_config.token, name: @okta_comp_config.name, current_company: @okta_comp_config.company)
  end

  def get_apps_usage
    (0..3).each do |i|
      month = Date.today.beginning_of_month-(i).month
      perform_in = 1.minutes * (i+1)
      Integrations::Okta::CreateAppUsageWorker.perform_in(perform_in, @okta_comp_config.id, month.to_s, i, @first_time_flag)
      break if !@first_time_flag
    end
  end
end
