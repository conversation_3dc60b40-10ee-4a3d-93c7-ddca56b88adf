class Integrations::Okta::CreateAppUsageWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(okta_config, month, index, first_time_flag)
    @okta_config = set_read_replica_db { Integrations::Okta::Config.find(okta_config) }
    @company_integration = @okta_config.company_integration
    @total_users = @okta_config.okta_users.count
    @first_time_flag =first_time_flag
    @index = index

    if client.authenticate?
      create_usage(month)
    end

    if @last_app_flag
      @company_integration.assign_attributes(last_synced_at: DateTime.now)
      @company_integration.save!
      usage_sync_pusher(@okta_config, 1)
    end
  rescue Exception => e
    if @last_app_flag
      @company_integration.assign_attributes(sync_status: :failed,
                                             status: false,
                                             last_synced_at: DateTime.now,
                                             active: true,
                                             error_message: e.message)

      @company_integration.save!

      if !@first_time_flag && e.message.include?("Invalid token provided")
        send_notification(@company_integration)
      end

    end
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    pusher(@okta_config, false, 0)
    save_logs
  end

  def create_usage month
    @okta_config.okta_apps.each_with_index do |app, index|
      external_actor_ids = []
      intg_app = set_read_replica_db { Integrations::App.find_by(name: app.name, company_id: @company_integration.company_id) }
      from_date = DateTime.now.beginning_of_month-(@index).month
      to_date = DateTime.now.end_of_month-(@index).month
      query_obj = filter_parms(from_date, to_date, app.okta_app_id)

      resp = client.fetch_apps_usage_data(query_obj)

      resp[0].each do |user|
        unless (external_actor_ids.any? {|h| h === user.actor.id })
          external_actor_ids << user.actor.id
        end
      end

      if intg_app
        app_usage = set_read_replica_db { Integrations::AppUsage.find_or_initialize_by(
                      app_id: intg_app.id,
                      month: month,
                      company_integration_id: @company_integration.id,
                      source_id: @company_integration.integration_id
                    ) }
        app_usage.assign_attributes(
          num_of_users: external_actor_ids.count,
          total_users: @total_users)

        app_usage.save!
      end
      @last_app_flag = @okta_config.okta_apps.count == (index + 1) && (!@first_time_flag || (@first_time_flag && @index == 3))
    end
  end

  def save_logs
    client.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def client
    client ||= Integrations::Okta::FetchData.new(token: @okta_config.token, name: @okta_config.name, current_company: @okta_config.company)
  end

  def filter_parms(from_date, to_date, app_id)
    {
      query: {
        since: from_date.to_formatted_s(:iso8601),
        until: to_date.to_formatted_s(:iso8601),
        filter: 'target.id eq "'+ app_id +'" and target.type eq "AppInstance" and eventType eq "user.authentication.sso"'
      }
    }
  end
end
