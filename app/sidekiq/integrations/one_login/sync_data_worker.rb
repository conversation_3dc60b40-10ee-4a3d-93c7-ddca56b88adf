class Integrations::OneLogin::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include DestroyIntegrationData
  include IntegrationAppSourceImporter
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(credential_id, first_time_flag)
    @starting_time = Time.current
    @onelogin_config = set_read_replica_db { Integrations::OneLogin::Config.find_by_id(credential_id) }
    return unless @onelogin_config

    @current_company = @onelogin_config.company
    @first_time_flag = first_time_flag
    @source = set_read_replica_db { Integration.find_by(name: 'one_login') }
    @company_integration = @onelogin_config.company_integration
    @fetched_users_emails = []
    @app_ids = []

    if client.authenticate?
      pusher(@onelogin_config, true, 0.2)
      save_apps
      pusher(@onelogin_config, true, 0.3)
      save_users

      unless @first_time_flag
        config_users = set_read_replica_db { @onelogin_config.users }
        deleted_users_emails = config_users.pluck(:email) - @fetched_users_emails.flatten.compact if @fetched_users_emails.present?
        remove_deleted_users(config_users, deleted_users_emails, @onelogin_config.user_sources, @onelogin_config.company) if deleted_users_emails.present?
      end

      pusher(@onelogin_config, true, 0.6)
      save_user_apps
      update_integration_status
      save_usage
    end
    intg_duration_analysis(@current_company.id, first_time_flag)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "Insufficient Permission",
      "Authentication Failure",
      "Gateway Time-out",
      "Net::OpenTimeout"
    ]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@onelogin_config, false, 0)
    save_logs
  end

  def save_users
    users_response = client.get_users

    @fetched_users_emails << users_response.map { |user| user.email } unless @first_time_flag

    users_response.each do |user|
      next if user.email.blank?
      one_login_user = set_read_replica_db { Integrations::OneLogin::User.find_or_initialize_by(company_id: @current_company.id, email: user.email) }
      one_login_user.assign_attributes(
                      external_user_id: user.id,
                      created_at: user.created_at,
                      firstname: user.firstname,
                      lastname: user.lastname,
                      open_id_name: user.openid_name,
                      state: user.state,
                      status: user.status,
                      config_id: @onelogin_config.id)

      begin
        one_login_user.save!
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.warn("OneLogin: Duplicate user #{one_login_user.inspect}")
      end
    end
  end

  def save_apps
    client.get_apps&.each do |app|
      one_login_app = set_read_replica_db { Integrations::OneLogin::App.find_or_initialize_by(company_id: @current_company.id, external_app_id: app.id, app_name: app.name) }
      one_login_app.assign_attributes(
                      connector_id: app.connector_id,
                      visible: app.visible,
                      config_id: @onelogin_config.id)

      begin
        one_login_app.save!
        @app_ids << one_login_app.integration_app_id if one_login_app.integration_app_id.present?
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.warn("OneLogin: Duplicate app #{one_login_app.inspect}")
      end
    end

    create_bulk_app_source(@onelogin_config.id, @app_ids, 'one_login')
  end

  def users
    @users ||= set_read_replica_db { Integrations::OneLogin::User.where(company_id: @current_company.id) }
  end

  def save_user_apps
    users.each do |user|
      apps = client.get_user_apps(user.external_user_id)
      apps.each do |app_name|
        existing_app = set_read_replica_db { Integrations::OneLogin::App.find_by(app_name: app_name, company_id: @current_company.id) }
        if existing_app
          Integrations::OneLogin::AppUser.find_or_create_by!(one_login_user_id: user.id, one_login_app_id: existing_app.id)
        end
      end
    end
  end

  def save_usage
    @company_integration = set_read_replica_db { CompanyIntegration.find_by(company_id: @current_company, integration_id: @source.id) }
    @total_users = users.count
    if @first_time_flag
      users.each_with_index do |user, user_index|
        Integrations::OneLogin::SyncUsageWorker.perform_async(@onelogin_config.id, @first_time_flag, user.id, Time.now.as_json, user_index)

        if users.count < 300
          perform_in = 5 * (user_index + 1)
          Integrations::OneLogin::SyncUsageWorker.perform_in(perform_in.seconds, @onelogin_config.id, @first_time_flag, user.id, (Time.now - 1.month).as_json, user_index)
        end
      end
    else
      users.each_with_index do |user, user_index|
        perform_in = 5 * user_index
        Integrations::OneLogin::SyncUsageWorker.perform_in(perform_in.seconds, @onelogin_config.id, @first_time_flag, user.id, Time.now.as_json, user_index)
      end
    end
  end

  def save_logs
    client.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def update_integration_status
    @company_integration.assign_attributes(sync_status: :successful, status: true, active: true)
    @company_integration.save!
    pusher(@onelogin_config, true, 1)
  end

  def client
    @client ||= Integrations::OneLogin::FetchData.new(@onelogin_config, @first_time_flag)
  end
end
