class Integrations::OneLogin::SyncUsageWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(onelogin_config_id, first_time_flag, user_id, date, user_index, retries = 0)
    @onelogin_config = set_read_replica_db { Integrations::OneLogin::Config.find(onelogin_config_id) }
    @first_time_flag = first_time_flag
    @current_company = @onelogin_config.company
    @company_integration = @onelogin_config.company_integration
    @last_user = users.count == (user_index + 1)
    @user = set_read_replica_db { Integrations::OneLogin::User.find(user_id) }
    @api_limit = api_calls_limit
    if @api_limit.remaining > 100
      @source = set_read_replica_db { Integration.find_by(name: 'one_login') }
      @retries = retries
      if client.authenticate?
        create_usage(@user, date) if @user

        if (@last_user)
          @company_integration.assign_attributes(last_synced_at: DateTime.now)
          @company_integration.save!
          usage_sync_pusher(@onelogin_config, 1)
        end
      end
    else
      Integrations::OneLogin::SyncUsageWorker.perform_in(@api_limit.reset.seconds, onelogin_config_id, first_time_flag, user_id, date.as_json, user_index, retries = 0)
    end
  rescue Exception => e
    if retries < 1
      if @onelogin_config
        Integrations::OneLogin::SyncUsageWorker.perform_in(2.hours, @onelogin_config.id, @first_time_flag, @user.id, (Time.now - 2.month).as_json, @last_user, retries + 1)
      end
    elsif @last_user
      @company_integration.assign_attributes(sync_status: :failed,
                                             status: false,
                                             last_synced_at: DateTime.now,
                                             active: true,
                                             error_message: e.message)
      error_messages = [
        "Insufficient Permission"
      ]

      if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
        send_notification(@company_integration)
      end

      @company_integration.save!
      Rails.logger.error(e)
      Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
      pusher(@onelogin_config, false, 0)
      save_logs
    end
  end

  def users
    @users ||= set_read_replica_db { Integrations::OneLogin::User.where(company_id: @current_company.id) }
  end

  def create_usage user, date
    usages = client.get_events user.external_user_id, date
    @total_users = users.count

    usages.each do |use|
      onelogin_app = set_read_replica_db { Integrations::OneLogin::App.find_by(external_app_id: use.app_id, company_id: @current_company) }
      if onelogin_app.present?
        app_usage = nil
        set_read_replica_db do
          intg_app = Integrations::App.find_by(name: onelogin_app.app_name, company_id: @current_company)
          app_usage = Integrations::AppUsage.find_or_initialize_by(app_id: intg_app.id,
                                                                   month: date.to_date.beginning_of_month,
                                                                   company_integration_id: @company_integration.id,
                                                                   source_id: @source.id)
        end
        app_usage.increment(:num_of_users, 1)
        app_usage.total_users = @total_users
        app_usage.save!
      end
    end
  end

  def save_logs
    client.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def api_calls_limit
   client.remaining_api_calls_count
  end

  def client
    @client ||= Integrations::OneLogin::FetchData.new(@onelogin_config, @first_time_flag)
  end
end
