class Integrations::AzureAd::SaveUsersWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include SyncCompanyUserFields
  include ReadReplicaDb
  include NotifyAdminsHelper
  include AzureAdCommonMethods

  attr_accessor :config, :first_time_flag

  sidekiq_options queue: 'integrations'

  def perform(params)
    @starting_time = Time.current
    @config = set_read_replica_db { Integrations::AzureAd::Config.find_by(id: params["config_id"]) }
    @groups = params["groups"]
    @is_resync = params["is_resync"]
    @sync_all_users = params["sync_all_users"]
    @next_page_token = params["next_page_token"]
    @first_time_flag = params["first_time_flag"]
    @current_company_id = params["current_company_id"]
    @excluded_attributes = params["excluded_attributes"]

    @disabled_users = []
    @user_account_status = {}
    @users_departments = {}

    save_users(@next_page_token)

    if @next_page_link&.include?('$skiptoken=')
      trigger_worker
    else
      pusher(config, true, 0.8)
      company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil
      )

      company_integration.save!
      pusher(config, true, 1)
      send_email_to_admins(@current_company_id, 'azure', @disabled_users) if @disabled_users.present?
      intg_duration_analysis(@current_company_id, @first_time_flag)
    end
  rescue Exception => e
    company_integration_failed

    error_messages = [
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request.",
      "Lifetime validation failed, the token is expired.",
      "Email is invalid",
      "UserPrincipal does not have the key ID configured.",
      "The refresh token has expired due to inactivity.",
      "The provided grant has expired due to it being revoked, a fresh auth token is needed."
    ]

    if !@first_time_flag && error_messages.find { |em| e.message.downcase.include?(em.downcase) }
      send_notification(company_integration)
    end

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em.downcase) }
    end
    pusher(config, false, 0)
  end

  def save_users(next_page_token)
    if @groups.present? && !@sync_all_users
      fetch_groups unless first_time_flag
      refresh_access_token unless first_time_flag
      save_groups_members(next_page_token)
      pusher(config, true, 0.4)
    elsif @sync_all_users
      response = client.get_users(next_page_token)
      return unless client.ok?

      users = response.parsed_response["value"]
      return unless users.present?

      pusher(config, true, 0.4)
      user_emails = users.map { |user| user["userPrincipalName"].downcase if user["userPrincipalName"].present? }.compact

      discovered_users = DiscoveredUser.where(company_id: company.id, email: user_emails).index_by(&:email)
      staff_users = company.users.where(email: user_emails).index_by(&:email)

      users.each do |user|
        next if user["userType"] == "Guest" && !config.allow_guest_users

        next unless user['userPrincipalName'].present?

        discovered_user = discovered_users[user['userPrincipalName'].downcase]
        staff_user = staff_users[user['userPrincipalName'].downcase]

        next if discovered_user.blank? && !user_account_enabled?(user)

        save_user(user, discovered_user, staff_user)
      end

      batch_user_integration_locations(users)

      @next_page_link = response.parsed_response["@odata.nextLink"]
    end
  end

  def fetch_groups
    Integrations::AzureAd::FetchGroupsWorker.new.perform(@config.id)
    @groups = @config.group_ids
  end

  def company
    @company ||= set_read_replica_db do
      config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do
      config.company_integration
    end
  end

  def refresh_access_token
    response = client.refresh_token

    if response && response["access_token"] && response["refresh_token"]
      expiry_time = Time.now + (response["expires_in"] - 600)

      config.update!(
        token: response["access_token"],
        expires_in: expiry_time,
        refresh_token: response["refresh_token"],
        skip_callbacks: true)

      config.reload
    end
  end

  def save_groups_members(next_page_token)
    @groups.each do |group|
      Integrations::AzureAd::SaveGroupUsersWorker.perform_async(company.id, group, @config.id, @excluded_attributes, next_page_token, @first_time_flag)
    end
  end

  def batch_user_integration_locations(users)
    return if users.empty?

    address_map = users.index_with do |user|
      [user['streetAddress'], user['city'], user['state'], user['postalCode'], user['country']]
        .compact
        .join(', ')
    end

    existing_locations = set_read_replica_db do
      Integrations::Location
        .where(address: address_map.values, company_id: company.id)
        .pluck(:address)
        .compact_blank
        .to_set
    end

    company_integration_name = set_read_replica_db do
      config.company_integration.integration.name
    end

    new_locations = []

    users.each do |user|
      address_string = address_map[user]
      next if address_string.blank? || existing_locations.include?(address_string)

      new_locations << {
        address: address_string,
        company_id: company.id,
        street_address: @excluded_attributes.include?('street_address') ? nil : user["streetAddress"],
        city: @excluded_attributes.include?('city') ? nil : user["city"],
        state: @excluded_attributes.include?('state') ? nil : user["state"],
        postal_code: @excluded_attributes.include?('postal_code') ? nil : user["postalCode"],
        country: @excluded_attributes.include?('country') ? nil : user["country"],
        source: company_integration_name
      }
    end

    Integrations::Location.insert_all(new_locations) unless new_locations.empty?
  end

  def client
    @azure_ad_service ||= Integrations::AzureAd::FetchData.new(company.id)
  end

  def company_integration_failed
    company_integration.update(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: client.error,
      user_error_message: user_error_message(client.error)
    )
  end

  def user_error_message(error)
    if error&.downcase.to_s.include?("email is invalid")
      return "Please update the invalid email addresses for your discovered users to proceed."
    elsif error&.downcase.to_s.include?("due to a configuration change made by your administrator")
      return "Configuration was changed by your administrator. Please unsync and then sync your integration again."
    end
  end

  def trigger_worker
    params = {
      "groups" => @groups,
      "config_id" => @config.id,
      "is_resync" => @is_resync,
      "sync_all_users" => @sync_all_users,
      "first_time_flag" => @first_time_flag,
      "current_company_id" => @current_company_id,
      "excluded_attributes" => @excluded_attributes,
      "next_page_token" => @next_page_link.split('$skiptoken=')[-1]
    }

    Integrations::AzureAd::SaveUsersWorker.perform_async(params)
  end
end
