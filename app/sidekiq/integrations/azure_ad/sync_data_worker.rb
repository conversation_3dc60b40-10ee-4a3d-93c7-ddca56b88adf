class Integrations::AzureAd::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  attr_accessor :config, :first_time_flag

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, groups=[], sync_all_users=false, excluded_attributes=[], is_resync=false)
    @starting_time = Time.current
    @user_account_status = {}
    @users_departments = {}
    @excluded_attributes = excluded_attributes
    self.config = set_read_replica_db do 
      Integrations::AzureAd::Config.find(config_id)
    end
    @current_company_id = self.config.company_id
    config.update(excluded_attributes: @excluded_attributes)
    self.first_time_flag = first_time_flag
    @groups = groups
    @sync_all_users = sync_all_users
    @is_resync = is_resync
    @disabled_users = []
    refresh_access_token
    pusher(config, true, 0.2)

    params = {
      "groups" => groups,
      "next_page_token" => nil,
      "config_id" => config_id,
      "is_resync" => @is_resync,
      "sync_all_users" => @sync_all_users,
      "first_time_flag" => first_time_flag,
      "current_company_id" => @current_company_id,
      "excluded_attributes" => @excluded_attributes
    }

    Integrations::AzureAd::SaveUsersWorker.perform_async(params)
    Integrations::AzureAd::RevokeDeletedUsersWorker.perform_async(config.id) unless first_time_flag
    intg_duration_analysis(@current_company_id, first_time_flag)
  rescue Exception => e
    company_integration_failed

    error_messages = [
      "invalid_grant",
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request.",
      "Lifetime validation failed, the token is expired.",
      "Email is invalid",
      "UserPrincipal does not have the key ID configured.",
      "The refresh token has expired due to inactivity.",
      "The provided grant has expired due to it being revoked, a fresh auth token is needed."
    ]

    if !@first_time_flag && error_messages.find { |em| e.message.downcase.include?(em.downcase) }
      send_notification(company_integration)
    end

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em.downcase) }
    end
    pusher(config, false, 0)
  end

  def company
    @company ||= set_read_replica_db do 
      config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do 
      config.company_integration
    end
  end

  def refresh_access_token
    response = client.refresh_token

    if response && response["access_token"] && response["refresh_token"]
      expiry_time = Time.now + (response["expires_in"] - 600)

      config.update!(
        token: response["access_token"],
        expires_in: expiry_time,
        refresh_token: response["refresh_token"],
        skip_callbacks: true)

      config.reload
    end
  end

  def client
    @azure_ad_service ||= Integrations::AzureAd::FetchData.new(company.id)
  end

  def company_integration_failed
    company_integration.update(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: client.error,
      user_error_message: user_error_message(client.error)
    )
  end

  def user_error_message(error)
    if error&.downcase.to_s.include?("email is invalid")
      return "Please update the invalid email addresses for your discovered users to proceed."
    elsif error&.downcase.to_s.include?("due to a configuration change made by your administrator")
      return "Configuration was changed by your administrator. Please unsync and then sync your integration again."
    end
  end
end
