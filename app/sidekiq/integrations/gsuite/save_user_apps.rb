class Integrations::Gsuite::SaveUserApps
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  attr_accessor :gsuite

  def perform(gsuite_config_id, first_time_flag, can_sync_detail_data)
    set_read_replica_db do
      @gsuite = Integrations::Gsuite::Config.find_by(id: gsuite_config_id)
      @company_integration = @gsuite.company_integration

      @users = Integrations::Gsuite::User.where(config_id: gsuite_config_id) if can_sync_detail_data || first_time_flag
    end

    @first_time_flag = first_time_flag
    @gsuite_config_id = gsuite_config_id
    if can_sync_detail_data || @first_time_flag
      save_user_apps
      save_user_licenses
    end

    update_integration_status

    Integrations::Gsuite::SaveAppsUsage.perform_async(@gsuite_config_id, @first_time_flag, can_sync_detail_data)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "Unauthorized",
      "forbidden: Not Authorized to access this resource/api",
      "forbidden: Caller does not have access to the customers reporting data.",
      "Authorization failed.",
      "Request had insufficient authentication scopes.",
      "Quota exceeded for quota metric"
    ]

    if !@first_time_flag && error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
      send_notification(@company_integration)
    end

    client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@gsuite, false, 0)
  end

  def save_user_apps
    return unless @users

    app_names = @users.flat_map { |user| client.get_user_apps(user.email)&.items&.map(&:display_text) }.compact.uniq
    existing_apps = set_read_replica_db { Integrations::Gsuite::App.where(config_id: @gsuite_config_id, name: app_names).index_by(&:name) }

    user_apps_data = []
    @users.each do |user|
      user_apps_response = client.get_user_apps(user.email)
      next unless user_apps_response
      user_apps = user_apps_response.items
      next unless user_apps

      user_apps.each do |user_app|
        existing_app = existing_apps[user_app.display_text]
        if existing_app.present?
          user_apps_data << { user_id: user.id, app_id: existing_app.id }
        end
      end
    end

    Integrations::Gsuite::AppUser.insert_all(user_apps_data) if user_apps_data.any?
  end

  def save_user_licenses
    licenses = client.get_gsuite_licenses
    return unless licenses

    user_emails = licenses.flat_map { |license| license.items&.map(&:user_id) }.compact.uniq
    users = set_read_replica_db { Integrations::Gsuite::User.where(email: user_emails, config_id: @gsuite_config_id).index_by(&:email) }

    app_names = licenses.flat_map { |license| license.items&.map(&:sku_name) }.compact.uniq
    apps = set_read_replica_db { Integrations::Gsuite::App.where(config_id: @gsuite_config_id, name: app_names).index_by(&:name) }

    license_data = []
    licenses.each do |license|
      license.items&.each do |item|
        user = users[item.user_id]
        app_license = apps[item.sku_name]
        license_data << { user_id: user.id, app_id: app_license.id } if user && app_license
      end
    end

    Integrations::Gsuite::AppUser.insert_all(license_data) if license_data.any?
  end

  def update_integration_status
    @company_integration.assign_attributes(sync_status: :successful, status:  true, active: true)
    @company_integration.save!
    pusher(@gsuite, true, 1)
  end

  private
  def client
    @client ||= Integrations::Gsuite::FetchData.new(@gsuite.company_integration.company.id, "read") unless gsuite.nil?
  end
end
