class Integrations::Gsuite::SaveAppsUsage
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include GsuiteCommonMethods
  include ReadReplicaDb
  sidekiq_options queue: 'integrations'

  attr_accessor :gsuite

  def perform(gsuite_config_id, first_time_flag, can_sync_detail_data)
    set_read_replica_db do 
      @gsuite = Integrations::Gsuite::Config.find_by(id: gsuite_config_id)
      @company_integration = @gsuite.company_integration
      @current_company = @gsuite.company
      @company_integration_id = @company_integration.id
    end
    @first_time_flag = first_time_flag

    refresh_access_token
    if @first_time_flag
      date = Time.current - 4.months - 4.days

      (0..120).each do |i|
        date = date + 1.day
        formatted_date = date.strftime('%F')
        save_apps_usage(formatted_date)
      end
    elsif can_sync_detail_data
      date = (Time.current - 4.days).strftime('%F')
      save_apps_usage(date)
    end
    save_licensed_usage

    @company_integration.assign_attributes(last_synced_at:  DateTime.now)
    @company_integration.save!
    usage_sync_pusher(@gsuite, 1)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "Unauthorized",
      "forbidden: Caller does not have access to the customers reporting data.",
      "Authorization failed.",
      "Request had insufficient authentication scopes."
    ]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@gsuite, false, 0)
  end

  def save_apps_usage(formatted_date)
    apps_data = client.get_apps_usage(formatted_date)
    apps_array = apps_data&.usage_reports&.first&.parameters&.first&.msg_value
    date = Date.parse(apps_data&.usage_reports&.first&.date).beginning_of_month if apps_data&.usage_reports&.first&.date.present?
    
    total_users_count = nil
    source_id = nil
    integrated_apps = nil

    set_read_replica_db do
      company_id = @current_company.id
      total_users_count = Integrations::Gsuite::User.where(config_id: @gsuite.id).count
      source_id = @gsuite.company_integration.integration.id
      client_names = apps_array&.map { |app| app["client_name"] }
      integrated_apps = Integrations::App.where(name: client_names, company_id: company_id).index_by(&:name)
    end

    apps_array&.each do |gsuite_app|
      integrated_app = integrated_apps[gsuite_app["client_name"]]

      if integrated_app.present?
        integrated_app_usage = Integrations::AppUsage.find_or_initialize_by(
          app_id: integrated_app.id,
          month: date,
          company_integration_id: @company_integration_id)

        integrated_app_usage.assign_attributes(
          num_of_users: gsuite_app["num_users"],
          total_users: total_users_count,
          source_id: source_id)

        integrated_app_usage.save!
      end
    end
  end

  def save_licensed_usage
    licensed_apps, intg_apps = fetch_licensed_apps
    current_month = Time.now.at_beginning_of_month

    licensed_apps.each do |licensed_app|
      intg_app = intg_apps[licensed_app.name]
      next unless intg_app
      licensed_app_usage = Integrations::AppUsage.find_or_initialize_by(
        app_id: intg_app.id,
        month: current_month,
        company_integration_id: @company_integration_id)
        licensed_app_usage.assign_attributes(
          num_of_users: intg_app.meta_data["consumed_licenses"],
          total_users: intg_app.meta_data["total_licenses"])
  
        licensed_app_usage.save!
    end
  end

  def fetch_licensed_apps
    set_read_replica_db do
      licensed_apps = Integrations::Gsuite::App.where(company_id: @current_company.id, app_type: :licensed)
      app_names = licensed_apps.pluck(:name)
      intg_apps = Integrations::App.where(name: app_names, company_id: @current_company.id, app_type: :licensed).index_by(&:name)
      [licensed_apps, intg_apps]
    end
  end

  private
  def client
    @client ||= Integrations::Gsuite::FetchData.new(@gsuite.company_integration.company.id, "read") unless gsuite.nil?
  end
end
