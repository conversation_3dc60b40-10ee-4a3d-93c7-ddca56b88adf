class Integrations::Gsuite::SyncDataWorker
  include LinkDefaultVendor
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include CheckAppLicensesValidation
  include IntegrationsAnalysis
  include DestroyIntegrationData
  include ReadReplicaDb
  include GsuiteCommonMethods

  sidekiq_options queue: 'integrations'

  attr_accessor :gsuite, :source_id

  def perform(gsuite_config_id, first_time_flag)
    @starting_time = Time.current
    set_read_replica_db do
      @gsuite = Integrations::Gsuite::Config.find_by(id: gsuite_config_id)
      @source_id = Integration.find_by(name: 'gsuite').id
      @company_integration = @gsuite.company_integration
      @company_id = @gsuite.company_id
    end
    @gsuite_config_id = gsuite_config_id
    @first_time_flag = first_time_flag
    @fetched_users_emails = []
    refresh_access_token
    pusher(@gsuite, true, 0.2)
    primary_details = save_primary_details

    unless @first_time_flag
      config_users = @gsuite.users
      deleted_users_emails = config_users.pluck(:email) - @fetched_users_emails.flatten.compact if @fetched_users_emails.present?
      remove_deleted_users(config_users, deleted_users_emails, @gsuite.user_sources, @gsuite.company) if deleted_users_emails.present?
    end
    
    refresh_access_token
    save_secondary_details if primary_details
    intg_duration_analysis(@company_id, first_time_flag)
  end

  def save_primary_details
    if @first_time_flag
      date = Time.current - 4.months - 4.days

      (0..120).each do |i|
        date = date + 1.day
        formatted_date = date.strftime('%F')
        save_apps(formatted_date)
      end
    else
      date = (Time.current - 4.days).strftime('%F')
      save_apps(date)
    end
    save_users
    pusher(@gsuite, true, 0.4)
    return true
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    error_messages = [
      "Unauthorized",
      "forbidden: Caller does not have access to the customers reporting data.",
      "Authorization failed.",
      "Request had insufficient authentication scopes.",
      "Account has been deleted"
    ]

    if !@first_time_flag && error_messages.find{|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    @company_integration.save!

    client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@gsuite, false, 0)
    return false
  end

  def save_secondary_details
    save_licenses
    sync_apps
    sync_users
    pusher(@gsuite, true, 0.6)

    Integrations::Gsuite::SaveUserApps.perform_async(@gsuite_config_id, @first_time_flag, can_sync_detail_data?)

    path = Rails.root.join("tmp/#{@company_id}_token.yaml")
    File.delete(path) if File.exist?(path)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "Unauthorized",
      "forbidden: Caller does not have access to the customers reporting data.",
      "Authorization failed.",
      "Request had insufficient authentication scopes.",
      "Quota exceeded for quota metric"
    ]

    if !@first_time_flag && error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
      send_notification(@company_integration)
    end

    client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@gsuite, false, 0)
  end

  def save_licenses
    executed_gsuite_apps = []
    @licenses = client.get_gsuite_licenses
    @licenses&.each do |license|
      license.items&.each do |item|
        gsuite_license = Integrations::Gsuite::App.find_or_initialize_by(
                          config_id: @gsuite_config_id,
                          name: item.sku_name )

        if !executed_gsuite_apps.include?(item.sku_name)
          gsuite_license.meta_data["total_licenses"] = 0 if gsuite_license.meta_data && gsuite_license.meta_data["total_licenses"]
          executed_gsuite_apps.push(item.sku_name)
        end

        lic = gsuite_license.meta_data["total_licenses"] + 1 if gsuite_license.meta_data && gsuite_license.meta_data["total_licenses"]
        gsuite_license.assign_attributes(
          meta_data: {
            product_name: item.product_name,
            sku_id: item.sku_id,
            product_id: item.product_id,
            total_licenses: lic || 1,
            consumed_licenses: lic || 1,
          },
          company_id: @company_id,
          app_type: 1)

        begin
          gsuite_license.save!
        rescue ActiveRecord::RecordNotUnique => e
          Rails.logger.warn("Gsuite: Duplicate app #{gsuite_license.inspect}")
        end
      end
    end
  end

  def save_apps(formatted_date)
    response = client.get_apps(formatted_date)
    gsuite_apps = response&.usage_reports&.first&.parameters&.first&.msg_value

    existing_apps = set_read_replica_db { Integrations::Gsuite::App.where(config_id: @gsuite_config_id).index_by(&:name) }

    gsuite_apps&.each do |gsuite_app|
      if gsuite_app["client_name"].present? && !gsuite_app["client_name"].include?("apps.googleusercontent.com")
        integration_app = existing_apps[gsuite_app["client_name"]] ||
                          Integrations::Gsuite::App.new(config_id: @gsuite_config_id, name: gsuite_app["client_name"])
        integration_app.assign_attributes(
          external_client_id: gsuite_app["client_id"],
          number_users: gsuite_app["num_users"] || 0,
          company_id: @company_id,
          app_type: 0
        )

        begin
          integration_app.save!
        rescue ActiveRecord::RecordNotUnique => e
          Rails.logger.warn("Gsuite: Duplicate app #{integration_app.inspect}")
        end
      end
    end
  end

  def save_users(next_page_token = nil)
    response = client.get_users(next_page_token)
    users_array = response.users

    @fetched_users_emails << users_array.map { |user| user.primary_email } unless @first_time_flag

    existing_users = set_read_replica_db { Integrations::Gsuite::User.where(email: users_array.map(&:primary_email),
                                                                            company_id: @company_id).index_by(&:email) }

    users_array&.each do |gsuite_user|
      integration_user = existing_users[gsuite_user.primary_email] ||
                         Integrations::Gsuite::User.new(email: gsuite_user.primary_email, company_id: @company_id)
      integration_user.assign_attributes(
        external_customer_id: gsuite_user.customer_id,
        name: gsuite_user.name.full_name,
        creation_time: gsuite_user.creation_time,
        is_admin: gsuite_user.is_admin,
        last_login_time: gsuite_user.last_login_time,
        config_id: @gsuite_config_id)

      begin
        integration_user.save!
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.warn("Gsuite: Duplicate user #{integration_user.inspect}")
      end
    end

    if response.next_page_token.present?
      save_users(response.next_page_token)
    end
  end

  def sync_apps
    apps = nil
    company = nil
    existing_apps = nil
    set_read_replica_db do
      apps = Integrations::Gsuite::App.where(config_id: @gsuite_config_id)
      company = @gsuite.company_integration.company
      existing_apps = Integrations::App.where(name: apps.pluck(:name), company_id: company.id)
                                       .index_by { |app| [app.name, app.app_type] }
    end
    apps.each do |app|
      integration_app = existing_apps[[app.name, app.app_type]] ||
                        Integrations::App.new(name: app.name,
                                              company_id: company.id,
                                              app_type: app.app_type)
      return if integration_app.id.present? && check_linked_app_licenses_validation?(integration_app.status, app.meta_data)

      app_product_name = app.meta_data.present? ? app.meta_data['product_name'] : app.name
      prod_name = product_name(app_product_name)
      create_default_vendor if prod_name

      integration_app.assign_attributes(num_of_users: app.number_users,
                                        description: app.external_client_id,
                                        meta_data: app.meta_data,
                                        friendly_name:app.name )
      if !app.meta_data.nil?
        integration_app.assign_attributes(total_users: app.meta_data["total_licenses"],
                                          used: app.meta_data["consumed_licenses"])
      end

      vendor = set_read_replica_db { integration_app.vendor || company.vendors.where(name: app.name).first }
      vendor = @vendor_info if vendor.nil? && prod_name

      if vendor.present?
        integration_app.vendor_id = vendor.id
        integration_app.status = 1
      elsif
        check_default_vendors(integration_app, app.name)
      else
        integration_app.status = 0
      end

      integration_app.save!

      app_source = set_read_replica_db do
        Integrations::AppSource.find_or_initialize_by(app_id: integration_app.id,
                                                      integration_id: @source_id,
                                                      config_id: @gsuite_config_id)
      end
      app_source.save! if app_source.new_record?
    end
  end

  def sync_users
    users = nil
    existing_users = nil
    existing_sources = nil

    set_read_replica_db do
      users = Integrations::Gsuite::User.where(config_id: @gsuite_config_id)
      existing_users = Integrations::User.where(company_id: @company_id, email: users.map(&:email)).index_by(&:email)
      existing_sources = Integrations::UserSource.where(integration_id: @source_id, user_id: existing_users.values.map(&:id)).index_by(&:user_id)
    end

    users.each do |user|
      integration_user = existing_users[user.email] || 
                         Integrations::User.new(email: user.email, company_id: @company_id)

      integration_user.assign_attributes(name: user.name,
                                         last_login_at: user.last_login_time,
                                         user_created_at: user.creation_time)

      integration_user.save!
      integration_source = existing_sources[integration_user.id] ||
                           Integrations::UserSource.new(user_id: integration_user.id, integration_id: @source_id, config_id: @gsuite_config_id)
      integration_source.save!
    end
  end

  private
  def client
    @client ||= Integrations::Gsuite::FetchData.new(@company_id, "read") unless gsuite.nil?
  end

  def can_sync_detail_data?
    holidays? ||  set_read_replica_db { Integrations::Gsuite::User.where(company_id: @company_id).count < 500 }
  end

  def holidays?
    Date.today.saturday? || Date.today.sunday?
  end

  def product_name(prod_name)
    product_info = set_read_replica_db { Integrations::ProductInformation.find_by("lower(product_name) = ? AND source = 'Google'", prod_name.downcase) }
    product_info.present?
  end

  def create_default_vendor
    @vendor_info ||= Vendor.find_or_create_by!(name: 'Google', company_id: @company_id)
  end
end
