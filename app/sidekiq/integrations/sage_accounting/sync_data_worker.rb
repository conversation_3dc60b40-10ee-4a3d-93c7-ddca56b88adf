class Integrations::SageAccounting::SyncDataWorker
  include Sidekiq::Job
  include PrimaryIntegration
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag)
    @starting_time = Time.current
    set_read_replica_db do
      @sage_accounting_config = Integrations::SageAccounting::Config.find(config_id)
      @company_integration = @sage_accounting_config.company_integration
      @current_company = @sage_accounting_config.company
    end

    @primary_integration_flag = set_primary_integration_flag "Integrations::SageAccounting::Config", @current_company
    @first_time_flag = first_time_flag

    refresh_access_token

    pusher(@sage_accounting_config, true, 0.2)
    save_transactions

    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil)
    @company_integration.save!
    pusher(@sage_accounting_config, true, 1)
    intg_duration_analysis(@current_company.id, first_time_flag)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message)

    @company_integration.save!

    error_messages = [
      "Your access token has expired. You can refresh the token or ask the user for a new grant.",
      "AuthorizationFailure",
      "Access denied"
    ]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@sage_accounting_config, false, 0)
    save_logs
  end

  def save_transactions
    company_integration = nil
    set_read_replica_db do
      integration = Integration.find_by(name: 'sage_accounting')
      company_integration = CompanyIntegration.find_by(company_id: @current_company, integration_id: integration.id)
    end

    fetch_transactions = service.transactions @first_time_flag

    fetch_transactions["$items"]&.each do |fetch_transaction|
      response = service.get_transaction(fetch_transaction["id"])

      next if response["date"].to_date > Date.today

      transaction = set_read_replica_db do 
        @current_company.general_transactions.find_or_initialize_by(
          company_id: @current_company.id,
          transaction_id: response["id"],
          company_integration_id: company_integration.id)
      end
      if !transaction.manually_edited
        transaction.assign_attributes(
                      transaction_date: response["date"],
                      amount: response["total_amount"],
                      transaction_type: response["transaction_type"]["id"],
                      primary_category: response["transaction_type"]["displayed_as"],
                      product_name: response["contact"]["displayed_as"])

        map_vendor(transaction , @current_company)
      end
    end
  end

  def map_vendor(transaction, company)
    TransactionMapping.new({transaction: transaction,
                            company: company,
                            current_company_user: company.admin_company_users.first,
                            primary_integration_flag: @primary_integration_flag},
                           source: 'Sage accounting')
                      .match_vendor
  end

  def refresh_access_token
    response = service.refresh_token

    if response && response["access_token"] && response["refresh_token"]
      expiry_time = Time.now + (response["expires_in"])
      refresh_token_expire_time = Time.now + (response["refresh_token_expires_in"])

      @sage_accounting_config.update(
        token: response["access_token"],
        expires_in: expiry_time,
        refresh_token: response["refresh_token"],
        refresh_token_expires_in: refresh_token_expire_time,
        skip_callbacks: true)

      @sage_accounting_config.reload
    end
  end

  def save_logs
    service.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def service
    @sage_accounting_service ||= Integrations::SageAccounting::FetchData.new(@current_company.id)
  end
end
