require 'malloc_trim'

class Integrations::Azure::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb
  include AzureCommonMethods

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag)
    @starting_time = Time.current
    set_read_replica_db do
      @azure_config = Integrations::Azure::Config.find_by(id: config_id)
      @company = @azure_config.company
      @company_integration = @azure_config.company_integration
    end
    @first_time_flag = first_time_flag

    refresh_access_token

    pusher(@azure_config, true, 0.2)
    save_transactions

    intg_duration_analysis(@company.id, first_time_flag)
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message,
                                           user_error_message: user_error_message(e.message))

    @company_integration.save!

    error_messages = [
      "invalid_grant",
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request.",
      "The security token included in the request is invalid.",
      "Authentication failed."
    ]

    if !@first_time_flag && error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
      send_notification(@company_integration)
    end

    azure_client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@azure_config, false, 0) if @first_time_flag
  ensure
    GC.start
    # Call malloc_trim to release unused memory
    MallocTrim.trim
  end

  def save_transactions
    vendor = get_vendor
    product_id = get_product_id(vendor)
    vendor_id = vendor.id
    subscriptions = azure_client.get_subscriptions&.parsed_response['value']
    Integrations::Azure::SaveUsageTransactionsWorker.perform_async(@azure_config.id, subscriptions, vendor_id, product_id, true, nil)
  end

  def get_vendor
    vendor = nil
    default_vendor = nil
    category = nil
    vendors = nil
    set_read_replica_db do
      vendors = @company.vendors
      categories = @company.categories
      vendor = vendors.find_by_name("Azure")
      default_vendor = DefaultVendor.find_by_name("Azure")
      category = categories.where(name: default_vendor.default_category.name).first
      category ||= categories.find_by(name: "Uncategorized")
    end
    vendor ||= vendors.find_or_create_by!(name: default_vendor.name,
                                          default_tags: [default_vendor.tag],
                                          category_id: category.id,
                                          logo_url: default_vendor.logo_url,
                                          is_cloud_platform: default_vendor.is_cloud_platform,
                                          friendly_name: default_vendor.friendly_name)
  end

  def get_product_id(vendor)
    product = nil
    default_product = nil
    set_read_replica_db do
      product = vendor.products.find_by_name("Azure")
      default_product = DefaultProduct.find_by_name("Azure") if product.nil?
    end
    if product.nil?
      product = Product.create!(name: default_product.name,
                                default_product_id: default_product.id,
                                url: default_product.url,
                                vendor_id: vendor.id,
                                company_id: @company.id)
    end
    product.id
  end

  def azure_client
    @azure_service ||= Integrations::Azure::FetchData.new(@company.id, @first_time_flag)
  end

  def user_error_message(message)
    if message.downcase.include?("Authentication failed.".downcase)
      return "Authentication failed, please re-sync the connector."
    end
  end
end
