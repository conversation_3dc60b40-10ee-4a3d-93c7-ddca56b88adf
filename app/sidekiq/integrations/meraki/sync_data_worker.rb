require 'malloc_trim'

class Integrations::Meraki::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include DiscoveredManagedAssetFinder
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include MerakiSyncData
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'integrations'

  def perform(credential_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @starting_time = Time.current
    @asset_count = 0
    @company_user_id = company_user_id

    set_read_replica_db do
      @meraki_config = Integrations::Meraki::Config.find(credential_id)
      @current_company = @meraki_config.company
      @company_integration = @meraki_config.company_integration
      @intg_id = Integration.find_by_name("meraki").id
    end

    @import_target = @meraki_config.import_type
    @new_discovered_assets_count = 0
    @new_managed_assets_count = 0
    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    pusher(@meraki_config ,true, 0.2)
    save_devices
    pusher(@meraki_config ,true, 0.5)
    @log_id = create_or_update_asset_history_log('Meraki')
    Integrations::Meraki::SaveAllClientsWorker.perform_async(credential_id, first_time_flag, is_resyncing, @log_id, @company_user_id)
    pusher(@meraki_config ,true, 0.8)
    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil,
                                           company_user_id: company_user_id,
                                           first_time_flag: first_time_flag, 
                                           failure_count: 0)
    @company_integration.save!
    pusher(@meraki_config, true, 1, {
      new_meraki_discovered_assets: @new_discovered_assets_count,
      new_meraki_managed_assets: @new_managed_assets_count
    })
    intg_duration_analysis(@current_company.id, first_time_flag)
  rescue Exception => e
    create_or_update_asset_history_log('Meraki')
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message,
                                           company_user_id: company_user_id,
                                           first_time_flag: first_time_flag,
                                           failure_count: @company_integration.failure_count + 1)
    @company_integration.save!

    error_messages = [
      "403 - Forbidden", #You don't have permissions to do that.
      "404 - Not Found", #The requested resource doesn't exist
      "401 - Unauthorized", #Incorrect API key
      "Net::ReadTimeout" #Server took too long to respond
    ]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@meraki_config, false, 0)
  ensure
    GC.start
    # Call malloc_trim to release unused memory
    MallocTrim.trim
  end

  def save_devices
    total_devices = []
    if @meraki_config.save_sm_devices
      total_devices = devices + sm_devices
    else
      total_devices = devices
    end

    total_devices&.each do |device_info|
      begin
        discovered_asset = nil
        managed_asset = nil
        display_name = device_info["name"] || device_info["mac"].try(:upcase)
        serial_number = device_info["serialNumber"] || device_info["serial"]
        ip_address = device_info["lanIp"]
        mac_addresses = []
        optional_data = {}
        mac_address = (device_info["mac"].try(:upcase) || device_info['wifiMac'].try(:upcase))
        mac_addresses << mac_address if mac_address_valid? mac_address
        public_ip = device_info["publicIp"]
        gateway = device_gateway(device_info)
        dns = device_dns(device_info)
        wan_ips = device_wan_ips(device_info)
        firmware = device_info['firmware']
        model = device_info['model']
        ssids = device_info['SSIDs']

        if device_info["device_type"] == "sm_device"
          device_manufacturer = ""
          @asset_device_type = :sm_device
        else
          device_manufacturer = "Cisco Meraki"
          @asset_device_type = :device
        end

        #params company, is_probe, serial_number, mac_addresses, display_name, ip_address, manufacturer, is_mac_duplicate
        discovered_asset = find_discovered_asset(@current_company, false, serial_number, mac_addresses, display_name, ip_address, device_manufacturer, false)

        if discovered_asset.blank? && ip_address.present? && serial_number.blank? && mac_addresses.blank?
          set_read_replica_db do
            next if @current_company.discovered_assets.find_by(ip_address: ip_address)
          end
        end

        if discovered_asset.blank?
          discovered_asset = DiscoveredAsset.ready_for_import.new(company_id: @current_company.id)
        end

        display_name_data = display_name.present? ? display_name : discovered_asset.display_name
        mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
        ip_address_data = ip_address.present? ? ip_address : discovered_asset.ip_address
        serial_data = serial_number.present? ? serial_number : discovered_asset.machine_serial_no

        is_lower_precedence = !is_higher_precedence?(**precedence_data(discovered_asset))
        gateway_data = gateway.present? ? gateway : discovered_asset.discovered_assets_hardware_detail&.gateway
        dns_data = dns.present? ? dns : discovered_asset.discovered_assets_hardware_detail&.dns
        public_ip_data = public_ip.present? ? public_ip : discovered_asset.discovered_assets_hardware_detail&.public_ip
        wan_ips_data = wan_ips.present? ? wan_ips : discovered_asset.discovered_assets_hardware_detail&.wan_ips
        firmware_data = firmware.present? ? firmware : discovered_asset.firmware
        model_data = model.present? ?  model : discovered_asset.model
        ssids_data = ssids.present? ? ssids : discovered_asset.discovered_assets_hardware_detail&.ssids

        discovered_asset.assign_attributes(
          display_name: display_name_data,
          asset_type: device_asset_type(device_info),
          mac_addresses: mac_addresses_data,
          ip_address: ip_address_data,
          manufacturer: device_manufacturer,
          machine_serial_no: serial_data,
          source: "meraki",
          os_name: device_info["osName"] || "",
          meraki_network_id: device_info["networkId"],
          discovered_asset_type: @asset_device_type,
          model: model_data,
          firmware: firmware_data,
          optional_details: device_description(device_info),
          lower_precedence: is_lower_precedence
        )

        unless discovered_asset.integration_location&.source == 'probe'
          discovered_asset.integrations_locations_id = refactor_address(device_info["address"])&.id
        end

        if device_info['address']
          discovered_asset.integration_location = set_read_replica_db do
            @current_company.integrations_locations.find_or_initialize_by(source: 'meraki', address: device_info['address'])
          end
          discovered_asset.integration_location.description = device_info['address']
        end

        # params company, serial_number, mac_addresses, display_name, manufacturer
        managed_asset = discovered_asset.managed_asset || find_managed_asset(@current_company, serial_data, mac_addresses_data, display_name_data, device_manufacturer)

        if managed_asset.present?
          discovered_asset.status = :imported
          discovered_asset.managed_asset_id = managed_asset.id
        elsif @import_target == "managed_asset" && device_manufacturer&.downcase.include?('meraki') 
          discovered_asset.status = :imported
        end

        discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if discovered_asset.discovered_assets_hardware_detail.blank?
        discovered_asset.discovered_assets_hardware_detail.assign_attributes(
          public_ip: public_ip_data,
          dns: dns_data,
          wan_ips: wan_ips_data,
          gateway: gateway_data,
          ssids: ssids_data
        )

        is_new = discovered_asset.new_record?
        discovered_asset.save!
        @asset_count += 1 if is_new
        @new_discovered_assets_count += 1 if is_new
        discovered_asset.discovered_assets_hardware_detail.save

        asset_source_data = {
          display_name: display_name,
          asset_type: device_asset_type(device_info),
          mac_addresses: mac_addresses,
          ip_address: device_info["lanIp"] ,
          manufacturer: device_manufacturer,
          machine_serial_no: serial_number,
          source: "meraki",
          os_name: device_info["osName"] || "",
          integrations_locations_id: refactor_address(device_info["address"])&.id,
          meraki_network_id: device_info["networkId"],
          discovered_asset_type: @asset_device_type,
          firmware: device_info["firmware"]
        }
        add_source(discovered_asset, asset_source_data)
        discovered_asset.reload

        if @import_target == "managed_asset" && device_manufacturer&.downcase.include?('meraki') && discovered_asset.managed_asset_id.blank?
          assets = Array.wrap(discovered_asset)
          BulkDiscoveredAssetsUpdate.new(assets, discovered_asset.company, nil).import_assets
          @new_managed_assets_count += 1
        end
      rescue => e
        Rails.logger.error(e)
        Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
        LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, discovered_asset.to_json, "save_devices").to_json)
      end
    end
  end
end
