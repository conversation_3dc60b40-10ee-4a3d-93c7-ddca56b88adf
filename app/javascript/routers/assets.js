import { mspRoutingMiddleware} from 'mixins/msp_helper';
import Dashboard from '../components/assets/dashboard.vue';
import NewAsset from '../components/assets/new_asset.vue';
import Assets from '../components/assets/assets.vue';
import PeopleAssets from '../components/assets/people_assets/assets.vue';
import DiscoveryTools from '../components/assets/discovery_tools/discovery_tools.vue';
import Probes from '../components/assets/discovery_tools/probes.vue';
import Logs from '../components/assets/discovery_tools/logs.vue';
import Agents from '../components/assets/discovery_tools/agents.vue';
import DiscoveredAssets from '../components/assets/discovered_assets/assets.vue';
import AdminDiscoveredAssets from '../components/assets/discovered_assets/admin_assets.vue';
import DiscoveredAssetShow from '../components/assets/discovered_assets/show_asset.vue';
import AccessDenied from '../components/help_tickets/access_denied.vue';
import AssetShow from '../components/assets/asset_show.vue';
import EditAsset from '../components/assets/edit_asset.vue';
import Overview from '../components/assets/discovery/overview.vue';
import Agent from '../components/assets/discovery/agent.vue';
import DiscoveryMenu from '../components/assets/discovery/discovery_menu.vue';
import NetworkProbe from '../components/assets/discovery/network_probe.vue';
import SelfOnboarding from '../components/assets/discovery/self_onboarding.vue';
import CustomizeAssetTypes from '../components/assets/customize/asset_types/index.vue';
import ImportAssets from '../components/assets/import_assets.vue';
import LifecycleManagement from '../components/assets/customize/lifecycle_management/index.vue';
import TableData from '../components/assets/customize/table_data/index.vue';
import DiscAssetTableData from '../components/assets/discovered_assets/discovered_asset_table_data.vue';
import CardData from '../components/assets/customize/card_data/index.vue';
import General from '../components/assets/show_page_elements/general.vue';
import SourcesDetails from '../components/assets/sources.vue';
import DiscoveredAssetGeneral from '../components/assets/discovered_assets/general.vue';
import DiscoveredAssetSoftwareDetails from '../components/assets/discovered_assets/software.vue';
import DiscoveredAssetDetails from '../components/assets/discovered_assets/discovered_asset_details.vue';
import DiscoveryLogXmlView from '../components/assets/discovery_log_xml_view.vue';
import ProbeDiscoveryDetails from '../components/assets/probe_discovery_details.vue';
import DiscoveredAssetLogsList from '../components/assets/discovered_assets/asset_logs_list.vue';
import CurrentConnections from '../components/assets/discovery_tools/current_connections.vue';
import {
  savePreviousScroll,
  scrollBehavior,
} from './mixins/scroll_behavior_mixin';
import sanitizeRoute from './mixins/sanitize_route_mixin';
import { trackRouteChange } from './mixins/route_tracking_mixin';
import CloudAssetAttributes from '../components/assets/cloud_asset_attributes.vue';
import Insights from '../components/assets/insights.vue';
import RiskCenter from '../components/assets/risk_center_360.vue';
import AutomatedTasks from '../components/automated_tasks/index.vue';
import Analytics from '../components/assets/analytics.vue';
import QRCodeSettings from '../components/assets/customize/qr_code/index.vue';
import Statuses from '../components/assets/customize/statuses/index.vue';
import Tags from '../components/assets/customize/tags/index.vue';

const router = new VueRouter({
  routes: [
    {
      name: 'access-denied',
      path: '/denied',
      component: AccessDenied,
    },
    {
      name: 'new',
      path: '/new',
      component: NewAsset,
    },
    {
      name: 'assets',
      path: '/assets',
      component: Assets,
    },
    {
      name: 'people_assets',
      path: '/people_assets/assigned',
      component: PeopleAssets,
      alias: '/people_assets',
      beforeEnter: (to, from, next) => {
        if (to.path === '/people_assets/') {
          next('/people_assets/assigned');
        }
        next();
      },
    },
    {
      name: 'unassigned_assets',
      path: '/people_assets/unassigned',
      component: PeopleAssets,
    },
    {
      name: 'archived_assets',
      path: '/people_assets/archived',
      component: PeopleAssets,
    },
    {
      name: 'admin_assets',
      path: '/discovered_assets/admin',
      component: AdminDiscoveredAssets,
    },
    {
      name: 'unrecognized',
      path: '/discovered_assets/unrecognized',
      component: DiscoveredAssets,
    },
    {
      name: 'ready_for_import',
      path: '/discovered_assets/ready_for_import',
      component: DiscoveredAssets,
      alias: '/discovered_assets',
      beforeEnter: (to, from, next) => {
        if (to.path === '/discovered_assets/') {
          next('/discovered_assets/ready_for_import');
        }
        next();
      },
    },
    {
      name: 'incomplete',
      path: '/discovered_assets/incomplete',
      component: DiscoveredAssets,
    },
    {
      name: 'imported',
      path: '/discovered_assets/imported',
      component: DiscoveredAssets,
    },
    {
      name: 'ignored',
      path: '/discovered_assets/ignored',
      component: DiscoveredAssets,
    },
    {
      name: 'logs',
      path: '/discovered_assets/logs',
      component: DiscoveredAssetLogsList,
    },
    {
      name: 'discovered_asset_show',
      path: '/discovered_assets/:id',
      component: DiscoveredAssetShow,
      children: [
        {
          path: '/',
          component: DiscoveredAssetGeneral,
          name: 'asset_general',
        },
        {
          path: 'cloud_asset_attributes',
          component: CloudAssetAttributes,
          name: 'discovered_asset_cloud_attributes',
        },
        {
          path: 'software',
          component: DiscoveredAssetSoftwareDetails,
          name: 'asset_software',
        },
        {
          path: 'details',
          component: DiscoveredAssetDetails,
          name: 'asset_details',
        },
        {
          path: 'sources',
          component: SourcesDetails,
          name: 'discovered_asset_sources',
        },
        {
          path: 'discovery_details',
          component: ProbeDiscoveryDetails,
          name: 'discovery_details',
        },
        {
          path: 'asset_discovery_xml_scans',
          component: DiscoveryLogXmlView,
          name: 'asset_discovery_xml_scans',
        },
      ],
    },
    {
      path: '/discovered_assets',
      component: DiscoveredAssets,
    },
    {
      name: 'discovery_tools',
      path: '/discovery_tools',
      component: DiscoveryTools,
      children: [
        {
          name: 'agents',
          path: '/discovery_tools/agents',
          component: Agents,
        },
        {
          path: '',
          beforeEnter: (to, from, next) => {
            next('/discovery_tools/connections');
          },
        },
        {
          name: 'probes',
          path: '/discovery_tools/probes',
          component: Probes,
        },
        {
          name: 'logs',
          path: '/discovery_tools/logs',
          component: Logs,
        },
        {
          name: 'connections',
          path: '/discovery_tools/connections',
          component: CurrentConnections,
        },
        {
          name: 'connectors',
          path: '/discovery_tools/connectors',
          component: Overview,
          children: [
            {
              path: '/',
              name: 'connectors',
              component: DiscoveryMenu,
            },
            {
              name: 'agent',
              path: '/discovery_tools/connectors/agent',
              component: Agent,
            },
            {
              name: 'network_probe',
              path: '/discovery_tools/connectors/network_probe',
              component: NetworkProbe,
            },
            {
              name: 'self_onboarding',
              path: '/discovery_tools/connectors/self_onboarding',
              component: SelfOnboarding,
            },
          ],
        },
      ],
    },
    {
      name: 'edit',
      path: '/:id/edit',
      component: EditAsset,
    },
    {
      name: 'import_assets',
      path: '/import_assets',
      component: ImportAssets,
    },
    {
      name: 'settings',
      path: '/settings',
      component: CustomizeAssetTypes,
    },
    {
      name: 'lifecycle_management',
      path: '/settings/lifecycle_management',
      component: LifecycleManagement,
    },
    {
      name: 'asset_types',
      path: '/settings/asset_types',
      component: CustomizeAssetTypes,
    },
    {
      name: 'statuses',
      path: '/settings/statuses',
      component: Statuses,
    },
    {
      name: 'tags',
      path: '/settings/tags',
      component: Tags,
    },
    {
      name: 'table_data',
      path: '/settings/table_data',
      component: TableData,
    },
    {
      name: 'discovered_asset_table_data',
      path: '/settings/discovered_asset_table_data',
      component: DiscAssetTableData,
    },
    {
      name: 'card_data',
      path: '/settings/card_data',
      component: CardData,
    },
    {
      name: 'qr_code',
      path: '/settings/qr_code',
      component: QRCodeSettings,
    },
    {
      name: 'insights',
      path: '/insights',
      component: Insights,
    },
    {
      name: 'risk_center',
      path: '/risk_center',
      component: RiskCenter,
    },
    {
      name: 'automated_tasks',
      path: '/automated_tasks',
      component: AutomatedTasks,
    },
    {
      name: 'analytics',
      path: '/analytics',
      component: Analytics,
    },
    {
      name: 'show',
      path: '/:id',
      component: AssetShow,
      children: [
        {
          path: '/',
          component: General,
          name: 'general',
        },
      ],
    },
    {
      path: '/',
      component: Dashboard,
      name: 'dashboard',
    },
  ],
  scrollBehavior,
  hashbang: false,
  mode: 'history',
  base: '/managed_assets',
});

// Toggle visibility of management view box
router.beforeEach((to, from, next) => mspRoutingMiddleware(to, from, next, 'assets'));

router.beforeEach(savePreviousScroll);
router.beforeEach((to, from, next) => sanitizeRoute(to, from, next));
router.beforeResolve((to, from, next) => trackRouteChange(to, from, next, 'assets'));

export default router;
