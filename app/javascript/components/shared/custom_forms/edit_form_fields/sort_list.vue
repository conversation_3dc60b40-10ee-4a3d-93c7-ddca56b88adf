<template>
  <div
    v-if="field.type == 'sort_list'"
    class="row mt-2"
  >
    <div class="col-md-5">
      {{ field.label }}
    </div>
    <div
      v-if="sortListVal"
      class="col-md-7 d-flex"
    >
      <div
        v-for="(opt, index) in options"
        :key="index"
        class="d-flex align-items-center col-6 px-0"
      >
        <input
          :id="`sort-preference-${uniqueName}-${index}`"
          type="radio"
          :name="`sort-preference-${uniqueName}`"
          :value="opt"
          :checked="internalValue === opt"
          :class="{'margin-left': index >= 1}"
          @input="onInput(opt)"
        >
        <label
          :for="`sort-preference-${uniqueName}-${index}`"
          class="mb-0 font-weight-normal ml-1"
        >
          {{ opt }}
        </label>
      </div>
    </div>
  </div>
</template>

<script>
  import customFormHelper from "mixins/custom_form_helper";

  export default {
    mixins: [customFormHelper],
    props: {
      field: {
        type: Object,
        required: true,
      },
      sortList: {
        required: false,
        default: () => ["Ascending", "First Name, Last Name"],
      },
      fieldName: {
        required: true,
        default: () => "",
      },
    },
    data() {
      return {
        optionsAvaliable: false,
        internalValue: this.sortList[0],
        uniqueName: this.fieldName,
      };
    },
    computed: {
      sortListVal() {
        return this.sortList;
      },
      options() {
        if (typeof this.field.options === 'string') {
          return JSON.parse(this.field.options);
        }
        return this.field.options;
      },
    },
    watch: {
      sortList(newVal) {
        const val = newVal[0];
        this.internalValue = val;
      },
    },
    methods: {
      onInput(val) {
        this.internalValue = val;
        const idx = this.options.findIndex(opt => opt === val);
        if (idx !== -1) {
          this.$emit("change-preference", this.options[idx]);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .margin-left {
    margin-left: 0.75rem;
  }
</style>
