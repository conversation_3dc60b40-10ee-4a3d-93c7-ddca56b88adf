<template>
  <span ref="originalContext">
    <Teleport to="body">
      <div
        ref="dropdown"
        class="basic-dropdown-wrap clearfix"
        :class="{ 
          'assignee-filter-grid' : isFiltersView,
          'conditions-index' : shouldApplyIndexClass,
        }"
      >
        <div
          v-if="showDropdown"
          class="basic-dropdown qr-modal"
          :class="[
            classProp,
            { 'basic-dropdown-content--full-height': isFullHeight },
            { 'basic-dropdown-content--above': isAbove }
          ]"
          :style="dropdownStyle"
        >
          <div
            ref="dropdownContent"
            class="basic-dropdown-content"
            :class="{ 
              'filter-view-margin-left' : isFiltersView,
              'filter-view-margin-top' : isHeightNotAvailable,
            }"
          >
            <slot />
          </div>
        </div>
      </div>
    </Teleport>
  </span>
</template>

<script>
import Teleport from "vue2-teleport";

export default {
  components: {
    Teleport,
  },
  props: {
    showDropdown: {
      type: <PERSON>olean,
      default: false,
    },
    dynamicContent: {
      type: Boolean,
      default: false,
    },
    dropdownHeight: {
      type: String,
      default: "",
    },
    position: {
      type: String,
      default: "bottom-right",
    },
    top: {
      type: Number,
      default: 0,
    },
    left: {
      type: Number,
      default: 0,
    },
    right: {
      type: Number,
      default: 0,
    },
    isFiltersView: {
      type: Boolean,
      default: false,
    },
    /* 
      By default this component assumes the wrapper element is the immediate parent node.
      E.g.:
        <template>
          <div class="this-is-the-assumed-wrapper">
            <basic-dropdown />
          </div>
        </template>

      If using this with a more complicated component, you can 
      manually pass in a named $ref from the parent component.
      E.g.:
        <template>
          <div class="assumed-wrapper-but-I-don't-want-it-to-be">
            <div ref="intededWrapper">
              <div class="foo">
                <basic-dropdown wrapper-element-ref="intededWrapper" />
              </div>
              <div class="bar">
                <basic-dropdown wrapper-element-ref="intededWrapper" />
              </div>
            </div>
          </div>
        </template>
      Manually pass in the wrapper element ref whenever the default mode doesn't work.
    */
    wrapperElementRef: {
      type: String,
      required: false,
      default: null,
    },
    isSurveyModule: {
      type: Boolean,
      default: false,
    },
    isCheckListModule: {
      type: Boolean,
      default: false,
    },
    isVendorModule: {
      type: Boolean,
      default: false,
    },
    isAssetModule: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      parentEl: null,
      classProp: "",
      parentPositions: {}, // Expects { left, right, top, bottom }
      dropdownTransform: "",
      visuallyShowDropdown: false,
      isFullHeight: false,
      isAbove: false,
      actualContentHeight: 0,
      isAboveThreshold: false,
      resizeObserver: null,
    };
  },
  computed: {
    dropdownStyle() {
      if (this.dynamicContent) {
        if (!this.dropdownHeight) {
          throw new Error('The dropdownHeight prop needs to be set when dynamicContent is set to true.');
        }

        // If the dyanmic content height is less than the passed in height 
        // then a `max-height` css will be determined by the actual content size,
        return `${this.dropdownTransform} height: ${this.dropdownHeight};`;
      }

      if (this.top) {
        return `${this.dropdownTransform} top: ${this.top}px !important;`;
      }

      if (this.maxWidth > 0) {
        return `${this.dropdownTransform} min-width: ${this.maxWidth}rem !important;`;
      }

      return this.dropdownTransform;
    },
    isHeightNotAvailable() {
      return this.isFiltersView && this.isAboveThreshold;
    },
    shouldApplyIndexClass() {
      return this.isSurveyModule || this.isCheckListModule || this.isVendorModule || this.isAssetModule;
    },
  },
  watch: {
    showDropdown(isVisible) {
      if (isVisible) {
        document.addEventListener('click', this.onOutsideClick, true);
        this.setParentEl();

        if (this.dynamicContent) {
          this.$nextTick(() => {
            const contentSizeObserver = new ResizeObserver(entries => {
              if (entries[0]?.contentRect?.height) {
                this.actualContentHeight = entries[0].contentRect.height;
                this.positionDropdown();
              }
            });
            contentSizeObserver.observe(this.$refs.dropdownContent, { box: 'border-box' });
          });
        } else {
          this.positionDropdown();
        }
      }
    },
  },
  created() {
    document.addEventListener('click', this.onOutsideClick, true);
  },
  mounted() {
    this.classProp = this.$el.classList.toString();
    this.$el.removeAttribute("class");
    this.setParentEl();
    this.positionDropdown();
    this.initResizeObserver();
  },
  destroyed() {
    document.removeEventListener('click', this.onOutsideClick, true);
    this.destroyResizeObserver();
  },
  methods: {
    initResizeObserver() {
      this.resizeObserver = new ResizeObserver(this.positionDropdown);
      if (this.parentEl) {
        this.resizeObserver.observe(this.parentEl);
      }
    },
    destroyResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },
    onOutsideClick(clickEvent) {
      if (this.$refs?.dropdown?.contains(clickEvent.target)) {
        return;
      }
      this.$emit('on-blur', clickEvent);
    },
    setParentEl() {
      // Default is to use the immediate parent node from the original context (before <Teleport> does its work)
      let parentEl;
      
      if (this.wrapperElementRef) {
        // Checks if the ref is from a v-for array
        if (/\[[0-9]+\]/g.test(this.wrapperElementRef)) {
          const [, refName, index] = this.wrapperElementRef.match(/(.*)\[([0-9]+)\]/);
          // Doubles the parent check ($parent.$parent) in cases where the $parent context changes (sweet-modal, teleport, etc.)
          parentEl = this.$parent.$refs?.[refName]?.[index] || this.$parent.$parent.$refs?.[refName]?.[index];
        
        } else {
          // Doubles the parent check ($parent.$parent) in cases where the $parent context changes (sweet-modal, teleport, etc.)
          parentEl = this.$parent.$refs?.[this.wrapperElementRef] || this.$parent.$parent.$refs?.[this.wrapperElementRef];
        }
      }

      if (!parentEl) {
        parentEl = this.$refs.originalContext.parentNode;
      }

      this.parentEl = parentEl;
    },
    positionDropdown() {
      this.parentPositions = this.parentEl.getBoundingClientRect();
      this.resetTransform();
      this.$nextTick(() => {
        this.setDropdownTransform();
      });
    },
    setDropdownTransform() {
      const { dropdown } = this.$refs;
      if (!dropdown) {
        return;
      }

      const navbarHeight = 3 * parseFloat(getComputedStyle(document.documentElement).fontSize);
      const dropdownPositions = dropdown.getBoundingClientRect();
      const { parentPositions } = this;
      const lowestY = window.innerHeight - (24 + navbarHeight); // Keeps a little space below for readability
      const farthestX = window.innerWidth;
      const dropdownWidth = dropdownPositions.width;
      const dropdownHeight = dropdownPositions.height;
      let maxWidth = farthestX;
      let maxHeight = lowestY;
      let contentHeight = dropdownHeight;
      let xPos = 0;
      let yPos = 0;

      // When this.dynamicContent is true, the resizeObserver will set `actualContentHeight` 
      // to prevent the content from being larger than necessary
      if (this.actualContentHeight && dropdownHeight > this.actualContentHeight) {
        contentHeight = this.actualContentHeight;
        maxHeight = contentHeight;
      }

      if (dropdownWidth > farthestX) {
        xPos = 0;
      } else if (parentPositions.left + dropdownWidth > farthestX) {
        xPos = parentPositions.right - dropdownWidth;
      } else if ((parentPositions.left + dropdownWidth < farthestX) && this.position === 'bottom-left') {
        xPos = parentPositions.left  - dropdownWidth;
      } else {
        xPos = parentPositions.left;
      }

      if (this.right) { xPos += this.right; }
      if (this.left) { xPos -= this.left; }

      // To avoid unnecessary bottom to top jumps as content loads, we still check the dropdown height, 
      // which should be explicit when this.dynamicContent is true
      if (dropdownHeight > lowestY) {
        yPos = navbarHeight;
        this.isFullHeight = true;
      } else if (parentPositions.bottom + dropdownHeight > lowestY) {
        yPos = Math.max(parentPositions.top - contentHeight, navbarHeight); // Ensure it can't be higher than the top
        this.isAbove = true;
      } else {
        yPos = parentPositions.bottom;
        maxHeight += navbarHeight;
      }

      // avoiding subpixels for better rendering
      xPos = Math.round(xPos);
      yPos = Math.round(yPos);
      maxHeight = Math.round(maxHeight);
      maxWidth = Math.round(maxWidth);

      // We set max-height and max-width explicitly here, rather than with the more simple 100vh/vw,
      // in order to properly calculate the positioning. A preset max-height of 100vh will, by definition,
      // never be larger than the screen, and positioning will be incorrectly calculated in the above logic.
      this.dropdownTransform = `
        transform: translate(${xPos}px, ${yPos}px);
        max-height: ${maxHeight}px;
        max-width: ${maxWidth}px;
      `;

      // **Wait for the dropdown to render before checking overlap**
      this.$nextTick(() => {
        if (this.isOverlappingModal()) {
          // Only move if overlapping modal
          xPos = parentPositions.left - dropdownWidth - 11;

          this.dropdownTransform = `
            transform: translate(${xPos}px, ${yPos}px);
            max-height: ${maxHeight}px;
            max-width: ${maxWidth}px;
          `;
        }
      });
    },
    isOverlappingModal() {
      return this.isOverlapping(".basic-dropdown", ".quick-view-container");
    },
    isOverlapping(elementSelector, modalSelector) {
      const element = document.querySelector(elementSelector);
      const modal = document.querySelector(modalSelector);
      if (!element || !modal) return false;

      const elementRect = element.getBoundingClientRect();
      const modalRect = modal.getBoundingClientRect();

      return (
        elementRect.right > modalRect.left &&
        elementRect.left < modalRect.right &&
        elementRect.bottom > modalRect.top &&
        elementRect.top < modalRect.bottom
      );
    },
    resetTransform() {
      this.isFullHeight = false;
      this.isAbove = false;
      this.dropdownTransform = `
        transform: translate(0,0);
        max-height: none;
        max-width: none;
      `;
    },
    emitClose() {
      this.$emit('on-close');
    },
    checkHeight(isAboveThreshold) {
      this.isAboveThreshold = isAboveThreshold;
    },
  },
};
</script>

<style lang="scss" scoped>
  .basic-dropdown-wrap {
    left: 0;
    position: fixed;
    top: 0;
    z-index: 121;
  }

  .basic-dropdown {
    border: 0 !important;
    box-shadow: map-get($semantic-shadows, 'top');
    display: block !important;
    left: 0 !important;
    position: relative !important;
    transition: none;
    top: 0 !important;
    visibility: visible !important;
  }

  .assignee-filter-grid {
    z-index: 20;
  }

  .filter-view-margin-left {
    margin-left: -1.2rem;
  }

  .filter-view-margin-top {
    margin-top: 3.2rem;
  }

  .conditions-index {
    z-index: 121;
  }
  
  @media (max-width: 768px) {
    .basic-dropdown {
      width: 80%!important;
      height: 50% !important;
    }
  }
</style>
