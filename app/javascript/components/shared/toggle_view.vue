<template>
  <div
    v-if="isFeed"
    class="readable-length--large"
    :class="[additionalClasses, { 'position-relative': disableArticles }]"
  >
    <div 
      v-if="disableArticles"
      class="box--disabled-overlay"
    />
    <slot name="feed" />
  </div>

  <div 
    v-else
    class="toggled-view-wrapper"
    :class="{ 'box box--display-container box--display-height border border-themed-moderate-lighter': useContainedLayout }"
  >
    <div 
      v-if="headerSlotProvided"
      :class="{ 'box__header': useContainedLayout }"
    >
      <slot name="header" />
    </div>

    <div
      v-if="subHeaderSlotProvided"
      class="box__subheader"
      :class="{ 'pb-0': includeSubHeaderSeparator }"
    >
      <slot name="subHeader" />
      <hr 
        v-if="includeSubHeaderSeparator"
        class="mt-3 mx-2 mb-0"
      >
    </div>

    <div
      v-if="emptyDataSlotProvided && dataCount <= 0"
    >
      <slot name="emptyData" />
    </div>

    <div :class="{ 'box__inner': useContainedLayout }">
      <template v-if="isList && dataCount > 0">
        <div
          v-if="isScrollable"
          :class="{ 
            'p-0 scrollable-table--modal': isModalList,
            'insights-list-width': isAssetsInsights,
            'accomodate-inner-box-scrolling-content': isContainedTable,
            'position-relative': disableArticles,
          }"
          :data-tc-view-hd-list="isList"
        >
          <div 
            v-if="disableArticles"
            class="box--disabled-overlay"
          />
          <!-- Note: While all contained tables will have some x-axis padding, only the box contained layout sets the left position to 24px -->
          <scrollable-table
            :key="refreshKey"
            class="mb-0 position-relative"
            :scroll-left-threshold="useContainedLayout ? 24 : 0"
            :class="{ 'accomodate-inner-box-scrolling-content': isContainedTable }"
            :min-width="minimumWidth"
            :settings-path="settingsPath"
            :is-contained-table="isContainedTable"
            :is-asset-list-view-table = "assetListViewTable"
            :table-class="`${tableClass} table not-as-small mb-0`"
            :settings-class="settingsStyle"
            :is-loading="isLoading"
            :show-settings-icon="showSettingsIcon"
            :can-reset-widths="canResetWidths"
            :is-assets-insights="isAssetsAnalytics"
            @custom-columns-updated="$emit('analytics-columns-updated')"
          >
            <slot name="list" />
          </scrollable-table>
        </div>
        <div v-else>
          <div
            class="mobile-view"
            :class="additionalClasses"
          >
            <div>
              <table
                class="table table-striped"
                style="table-layout: fixed"
              >
                <slot name="list" />
              </table>
            </div>
          </div>
        </div>
      </template>

      <div
        v-show="isGrid"
        ref="gridViewWrap"
        class="grid-view-wrapper"
        :class="{ 'px-4 py-3 overflow-auto': useContainedLayout }"
      >
        <div
          class="mobile-view"
          :class="[
            additionalClasses, 
            { 'row': useFlexClasses, 'position-relative': disableArticles }
          ]"
        >
          <div 
            v-if="disableArticles"
            class="box--disabled-overlay"
          />
          <slot name="grid" />
        </div>
      </div>

      <div
        v-show="isSplitPane"
        class="mobile-view"
        :class="[
          additionalClasses, 
          { 
            'row': useFlexClasses, 
            'px-4 py-3 accomodate-inner-box-scrolling-content': useContainedLayout 
          }
        ]"
      >
        <slot name="splitPane" />
      </div>

      <div
        v-show="isGroupedList"
        class="mobile-view"
        :class="[additionalClasses, { 'row': useFlexClasses, 'px-4 pt-4 pb-2 overflow-auto': isHelpdesk }]"
      >
        <slot name="groupedList" />
      </div>

      <div
        v-show="isKanban"
        ref="kanbanViewWrap"
        class="mobile-view kanban-board"
        :class="{ 'p-4 overflow-auto': useContainedLayout }"
      >
        <slot name="kanban" />
      </div>

      <slot name="additionalContent" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ScrollableTable from './scrollable_table.vue';

export default {
  components: { ScrollableTable },
  props: {
    view: {
      type: String,
      default: null,
    },
    tableType: {
      type: [String, Array],
      default: null,
    },
    tableClass: {
      type: String,
      default: "table-striped",
    },
    settingsStyle: {
      type: String,
      required: false,
      default: "",
    },
    minWidth: {
      type: String,
      default: "85rem",
    },
    isModalList: {
      type: Boolean,
      default: false,
    },
    settingsPath: {
      type: String,
      required: false,
      default: null,
    },
    additionalClasses: {
      type: [Object, String],
      default: null,
    },
    isLoading: {
      type: Boolean,
      required: false,
      default: false,
    },
    dataCount: {
      default: 10,
      type: Number,
      required: false,
    },
    useContainedLayout: {
      type: Boolean,
      default: false,
      required: false,
    },
    assetListViewTable: {
      type: Boolean,
      default: false,
      required: false,
    },
    useFlexClasses: {
      type: Boolean,
      requred: false,
      default: true,
    },
    showSettingsIcon: {
      type: Boolean,
      required: false,
      default: true,
    },
    canResetWidths: {
      type: Boolean,
      default: false,
    },
    isAssetsInsights: {
      type: Boolean,
      required: false,
      default: false,
    },
    isAssetsAnalytics: {
      type: Boolean,
      required: false,
      default: false,
    },
    includeSubHeaderSeparator: {
      type: Boolean,
      default: true,
    },
    isHelpdesk: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      refreshKey: 0,
    };
  },
  computed: {
    ...mapGetters(['disableArticles']),
    isScrollable() {
      return this.tableType?.toString().includes("scrollable");
    },
    isContainedTable() {
      return this.useContainedLayout || this.tableType?.toString().includes("contained");
    },
    isList() {
      return this.view === "list" || this.view === 'splitList';
    },
    isGrid() {
      return this.view === "grid";
    },
    isFeed() {
      return this.view === "feed";
    },
    isKanban() {
      return this.view === "kanban";
    },
    isGroupedList() {
      return this.view === "groupedList";
    },
    isSplitPane() {
      return this.view === "splitPane";
    },
    minimumWidth() {
      return this.minWidth;
    },
    headerSlotProvided() {
      return this.$slots.header;
    },
    subHeaderSlotProvided() {
      return this.$slots.subHeader;
    },
    emptyDataSlotProvided() {
      return this.$slots.emptyData;
    },
  },
  watch: {
    isLoading(newLoadingVal) {
      if (!newLoadingVal) {
        this.refreshKey += 1;
      }
    },
  },
  // mounted() {
  //   this.setUpSimpleBar("gridViewWrap");
  //   this.setUpSimpleBar("kanbanViewWrap");
  // },
  // methods: {
  //   setUpSimpleBar(refName) {
  //     this.$nextTick(() => {
  //       requestAnimationFrame(() => {
  //         if (SimpleBar != null && this.$refs[refName] && typeof(this.$refs[refName]) === "object" ) {
  //           this.simpleBar = new SimpleBar(this.$refs[refName], { autoHide: false });
  //         }
  //       });
  //     });
  //   },
  // },
};
</script>

<style lang="scss" scoped>
  .mobile-view {
    @media($max: $medium) {
      margin: 0;
    }
  }

  .insights-list-width {
    max-width: 1400px;
  }

  // TODO: Scope to contained and scrollable tables - current CSS assumes all toggled view list items will be the scrollable contained table, which will not always be the case
  .toggled-view-wrapper {
    :deep(.fade-right:before) {
      background: linear-gradient(90deg, rgb(0% 0% 0% / 0) 0%, rgb(0% 0% 0% / 0.00006103515625) 6.25%, rgb(0% 0% 0% / 0.00048828125) 12.5%, rgb(0% 0% 0% / 0.00164794921875) 18.75%, rgb(0% 0% 0% / 0.00390625) 25%, rgb(0% 0% 0% / 0.00762939453125) 31.25%, rgb(0% 0% 0% / 0.01318359375) 37.5%, rgb(0% 0% 0% / 0.02093505859375) 43.75%, rgb(0% 0% 0% / 0.03125) 50%, rgb(0% 0% 0% / 0.04449462890625) 56.25%, rgb(0% 0% 0% / 0.06103515625) 62.5%, rgb(0% 0% 0% / 0.08123779296875) 68.75%, rgb(0% 0% 0% / 0.10546875) 75%, rgb(0% 0% 0% / 0.13409423828125) 81.25%, rgb(0% 0% 0% / 0.16748046875) 87.5%, rgb(0% 0% 0% / 0.20599365234375) 93.75%, rgb(0% 0% 0% / 0.25) 100% );
    }

    :deep(.table--spaced) {
      thead {
        tr {
          color: $themed-black-white;
          background-color: $themed-fair;
        }
  
        th:first-child {
          border-top-left-radius: $border-radius;
          border-bottom-left-radius: $border-radius;
        }
  
        th:last-child {
          border-top-right-radius: $border-radius;
          border-bottom-right-radius: $border-radius;
        }
      }
    }

    .bg-light &,
    .bg-themed-light &,
    .bg-light,
    .bg-themed-light,
    &.box--display-container {
      :deep(.table--spaced tbody tr) {
        background: $themed-box-bg;
      }
    } 
  }

  .kanban-board {
    display: flex;
    overflow: auto;
    flex-direction: column;
  }

  ::v-deep(.box--disabled-overlay) {
    z-index: 2;
  }
</style>
