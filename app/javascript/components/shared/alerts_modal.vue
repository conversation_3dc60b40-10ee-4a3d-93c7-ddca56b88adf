<template>
  <div style="z-index: 10">
    <sweet-modal
      ref="modal"
      v-sweet-esc
      title="Alerts"
      data-tc-ticket-alerts-modal
    >
      <template>
        <div
          v-if="actionableAlerts && actionableAlerts.length != 0"
          class="mb-3"
        >
          <div class="mb-4">
            Below are the current alerts for you:
          </div>
          <div
            v-for="(alert, index) in actionableAlerts"
            :key="`${alert.id}_${index}`"
            class="row mb-2"
          >
            <div class="col-md-7">
              <a
                v-if="alert.link"
                :href="alert.link"
                class="text-danger"
                :data-tc-alert-message="alert.message"
                v-html="alert.message"
              />
              <span
                v-else
                class="text-danger"
                v-html="alert.message"
              />
            </div>
            <div class="col-md-4">
              <span class="text-muted">{{ createdAt(alert) }}</span>
            </div>
            <div class="col-md-1">
              <a
                v-tooltip="'Dismiss'"
                href="#"
                class="alert--dismiss"
                @click="dismissAlert(alert)"
              >
                <i class="nulodgicon-trash-b" />
              </a>
            </div>
          </div>
        </div>
        <div
          v-else
          class="h5 text-muted mt-4"
        >
          There are currently no alerts.
        </div>
        <nav v-if="pageCount > 1">
          <paginate
            ref="paginate"
            class="my-3 px-2 justify-content-center"
            :click-handler="pageSelected"
            :container-class="'pagination pagination-sm'"
            :next-class="'next-item'"
            :next-link-class="'page-link'"
            :next-text="'Next'"
            :page-class="'page-item'"
            :page-count="pageCount"
            :page-link-class="'page-link'"
            :prev-class="'prev-item'"
            :prev-link-class="'page-link'"
            :prev-text="'Prev'"
            :selected="indexPage"
          />
        </nav>
      </template>
    </sweet-modal>
  </div>
</template>

<script>
import http from 'common/http';
import { SweetModal } from 'sweet-modal-vue';
import MomentTimezone from 'mixins/moment-timezone';
import Paginate from 'vuejs-paginate';

export default {
  components: {
    SweetModal,
    Paginate,
  },
  data() {
    return {
      perPage: 25,
      pageCount: 0,
      indexPage: 0,
    };
  },
  mounted() {
    this.fetchActionableAlerts();
  },
  mixins: [MomentTimezone],
  props: ['actionableAlerts'],
  computed: {
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  methods: {
    createdAt(alert) {
      if (alert.createdAt) {
        return this.timezoneMoment(alert.createdAt, Vue.prototype.$timezone);
      }
      return null;
    },
    open() {
      this.$refs.modal.open();
    },
    close() {
      this.$refs.modal.close();
    },
    dismissAlert(alert) {
      http
        .delete(`/contributor_actionable_alerts/${alert.id}.json`)
        .then(() => {
          this.fetchActionableAlerts();
        }).catch(() => {
          // We're not going to do anything here.
        });
    },
    fetchActionableAlerts() {
      let params = {
        page: this.indexPage + 1,
        per_page: this.perPage,
      };

      if (this.isAssetsModule) {
        params.module = 'assets';
      }
      http
        .get('/current_actionable_alerts.json', { params } )
        .then(res => {
          this.pageCount = res.data.pageCount;
          this.$emit('update-alerts', res.data);
        });
    },
    pageSelected(p) {
      this.indexPage = p - 1;
      this.fetchActionableAlerts();
    },
  },
};
</script>

<style lang="scss" scoped>
  .sweet-modal {
    max-width: 62rem;
    width: 32rem !important;
  }

  .alert--dismiss{
    color: $themed-fair;

    &:hover {
      color: red;
    }
  }
</style>