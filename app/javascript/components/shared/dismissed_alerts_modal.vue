<template>
  <div>
    <sweet-modal
      ref="dismissedAlertsModal"
      v-sweet-esc
      title="Dismissed Alerts"
    >
      <div 
        v-if="loading"
        class="text-center"
      >
        <p class="loading-text">
          Please wait while we fetch dismissed alerts.
        </p>
        <sync-loader
          class="mt-3"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
      <div
        v-else-if="dismissedAlerts.length > 0"
        class="d-flex flex-column align-items-end"
      >
        <connection-alert 
          v-for="(alert) in dismissedAlerts"
          :key="alert.type"
          :alert-type="alert.type"
          :asset-count="alert.count"
          :source="alert.name"
          :integration-id="alert.id"
          :delete-flag="true"
          @fetch-dismissed="fetchDismissedAlerts"
        />
      </div>
      <div v-else>
        <h6>Currently, there are no dismissed alerts</h6>
      </div>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import connectionAlert from '../assets/discovery_tools/connection_alert.vue';

  export default {
    components: {
      SweetModal,
      connectionAlert,
      SyncLoader,
    },
    data() {
      return {
        alerts: [],
        lading: false,
      };
    },
    computed: {
      dismissedAlerts() {
        return this.alerts.length > 0
          ? this.alerts.flatMap(alert => (
              [
                { count: alert.alertInfo.newAsset, type: "newAsset", name: this.getName(alert.source), id: alert.id },
                { count: alert.alertInfo.outOfSync, type: "outOfSync", name: this.getName(alert.source), id: alert.id },
                { count: alert.alertInfo.notReporting, type: "notReporting", name: this.getName(alert.source), id: alert.id },
                { count: alert.alertInfo.failed, type: "failed", name: this.getName(alert.source), id: alert.id },
              ].filter(alertToFilter => typeof alertToFilter.count === "number")
            ))
          : [];
      },
    },
    methods: {
      openModal() {
        this.fetchDismissedAlerts();
        this.$refs.dismissedAlertsModal.open();
      },
      fetchDismissedAlerts() {
        this.loading = true;
        http
          .get('company_integrations/dismissed_alerts')
          .then((res) => {
            this.loading = false;
            this.alerts = res.data.dismissedAlerts;
          });
      },
      getName(source) {
        return source.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" "); 
      },
    },
  };
</script>

<style lang="scss" scoped>
  h6 {
    font-weight: 100;
  }
  .sweet-modal  {
    width: 25rem !important;
  }
</style>
