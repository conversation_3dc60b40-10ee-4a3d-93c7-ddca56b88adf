import permissionsHelper from 'mixins/permissions_helper';
import strings from 'mixins/string';
import { mapGetters, mapMutations } from 'vuex';

export default {
  mixins: [permissionsHelper, strings],
  data() {
    return {
      errors: [],
      isLoading: false,
      hoverIntgItem: false,
      defaultTooltipMsg: 'Something went wrong please try again with valid credentials.',
      integratedIntegrations: [],
    };
  },
  created() {
    this.setCompanyChannelKey(this.$currentCompanyGuid);
  },
  computed: {
    ...mapGetters([
      'companyIntegrations',
      'companyChannelKey',
    ]),
    ...mapGetters('GlobalStore', ['syncingConnectors']),
    isPending() {
      if (this.intgDetail) {
        if (this.intgDetail.name === 'kaseya' && !this.intgDetail.kaseyaAuthenticated) {
          return false;
        }
        return this.intgDetail.syncStatus === "pending";
      }
      return false;
    },
    isSuccess() {
      return this.intgDetail && this.intgDetail.syncStatus === "successful";
    },
    isFailed() {
      return this.intgDetail && this.intgDetail.syncStatus === "failed";
    },
    isAnyChannelConnected() {
      return this.intgDetail && this.intgDetail.isConnected;
    },
    isHelpDeskIntegration() {
      return this.intgDetail && this.intgDetail.isHelpDeskIntegration;
    },
    isDeactivated() {
      return this.intgDetail && !this.intgDetail.active;
    },
    canManage() {
      return this.isWrite || this.isScoped;
    },
    errorForUser() {
      return this.intgDetail.userErrorMessage ? this.intgDetail.userErrorMessage : this.defaultTooltipMsg;
    },
    tooltipErrorMsg() {
      if (this.intgDetail && this.intgDetail.errorMessage) {
        let superAdminError = "";
        if (this.$superAdminUser) {
          const message = this.intgDetail.errorMessage;
          let filteredMessage = message.replace(/[^a-zA-Z:0-9 ]/g, "").slice(0, 150);
          filteredMessage =  message.length > 150 ? `${filteredMessage} ...` : filteredMessage;
          superAdminError =  `Details: ${filteredMessage}`;
        }
        return `<p>${this.errorForUser}</p> ${superAdminError}`;
      }
      return "";
    },
  },
  methods: {
    ...mapMutations([
      'setCompanyChannelKey',
      'setMerakiInfo',
    ]),
    ...mapMutations('GlobalStore', [
      'setSyncingConnectors', 
      'setImportedUbiquitiDiscoveredAssets', 
      'setImportedUbiquitiManagedAssets', 
      'setImportedKaseyaDiscoveredAssets', 
      'setImportedKaseyaManagedAssets',
      'setImportedMerakiDiscoveredAssets', 
      'setImportedMerakiManagedAssets',
      'setImportedSophosDiscoveredAssets',
      'setImportedSophosManagedAssets',
    ]),

    setupPusherListeners() {
      if (this.$pusher && this.companyChannelKey) {
        for (let i = 0; i < this.pusherIntegrations.length; i+=1) {
          const channel = this.$pusher.subscribe(this.companyChannelKey);
          channel.unbind(`${this.pusherIntegrations[i]}-config`);
          channel.bind(`${this.pusherIntegrations[i]}-config`, data => {
            if (data.value) {
              this.loadingValue[`${this.pusherIntegrations[i]}`] = data.value;
              this.addSyncingConnectors(`${this.pusherIntegrations[i]}`, data.value);
              this.setSyncingConnectors(this.updateConnectorValue(`${this.pusherIntegrations[i]}`, data.value));
              this.checkIntegrationCompletion(data.value, this.pusherIntegrations[i], `${this.pusherIntegrations[i]}`, data.count);
            }
            if (this.modulePrefix === "managed_assets" && data.isCompleted) {
              this.setMerakiInfo(data);
            }
          }, this);
        }
      }
    },
    addSyncingConnectors(intgName, value) {
      if (value) {
        const con = this.syncingConnectors.some(connector => connector.shortName === intgName);
        if (con) {
          this.setSyncingConnectors(this.updateConnectorValue(intgName, value));
        } else {
          const connector = this.onboardingOptions.find((conn) => conn.searchableName === intgName);
          this.setSyncingConnectors([
            ...this.syncingConnectors,
            {
              image: connector.imgPath,
              name: connector.name,
              shortName: connector.searchableName,
              status: 'In Progress',
              loadingValue: value,
            },
          ]);
        }
      }
    },
    updateConnectorValue(intgName, value) {
      return this.syncingConnectors.map((conn) => {
        if (conn.shortName === intgName) {
          return {
            ...conn,
            loadingValue: value,
          };
        }
        return conn;
      });
    },
    updateConnectorStatus(intgName, status) {
      return this.syncingConnectors.map((conn) => {
        if (conn.shortName === intgName) {
          return {
            ...conn,
            status,
          };
        }

        return conn;
      });
    },
    unsubscribePusher() {
      if (this.$pusher) {
        this.$pusher.unsubscribe(this.companyChannelKey);
      }
    },
    checkIntegrationCompletion(value, intgName, loadingValue, count) {
      if (value === 1) {
        this.$store.dispatch('fetchCompanyIntegrations');
        this.loadingValue[loadingValue] = 0;
        if ( intgName === "kaseya" ) {
          this.setImportedKaseyaDiscoveredAssets(count.new_kaseya_discovered_assets);
          this.setImportedKaseyaManagedAssets(count.new_kaseya_managed_assets);
        } else if (intgName === "ubiquiti") {
          this.setImportedUbiquitiDiscoveredAssets(count.new_ubiquiti_discovered_assets);
          this.setImportedUbiquitiManagedAssets(count.new_ubiquiti_managed_assets);
        } else if (intgName === "meraki") {
          this.setImportedMerakiDiscoveredAssets(count.new_meraki_discovered_assets);
          this.setImportedMerakiManagedAssets(count.new_meraki_managed_assets);
        } else if (intgName === "sophos") {
          this.setImportedSophosDiscoveredAssets(count.new_sophos_discovered_assets);
          this.setImportedSophosManagedAssets(count.new_sophos_managed_assets);
        }
        if (!this.integratedIntegrations.includes(intgName)) {
          this.emitSuccess(`${this.toTitle(intgName)} integrated successfully!`);
          this.integratedIntegrations.push(intgName);
        }
        this.setSyncingConnectors(this.updateConnectorStatus(intgName, 'Connected'));
        if (this.modulePrefix === "managed_assets") {
          this.$store.dispatch('fetchDiscoveredAssetsSummary');
        }
      } else if (value === 0) {
        this.setSyncingConnectors(this.updateConnectorStatus(intgName, 'Failed'));
        this.emitError(`${this.toTitle(intgName)} failed to integrate. Please try again.`);
        this.$store.dispatch('fetchCompanyIntegrations');
      }
    },
  },
};
