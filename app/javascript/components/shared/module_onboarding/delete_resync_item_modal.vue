<template>
  <sweet-modal
    ref="deleteItemModal"
    :title="`Before you delete ${integrationName}`"
    class="delete-item-modal"
    @close="clearData"
  >
    <template slot="default">
      <div
        v-if="hasIntegrationInfo"
        class="rounded bg-lighter p-3 border border-light mb-3"
      >
        <h6 class="text-secondary">Current Account</h6>
        <div>
          <p
            v-for="(info, key) in integrationInfo"
            :key="key"
            class="mb-1 font-weight-bold text-secondary option-header"
          >
            {{ key }}:
            <span class="text-muted mb-0">
              {{ info }}
            </span>
          </p>
        </div>
      </div>
      <div>
        <p class="font-weight-bold">
          Please select an option below
          <span class="ml-3 d-inline-block">
            <sync-loader
              :loading="loading"
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </p>

        <label
          v-if="isAssetIntg && integration"
          key="0"
          class="w-100 p-2 mb-0"
          @click="$emit('resync-option-selected')"
        >
          <input
            v-model="actionChoice"
            value="resync"
            type="radio"
          >
          <i class="nulodgicon-checkmark" />
          <div class="d-block align-middle ml-5 pl-2">
            <p class="mb-0 font-weight-bold text-secondary option-header">
              Resync
            </p>
            <p class="text-muted mb-0">
              This will resync the account. Assets that are already synced will remain. However, new assets will show up.
            </p>
          </div>
          <i class="nulodgicon-checkmark" />
        </label>

        <label
          v-if="integration"
          key="1"
          class="w-100 p-2 mb-0"
          @click="$emit('deactivate-option-selected')"
        >
          <input
            v-model="actionChoice"
            value="deactivate"
            type="radio"
          >
          <i class="nulodgicon-checkmark" />
          <div class="d-block align-middle ml-5 pl-2">
            <p class="mb-0 font-weight-bold text-secondary option-header">
              Deactivate
            </p>
            <p class="text-muted mb-0">
              This will deactivate the account. {{ titleize(integrationType) }} that are already synced will remain. However, new {{ integrationType }} won't show up.
            </p>
          </div>
          <i class="nulodgicon-checkmark" />
        </label>

        <label
          key="2"
          class="w-100 p-2 mb-0"
          @click="$emit('delete-option-selected')"
        >
          <input
            v-model="actionChoice"
            value="delete"
            type="radio"
          >
          <i class="nulodgicon-checkmark" />
          <div class="d-block align-middle ml-5 pl-2">
            <p class="mb-0 font-weight-bold text-secondary option-header">
              Delete
            </p>
            <p class="text-muted mb-0">
              This will delete all information related to this integration which includes discovered {{ integrationType }}.
            </p>
          </div>
        </label>
        <div 
          v-if="showImportOption"
          class="mt-3"
        >
          <p class="font-weight-bold">Choose how to import your devices:</p>
          <label
            v-if="integration"
            key="3"
            class="w-100 p-2 mb-0"
            @click="$emit('import-as-managed-asset')"
          >
            <input
              v-model="importType"
              value="managed_asset"
              type="radio"
            >
            <i class="nulodgicon-checkmark" />
            <div class="d-block align-middle ml-5 pl-2 pt-1">
              <p class="mb-0 font-weight-bold text-secondary option-header">
                {{ moveAllDevices ? 'Managed Assets' : integrationName + ' devices to Managed Assets and others to Discovered Assets' }}
              </p>
            </div>
            <i class="nulodgicon-checkmark" />
          </label>
          <label
            v-if="integration"
            key="4"
            class="w-100 p-2 mb-0"
            @click="$emit('import-as-discovered-asset')"
          >
            <input
              v-model="importType"
              value="discovered_asset"
              type="radio"
            >
            <i class="nulodgicon-checkmark" />
            <div class="d-block align-middle ml-5 pl-2 pt-1">
              <p class="mb-0 font-weight-bold text-secondary option-header">
                {{ moveAllDevices ? 'Discovered Assets' : 'All devices to Discovered Assets' }}
              </p>
            </div>
            <i class="nulodgicon-checkmark" />
          </label>
        </div>
      </div>
    </template>
    <button
      slot="button"
      class="btn btn-sm btn-link text-secondary mr-2"
      @click.stop="close"
    >
      Cancel
    </button>
    <button
      id="delete-integration"
      slot="button"
      class="btn btn-sm btn-primary font-weight-bold"
      :disabled="!actionChoice && (importType === orignalImportType)"
      @click.stop="deleteIntegration"
    >
      Confirm
    </button>
  </sweet-modal>
</template>

<script>
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import strings from 'mixins/string';
  import _get from 'lodash/get';
  import common from './common';

  export default {
    components: {
      SweetModal,
      SyncLoader,
    },
    mixins: [strings, common],
    props: ['integration', 'integrationType'],
    data() {
      return {
        actionChoice: null,
        loading: false,
        importType: null,
        orignalImportType: null,
      };
    },
    computed: {
      integrationName() {
        return this.toTitle(_get(this, 'integration.name', "this integration"));
      },
      moveAllDevices() {
        const connector = ['Kaseya', 'Sophos'];
        return connector.includes(this.integrationName);
      },
      showImportOption() {
        const allowedIntegrations = ["Ubiquiti", "Meraki", "Kaseya", "Sophos"];
        return allowedIntegrations.includes(this.integrationName);
      },
      integrationInfo() {
        const info = {
          'Username': _get(this, 'integration.userName'),
          'Token': _get(this, 'integration.token'),
          'Password': _get(this, 'integration.password'),
          'Access Key': _get(this, 'integration.accessKey'),
          'Client Id': _get(this, 'integration.clientId'),
          'Client Secret': _get(this, 'integration.clientSecret'),
          'Integrator Username': _get(this, 'integration.integratorUsername'),
        };
        return Object.fromEntries(
          Object.entries(info).filter(([key, value]) => {
            if (key.length) {
              return value !== undefined && value !== "";
            }
            return value;
          })
        );
      },
      hasIntegrationInfo() {
        return Object.keys(this.integrationInfo).length > 0;
      },
      isAssetIntg() {
        return this.integrationType === 'assets';
      },
    },
    watch: {
      integration: {
        handler() {
          this.onWorkspaceChange();
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      onWorkspaceChange() {
        this.orignalImportType = this.integration?.importType;
        this.importType = this.integration?.importType;
      },
      open() {
        this.$refs.deleteItemModal.open();
      },
      close() {
        this.clearData();
        this.$refs.deleteItemModal.close();
      },
      clearData() {
        this.loading = false;
        this.actionChoice = null;
      },
      toggleOption(target) {
        this.importType = target;
      },
      async deleteIntegration() {
        this.loading = true;
        this.$emit('update-intg-vendors-count', this.integration.name);
        if (this.importType !== null && this.orignalImportType !== this.importType ) {
          const params = { import_type: this.importType };
          await http
            .put(`/integrations/${this.integration.name}/configs/${this.integration.configId}/import_type.json`, params)
            .then(() => {
              this.emitSuccess('Integration import place changed');
             })
            .catch(error => {
              this.close();
              this.emitError(`Sorry, there was an error while chnaging import place for this integration. ${error}`);
              return;
            });
        }
        if (this.actionChoice === "delete") {
          http
            .delete(`/integrations/${this.integration.name}/configs/${this.integration.configId}.json`)
            .then(() => {
              this.emitSuccess('Successfully deleted integration');
              this.$store.dispatch('fetchCompanyIntegrations');
              this.$store.dispatch('fetchDiscoveredAssetsSummary');
              this.setSyncingConnectors(this.syncingConnectors.filter(connector => connector.shortName !== this.integration.name));
             })
            .catch(error => {
              this.emitError(`Sorry, there was an error while deleting this integration. ${error.response.data.message}`);
            })
            .finally(() => {
              this.close();
              this.$emit('open-delete-integration-modal');
            });
          return;
        } else if (this.actionChoice === "deactivate") {
          http
            .post(`/integrations/${this.integration.name}/configs/${this.integration.configId}/deactivate.json`)
            .then(() => {
              this.$store.dispatch('fetchCompanyIntegrations');
              this.setSyncingConnectors(this.syncingConnectors.filter(connector => connector.shortName !== this.integration.name));
              this.emitSuccess('Successfully deactivated integration');
             })
            .catch(error => {
              this.emitError(`Sorry, there was an error while deactivating this integration. ${error.response.data.message}`);
            })
            .finally(() => {
              this.close();
            });
        } else if (this.isAssetIntg && this.actionChoice === 'resync') {
          const params = { id: this.integration.configId, integration_name: this.integration.name, is_resyncing: true };
          http
            .post(`/integrations/${this.integration.name}/resync.json`, params)
            .then(() => {
              this.close();
              this.$store.dispatch('fetchCompanyIntegrations');
              this.setSyncingConnectors(this.syncingConnectors.filter(connector => connector.shortName !== this.integration.name));
              this.$emit('set-resync-pusher', this.integration.name);
              this.emitSuccess('Integration resynced started');
             })
            .catch(error => {
              this.close();
              this.emitError(`Sorry, there was an error while resyncing this integration. ${error}`);
            });
        } else {
          this.$store.dispatch('fetchCompanyIntegrations');
          this.close();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
.delete-item-modal {
  :deep(.sweet-content) {
    padding-bottom: 1rem;
    padding-top: 1rem;
    text-align: left;
  }
}
.nulodgicon-checkmark {
  background-color: white;
  border-radius: 50%;
  border: 1px solid $themed-fair;
  color: white;
  display: block;
  font-size: 0.875rem;
  height: 1.5rem;
  line-height: 1.5rem;
  position: absolute;
  left: 1rem;
  text-align: center;
  top: 50%;
  transition: $transition-base;
  transform: translateY(-50%);
  width: 1.5rem;
}

:checked ~ .nulodgicon-checkmark {
  background-color: $red;
  border-color: $red;
}

label {
  cursor: pointer;
  transition: $transition-base;
  position: relative;

  &:hover {
    background-color: $themed-light;
  }
}

[type='radio'] {
  display: none;
}
</style>
