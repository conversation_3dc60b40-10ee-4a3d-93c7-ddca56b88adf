<template>
  <div>
    <logs-sub-menu
      v-if="isNotGroupedAT"
      :module="modName[module]"
      :module-link="modLink[module]"
      :entity-name="entityName[module]"
      :entity-link="entityLink[module]"
    />
    <div
      v-if="isNotGroupedAT"
      class="mb-3 d-flex-column--medium"
    >
      <div class="d-inline float-right d-flex--responsive">
        <dropdown-filter
          id-label="value"
          label="Changed by"
          class="mb-sm-2 mb-md-0"
          :include-all="false"
          :options="changedByNames"
          :value="changedByNameValue.value"
          @selected="filterByChangedBy"
        />
        <dropdown-filter
          v-if="isHelpDeskModule"
          id-label="value"
          label="Activity Type"
          class="mb-sm-2 mb-md-0"
          :include-all="false"
          :options="activityTypes"
          :value="activityTypesValue.value"
          @selected="filterByActivityType"
        />
        <div
          v-if="pageCount > 1"
          class="clearfix d-inline ml-3"
        >
          <paginate
            ref="paginate"
            class="float-right mb-0"
            :click-handler="pageSelected"
            :container-class="'pagination pagination-sm'"
            :next-class="'next-item'"
            :next-link-class="'page-link'"
            :next-text="'Next'"
            :page-class="'page-item'"
            :page-count="pageCount"
            :page-link-class="'page-link'"
            :prev-class="'prev-item'"
            :prev-link-class="'page-link'"
            :prev-text="'Prev'"
            :selected="indexPage"
          />
        </div>
      </div>
    </div>
    <div
      v-if="(activities && activities.length == 0) || isNewGroupedAT || isNewAssetAT"
      class="mt-5 text-center"
    >
      <h3 class="text-secondary mb-4">
        No logs present.
      </h3>
    </div>
    <div
      v-else-if="activities && activities.length > 0"
      class="my-1"
    >
      <div class="row py-2 align-items-center font-weight-bold">
        <div class="col pr-sm-2 pr-md-3 d-flex">
          <header
            v-for="(header, index) in headers"
            :key="index"
            :class="rightClass[header.title]"
            @click="applyColumnSorting(header)"
          >
            {{ header.title }}
            <i
              v-if="header.sortable"
              class="text-muted"
              :class="arrowClass(header)"
            />
          </header>
        </div>
      </div>
      <div
        v-for="activity in activities"
        :key="activity.id"
        class="activity"
      >
        <router-link
          :to="checkEntityType(activity)"
          class="text-themed-base"
          :class="{'unclickable': !!taskId}"
          target="_blank"
        >
          <div class="clearfix">
            <div class="row py-1 text mt-1">
              <div class="col pr-sm-2 pr-md-3 d-flex align-items-center">
                <div class="col-6">
                  <span
                    v-if="checkFormStatus(activity.data)"
                    class="action-icon position-relative"
                    style="background: #495057;"
                  >
                    <i
                      class="nulodgicon-archive position-absolute"
                      style=" bottom: -1px;"
                    />
                  </span>
                  <span
                    v-else
                    class="action-icon action-icon-edit position-relative align-middle"
                  >
                    <i
                      class="position-absolute nulodgicon-edit"
                      style="bottom: -1px"
                    />
                  </span>
                  <span
                    v-if="activity.entityType == 'HelpdeskSetting'"
                    class="ml-2"
                  >
                    <strong class="font-weight-bold">
                      General Settings
                    </strong>
                  </span>
                  <span
                    v-else-if="activity.entityType == 'CompanyMailer'"
                    class="ml-2"
                  >
                    <strong class="font-weight-bold">
                      Email Notifications Settings
                    </strong>
                  </span>
                  <span
                    v-else-if="activity.entityType == 'Workspace'"
                    class="ml-2"
                    :data-tc-activity="workspaceName(activity)"
                  >
                    <strong class="font-weight-bold">
                      {{ workspaceName(activity) }}
                    </strong>
                  </span>
                  <span
                    v-else-if="activity.entityType == 'Policy'"
                    class="ml-2"
                    :data-tc-activity="policyName(activity)"
                  >
                    <strong class="font-weight-bold">
                      {{ policyName(activity) }}
                    </strong>
                  </span>
                  <span
                    v-else-if="activity.entityType == 'AutomatedTask'"
                    class="text-color ml-2"
                    :data-tc-activity="automatedTaskName(activity)"
                  >
                    <strong class="font-weight-bold">
                      {{ automatedTaskName(activity) }}
                    </strong>
                  </span>
                  <span
                    v-else-if="activity.entityType == 'AssetAutomatedTask'"
                    class="text-color ml-2"
                  >
                    <strong class="font-weight-bold">
                      {{ automatedTaskName(activity) }}
                    </strong>
                  </span>
                  <span
                    v-else-if="activity.entityType == 'Group'"
                    class="text-color ml-2"
                    :data-tc-activity="groupName(activity)"
                  >
                    <strong class="font-weight-bold">
                      {{ groupName(activity) }}
                    </strong>
                  </span>
                  <span
                    v-if="checkFormStatus(activity.data)"
                    class=" ml-2 archived-activity"
                    :data-tc-activity="formName(activity)"
                  >
                    <strong
                      class="font-weight-bold"
                    >
                      {{ formName(activity) }}
                    </strong>
                    status changed from <strong>{{ previousValue(activity) }}</strong> to <strong>{{ currentValue(activity) }}</strong>
                  </span>
                  <span
                    v-else
                    class=" ml-1"
                    :data-tc-activity="formName(activity)"
                  >
                    <strong
                      class="font-weight-bold"
                    >
                      {{ formName(activity) }}
                    </strong>
                    {{ activity.activityType }} by {{ user(activity) }}
                  </span>
                </div>
                <div class="col-2 text-center">
                  <span
                    v-if="activity.activityType == 'updated' && !checkFormStatus(activity.data, activity.entityType)"
                    class="btn btn-link btn-sm clickable mr-2 pointer-events-all"
                    @click.prevent.stop="moduleChangesActivities(activity)"
                  >
                    View Changes
                  </span>
                </div>
                <div class="col-4 mt-sm-1 mt-md-0 text-right small text-muted p--responsive">
                  <span>{{ user(activity) }}</span>
                  <br>
                  <span class="small">{{ createdAt(activity) }}</span>
                </div>
              </div>
            </div>
          </div>
        </router-link>
      </div>
    </div>
    <div
      v-else
      class="my-5 clearfix"
    >
      <clip-loader
        :loading="true"
        class="ml-3 mt-1"
        color="#0d6efd"
        size="1.5rem"
      />
    </div>
    <div
      v-if="pageCount > 1"
      class="clearfix"
    >
      <paginate
        ref="paginate"
        class="float-right mb-0"
        :click-handler="pageSelected"
        :container-class="'pagination pagination-sm'"
        :next-class="'next-item'"
        :next-link-class="'page-link'"
        :next-text="'Next'"
        :page-class="'page-item'"
        :page-count="pageCount"
        :page-link-class="'page-link'"
        :prev-class="'prev-item'"
        :prev-link-class="'page-link'"
        :prev-text="'Prev'"
        :selected="indexPage"
      />
    </div>
    <sweet-modal
      ref="moduleSettingChangesModal"
      :title="title"
    >
      <template>
        <module-settings-changes
          v-if="moduleSettingsActivity"
          :activity="moduleSettingsActivity.data"
          @close="closeEditModal"
        />
      </template>
    </sweet-modal>
  </div>
</template>

<script>
  import _ from "lodash";
  import http from 'common/http';
  import Paginate from 'vuejs-paginate';
  import ClipLoader from 'vue-spinner/src/ClipLoader.vue';
  import momentTimezone from 'mixins/moment-timezone';
  import companyUser from 'mixins/company_user';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import moduleSettingsChanges from './module_logs_changes.vue';
  import DropdownFilter from "./dropdown_filter.vue";
  import LogsSubMenu from "./logs_sub_menu.vue";

  export default {
    components: {
      Paginate,
      ClipLoader,
      SweetModal,
      LogsSubMenu,
      DropdownFilter,
      moduleSettingsChanges,
    },
    mixins: [momentTimezone, companyUser, permissionsHelper],
    props: ["module", "taskId"],
    data() {
      return {
        activities: null,
        perPage: !this.taskId ? 30 : 1000, // Note: This is temporary and should be removed when the taskId filtering is done on the backend
        companyId: null,
        pageCount: 0,
        indexPage: 0,
        activityType: null,
        moduleSettingsActivity: null,
        title: null,
        fieldLabel: null,
        changedBy: null,
        changedByNames: [],
        changedByFilter: null,
        changedByNameValue: { name: "All", value: "all" },
        activityTypes: [
          { name: "All", value: "all" },
          { name: "General Settings", value: "HelpdeskSetting" },
          { name: "Custom Form", value: "CustomForm" },
          { name: "Email Notifications", value: "CompanyMailer" },
          { name: "Workspace", value: "Workspace"} ,
          { name: "SLA Policy", value: "Policy" },
          { name: "Automated Tasks", value: "AutomatedTask" },
          { name: "Asset Automated Tasks", value: "AssetAutomatedTask" },
        ],
        activityTypesValue: { name: "All", value: "all" },
        modName: {
          helpdesk: "Help Desk",
          company_user: "Company",
        },
        modLink: {
          helpdesk: "/settings/event_log",
          company_user: "/event_logs",
        },
        entityName: {
          helpdesk: "Ticket",
          company_user: "User",
        },
        entityLink: {
          helpdesk: "/ticket_activities",
          company_user: "/user_activities",
        },
        sortHeader: {},
        headers: [
          {
            title: 'Actions',
            sortable: false,
          },
          {
            title: 'What Changed',
            sortable: false,
          },
          {
            title: 'Performed By',
            fieldName: 'first_name',
            sortable: true,
            sortDirection: 'desc',
          },
        ],
        rightClass: {
          'Actions' : 'col-6',
          'What Changed' : 'col-2 text-center',
          'Performed By' : 'col-4 text-right clickable',
        },
      };
    },
    computed: {
      isHelpDeskModule() {
        return this.module === "helpdesk";
      },
      isAutomatedTaskLogs() {
        return this.$route.path === '/automated_tasks';
      },
      isNotGroupedAT() {
        return !this.taskId && !this.isAutomatedTaskLogs;
      },
      isNewGroupedAT() {
        return !this.taskId && this.isAutomatedTaskLogs && this.module === "helpdesk";
      },
      isNewAssetAT() {
        return !this.taskId && this.isAutomatedTaskLogs && this.module === "asset";
      },
    },
    methods: {
      onWorkspaceChange() {
        const params = {archived : true};
        this.$store.dispatch("fetchCompanyUserOptions", params);
        this.fetchActivities();
      },
      moduleChangesActivities(activity) {
        if (activity.entityType === "CustomForm") {
          this.fieldLabel = JSON.parse(activity.data).field_label;
          if (this.fieldLabel) {
            this.fieldLabel = this.fieldLabel.concat(' field');
          }
        }
        this.moduleSettingsActivity = activity;
        this.titleValue(activity);
        this.$refs.moduleSettingChangesModal.open();
      },
      titleValue(activity) {
        if (activity.entityType === "CustomForm") {
          this.title = JSON.parse(activity.data).field_activity_type;
          if (this.fieldLabel && this.moduleSettingsActivity) {
            this.title = `${this.fieldLabel  } ${  this.title.toLowerCase()}`;
            return this.title;
          }
          return this.title;
        }
        else if (activity.entityType === "HelpdeskSetting") {
          this.title = "General Settings Updated";
          return this.title;
        }
        else if (activity.entityType === "CompanyMailer") {
          this.title = "Email Notifications Settings Updated";
          return this.title;
        }
        else if (activity.entityType === "Workspace") {
          this.title = `${this.workspaceName(activity)  } ${  activity.activityType}`;
          return this.title;
        }
        else if (activity.entityType === "Policy") {
          this.title = `${this.policyName(activity)  } ${  activity.activityType}`;
          return this.title;
        }
        else if (activity.entityType === "AutomatedTask") {
          this.title = `${this.automatedTaskName(activity)} ${activity.activityType}`;
          return this.title;
        }
        else if (activity.entityType === "Group") {
          this.title = `${this.groupName(activity)} ${activity.activityType}`;
          return this.title;
        }
        else if (activity.entityType === "AssetAutomatedTask") {
          this.title = `${this.automatedTaskName(activity)} ${activity.activityType}`;
          return this.title;
        }
        return null;
      },
      checkEntityType(activity) {
        if (activity.entityType === "HelpdeskSetting") {
          return `/settings/general`;
        } else if (activity.entityType === "CompanyMailer") {
          return `/settings/email_notifications`;
        } else if (activity.entityType === "Workspace") {
          return `/workspaces/${JSON.parse(activity.workspaceId)}`;
        } else if (activity.entityType === "Policy") {
          return `/settings/sla/policies/${JSON.parse(activity.entityId)}`;
        } else if (activity.entityType === "AutomatedTask") {
          return `/automated_tasks/${JSON.parse(activity.entityId)}/edit`;
        } else if (this.isHelpDeskModule) {
          return `/settings/custom_forms/${JSON.parse(activity.data).form_id}/edit`;
        }  else if (activity.entityType === "Group") {
          return `/groups/${JSON.parse(activity.entityId)}/edit`;
        } else if (activity.entityType === "AssetAutomatedTask") {
          return `/automated_tasks/${JSON.parse(activity.entityId)}/edit`;
        }
        return `/custom_forms/company_users/${JSON.parse(activity.data).form_id}/edit`;
      },
      closeEditModal() {
        this.$refs.moduleSettingChangesModal.close();
      },
      fetchActivities() {
        if (this.activityType === null) {
          this.activityType = this.activityTypesValue.value;
        }
        if (this.taskId) {
          this.activityType = 'AutomatedTask';
        }
        if (this.taskId && this.module === "asset") {
          this.activityType = 'AssetAutomatedTask';
        }
        if (this.changedBy == null) {
          this.changedBy = this.changedByNameValue.value;
        }
        const params = {
          per_page: this.perPage,
          page: this.indexPage + 1,
          activity_type: this.activityType,
          filter: this.module,
          changed_by: this.changedBy,
          sort_field: this.sortHeader.fieldName,
          sort_direction: this.sortHeader.sortDirection,
        };

        const url = '/event_logs.json';
        http
          .get(url, { params })
          .then(res => {
            const allFilter = [{ name: "All", value: "all" }];
            if (this.taskId) {
              this.activities = res.data.customFormActivities.filter(a => a.entityId === this.taskId);
              this.pageCount = res.data.pageCount;
            } else if (!this.taskId && this.isAutomatedTaskLogs) {
              this.activities = null;
              this.pageCount = 0;
            } else {
              this.activities = res.data.customFormActivities;
              this.pageCount = res.data.pageCount;
            }
            this.changedByNames = allFilter.concat(res.data.ownerNamesList);
            this.changedByFilter = this.changedByNames;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error fetching activities.');
          });
      },
      checkFormStatus(data, type = null) {
        const formStatus = _.mapKeys(JSON.parse(data), (value, key) => _.camelCase(key));
        const {currentValue} = formStatus;
        const {previousValue} = formStatus;
        if (currentValue && previousValue) {
          if (currentValue.is_active === true && previousValue.is_active === false ||
              currentValue.is_active === false && previousValue.is_active === true) {
              return true;
          }
        } else if (!currentValue && !previousValue && (type === "AutomatedTask" || type === "AssetAutomatedTask")){
          return true;
        }
        return false;
      },
      user(activity) {
        const activityData = JSON.parse(activity.data);
        if (!activity.ownerId && activity.data.email) {
          return this.activity.data.email;
        } else if (activityData.owner_name) {
          return activityData.owner_name;
        } else if (activity.ownerId && activity.firstName !== 'super admin'){
          return `${activity.firstName} ${activity.lastName}`;
        }
          return this.companyUserName(activity.ownerId);

      },
      formName(activity) {
        const form = JSON.parse(activity.data);
        if (form.form_name) {
          return `Custom Form ${form.form_name}`;
        }
        return null;
      },
      workspaceName(activity) {
        const workspace = JSON.parse(activity.data);
        return `Workspace ${workspace.workspace_name}`;
      },
      policyName(activity) {
        const policy = JSON.parse(activity.data);
        return `SLA Policy ${policy.policy_name}`;
      },
      automatedTaskName(activity) {
        const automatedTask = JSON.parse(activity.data);
        const taskName = automatedTask.task_name ? automatedTask.task_name : `#${activity.entityId}`;
        return `Automated Task ${taskName}`;
      },
      groupName(activity) {
        const group = JSON.parse(activity.data);
        const taskName = group.group_name ? group.group_name : `#${activity.entityId}`;
        return `Group ${taskName}`;
      },
      previousValue(activity) {
        const form = _.mapKeys(JSON.parse(activity.data), (value, key) => _.camelCase(key));
        if (form.previousValue.is_active === false) {
          return 'archive';
        }
        return 'unarchive';
      },
      currentValue(activity) {
        const form = _.mapKeys(JSON.parse(activity.data), (value, key) => _.camelCase(key));
        if (form.currentValue.is_active === false) {
          return 'archive';
        }
        return 'unarchive';
      },
      createdAt(activity) {
        return this.timezoneMoment(activity.createdAt, Vue.prototype.$timezone);
      },
      ownerName(activity) {
        if (activity.firstName || activity.lastName) {
          return `${activity.firstName} ${activity.lastName}`;
        }
        return "System";
      },
      pageSelected(p) {
        this.indexPage = p - 1;
        this.fetchActivities();
      },
      filterByCompany(filter) {
        this.companyId = filter;
        this.fetchActivitiesAndSetPage();
      },
      filterByActivityType(filter) {
        this.activityType = filter;
        this.activityTypesValue = this.activityTypes.find(f => f.value === filter);
        this.fetchActivitiesAndSetPage();
      },
      filterByChangedBy(filter) {
        if (filter === "all") {
          this.changedBy = null;
        } else {
          const filterOwner = this.changedByFilter.find(owner => owner.name === filter);
          this.changedBy = filterOwner ? filterOwner.id : null;
        }
        this.changedByNameValue = this.changedByNames.find(f => f.value === filter);
        this.fetchActivitiesAndSetPage();
      },
      fetchActivitiesAndSetPage() {
        this.indexPage = 0;
        this.fetchActivities();
      },
      arrowClass(header) {
        if (!this.sortHeader) {
          return "color-hidden";
        }
        if (this.sortHeader === header) {
          return this.sortHeader.sortDirection === 'asc' ? 'nulodgicon-arrow-up-b' : 'nulodgicon-arrow-down-b';
        }
        return null;
      },
      applyColumnSorting(column) {
        if (column.sortable) {
          const header = this.headers.find(head => head === column);
          header.sortDirection = header.sortDirection === 'asc' ? 'desc' : 'asc';
          this.sortHeader = header;
          this.fetchActivities();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon-nulodgicon-person { background-color: $color-home; }
  .action-icon-nulodgicon-ios-copy-outline { background-color: $color-contract; }
  .action-icon-genuicon-assets { background-color: $color-vendors; }
  .action-icon-nulodgicon-ios-telephone-outline { background-color: $color-telecom; }
  .action-icon-nulodgicon-location { background-color: $red; }
  .action-icon-nulodgicon-ios-box-outline { background-color: $color-assets }
  .action-icon-edit { background-color: $yellow }
  .activity {
    border-top: 1px solid $themed-fair;
    .activity-item {
      color: $themed-dark;
    }
      &:hover {
        background-color: $themed-lighter;
      }
    }
  .d-flex--responsive {
    @media($max: $medium) {
      display: flex !important;
      flex-direction: row;
      justify-content: flex-end;
    }
    @media($max: $small) {
      flex-direction: column;
      align-items: flex-end;
    }
  }
  .archived-activity {
    display: inline-block;
    vertical-align: top;
    width: 90%;
  }
</style>
