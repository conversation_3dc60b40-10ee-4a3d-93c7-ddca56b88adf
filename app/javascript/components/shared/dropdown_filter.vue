<template>
  <div class="dropdown d-inline-block w-100 w-md-auto">
    <a
      href="#"
      :class="buttonClass + ' w-100 w-md-auto text-left'"
      :data-uid="_uid"
      data-tc-drop-down-filters
      @click.prevent.stop="toggleFilters"
    >
      <span
        v-if="label"
        :data-tc-label="label"
      >{{ label }}: </span>
      <span class="max-width displayed-label p--responsive">{{ displayedLabel }}</span>
      <i
        v-if="selectedOption && selectedOption.customIconClass"
        class="d-inline-block h6 align-middle mx-1 mb-0"
        :class="selectedOption.customIconClass"
      />
    </a>

    <div
      ref="dropdown"
      v-click-outside="onCloseDropdown"
      class="dropdown-menu not-as-small bg-themed-box-bg dropdown-filter"
      :class="{hidden: !isOpen, 'dropdown-width': isSystemDetail}"
      :data-tc-filters-holder="label"
    >
      <div v-if="includeAll">
        <a
          class="dropdown-item"
          href="#"
          :data-tc-filter-option="defaultLabel"
          @click.stop.prevent="selectOption(null)"
        >{{ defaultLabel }}</a>
        <hr
          v-if="options && options.length > 0"
          class="my-2"
        >
      </div>
      <div v-else>
        <p class="dropdown-item mb-0">No items present</p>
      </div>
      <a
        v-for="option in options"
        :key="option[idLabel]"
        href="#"
        class="dropdown-item d-flex justify-content-between"
        :class="optionClass(option)"
        :data-tc-filter-option="option.name"
        @click.stop.prevent="selectOption(option)"
      >
        <span
          v-tooltip="showTooltip(option[nameLabel])"
          class="max-width label"
        >
          {{ truncateText(option[nameLabel]) }}
        </span>
        <i
          v-show="isSelected(option)"
          class="nulodgicon-checkmark float-right ml-3 is-checked"
        />
        <i
          v-if="option.customIconClass"
          class="h6 align-middle mx-1"
          :class="option.customIconClass"
        />
      </a>
      <div
        v-if="showMore && !loadingLocations"
        slot="afterList"
      >
        <div
          class="not-as-small py-3 cursor-pointer text-center"
          @click.stop.prevent="fetchMore"
        >
          <a href="#">+ Show More</a>
        </div>
      </div>
      <div
        v-if="loading || loadingLocations"
        slot="caret"
      >
        <clip-loader
          loading
          class="loader-position"
          color="#000"
          size="1.25rem"
        />
      </div>
    </div>
  </div>
</template>
<script>
import _ from 'lodash';
import dropdownClose from 'mixins/dropdown_close_outside_click';
import permissionsHelper from 'mixins/permissions_helper';
import clipLoader from 'vue-spinner/src/ClipLoader.vue';
import { mapGetters } from 'vuex';
import vClickOutside from 'v-click-outside';
import strings from '../../mixins/string';

export default {
  directives: {
    clickOutside: vClickOutside.directive,
  },
  components: {
    clipLoader,
  },
  mixins: [dropdownClose, permissionsHelper, strings],
  props: {
    includeAll: {
      type: Boolean,
      default: true,
    },
    defaultLabel: {
      type: String,
      default: 'All',
    },
    options: {
      type: Array,
      default: () => [],
    },
    label: {
      type: String,
      default: '',
    },
    nameLabel: {
      type: String,
      default: 'name',
    },
    idLabel: {
      type: String,
      default: '',
    },
    value: {
      type: [Object, String, Number],
      default: null,
    },
    buttonClass: {
      type: String,
      default: "dropdown-toggle btn btn-text text-secondary btn-sm p--responsive",
    },
    showMore: {
      type: Boolean,
      default: false,
    },
    loadingLocations: {
      type: Boolean,
      default: false,
    },
    isSystemDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isOpen: false,
    };
  },
  computed: {
    ...mapGetters('inDepthTicketReport', [
      'loading',
    ]),
    ...mapGetters(['loading']),
    selectedOption() {
      if (this.value !== null && this.value !== undefined) {
        return this.options.find(option => {
          const selected = this.value;
          if (this.idLabel) {
            return option[this.idLabel] === selected;
          }
          return _.isEqual(option, selected);
        });
      }
      return null;
    },
    displayedLabel() {
      const selected = this.selectedOption;
      if (selected) {
        return selected[this.nameLabel];
      } else if (this.includeAll) {
        return this.defaultLabel;
      }
      return '';
    },
  },
  methods: {
    onWorkspaceChange() {
      this.assignMutationObserver("isOpen");
      document.querySelector('body').addEventListener('click', (e) => {
        if (e.target && e.target.dataset && this.uid !== e.target.dataset.uid) {
          this.isOpen = false;
        }
      });
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' || e.key === 'Esc') {
          this.isOpen = false;
        }
      });
    },
    optionClass(option) {
      return {
        selected: this.isSelected(option),
      };
    },
    toggleFilters() {
      this.$emit('clicked'); // adding for components that want to catch the click event
      this.hideAllOtherMenus();
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.$emit("open");
      } else {
        this.$emit("close");
      }
    },
    isSelected(option) {
      if (!option) {
        return null;
      } else if (this.idLabel) {
        return this.value === option[this.idLabel];
      }
      return _.isEqual(this.value, option);
    },
    selectOption(option) {
      this.isOpen = false;
      if (this.isSelected(option) || !option) {
        this.$emit('selected', null);
      } else if (this.idLabel) {
        this.$emit('selected', option[this.idLabel]);
      } else {
        this.$emit('selected', option);
      }
    },
    truncateText(lable) {
      return lable?.length >= 20 ? this.truncate(lable, 20) : lable;
    },
    showTooltip(lable) {
      return lable?.length >= 20 ? lable : null;
    },
    fetchMore() {
      this.$emit('update-offset-and-limit');
    },
    onCloseDropdown() {
      this.isOpen = false;
    },
  },
};
</script>

<style lang="scss" scoped>
  .dropdown-menu {
    display: block;
    left: auto;
    right: 0;
    z-index: 10000;
    overflow-y: auto;
    max-height: 410px;
  }

  .is-checked {
    background: $success;
    color: white;
    border-radius: 50%;
    width: 13px;
    height: 13px;
    text-align: center;
    line-height: 13px;
    font-size: xx-small;
    margin-top: 5px;
  }

  .max-width {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: inline-block;
    vertical-align: bottom;

    @media(max-width: 767px) {
      vertical-align: middle;
    }

    &.label {
      max-width: 400px;
    }

    &.displayed-label {
      max-width: 230px;
    }
  }

  .dropdown-width {
    width: 16rem;
  }
</style>
