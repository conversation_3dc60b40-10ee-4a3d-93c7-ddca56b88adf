<template>
  <div v-if="associatedItem">
    <div
      class="mb-1 mt-3 not-as-big clickable"
      @click="openAssociatedItems(`${associatedType}`)"
    >
      Associated Assignment(s) Information
      <small>
        <i
          class="clickable small"
          :class="arrowDirection(openedAccordion)"
        />
      </small>
    </div>

    <div v-if="associatedItem">
      <div
        v-if="openedAccordion == associatedType"
        class="modal-max-height associated-scrollable overflow-auto mb-3"
      >
        <p class="text-muted">
          This asset is associated with the following users.
        </p>
        <table
          v-if="associatedItem"
          class="table table-striped"
        >
          <thead>
            <tr>
              <th class="nav-dark-bg-text">
                Assignment
              </th>
              <th class="nav-dark-bg-text">
                Name
              </th>
              <th
                v-show="readyToAssign"
                class="nav-dark-bg-text"
              >
                Assign to asset
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="currentAsset.managedBy">
              <td class="font-weight-semi-bold">
                Managed By
              </td>
              <td>
                {{ currentAsset.managedBy ? currentAsset.managedBy.fullName : 'No Teammate' }}
              </td>
              <td
                v-if="readyToAssign"
                class="not-as-small text-muted w-50"
              >
                <asset-options
                  :value="managedByValue"
                  @select="managedByChange"
                  @remove="managedByRemove"
                >
                  <template slot="label">
                    <slot name="label" />
                  </template>

                  <template #image="{ option }">
                    <img
                      :src="`/managed_asset_images/${option.id}`"
                      :style="imageStyle"
                    >
                  </template>
                </asset-options>
              </td>
            </tr>
            <tr v-if="currentAsset.usedBy">
              <td class="font-weight-semi-bold">
                Used By
              </td>
              <td>
                {{ currentAsset.usedBy ? currentAsset.usedBy.fullName : 'No Teammate' }}
              </td>
              <td
                v-show="readyToAssign"
                class="not-as-small text-muted w-50"
              >
                <asset-options
                  :value="usedByValue"
                  @select="usedByChange"
                  @remove="usedByRemove"
                >
                  <template slot="label">
                    <slot name="label" />
                  </template>

                  <template #image="{ option }">
                    <img
                      :src="`/managed_asset_images/${option.id}`"
                      :style="imageStyle"
                    >
                  </template>
                </asset-options>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import strings from "mixins/string";
  import associatedInformation from "mixins/assets/associated_information";
  import AssetOptions from "../asset_options.vue";

  export default {
    components: {
      AssetOptions,
    },
    mixins: [strings, associatedInformation],
    data() {
      return {
        usedByValue: null,
        managedByValue: null,
      };
    },
    computed: {
      ...mapGetters(["currentAsset"]),
      associatedItem() {
        return this.currentAsset ? this.currentAsset.usedBy || this.currentAsset.managedBy : [];
      },
      imageStyle() {
        return 'height: 1.25rem; width: 1.875rem; object-fit: contain;';
      },
    },
    methods: {
      usedByChange(obj) {
        this.usedByValue = obj.asset;
        this.itemsObject.usedBy = {
          usedById: this.currentAsset.usedBy.id,
          assetId: obj.asset.id,
        };
      },
      usedByRemove() {
        this.usedByValue = null;
        this.itemsObject.usedBy = null;
      },
      managedByChange(obj) {
        this.managedByValue = obj.asset;
        this.itemsObject.managedBy = {
          managedById: this.currentAsset.managedBy.id,
          assetId: obj.asset.id,
        };
      },
      managedByRemove() {
        this.managedByValue = null;
        this.itemsObject.managedBy = null;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .modal-max-height {
      max-height: 13rem;
  }
</style>
