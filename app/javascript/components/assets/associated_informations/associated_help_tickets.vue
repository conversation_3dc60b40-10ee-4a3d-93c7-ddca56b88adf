<template>
  <div v-if="associatedItems && associatedItems.length">
    <div
      class="mb-1 mt-3 not-as-big clickable"
      @click="openAssociatedItems(`${associatedType}`)"
    >
      Associated Ticket(s)
      <small v-if="associatedItems.length > 0">
        ({{ associatedItems.length }})&nbsp;
        <i
          class="clickable small"
          :class="arrowDirection(openedAccordion)"
        />
      </small>
    </div>

    <div
      v-if="openedAccordion == associatedType"
      :class="scrollableTable"
      class="associated-scrollable overflow-auto mb-3"
    >
      <p class="text-muted">
        This asset is associated with the following tickets.
      </p>
      <table class="table table-striped ">
        <thead>
          <tr>
            <th class="nav-dark-bg-text">
              Ticket #
            </th>
            <th class="nav-dark-bg-text">
              Subject
            </th>
            <th
              v-show="readyToAssign"
              class="nav-dark-bg-text"
            >
              Assign to asset
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(ticket, index) in associatedItems"
            :key="ticket.id"
          >
            <td class="truncate">
              #{{ ticket.ticketNumber }}
            </td>
            <td
              v-tooltip="{
                content: ticket.subject,
                show: (ticket.subject && ticket.subject.length > 30 && hoveredIndex == ticket.id),
                trigger: 'manual'
              }"
              class="truncate"
              @mouseover="hoveredIndex = ticket.id"
              @mouseleave="hoveredIndex = null"
            >
              {{ truncate(ticket.subject, 25) }}
            </td>
            <td
              v-show="readyToAssign"
              class="not-as-small text-muted w-50"
            >
              <asset-options
                compact
                :asset-index="index"
                :value="itemValues[index]"
                :associated-merged-assets="associatedMergedAssets"
                @select="handleItemChange"
                @remove="handleItemRemove"
              >
                <template slot="label">
                  <slot name="label" />
                </template>

                <template #image="{ option }">
                  <img
                    :src="`/managed_asset_images/${option.id}`"
                    :style="imageStyle"
                  >
                </template>
              </asset-options>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import strings from "mixins/string";
  import associatedInformation from "mixins/assets/associated_information";
  import permissionsHelper from 'mixins/permissions_helper';
  import AssetOptions from "../asset_options.vue";

  export default {
    components: {
      AssetOptions,
    },
    mixins: [strings, associatedInformation, permissionsHelper],
    computed: {
      ...mapGetters(["currentAsset", "associateTicketsHistory"]),
      associatedItems() {
        return this.associateTicketsHistory;
      },
      imageStyle() {
        return 'height: 20px; width: 30px; object-fit: contain;';
      },
    },
    watch: {
      currentAsset() {
        if (this.hasHelpTickets()) {
          this.$store.dispatch("fetchAssociateTicketsHistory", this.currentAsset.helpTickets);
        }
      },
    },
    methods: {
      onWorkspaceChange() {
        if (this.hasHelpTickets()) {
          this.$store.dispatch("fetchAssociateTicketsHistory", this.currentAsset.helpTickets);
        }
      },
      hasHelpTickets() {
        return this.currentAsset?.helpTickets?.length;
      },
    },
  };
</script>
