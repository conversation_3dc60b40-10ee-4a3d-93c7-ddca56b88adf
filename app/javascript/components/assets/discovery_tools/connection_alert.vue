<template>
  <div
    v-if="alertVisible"
    class="alert-item d-flex align-items-center mb-3"
    :class="alertsMapping[alertType].class"
  >
    <div class="alert-item-content d-flex align-items-center">
      <i
        class="mx-3 icon"
        :class="[alertsMapping[alertType].icon]"
      />
      <p class="inner-text m-0 pr-3">
        {{ alertsMapping[alertType].messageStart }}
        <strong>
          {{ source }}
        </strong>
        {{ alertsMapping[alertType].messageEnd }}
      </p>
      <i 
        v-tooltip="actionTip"
        class="nulodgicon-android-close mr-3"
        @click.stop="dismissAlert"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';

  export default {
    props: {
      isSyncError: {
        type: Boolean,
        default: false,
      },
      alertType: {
        type: String,
        required: true,
      },
      assetCount: {
        type: Number,
        default: 1,
      },
      source: {
        type: String,
        default: 'integration',
      },
      integrationId: {
        type: Number,
        default: 0,
      },
      deleteFlag: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        alertsMapping: {
          outOfSync: {
            icon: "genuicon-info-circled out-of-sync-icon",
            class: "out-of-sync",
            messageStart: `We found ${this.assetCount} active ${this.pluralize('asset', 'assets')} in Genuity that ${this.pluralize('is', 'are')} out of sync with what `,
            messageEnd: `is reporting.`,
            key: 'out_of_sync',
          },
          notReporting: { 
            icon: "genuicon-exclamation-circle not-reporting-icon",
            class: "not-reporting",
            messageStart: `We found ${this.assetCount} active ${this.pluralize('asset', 'assets')} in Genuity that `,
            messageEnd: ` ${this.pluralize('is', 'are')} not reporting.`,
            key: 'not_reporting',
          },
          newAsset: { 
            icon: "nulodgicon-android-add-circle new-asset-icon",
            class: "new-asset",
            messageStart: `We found ${this.assetCount} new ${this.pluralize('asset', 'assets')} in`,
            messageEnd: `.`,
            key: 'new_asset',
          },
          failed: { 
            icon: "genuicon-exclamation-triangle failed-icon",
            class: "failed",
            messageStart: ``,
            messageEnd: `is no longer syncing.`,
            key: 'failed',
          },
        },
        alertVisible: true,
      };
    },
    computed: {
      actionTip() {
        return this.deleteFlag ? "Delete this alert" : "Dismiss this alert";
      },
    },
    methods: {
      pluralize(singular, plural) {
        return this.assetCount === 1 ? singular : plural;
      },
      dismissAlert() {
        const params = { alert_key: this.alertsMapping[this.alertType].key, count: this.assetCount };
        if (this.deleteFlag) { params.delete = true; }

        http
          .post(`company_integrations/${this.integrationId}/dismiss_delete_alert`, params)
          .then(() => {
            this.$store.dispatch('fetchCompanyIntegrations');
            this.$store.dispatch('fetchCompanyAlertsInfo');
            if (this.deleteFlag) {
              this.$emit('fetch-dismissed'); 
            }
          });
      },
    },
  };
</script>

<style lang="scss" scoped>

  .alert-item-content {
    width: 120%;
    height: 120%;
    margin-left: 0.45rem;
    background: var(--themed-main-bg);
    border-radius: 0.75rem;
  }

  .alert-item {
    border-radius: 0.5rem;
    overflow: hidden;
    max-width: 25.5rem;
    width: fit-content;
    z-index: 110;
    box-shadow: 0px 8px 24px rgb(0,0,0,0.3);
  }

  .out-of-sync {
    background: #fbba1e !important;
    height: 4.5rem;
  }

  .not-reporting {
    background: #f17f1e !important;
    height: 4.5rem;
  }

  .new-asset {
    background: var(--themed-link);
    height: 3.5rem;
  }

  .failed {
    background: red;
    height: 3.5rem;
  }

  .out-of-sync-icon {
    color: #fbba1e !important;
  }

  .not-reporting-icon {
    color: #f17f1e!important;

  }

  .new-asset-icon {
    color: var(--themed-link) !important;
  }

  .failed-icon {
    color: red !important;
  }

  .icon {
    font-size: 1.5rem;
  }

  .inner-text {
    color: var(--themed-secondary);
  }

  .nulodgicon-android-close {
    color: var(--themed-very-muted) !important;
  }

  .nulodgicon-android-close:hover {
    color: var(--themed-muted) !important;
    cursor: pointer;
  }

  strong {
    border-bottom: 0.15rem solid var(--themed-secondary);
  }
</style>
