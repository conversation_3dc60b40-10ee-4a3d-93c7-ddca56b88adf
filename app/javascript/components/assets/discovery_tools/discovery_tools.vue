<template>
  <div class="d-flex">
    <div class="mt-4 align-items-center vertical-menu bg-lighter justify-content-center box box--with-heading">
      <div class="box__heading p-2 pl-2 text-center heading d-flex align-items-center">
        <i class="genuicon-nav-connectors sub-menu-item__icon icon pr-1" />
        <p class="p-0 m-0 heading-text">
          Asset Discovery Tools
        </p>
      </div>
      <div class="d-flex flex-column inner-content w-100">
        <router-link
          to="/discovery_tools/connections"
          class="subpage-menu__item menu-item"
        >
          Your Connections
        </router-link>
        <router-link
          to="/discovery_tools/agents"
          class="subpage-menu__item menu-item"
          :class="{'router-link-exact-active router-link-active': subIsActive(['/discovery_tools/connectors/agent'])}"
        >
          Discovery Agents
          <span
            v-if="expiredAgentsCount > 0"
            v-tooltip="tooltipText('agents')"
            class="data-badge sub-menu-badge"
          >{{ expiredAgentsCount }}</span>
        </router-link>
        <router-link
          to="/discovery_tools/probes"
          class="subpage-menu__item menu-item"
          :class="{'router-link-exact-active router-link-active': subIsActive(['/discovery_tools/connectors/network_probe'])}"
        >
          Genuity Probes
          <span
            v-if="expiredProbesCount > 0"
            v-tooltip="tooltipText('probes')"
            class="data-badge sub-menu-badge"
          >{{ expiredProbesCount }}</span>
        </router-link>
        <router-link
          to="/discovery_tools/logs"
          class="subpage-menu__item menu-item"
        >
          Integration Logs
        </router-link>
        <div class="border-top border-secondary-subtle mt-3 mx-4"/>
        <router-link
          id="assetDiscoveryHolder"
          to="/discovery_tools/connectors"
          class="subpage-menu__item menu-item"
          :class="{'router-link-exact-active router-link-active': subIsActive(['/network_probe', '/agent', '/self_onboarding', '/discovery_tools/connectors']) || isDiscoveryToolsRoute()}"
        >
          + Add Connectors
        </router-link>
      </div>
    </div>
    <router-view />
  </div>
</template>

<script>

import { mapGetters } from 'vuex';
import permissionsHelper from 'mixins/permissions_helper';

export default {
  mixins: [permissionsHelper],
  computed: {
    ...mapGetters([
      'appsLocationSummary',
    ]),
    expiredAgentsCount() {
      return this.appsLocationSummary.agentCount;
    },
    expiredProbesCount() {
      return this.appsLocationSummary.probeCount;
    },
    showExport() {
      return this.$route.path === "/discovery_tools/agents";
    },
  },
  methods: {
    tooltipText(type) {
      return `Number of ${type} using an outdated version`;
    },
    onWorkspaceChange() {
      this.$store.dispatch('fetchLatestProbeVersion');
      this.$store.dispatch('fetchLatestAgentVersion');
      this.$store.dispatch('fetchAppsLocationSummary');
    },
    subIsActive(input) {
      const paths = Array.isArray(input) ? input : [input];
      return paths.includes(this.$route.path);
    },
    downloadAssetData() {
      const url = '/managed_assets/discovery_tools/agents/export';
      window.open(url, '_blank');
    },
    isDiscoveryToolsRoute() {
      return this.$route.path === '/discovery_tools' || this.$route.path === '/discovery_tools/';
    },
  },
};
</script>

<style lang="scss">
  .vertical-menu {
    min-width: 12.4rem;
    max-width: 12.4rem;
    max-height: 20rem;
    min-height: 20rem;
  }

  .icon {
    color: #d0d0d0 !important;
  }

  .heading {
    background-color: #4f3c84;
    padding: 0.8rem !important;
    padding-left: 1.2rem !important
  }

  .heading-text {
    font-size: 0.88rem !important;
  }

  .inner-content:last-child {
    margin-bottom: 0.75rem;
  }

  p {
    font-size: 0.9rem !important;
  }

  .menu-item {
    padding: 0.45rem 1rem;
    margin: 0.55rem 0.8rem !important;
    margin-bottom: 0rem !important;
    border-radius: 0.45rem !important;
    font-size: 1rem !important;
  }
</style>
