<template>
  <div class="pb-5 w-100 margin-top ml-5">
    <div v-if="probeLocations === null">
      <div class="clearfix mt-3">
        <h5 class="float-left">
          Loading Probes
        </h5>
        <sync-loader
          :loading="true"
          class="float-left ml-3 mt-1"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
    </div>

    <div
      v-else
      class="mt-3"
    >
      <div class="row mt-5 mb-4">
        <div class="col-md-6">
          <input
            ref="searchTerms"
            class="form-control"
            placeholder="Search probes by computer name, IP address or mac address"
            :value="search"
            @input="updateSearch"
          >
        </div>
        <div class="col-md-6 d-flex float-right justify-content-end mt-1">
          <span class="small">
            <span class="text-muted align-text-bottom mr-1">
              <span>Results per page</span>
            </span>
            <select
              id="filtersPerPage"
              class="form-control form-control-sm d-inline-block select-per-page-filter"
              :input="pageSize"
              :value="pageSize"
              data-tc-filters-per-page
              @input="changePageSize"
            >
              <option>25</option>
              <option>50</option>
              <option>100</option>
            </select>
          </span>
          <div
            v-if="pageCount > 1"
            class="ml-3"
          >
            <paginate
              ref="paginate"
              :click-handler="pageSelected"
              :container-class="'pagination pagination-sm'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="page"
              :page-range="0"
              class="float-right mb-0"
            />
          </div>
        </div>
      </div>

      <out-dated-apps
        :app-locations="probeLocations"
        :app-name="'probe'"
      />

      <div v-if="probeLocations.length > 0">
        <table class="table mb-0">
          <thead>
            <tr>
              <th class="text-center">
                Status
              </th>
              <th>Computer Name</th>
              <th>Probe Name</th>
              <th>Mac Addresses</th>
              <th
                class="cursor-pointer"
                @click="sortProbes"
              >
                Version
                <i :class="activeSortIcon" />
              </th>
              <th>Location</th>
              <th>Managed by</th>
              <th>Last Refreshed</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="probe in sortedLocationApps"
              :key="probe.id"
              class="text-secondary"
            >
              <td class="small align-middle center-status">
                <span
                  v-if="enabledStatusText(probe.isEnabled) === 'Active' && !probe.expiredVersion"
                  v-tooltip="enabledStatusText(probe.isEnabled)"
                  class="network-status success"
                />
                <span
                  v-else-if="probe.expiredVersion"
                  v-tooltip="'Outdated'"
                  class="network-status app-outdated"
                />
                <span
                  v-else
                  v-tooltip="enabledStatusText(probe.isEnabled)"
                  class="network-status warning"
                />
              </td>
              <td class="small align-middle">
                <button
                  v-tooltip="{
                    content: probe.computerName,
                    show: hoveredIndex == probe.id && probe.computerName.length > 19,
                    trigger: 'manual',
                  }"
                  class="btn btn-sm btn-link"
                  @click.prevent="viewMoreInfo(probe)"
                  @mouseover="hoveredIndex = probe.id"
                  @mouseleave="hoveredIndex = null"
                >
                  <span>{{ truncate(probe.computerName, 19) }}</span>
                </button>
              </td>
              <td class="small align-middle">
                <span v-tooltip="probeNameText(probe)">
                  {{ truncate(probeNameText(probe), 15) }}
                </span>
              </td>
              <td class="small align-middle">
                <div
                  v-if="probe.macAddresses && probe.macAddresses.length > 1"
                  class="tooltip"
                >
                  {{ macAddressShortened(probe.macAddresses) }}
                  <span class="tooltiptext">{{ probe.macAddresses.join() }}</span>
                </div>
                <div v-else>
                  <span>{{ macAddressShortened(probe.macAddresses) }}</span>
                </div>
              </td>
              <td>
                <div class="flex text-center">
                  <span>
                    {{ probe.appVersion }}
                  </span>
                  <span
                    v-show="probe.expiredVersion"
                    v-tooltip="'The installed version is outdated and will no longer post the updates. Please download the latest version'"
                    class="warning-icon"
                  >
                    <i class="genuicon-info-circled" />
                  </span>
                </div>
              </td>
              <td class="small align-middle">
                <span>{{ locationText(probe) }}</span>
              </td>
              <td class="small align-middle">
                <span>{{ managedByText(probe) }}</span>
              </td>
              <td class="small align-middle">
                <span>{{ showDate(probe.lastScannedAt) }}</span>
              </td>
              <td
                v-if="isWrite"
                class="align-middle pr-0 border-0 d-flex btns-holder"
              >
                <span :class="{'disable-cursor': probe.expiredVersion}">
                  <material-toggle
                    :key="componentKey"
                    class="probe-status-toggle"
                    :init-active="probe.isEnabled && !probe.expiredVersion"
                    :class="{'disable-toggle': probe.expiredVersion}"
                    @toggle-sample="toggleStatus(probe.id)"
                  />
                </span>
                <button
                  class="btn btn-sm btn-link"
                  @click.prevent="viewEditModal(probe)"
                >
                  <i class="nulodgicon-edit mr-1" />
                </button>
                <button
                  class="btn btn-sm btn-link text-danger"
                  @click.prevent="openRemoveModal(probe.id)"
                >
                  <i class="nulodgicon-trash-b mr-1" />
                </button>
              </td>
              <td v-if="!isWrite" />
            </tr>
          </tbody>
        </table>

        <div class="more-info-modal">
          <sweet-modal
            ref="moreInfoModal"
            v-sweet-esc
            @close="changeViewLogStatus()"
          >
            <template slot="default">
              <div class="row m-0">
                <div
                  v-if="viewMoreInfoProbe"
                  class="heading-area col-12"
                >
                  <h4
                    class="mb-0 text-white"
                    style="line-height: 2rem;"
                  >
                    More information about <strong>{{ viewMoreInfoProbe.computerName }}</strong>
                  </h4>
                </div>
                <div
                  v-if="viewMoreInfoProbe"
                  class="info-area col-12"
                >
                  <div class="row">
                    <div class="col-12">
                      <p class="font-weight-bold mb-1">
                        <u>Basic Information</u>
                      </p>
                    </div>
                    <div class="col-md-6 col-sm-12">
                      <p
                        class="font-weight-bold mb-0"
                        style="word-wrap: break-word;"
                      >
                        IP Address
                      </p>
                      <p>{{ viewMoreInfoProbe.ipAddress }}</p>

                      <p class="font-weight-bold mb-0">
                        MAC Address
                      </p>
                      <p>
                        <span class="d-inline-block align-middle">
                          <ul class="mb-0">
                            <li
                              v-for="macAddress in viewMoreInfoProbe.macAddresses"
                              :key="macAddress"
                            >
                              {{ macAddress }}
                            </li>
                          </ul>
                        </span>
                      </p>

                      <p class="font-weight-bold mb-0">
                        Location
                      </p>
                      <p>{{ locationText(viewMoreInfoProbe) }}</p>
                    </div>
                    <div class="col-md-6 col-sm-12">
                      <p class="font-weight-bold mb-0">
                        Managed by
                      </p>
                      <p>{{ managedByText(viewMoreInfoProbe) }}</p>
                      <p class="font-weight-bold mb-0">
                        Status
                      </p>
                      <p>{{ enabledStatusText(viewMoreInfoProbe.isEnabled) }}</p>
                      <p class="font-weight-bold mb-0">
                        Last Refreshed
                      </p>
                      <p>{{ showDate(viewMoreInfoProbe.lastScannedAt) }}</p>
                    </div>
                  </div>
                  <div
                    v-if="isSuperAdmin && !viewLogs"
                    class="text-center"
                  >
                    <a
                      href="#"
                      @click.stop.prevent="getLogs(currentProbe.id)"
                    >
                      View logs
                    </a>
                  </div>
                  <span v-if="loading">
                    <sync-loader
                      :loading="true"
                      class="ml-3 mt-1"
                      color="#0d6efd"
                      size="0.5rem"
                    />
                  </span>
                  <span v-else>
                    <div
                      v-if="viewLogs"
                      class="mb-2"
                    >
                      <p class="font-weight-bold mb-1">
                        <u>Log Files</u>
                      </p>
                      <div
                        v-if="discoveryLogs.length > 0"
                        class="mt-2 log-scroll-view"
                      >
                        <table class="table mb-0">
                          <thead>
                            <tr>
                              <th>File Name</th>
                              <th>Created At</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="log in discoveryLogs"
                              :key="log.id"
                            >
                              <td>
                                <a
                                  class="float-left"
                                  :href="log.url"
                                >
                                  <div class="d-inline-block mt-1">
                                    <i class="nulodgicon-file-o ml-2" />
                                    {{ log.name }}
                                  </div>
                                </a>
                              </td>
                              <td>{{ showDate(log.createdAt) }}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div v-else>
                        <p class="ml-1"> There are no logs present. </p>
                      </div>
                    </div>
                  </span>
                  <button
                    class="btn btn-link text-secondary float-right mr-3"
                    @click.stop="closeMoreInfoModal"
                  >
                    Close
                  </button>
                </div>
              </div>
            </template>
          </sweet-modal>
        </div>

        <sweet-modal
          ref="removeModal"
          v-sweet-esc
          title="Before we proceed..."
        >
          <template slot="default">
            <div class="text-center">
              <h6 class="mb-3">
                Are you sure you want to remove selected probe installation?
              </h6>
            </div>
          </template>
          <button
            slot="button"
            class="btn btn-link text-secondary"
            @click.stop="closeRemove"
          >
            No
          </button>
          <button
            slot="button"
            class="btn btn-primary ml-2"
            @click.stop="removeLocation"
          >
            Yes, Remove.
          </button>
        </sweet-modal>

        <sweet-modal
          ref="editProbeModal"
          v-sweet-esc
          blocking
          title="Edit Probe Installation"
          width="58%"
          class="subnet-nested-modal"
          @close="resetEditProbeModal()"
        >
          <template slot="default">
            <div class="row">
              <div class="col-md-6 mb-2">
                Location
              </div>
              <div
                v-if="currentProbe"
                class="col-md-6 mb-2"
              >
                <location-options
                  :multiple="false"
                  :probe-location="currentProbe.location"
                  :module-type="'Network Probe'"
                  @input="onLocationChange"
                  @remove="removeProbeLocation"
                  @fetch-locations="fetchLocations"
                />
              </div>
              <div class="col-md-6 mb-2">
                Managed by
              </div>
              <div
                v-if="currentProbe"
                class="col-md-6 mb-2"
              >
                <contributors-select
                  :value="managedBy"
                  data-tc-managed-by
                  @select="setManagedById"
                />
              </div>
              <div class="col-md-6 mb-2">
                Status
              </div>
              <div
                v-if="currentProbe"
                class="col-md-6 mb-2"
              >
                <material-toggle
                  :key="componentKey"
                  :init-active="currentProbe.isEnabled"
                  @toggle-sample="toggleStatus(currentProbe.id)"
                />
                <span>
                  {{
                    enabledStatusText(currentProbe.isEnabled)
                  }}
                </span>
              </div>
            </div>
            <div
              v-if="currentProbe && currentProbe.probeConfig"
              class="row"
            >
              <h5 class="col-md-12 mb-3">
                Probe Configurations
              </h5>
              <div class="col-12">
                <div class="mb-2">
                  <div class="row">
                    <div class="col-6">
                      Probe Name
                    </div>

                    <div class="col-6">
                      <div class="probe-name-edit-container">
                        <div
                          v-if="editProbeName"
                          class="align-items-center d-flex"
                        >
                          <div class="mr-2">
                            <input
                              id="probe-name"
                              v-model="currentProbe.probeConfig.probeName"
                              class="form-control"
                              type="text"
                              name="probename"
                            >
                          </div>
                          <div>
                            <span
                              class="btn-link cursor-pointer"
                              @click.stop="updateProbeName()"
                            >
                              <i class="nulodgicon-checkmark" />
                            </span>
                            <span 
                              class="remove-alert"
                              @click.stop="cancelEditProbeName()"
                            >
                              &times;
                            </span>
                          </div>
                        </div>

                        <div v-else>
                          <span v-tooltip="probeNameText(currentProbe)">
                            {{ truncate(probeNameText(currentProbe), 20) }}
                          </span>
                          <button
                            class="btn btn-sm btn-link w-auto"
                            @click.stop="enableEditProbeName()"
                          >
                            <i class="nulodgicon-edit mr-1" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-2">
                  <div class="row">
                    <div class="col-md-6">
                      App Version
                    </div>
                    <div class="col-md-6">
                      {{ currentProbe.appVersion }}
                    </div>
                  </div>
                </div>

                <div class="mt-4">
                  <div class="sub-menu clearfix">
                    <div class="float-left module-sub-tabs">
                      <span
                        class="sub-menu-item cursor-pointer"
                        :class="{'router-link-exact-active': selectedMenu == 'subnet_config'}"
                        @click.prevent="setMenu('subnet_config')"
                      >
                        SUBNETS
                      </span>
                      <span
                        class="sub-menu-item cursor-pointer"
                        :class="{'router-link-exact-active': selectedMenu == 'wmi_config'}"
                        @click.prevent="setMenu('wmi_config')"
                      >
                        WMI
                      </span>
                      <span
                        class="sub-menu-item cursor-pointer"
                        :class="{'router-link-exact-active': selectedMenu == 'ssh_config'}"
                        @click.prevent="setMenu('ssh_config')"
                      >
                        SSH
                      </span>
                      <span
                        class="sub-menu-item cursor-pointer"
                        :class="{'router-link-exact-active': selectedMenu == 'snmp_config'}"
                        @click.prevent="setMenu('snmp_config')"
                      >
                        SNMP
                      </span>
                      <span
                        class="sub-menu-item cursor-pointer"
                        :class="{'router-link-exact-active': selectedMenu == 'snmpv_three_config'}"
                        @click.prevent="setMenu('snmpv_three_config')"
                      >
                        SNMPV3
                      </span>
                      <span
                        class="sub-menu-item cursor-pointer"
                        :class="{'router-link-exact-active': selectedMenu == 'schedule_config'}"
                        @click.prevent="setMenu('schedule_config')"
                      >
                        SCHEDULER
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  v-if="selectedMenu == 'snmp_config'"
                  class="mb-2"
                >
                  <snmp-configs
                    :current-probe="currentProbe"
                    @update-btn-visibility="updateSubmitButtonVisibility"
                    @update-probe-config="updateProbeConfig"
                  />
                </div>
                <div
                  v-if="selectedMenu == 'snmpv_three_config'"
                  class="mb-2"
                >
                  <snmpv-three-configs
                    :current-probe="currentProbe"
                    @update-btn-visibility="updateSubmitButtonVisibility"
                    @update-probe-config="updateProbeConfig"
                  />
                </div>
                <div
                  v-if="selectedMenu == 'wmi_config'"
                  class="mb-2"
                >
                  <wmi-configs
                    :current-probe="currentProbe"
                    @update-btn-visibility="updateSubmitButtonVisibility"
                    @update-probe-config="updateProbeConfig"
                  />
                </div>
                <div
                  v-if="selectedMenu == 'ssh_config'"
                  class="mb-2"
                >
                  <ssh-configs
                    :current-probe="currentProbe"
                    @update-btn-visibility="updateSubmitButtonVisibility"
                    @update-probe-config="updateProbeConfig"
                  />
                </div>
                <div
                  v-if="selectedMenu == 'subnet_config'"
                  class="mb-2"
                >
                  <subnet-configs
                    :current-probe="currentProbe"
                    @update-btn-visibility="updateSubmitButtonVisibility"
                    @update-probe-config="updateProbeConfig"
                  />
                </div>
                <div
                  v-if="selectedMenu == 'schedule_config'"
                  class="mb-2"
                >
                  <schedule-config
                    :current-probe="currentProbe"
                  />
                </div>
              </div>
            </div>
          </template>
          <button
            slot="button"
            class="btn btn-link text-secondary"
            @click.stop="closeEditModal"
          >
            Cancel
          </button>
          <button
            slot="button"
            v-tooltip="submitBtnTooltipText"
            class="btn btn-primary ml-2"
            :disabled="shouldDisableSubmitBtn"
            @click.stop="editProbeInstallation"
          >
            Save
          </button>
        </sweet-modal>
      </div>
      <div
        v-else-if="loading"
        class="text-center mt-5 clear-both"
      >
        <h5 class="text-secondary font-weight-normal">
          Loading
          <span
            class="ml-3 d-inline-block"
          >
            <sync-loader
              :loading="true"
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </h5>
      </div>

      <div
        v-else
        class="text-center mt-5 clear-both"
      >
        <h4>There are no probes installed yet.</h4>
        <h5 class="text-secondary font-weight-normal">
          <router-link to="/discovery_tools/connectors/network_probe">
            Download and install the probe
          </router-link>
          to get started.
        </h5>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import { mapGetters } from 'vuex';
import { SweetModal } from 'sweet-modal-vue';
import Paginate from 'vuejs-paginate';
import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
import _ from "lodash";
import sortHelper from "mixins/sorting_helper";
import StringHelper from "mixins/string";
import permissionsHelper from "mixins/permissions_helper";
import locationsFilter from 'mixins/locations_filter';
import ContributorsSelect from '../../shared/contributors_select.vue';
import MaterialToggle from '../../shared/material_toggle.vue';
import SnmpvThreeConfigs from './snmpv_three_configs.vue';
import SnmpConfigs from './snmp_configs.vue';
import SshConfigs from './ssh_configs.vue';
import SubnetConfigs from './subnet_configs.vue';
import WmiConfigs from './wmi_configs.vue';
import ScheduleConfig from './schedule_config.vue';
import OutDatedApps from '../../shared/outdated_location_apps.vue';
import LocationOptions from '../../shared/location_options.vue';
import pageSizeInCookies from '../../../mixins/page_size_helper';

export default {

  components: {
    SweetModal,
    ContributorsSelect,
    SyncLoader,
    MaterialToggle,
    Paginate,
    SnmpConfigs,
    SnmpvThreeConfigs,
    SshConfigs,
    SubnetConfigs,
    WmiConfigs,
    ScheduleConfig,
    OutDatedApps,
    LocationOptions,
  },
  mixins: [
    sortHelper,
    StringHelper,
    permissionsHelper,
    locationsFilter,
    pageSizeInCookies,
  ],
  data() {
    return {
      probeLocations: [],
      pageCount: 1,
      pageIndex: 0,
      pageSize: 25,
      searchTerm: '',
      search: null,
      currentId: null,
      viewMoreInfoProbe: null,
      currentProbe: null,
      loading: false,
      viewLogs: false,
      discoveryLogs: [],
      componentKey: 0,
      selectedMenu: 'subnet_config',
      activeSort: "ComputerName",
      activeSortDirection: "desc",
      hoveredIndex: null,
      preProbeName: '',
      editProbeName: false,
      shouldDisableSubmitBtn: false,
    };
  },
  computed: {
    ...mapGetters(['companyLocations','companyUsers']),
    page() {
      return this.pageIndex;
    },
    managedBy() {
      if (this.currentProbe.managedByContributorId) {  
        return { id: this.currentProbe.managedByContributorId };
      }
      return null;
    },
    sortedLocationApps() {
      if (this.probeLocations && this.probeLocations.length > 0)
        return this.activeSort === 'ComputerName' ? this.sortByExpiredVersion : this.probeLocations;
      return [];
    },
    sortByExpiredVersion() {
      return _.orderBy(this.probeLocations, [c => c.expiredVersion], [this.activeSortDirection]);
    },
    activeSortIcon() {
      if (this.activeSortDirection === 'asc') {
          return 'nulodgicon-arrow-up-b';
        }
      return 'nulodgicon-arrow-down-b';
    },
    submitBtnTooltipText() {
      return this.shouldDisableSubmitBtn ? 'Submit the required action before saving probe installation' : '';
    },
  },
  watch: {
    probeLocations() {
      if (this.probeLocations.length && !this.locations?.length) {
        const locationParams = {
          offset: this.offset,
          limit: this.fetchedLocations,
        };
        this.fetchLocations(locationParams);
      }
    },
  },
  methods: {
    onWorkspaceChange() {
      this.$store.dispatch('fetchCompanyLocations');
      this.$store.dispatch('fetchCompanyUserOptions');
      this.getProbeLocations();
      this.checkPerPage();
    },
    setManagedById(contributor){
      this.currentProbe.managedByContributorId = contributor.id;
    },
    setPageIndex(pageIndex) {
      this.pageIndex = pageIndex;
    },
    pageSelected(p) {
      this.setPageIndex(p - 1);
      this.getProbeLocations();
    },
    checkPerPage() {
      if (this.perPagePresentInCookie('assets-probe-module=')) {
        const cookieData = this.getCookieValue('assets-probe-per-page');
        if (cookieData) {
          this.pageSize = parseInt(cookieData, 10);
        }
      }
    },
    changePageSize(e) {
      this.pageSize = e.currentTarget.value;
      this.setPageIndex(0);
      this.setPerPageInCookie("assets-probe", parseInt(this.pageSize, 10));
      this.getProbeLocations();
    },
    getProbeLocations() {
      this.loading = true;
      const params = {
        page: this.pageIndex + 1,
        page_size: this.pageSize,
        sort_by: this.activeSort,
        order_by: this.activeSortDirection,
      };

      if (this.search) {
        params.search = this.search;
      }
      http
        .get('/probe_locations.json', { params })
        .then((res) => {
          this.probeLocations = res.data.probeLocations;
          this.loading = false;
          this.$store.state.loading = false;
          this.pageCount = res.data.pageCount;
        })
        .catch(() => {
          this.loading = false;
          this.$store.state.loading = false;
          this.emitError(
            'Sorry, there was an error loading probe installations. Please try again.'
          );
        });
    },
    updateSearch: _.debounce(
      function updateSearch() {
        this.setPageIndex(0);
        if (this.$refs.searchTerms.value) {
          this.searchTerm = "all";
          this.search = this.$refs.searchTerms.value;
        } else {
          this.searchTerm = null;
          this.search = null;
        }
        this.getProbeLocations();
      },
      1000
    ),
    macAddressShortened(macAddresses) {
      if (macAddresses === null || macAddresses.length === 0) {
        return '';
      }
      if (macAddresses.length === 1) {
        return macAddresses[0];
      }
      return `${macAddresses[0]}, ...`;
    },
    showDate(date) {
      if (date === null || date === '') {
        return 'Not Scanned Yet';
      }
      return moment(date.toString()).format('MMM DD, YYYY, h:mm:ss a');
    },
    enabledStatusText(status) {
      if (status) {
        return 'Active';
      }
      return 'Disabled';
    },
    tooltipText(status) {
      if (status) {
        return 'Data coming from this computer will be ignored';
      }
      return 'Data coming from this computer will be added';
    },
    locationText(probe) {
      if (probe.location != null) {
        return probe.location.name;
      }
      return 'Not set';
    },
    managedByText(probe) {
      if (probe.managedByContributorId) {
        return `${probe.manager}`;
      }
      return "Not set";
    },
    probeNameText(probe) {
      if (probe.probeConfig && probe.probeConfig.probeName) {
        return probe.probeConfig.probeName;
      }
      return "Not set";
    },
    enableEditProbeName() {
      this.editProbeName = true;
    },
    cancelEditProbeName() {
      if (this.editProbeName) {
        this.currentProbe.probeConfig.probeName = this.preProbeName;
        this.editProbeName = false;
      }
    },
    updateProbeName() {
      if (this.currentProbe.probeConfig.probeName.trim() !== '') {
        http
          .patch(`/probe_configs/${this.currentProbe.probeConfig.id}.json`, {
            probe_name: this.currentProbe.probeConfig.probeName,
          })
          .then((res) => {
            this.currentProbe.probeConfig = res.data;
            this.editProbeName = false;
            this.emitSuccess('Probe name updated successfully');
          })
          .catch((error) => {
            let errorMessage = 'Sorry, there was an error updating the Probe name. Please try again.';
            if (error.response.data.message) {
              errorMessage = error.response.data.message;
            }
            this.emitError(errorMessage);
          });
      } else {
        this.currentProbe.probeConfig.probeName = this.preProbeName;
        this.editProbeName = false;
        this.emitError('Probe name cannot be empty.');
      }
    },
    toggleStatusText(status) {
      if (status) {
        return 'Disable';
      }
      return 'Active';
    },
    toggleSuccessText(status) {
      if (status) {
        return 'Probe is successfully enabled';
      }
      return 'Probe is successfully disabled';
    },
    toggleStatus(id) {
      http
        .put(`/probe_locations/${id}.json`, { toggle: true })
        .then(() => {
          this.probeLocations.find(x => x.id === id).isEnabled = !this.probeLocations.find(
            x => x.id === id
          ).isEnabled;
          this.emitSuccess(
            this.toggleSuccessText(this.probeLocations.find(x => x.id === id).isEnabled)
          );
        })
        .catch((error) => {
          let errorMessage = 'Sorry, there was an error toggling status. Please try again.';
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          this.emitError(errorMessage);
        })
        .finally(() => {
          this.componentKey += 1;
        });
    },
    editProbeInstallation() {
      http
        .put(`/probe_locations/${this.currentProbe.id}.json`, {
          toggle: false,
          probe: this.currentProbe,
        })
        .then((res) => {
          this.probeLocations = res.data;
          this.$refs.editProbeModal.close();
          this.emitSuccess('Probe edited successfully');
        })
        .catch((error) => {
          let errorMessage = 'Sorry, there was an error editing probe. Please try again.';
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          this.emitError(errorMessage);
        });
    },
    removeLocation() {
      http
        .delete(`/probe_locations/${this.currentId}.json`)
        .then((res) => {
          this.probeLocations = res.data;
          this.$refs.removeModal.close();
        })
        .catch((error) => {
          let errorMessage = 'Sorry, there was an error deleting the installation status. Please try again.';
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          this.emitError(errorMessage);
        });
    },
    openRemoveModal(id) {
      this.currentId = id;
      this.$refs.removeModal.open();
    },
    closeRemove() {
      this.$refs.removeModal.close();
    },
    closeMoreInfoModal() {
      this.changeViewLogStatus();
      this.$refs.moreInfoModal.close();
    },
    viewMoreInfo(probe) {
      this.currentProbe = probe;
      this.viewMoreInfoProbe = probe;
      this.$refs.moreInfoModal.open();
    },
    changeViewLogStatus() {
      this.viewLogs = false;
    },
    resetEditProbeModal() {
      this.selectedMenu = null;
      this.cancelEditProbeName();
    },
    closeEditModal() {
      this.$refs.editProbeModal.close();
    },
    viewEditModal(probe) {
      this.currentProbe = probe;
      this.selectedMenu = 'subnet_config';
      this.preProbeName = this.currentProbe?.probeConfig?.probeName;
      this.$refs.editProbeModal.open();
    },
    getLogs(id) {
      this.loading = true;
      this.viewLogs = !this.viewLogs;
      http
        .get(`/probe_locations/${id}/logs.json`)
        .then((res) => {
          this.discoveryLogs = res.data.discoveryLogs;
        })
        .catch((error) => {
          this.viewLogs = !this.viewLogs;
          this.emitError(`Sorry, there was an error fetching logs (${error.response.data.message}).`);
        })
        .finally(() => { 
          this.loading = false;
        });
    },
    setMenu(val) {
      this.selectedMenu = val;
    },
    sortProbes() {
      this.sortItem('appVersion');
      this.getProbeLocations();
    },
    onLocationChange(location) {
      this.currentProbe.location = location;
      this.currentProbe.locationId = location.id;
    },
    removeProbeLocation() {
      this.currentProbe.location = null;
      this.currentProbe.locationId = null;
    },
    updateSubmitButtonVisibility(val) {
      this.shouldDisableSubmitBtn = val;
    },
    updateProbeConfig(probeConfig) {
      this.currentProbe = probeConfig;
    },
  },
};
</script>

<style lang="scss" scoped>
tr td {
  padding: 10px;
}
.more-info-modal :deep(.sweet-content) {
  padding: 0px !important;

  .heading-area {
    background-color: $themed-dark-drawer-bg;
    color: white;
    padding: 1rem;
    padding-left: 25px;
    text-align: left;
  }

  .info-area {
    padding: 1rem;
    padding-left: 35px;
    text-align: left;
  }
}
.btn-link:hover {
  background-color: Transparent !important;
}
.tooltip {
  position: relative;
  display: inline-block;
}
.tooltip .tooltiptext {
  visibility: hidden;
  width: 150px;
  background-color: $themed-muted;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 10px;
  position: absolute;
  z-index: 1;
  bottom: 150%;
  left: 50%;
  margin-left: -70px;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
}
.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: $gray-600 transparent transparent transparent;
}
.center-status{
  text-align: center;
  vertical-align: middle;
}
:deep(.slide-toggle) {
  margin-bottom: .25rem;
}
.probe-status-toggle {
  margin-top: .28rem;
}
.log-scroll-view {
  max-height: 15em;
  overflow-y: scroll;
}
.cursor-pointer {
  cursor: pointer;
}
.probe-name-edit-container {
  min-height: 2.5rem;
}
:deep(.config-table) {
  tr {
    td {
      padding: 1rem 0.75rem;
      .error-text {
        position: absolute;
      }
    }
  }
}

.remove-alert {
  cursor: pointer;
  font-size: 1.2rem;
  &:hover {
    opacity: 0.65;
  }
}

.warning-icon {
  display: inline-block;
  font-size: 0.75rem;
  font-weight: 500;
  height: 1rem;
  line-height: 1;
  margin: 0 -0.1875rem;
  min-width: 1.25rem;
  padding: 0.1875rem 0.25rem;
  position: relative;
  text-align: center;
  top: 0;
  color: $color-caution;
}

 .cursor-pointer {
   cursor: pointer;
 }

 .app-outdated {
    background: lightgrey;
    border: 1px solid lightgrey;
  }

  .disable-toggle {
    pointer-events: none;
  }

  .disable-cursor {
    cursor: not-allowed;
  }

  .margin-top {
    margin-top: -0.55rem;
  }
</style>
