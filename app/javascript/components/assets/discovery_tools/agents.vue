<template>
  <div class="pb-5 w-100 margin-top ml-5">
    <div v-if="agentLocations === null">
      <div class="clearfix mt-3">
        <h5 class="float-left">
          Loading Agents
        </h5>
        <sync-loader
          :loading="true"
          class="float-left ml-3 mt-1"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
    </div>

    <div
      v-else
      class="mt-3"
    >
      <div class="row mt-5 mb-4">
        <div class="col-md-6">
          <input
            ref="searchTerms"
            class="form-control"
            placeholder="Search agents by computer name, IP address or mac address"
            :value="search"
            @input="updateSearch"
          >
        </div>
        <div class="col-md-6 d-flex float-right justify-content-end mt-1">
          <span class="small">
            <span class="text-muted align-text-bottom mr-1">
              <span>Results per page</span>
            </span>
            <select
              id="filtersPerPage"
              class="form-control form-control-sm d-inline-block select-per-page-filter"
              :input="pageSize"
              :value="pageSize"
              data-tc-filters-per-page
              @input="changePageSize"
            >
              <option>25</option>
              <option>50</option>
              <option>100</option>
            </select>
          </span>
          <div
            v-if="pageCount > 1"
            class="ml-3"
          >
            <paginate
              ref="paginate"
              :click-handler="pageSelected"
              :container-class="'pagination pagination-sm'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="page"
              class="float-right mb-0"
            />
          </div>
        </div>
      </div>
      <out-dated-apps
        :app-locations="agentLocations"
        :app-name="'agent'"
      />
      <div v-if="agentLocations.length > 0">
        <table class="table mb-0">
          <thead>
            <tr>
              <th class="text-center">
                Status
              </th>
              <th>Computer Name</th>
              <th>IP Address</th>
              <th>Mac Addresses</th>
              <th
                class="cursor-pointer"
                @click="sortAgents"
              >
                Version
                <i :class="activeSortIcon" />
              </th>
              <th>Location</th>
              <th>Managed by</th>
              <th>Last Refreshed</th>
              <th v-if="isSuperAdmin">
                App Type
              </th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="agent in sortedLocationApps"
              :key="agent.id"
              class="text-secondary"
            >
              <td v-if="agent.os == 'MAC OS'">
                <div class="d-flex justify-content-center">
                  <img
                    src="https://nulodgic-static-assets.s3.amazonaws.com/images/logos/apple_mac.png"
                    width="22"
                  >
                </div>
              </td>
              <td
                v-else-if="showDeleteAgentWarning(agent, 'condition')"
                class="small align-middle center-status"
              >
                <span
                  v-tooltip="showDeleteAgentWarning(agent, 'tooltip')"
                  class="network-status app-expired"
                />
              </td>
              <td
                v-else-if="!agent.inactive || enabledStatusText(agent.isEnabled) != 'Active'"
                class="small align-middle center-status"
              >
                <span
                  v-if="enabledStatusText(agent.isEnabled) === 'Active' && !agent.expiredVersion"
                  v-tooltip="enabledStatusText(agent.isEnabled)"
                  class="network-status success"
                />
                <span
                  v-else-if="agent.expiredVersion"
                  v-tooltip="'Outdated'"
                  class="network-status app-outdated"
                />
                <span
                  v-else
                  v-tooltip="enabledStatusText(agent.isEnabled)"
                  class="network-status warning"
                />
              </td>
              <td
                v-else
                class="small align-middle center-status"
              >
                <span
                  v-tooltip="`Inactive ${agent.lastActiveTime.replace('about','since')}`"
                  class="network-status in-active"
                />
              </td>
              <td class="small align-middle">
                <button
                  v-tooltip="{
                    content: agent.computerName,
                    show: hoveredIndex == agent.id && agent.computerName.length > 19,
                    trigger: 'manual',
                  }"
                  class="btn btn-sm btn-link"
                  @click.prevent="viewMoreInfo(agent)"
                  @mouseover="hoveredIndex = agent.id"
                  @mouseleave="hoveredIndex = null"
                >
                  <span>{{ truncate(agent.computerName, 19) }}</span>
                </button>
              </td>
              <td class="small align-middle">
                <span>{{ agent.ipAddress }}</span>
              </td>
              <td class="small align-middle">
                <div
                  v-if="agent.macAddresses && agent.macAddresses.length > 1"
                  class="tooltip"
                >
                  {{ macAddressShortened(agent.macAddresses) }}
                  <span class="tooltiptext"> {{ agent.macAddresses.join() }}</span>
                </div>
                <div v-else>
                  <span>{{ macAddressShortened(agent.macAddresses) }}</span>
                </div>
              </td>
              <td>
                <div class="flex text-center">
                  <span>
                    {{ agent.appVersion }}
                  </span>
                  <span
                    v-show="agent.expiredVersion"
                    v-tooltip="'The installed version is outdated and will no longer post the updates. Please download the latest version'"
                    class="warning-icon"
                  >
                    <i class="genuicon-info-circled" />
                  </span>
                </div>
              </td>
              <td class="small align-middle">
                <span>{{ locationText(agent) }}</span>
              </td>
              <td class="small align-middle">
                <span>{{ managedByText(agent) }}</span>
              </td>
              <td class="small align-middle">
                <span>{{ showDate(agent.lastActive) }}</span>
              </td>
              <td
                v-if="agent.os == 'MAC OS' && isSuperAdmin"
                class="small align-middle"
              >
                <span>{{ agentAppType(agent) }}</span>
              </td>
              <td
                v-if="isWrite"
                class="align-middle pr-0 border-0 d-flex"
              >
                <!-- Hide enable and disable functionality
                  <span :class="{'disable-cursor': agent.expiredVersion}">
                  <material-toggle
                    class="agent-status-toggle"
                    :init-active="agent.isEnabled && !agent.expiredVersion"
                    :key="componentKey"
                    :class="{'disable-toggle': agent.expiredVersion}"
                    @toggle-sample="toggleStatus(agent.id)"
                  />
                </span> -->
                <button
                  class="btn btn-sm btn-link"
                  @click.prevent="viewEditModal(agent)"
                >
                  <i class="nulodgicon-edit mr-1" />
                </button>
                <button
                  class="btn btn-sm btn-link text-danger"
                  @click.prevent="openRemoveModal(agent.id)"
                >
                  <i class="nulodgicon-trash-b mr-1" />
                </button>
              </td>
              <td v-if="!isWrite" />
            </tr>
          </tbody>
        </table>

        <div class="more-info-modal">
          <sweet-modal
            ref="moreInfoModal"
            v-sweet-esc
            @close="changeViewLogStatus()"
          >
            <template slot="default">
              <div class="row m-0">
                <div
                  v-if="viewMoreInfoAgent"
                  class="heading-area col-12"
                >
                  <h4
                    class="mb-0 text-white"
                    style="line-height: 2rem"
                  >
                    More information about
                    <strong>{{ viewMoreInfoAgent.computerName }}</strong>
                  </h4>
                </div>
                <div
                  v-if="viewMoreInfoAgent"
                  class="info-area col-12"
                >
                  <div class="row">
                    <div class="col-12">
                      <p class="font-weight-bold mb-1">
                        <u>Basic Information</u>
                      </p>
                    </div>
                    <div class="col-md-6 col-sm-12">
                      <p
                        class="font-weight-bold mb-0"
                        style="word-wrap: break-word"
                      >
                        IP Address
                      </p>
                      <p>{{ viewMoreInfoAgent.ipAddress }}</p>

                      <p class="font-weight-bold mb-0">
                        MAC Address
                      </p>
                      <p>
                        <span class="d-inline-block align-middle">
                          <ul class="mb-0">
                            <li
                              v-for="macAddress in viewMoreInfoAgent.macAddresses"
                              :key="macAddress"
                            >
                              {{ macAddress }}
                            </li>
                          </ul>
                        </span>
                      </p>

                      <p class="font-weight-bold mb-0">
                        Location
                      </p>
                      <p>{{ locationText(viewMoreInfoAgent) }}</p>
                    </div>
                    <div class="col-md-6 col-sm-12">
                      <p class="font-weight-bold mb-0">
                        Managed by
                      </p>
                      <p>{{ managedByText(viewMoreInfoAgent) }}</p>

                      <p class="font-weight-bold mb-0">
                        Status
                      </p>
                      <p>
                        {{ enabledStatusText(viewMoreInfoAgent.isEnabled) }}
                      </p>

                      <p class="font-weight-bold mb-0">
                        Last Refreshed
                      </p>
                      <p>{{ showDate(viewMoreInfoAgent.lastActive) }}</p>
                    </div>
                  </div>
                  <div
                    v-if="isSuperAdmin && !viewLogs"
                    class="text-center"
                  >
                    <a
                      href="#"
                      @click.stop.prevent="getLogs(currentAgent.id)"
                    >
                      View logs
                    </a>
                  </div>
                  <span v-if="loading">
                    <sync-loader
                      :loading="true"
                      class="ml-3 mt-1"
                      color="#0d6efd"
                      size="0.5rem"
                    />
                  </span>
                  <span v-else>
                    <div
                      v-if="viewLogs"
                      class="mb-2"
                    >
                      <p class="font-weight-bold mb-1">
                        <u>Log Files</u>
                      </p>
                      <div
                        v-if="discoveryLogs.length > 0"
                        class="mt-2 log-scroll-view"
                      >
                        <table class="table mb-0">
                          <thead>
                            <tr>
                              <th>File Name</th>
                              <th>Created At</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="log in discoveryLogs"
                              :key="log.id"
                            >
                              <td>
                                <a
                                  class="float-left"
                                  :href="log.url"
                                >
                                  <div class="d-inline-block mt-1">
                                    <i class="nulodgicon-file-o ml-2" />
                                    {{ log.name }}
                                  </div>
                                </a>
                              </td>
                              <td>{{ showDate(log.createdAt) }}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div v-else>
                        <p class="ml-1">There are no logs present.</p>
                      </div>
                    </div>
                  </span>
                  <div 
                    v-if="isSuperAdmin"
                    class="mt-4"
                  >
                    <p class="font-weight-bold mb-1">
                      <u>MacAgent Space Usage</u>
                    </p>
                    <p class="font-weight-bold mb-0">Total Space Usage: {{ cumulativeStorageInfo.total }}</p>
                    <p class="font-weight-bold mb-2 mt-4">Detailed Usage Information:</p>
                    <table class="table table-bordered mt-2">
                      <thead>
                        <tr>
                          <th>File Path</th>
                          <th>Size</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr 
                          v-for="(info, key) in filteredStorageInfo"
                          :key="key"
                        >
                          <td>{{ info.path }}</td>
                          <td>{{ info.size }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <button
                    class="btn btn-link text-secondary float-right mr-3"
                    @click.stop="closeMoreInfoModal"
                  >
                    Close
                  </button>
                </div>
              </div>
            </template>
          </sweet-modal>
        </div>

        <sweet-modal
          ref="removeModal"
          v-sweet-esc
          title="Before we proceed..."
        >
          <template slot="default">
            <div class="text-center">
              <h6 class="mb-3">
                Are you sure you want to remove selected agent installation?
              </h6>
            </div>
          </template>
          <button
            slot="button"
            class="btn btn-link text-secondary"
            @click.stop="closeRemove"
          >
            No
          </button>
          <button
            slot="button"
            class="btn btn-primary ml-2"
            @click.stop="removeLocation"
          >
            Yes, Remove.
          </button>
        </sweet-modal>

        <sweet-modal
          ref="editAgentModal"
          v-sweet-esc
          title="Edit Agent Installation"
          class="edit-agent-modal"
        >
          <template slot="default">
            <div class="row">
              <div class="col-md-6 mb-2">
                Location
              </div>
              <div
                v-if="currentAgent"
                class="col-md-6 mb-2"
              >
                <location-options
                  :agent-location="selectedLocation"
                  :module-type="'Agent Location'"
                  @input="onLocationChange"
                  @remove="removeAgentLocation"
                  @fetch-locations="fetchLocations"
                />
              </div>
              <div class="col-md-6 mb-2">
                Managed by
              </div>
              <div
                v-if="currentAgent"
                class="col-md-6 mb-2"
              >
                <contributors-select
                  :value="managedBy"
                  data-tc-managed-by
                  @select="setManagedById"
                />
              </div>
              <!-- Hide enable and disable toggle temporary
              <div class="col-md-6 mb-2">Status</div>
              <div v-if="currentAgent" class="col-md-6 mb-2">
                <material-toggle
                  :init-active="currentAgent.isEnabled"
                  :key="componentKey"
                  @toggle-sample="toggleStatus(currentAgent.id)"
                />
              </div> -->
            </div>
          </template>
          <button
            slot="button"
            class="btn btn-link text-secondary"
            @click.stop="closeEditModal"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-primary ml-2"
            @click.stop="editAgentInstallation"
          >
            Save
          </button>
        </sweet-modal>
      </div>
      <div
        v-else-if="loading"
        class="text-center mt-5 clear-both"
      >
        <h5 class="text-secondary font-weight-normal">
          Loading
          <span class="ml-3 d-inline-block">
            <sync-loader
              :loading="true"
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </h5>
      </div>

      <div
        v-else
        class="text-center mt-5 clear-both"
      >
        <h4>There are no agent installed yet.</h4>
        <h5
          v-if="isWrite"
          class="text-secondary font-weight-normal"
        >
          <router-link to="/discovery_tools/connectors/agent">
            Download and install the agent
          </router-link>
          to get started.
        </h5>
      </div>
    </div>
  </div>
</template>

<script>
import http from "common/http";
import _ from "lodash";
import { mapGetters } from "vuex";
import { SweetModal } from "sweet-modal-vue";
import Paginate from "vuejs-paginate";
import SyncLoader from "vue-spinner/src/SyncLoader.vue";
import sortHelper from "mixins/sorting_helper";
import StringHelper from "mixins/string";
import locationsFilter from 'mixins/locations_filter';
import permissionsHelper from "mixins/permissions_helper";
import OutDatedApps from '../../shared/outdated_location_apps.vue';
import ContributorsSelect from '../../shared/contributors_select.vue';
import pageSizeInCookies from '../../../mixins/page_size_helper';
import LocationOptions from '../../shared/location_options.vue';

export default {

  components: {
    SweetModal,
    ContributorsSelect,
    SyncLoader,
    Paginate,
    OutDatedApps,
    LocationOptions,
  },
  mixins: [
    sortHelper,
    StringHelper,
    permissionsHelper,
    pageSizeInCookies,
    locationsFilter,
  ],
  data() {
    return {
      agentLocations: [],
      pageCount: 1,
      pageIndex: 0,
      pageSize: 25,
      searchTerm: "",
      search: null,
      currentId: null,
      viewMoreInfoAgent: null,
      currentAgent: null,
      loading: false,
      viewLogs: false,
      discoveryLogs: [],
      componentKey: 0,
      activeSort: "ComputerName",
      activeSortDirection: "desc",
      hoveredIndex: null,
      selectedLocation: null,
    };
  },
  computed: {
    ...mapGetters(["companyLocations", "companyUsers"]),
    page() {
      return this.pageIndex;
    },
    managedBy() {
      if (this.currentAgent.managedByContributorId) {  
        return { id: this.currentAgent.managedByContributorId };
      }
      return null;
    },
    expiredAgents() {
      return this.agentLocations.filter(agent => agent.expiredVersion);
    },
    sortedLocationApps() {
      if (this.agentLocations?.length) {
        return this.activeSort === 'ComputerName' ? this.sortByExpiredVersion : this.agentLocations; 
      }
      return [];
    },
    sortByExpiredVersion() {
      return _.orderBy(this.agentLocations, [c => c.expiredVersion], [this.activeSortDirection]);
    },
    activeSortIcon() {
      if (this.activeSortDirection === 'asc') {
          return 'nulodgicon-arrow-up-b';
        }
      return 'nulodgicon-arrow-down-b';
    },
    cumulativeStorageInfo() {
      return this.viewMoreInfoAgent.cumulativeStorageInfo;
    },
    filteredStorageInfo() {
      const { total, ...rest } = this.cumulativeStorageInfo;
      return rest;
    },
  },
  watch: {
    agentLocations() {
      if (this.agentLocations?.length && !this.locations?.length) {
        const locationParams = {
          offset: this.offset,
          limit: this.fetchedLocations,
        };
        this.fetchLocations(locationParams);
      }
    },
  },
  methods: {
    onWorkspaceChange() {
      this.$store.dispatch("fetchCompanyLocations");
      this.$store.dispatch("fetchCompanyUserOptions");
      this.getAgentLocations();
      this.checkPerPage();
    },
    showDeleteAgentWarning(agent, action) {
      const lastActiveDate = moment(agent.lastActive.toString());
      const daysDiff = moment().diff(lastActiveDate, 'days');
      const actionsObj = {
        'condition': daysDiff >= 30,
        'tooltip': `Deletion in ${45 - daysDiff} days`,
      };
      return actionsObj[action];
    },
    setManagedById(contributor){
      this.currentAgent.managedByContributorId = contributor.id;
    },
    agentAppType(agent) {
      if (agent.isAdminApp == null) return "Unknown app";
      return agent.isAdminApp ? "Admin app" : "Non admin app";
    },
    setPageIndex(pageIndex) {
      this.pageIndex = pageIndex;
    },
    pageSelected(p) {
      this.setPageIndex(p - 1);
      this.getAgentLocations();
    },
    checkPerPage() {
      if (this.perPagePresentInCookie('assets-agent-module=')) {
        const cookieData = this.getCookieValue('assets-agent-per-page');
        if (cookieData) {
          this.pageSize = parseInt(cookieData, 10);
        }
      }
    },
    changePageSize(e) {
      this.pageSize = e.currentTarget.value;
      this.setPageIndex(0);
      this.setPerPageInCookie("assets-agent", parseInt(this.pageSize, 10));
      this.getAgentLocations();
    },
    getAgentLocations() {
      this.loading = true;
      const params = {
        page: this.pageIndex + 1,
        page_size: this.pageSize,
        sort_by: this.activeSort,
        order_by: this.activeSortDirection,
      };

      if (this.search) {
        params.search = this.search;
      }
      http
        .get("/agent_locations.json", { params })
        .then((res) => {
          this.agentLocations = res.data.agentLocations;
          this.loading = false;
          this.$store.state.loading = false;
          this.pageCount = res.data.pageCount;
        })
        .catch(() => {
          this.loading = false;
          this.$store.state.loading = false;
          this.emitError("Sorry, there was an error loading agent installations. Please try again.");
        });
    },
    updateSearch: _.debounce(function updateSearch() {
      this.setPageIndex(0);
      if (this.$refs.searchTerms.value) {
        this.searchTerm = "all";
        this.search = this.$refs.searchTerms.value;
      } else {
        this.searchTerm = null;
        this.search = null;
      }
      this.getAgentLocations();
    }, 1000),
    macAddressShortened(macAddresses) {
      if (macAddresses === null || macAddresses.length === 0) {
        return "";
      }
      if (macAddresses.length === 1) {
        return macAddresses[0];
      }
      return `${macAddresses[0]}, ...`;
    },
    showDate(date) {
      if (date === null || date === "") {
        return "Not Scanned Yet";
      }
      return moment(date.toString()).format("MMM DD, YYYY, h:mm:ss a");
    },
    enabledStatusText(status) {
      if (status) {
        return "Active";
      }
      return "Disabled";
    },
    tooltipText(status) {
      if (status) {
        return "Data coming from this computer will be ignored";
      }
      return "Data coming from this computer will be added";
    },
    locationText(agent) {
      if (agent.location != null) {
        return agent.location.name;
      }
      return "Not set";
    },
    managedByText(agent) {
      if (agent.managedByContributorId) {
        return `${agent.manager}`;
      }
      return "Not set";
    },
    toggleStatusText(status) {
      if (status) {
        return "Disable";
      }
      return "Active";
    },
    toggleSuccessText(status) {
      if (status) {
        return "Agent is successfully enabled";
      }
      return "Agent is successfully disabled";
    },
    toggleStatus(id) {
      http
        .put(`/agent_locations/${id}.json`, { toggle: true })
        .then(() => {
          this.agentLocations.find(
            (x) => x.id === id
          ).isEnabled = !this.agentLocations.find((x) => x.id === id).isEnabled;
          this.emitSuccess(
            this.toggleSuccessText(
              this.agentLocations.find((x) => x.id === id).isEnabled
            )
          );
        })
        .catch((error) => {
          let errorMessage =
            "Sorry, there was an error toggling status. Please try again.";
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          this.emitError(errorMessage);
        })
        .finally(() => {
          this.componentKey += 1;
        });
    },
    editAgentInstallation() {
      http
        .put(`/agent_locations/${this.currentAgent.id}.json`, {
          toggle: false,
          agent: this.currentAgent,
        })
        .then((res) => {
          this.agentLocations = res.data;
          this.$refs.editAgentModal.close();
          this.emitSuccess("Agent edited successfully");
        })
        .catch((error) => {
          let errorMessage =
            "Sorry, there was an error editing agent. Please try again.";
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          this.emitError(errorMessage);
        });
    },
    removeLocation() {
      http
        .delete(`/agent_locations/${this.currentId}.json`)
        .then((res) => {
          this.agentLocations = res.data;
          this.$refs.removeModal.close();
          this.emitSuccess("Successfully removed the agent location.");
        })
        .catch((error) => {
          let errorMessage =
            "Sorry, there was an error deleting the installation status. Please try again.";
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          this.emitError(errorMessage);
          this.$refs.removeModal.close();
        });
    },
    openRemoveModal(id) {
      this.currentId = id;
      this.$refs.removeModal.open();
    },
    closeRemove() {
      this.$refs.removeModal.close();
    },
    closeMoreInfoModal() {
      this.changeViewLogStatus();
      this.$refs.moreInfoModal.close();
    },
    viewMoreInfo(agent) {
      this.currentAgent = agent;
      this.viewMoreInfoAgent = agent;
      this.$refs.moreInfoModal.open();
    },
    changeViewLogStatus() {
      this.viewLogs = false;
    },
    closeEditModal() {
      this.$refs.editAgentModal.close();
    },
    viewEditModal(agent) {
      this.currentAgent = agent;
      this.selectedLocation = this.currentAgent.location;
      this.$refs.editAgentModal.open();
    },
    getLogs(id) {
      this.loading = true;
      this.viewLogs = !this.viewLogs;
      http
        .get(`/agent_locations/${id}/logs.json`)
        .then((res) => {
          this.discoveryLogs = res.data.discoveryLogs;
        })
        .catch((error) => {
          this.viewLogs = !this.viewLogs;
          this.emitError(`Sorry, there was an error fetching logs (${error.response.data.message}).`);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    sortAgents() {
      this.sortItem('appVersion');
      this.getAgentLocations();
    },
    onLocationChange(location) {
      this.selectedLocation = location;
      this.currentAgent.locationId = location.id;
    },
    removeAgentLocation() {
      this.selectedLocation = null;
      this.currentAgent.locationId = null;
    },
  },
};
</script>

<style lang="scss" scoped>
tr td {
  padding: 10px;
}
.more-info-modal :deep() {
  .sweet-content {
    padding: 0px !important;

    .heading-area {
      background-color: $themed-dark-drawer-bg;
      color: white;
      padding: 1rem;
      padding-left: 25px;
      text-align: left;
    }

    .info-area {
      padding: 1rem;
      padding-left: 35px;
      text-align: left;
    }
  }
}
.btn-link:hover {
  background-color: Transparent !important;
}
.tooltip {
  position: relative;
  display: inline-block;
}
.tooltip .tooltiptext {
  visibility: hidden;
  width: 150px;
  background-color: $themed-muted;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 10px;
  position: absolute;
  z-index: 1;
  bottom: 150%;
  left: 50%;
  margin-left: -70px;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
}
.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: $gray-600 transparent transparent transparent;
}
.center-status {
  text-align: center;
  vertical-align: middle;
}
:deep(.slide-toggle) {
  margin-bottom: 0.25rem;
}
.agent-status-toggle {
  margin-top: 0.28rem;
}
.log-scroll-view {
  max-height: 15em;
  overflow-y: scroll;
}

.warning-icon {
  display: inline-block;
  font-size: 0.75rem;
  font-weight: 500;
  height: 1rem;
  line-height: 1;
  margin: 0 -0.1875rem;
  min-width: 1.25rem;
  padding: 0.1875rem 0.25rem;
  position: relative;
  text-align: center;
  top: 0;
  color: $color-caution;
}

 .cursor-pointer {
   cursor: pointer;
 }

  .app-outdated {
    background: lightgrey;
    border: 1px solid lightgrey;
  }

  .app-expired {
    background: red;
    border: 1px solid red;
  }

  .disable-toggle {
    pointer-events: none;
  }

  .disable-cursor {
    cursor: not-allowed;
  }
  .edit-agent-modal :deep(.sweet-modal) {
    max-width: 45rem;
  }

  .edit-agent-modal :deep(.sweet-content-content) {
    min-height: 12rem;
  }

  .edit-agent-modal :deep(.dropdown-height .multiselect__content-wrapper) {
    max-height: 13rem !important;
  }

  .margin-top {
    margin-top: -0.55rem;
  }
</style>
