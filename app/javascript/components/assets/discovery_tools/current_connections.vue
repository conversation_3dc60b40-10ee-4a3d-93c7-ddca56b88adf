<template>
  <div class="mt-4 pt-1 ml-5 w-100">
    <div class="d-flex align-items-center justify-content-between mb-2">
      <div class="d-flex align-items-center">
        <h4>
          Active Connectors 
        </h4>
        <div class="subpage-menu ml-2 mb-2">
          <div
            v-for="(item, index) in menuItems"
            :key="item.label"
            :class="['subpage-menu__item', { 'router-link-exact-active': item.state }, 'pointer']"
            @click.stop="setMenuItemActive(index)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div
        v-tooltip="'Alerts that were dismissed'"
        class="mb-2 dismissed-alerts pl-2 pr-3 py-1 mr-2"
        @click.stop="openDismissedAlerts"
      >
        <i class="genuicon-alerts sub-menu-item__icon"/>
        Dismissed Alerts
      </div>
    </div>
    <div class="active-integrations">
      <div 
        v-if="activeIntegrations.length > 0"
        class="row mr-2"   
      >
        <current-connection
          v-for="(item, index) in activeIntegrations"
          :ref="item.integrationOption.name"
          :key="`${item.integrationDetail.id}-${item.integrationDetail.name}`"  
          :integration-detail="item.integrationDetail"
          :integration-option="item.integrationOption"
          :index="index"
          :selected-menu="selectedMenu"
          :disabled="false"
          :customizable="isCustomizable(item.integrationOption.searchableName)"
          class="col-4 mb-3 pr-1"
          @set-resync-pusher="setResyncPusher"
        />
        <div class="col-4 mb-3 pr-1">
          <add-connector-card v-if="selectedMenu === 'all'"/>
        </div>
      </div>
      <div 
        v-else-if="loading"
        class="text-center"
      >
        <p class="loading-text">
          Please wait while we fetch your current integrations.
        </p>
        <sync-loader
          class="mt-3"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
      <div v-else>
        <div
          v-if="selectedMenu === 'all'"  
          class="col-4 mb-3 pl-0 pr-3"
        >
          <add-connector-card/>
        </div>
        <h5 
          v-else 
          class="text-center mt-4"
        >
          You have no connectors here
        </h5>
      </div> 
    </div>
    <div v-if="inActiveIntegrations.length > 0 && selectedMenu === 'all'" >
      <h4 class="mt-5">
        Inactive Connectors 
      </h4>
      <div class="row mr-2">
        <current-connection
          v-for="(item) in inActiveIntegrations"
          :key="`${item.integrationDetail.id}-${item.integrationOption.name}`"
          :integration-detail="item.integrationDetail"
          :integration-option="item.integrationOption"
          :disabled="true"
          class="col-4 mb-3 pr-1"
          @set-resync-pusher="setResyncPusher"
        />
      </div>
    </div>
    <dismissed-alerts-modal ref="dismissedAlertsModal"/>
  </div>    
</template>

<script>
  import { mapGetters } from 'vuex';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import onboardingOptions from '../onboarding_options';
  import currentConnection from '../discovery/current_connection.vue';
  import addConnectorCard from '../discovery/add_connector_card.vue';
  import common from '../../shared/module_onboarding/common';
  import dismissedAlertsModal from '../../shared/dismissed_alerts_modal.vue';

  const CUSTOMIZABLE_INTEGRATIONS = ['meraki', 'ubiquiti', 'aws_assets', 'google_assets'];
  const PUSHER_SETUP_INTEGRATIONS = [...CUSTOMIZABLE_INTEGRATIONS, 'azure_assets', 'ms_intune_assets', 'azure_ad_assets', 'kaseya', 'kandji', 'jamf_pro', 'google_workspace'];

  export default {
    components: {
      currentConnection,
      SyncLoader,
      addConnectorCard,
      dismissedAlertsModal,
    },
    mixins: [onboardingOptions, common],
    data() {
      return {
        loadingValue: PUSHER_SETUP_INTEGRATIONS.reduce((acc, int) => ({ ...acc, [int]: 0.1 }), {}),
        pusherIntegrations: PUSHER_SETUP_INTEGRATIONS,
        activeIntegrations: [],
        inActiveIntegrations: [],
        menuItems: [
          { label: "All Active", state: true, filter: "all" },
          { label: "Has Alerts", state: false, filter: "alerts" },
          { label: "Sync Errors", state: false, filter: "failed" },
        ],
        selectedMenu: 'all',
        dismissDelete: true,
      };
    },
    computed: {
      ...mapGetters([
        'companyIntegrations',
        'loading',
        'deletingIntegrations',
        'integrationsWithAlerts',
      ]),
    },
    watch: {
      companyIntegrations(integrations) {
        this.setCompanyIntegrations(integrations);  
      },
      deletingIntegrations() {
        this.activeIntegrations = this.activeIntegrations
          .filter(integration => !this.deletingIntegrations.includes(integration.integrationOption.openModalStr))
          .map(integration => (
            {
              integrationDetail: integration.integrationDetail,
              integrationOption: integration.integrationOption,
            })
          );
      },
      selectedMenu() {
        this.setCompanyIntegrations(this.companyIntegrations);
      },
      integrationsWithAlerts() {
        if (this.integrationsWithAlerts.length > 0) {
          this.setCompanyIntegrations(this.companyIntegrations);
        }
      },
    },
    created() {
      this.$store.dispatch('fetchCompanyIntegrations');
      this.$store.dispatch('fetchCompanyAlertsInfo');
    },
    mounted() {
      this.$store.dispatch('fetchCompanyIntegrations');
      this.setupPusherListeners();
    },
    methods: {
      setMenuItemActive(index) {
        this.menuItems.forEach(item => { item.state = false; });
        this.menuItems[index].state = true;
        this.selectedMenu = this.menuItems[index].filter;
      },
      setResyncPusher(intgName) {
        this.loadingValue[intgName] = 0.1;
      },
      isCustomizable(searchableName) {
        return CUSTOMIZABLE_INTEGRATIONS.includes(searchableName);
      },
      setCompanyIntegrations(integrations) {
        this.activeIntegrations = integrations
          .filter(integration => (integration.active || integration.syncStatus === "pending") && !this.deletingIntegrations.includes(integration.name))
          .map(integration => {
            const onboardingOption = this.onboardingOptions.find(
              option => option.searchableName === integration.name
            );

            if (!onboardingOption) return null;

            return {
              integrationDetail: integration,
              integrationOption: onboardingOption,
            };
          }).filter(item => item !== null);

        if (this.selectedMenu === "failed") {
          this.activeIntegrations = this.activeIntegrations.filter(integration => integration.integrationDetail.syncStatus === this.selectedMenu);
        }

        if (this.selectedMenu === "alerts") {
          this.activeIntegrations = this.activeIntegrations.filter(integration => 
            this.integrationsWithAlerts.includes(integration.integrationOption.searchableName) &&
            integration.integrationDetail.syncStatus !== "pending"
          );
        }

        this.inActiveIntegrations = [];
        this.inActiveIntegrations = integrations
          .filter(integration => !integration.active && integration.syncStatus !== "pending")
          .map(integration => (
            {
              integrationDetail: integration,
              integrationOption: this.onboardingOptions.find(option => option.searchableName === integration.name) || null,
            })
          );     
      },
      openDismissedAlerts() {
        this.$refs.dismissedAlertsModal.openModal();
      },
    },
  };
</script>

<style lang="scss" scoped>
  h4, h5 {
    font-weight: 100;
  }

  .active-integrations {
    min-height: 14rem;
  }

  .outer-message {
    display: flex;
    width: 15rem;
    background: red;
    align-items: center;
    border-radius: 0.5rem;
    z-index: 9999;
    position: absolute;
    left: 92rem;
    bottom: 2rem;
    overflow: hidden;
    bottom: -8rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
  }

  .inner-message {
    display: flex;
    align-items: center;
    padding: 0.8rem 1rem;
    background: white;
    width: 100%;
    margin-left: 0.5rem;
    border-radius: 0.2rem;
  }

  .highlighted-word {
    font-weight: bold;
    text-decoration: underline;
  }

  .dismiss {
    color: inherit;
    cursor: pointer;
    transition: color 0.2s;
  }

  .dismiss:hover {
    color: #222;
  }

  .loading-text {
    font-size: 1.2rem !important;
  }

  .pointer {
    cursor: pointer;
  }

  .alert-container {
    z-index: 100;
    position: fixed;
    right: 2.5rem;
    bottom: 4.25rem;
  }

  .dismissed-alerts {
    cursor: pointer;
    color: var(--themed-secondary);
    background-color: var(--themed-lighter);
    border: 0.1rem solid var(--themed-badge-border);
    border-radius: 1rem;
    font-size: 0.875rem;
  }

  .dismissed-alerts:hover {
    background-color: var(--themed-light);
  }
</style>
