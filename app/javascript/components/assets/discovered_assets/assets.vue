<template>
  <div>
    <div class="d-flex row justify-content-between px-3">
      <status-menu />
    </div>
    <assets-alert-modal
      v-if="isAlertModalVisible"
      :open-modal="isAlertModalVisible"
      @close-modal="closeAlertModal"
    />
    <discovered-asset-list class="bg-themed-light mt-4" />
  </div>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import permissionsHelper from 'mixins/permissions_helper';
import pageSizeHelper from 'mixins/page_size_helper';
import DiscoveredAssetList from './asset_list.vue';
import StatusMenu from './status_menu.vue';
import assetsAlertModal from '../assets_alert_modal.vue';

export default {
  components: {
    DiscoveredAssetList,
    StatusMenu,
    assetsAlertModal,
  },
  mixins: [permissionsHelper, pageSizeHelper],
  data() {
      return {
        isAlertModalVisible: false,
      };
    },
  computed: {
    ...mapGetters([
      'discoveredAssets',
      'pageCount',
      'pageIndex',
      'pageSize',
      'loading',
      'assetSources',
      'discoveredAssetSource',
      'discoveredAssetLocation',
      'discoveredAssetStatus',
      ]),
    showLoading() {
      return !this.discoveredAssets;
    },
    discoveredAssetSources() {
      return this.assetSources.filter((as) => as.id !== "manually_added" && as.id !== "uploaded");
    },
    page() {
      return this.pageIndex;
    },
    status() {
      return (this.$route.name || 'ready_for_import');
    },
    assetSourceId() {
      if (this.discoveredAssetSource) {
        return this.discoveredAssetSource.id;
      }

      return null;
    },
    currentLocationId() {
      if (this.discoveredAssetLocation) {
        return this.discoveredAssetLocation.id;
      }

      return null;
    },
  },
  watch: {
    $route(){
      this.setPageIndex(0);
    },
  },
  methods: {
    ...mapMutations([
      'setDiscoveredAssetStatus',
      'setPageIndex',
      'setDiscoveredAssetSource',
      'setPageSize',
    ]),
    onWorkspaceChange() {
      this.$store.dispatch('fetchDiscoveredAssets');
      this.updateDiscoveredAssets();
    },
    updateDiscoveredAssets() {
      if (this.discoveredAssetStatus !== this.status) {
        this.checkPerPage("discovered-assets", this.setPageSize);
        this.setDiscoveredAssetStatus(this.status);
        this.$store.dispatch('fetchDiscoveredAssets');
      }
    },
    pageSelected(p) {
      this.setPageIndex(p - 1);
      this.$store.dispatch("fetchDiscoveredAssets");
    },
    filterSource(assetSourceId) {
      if (assetSourceId) {
        this.assetSources.forEach((type) => {
          if (type.id === assetSourceId) {
            this.setDiscoveredAssetSource(type);
          }
        });
      } else {
        this.setDiscoveredAssetSource(null);
      }
      this.$store.dispatch('fetchDiscoveredAssets');
    },
  },
};
</script>
