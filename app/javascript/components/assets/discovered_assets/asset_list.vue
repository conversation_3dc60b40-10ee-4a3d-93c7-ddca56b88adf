<template>
  <div>
    <div v-if="!assetsToImport">
      <div class="clearfix align-items-end mt-3">
        <div class="mt-2">
          <div class="rounded-top text-white box__header">
            <div class="row justify-content-between">
              <div class="d-flex">
                <h5 class="d-flex font-weight-bold d-inline-block mr-3 ml-4 mt-2 mb-0">
                  Discovered Assets
                </h5>
                <div
                  v-if="loading"
                  class="d-flex align-items-center ml-n3 mt-2"
                >
                  <span class="d-inline-block align-top ml-2">
                    <pulse-loader
                      color="#0d6efd"
                      size="0.5rem"
                      :loading="true"
                    />
                  </span>
                </div>
                <div
                  v-if="assetsPresent && !loading"
                  :class="{
                    'asset-heading--bulk-selecting d-flex align-items-center':
                      isBulkSelecting,
                  }"
                >
                  <div class="d-flex font-weight-normal d-inline-block mr-3 ml-n3 mt-2">
                    <span
                      v-if="pageCount <= 1"
                      class="ml-1 not-as-small text-themed-fair"
                    >
                      (Showing {{ totalFilteredAssetsCount }} of {{ totalFilteredAssetsCount }})
                    </span>
                    <span
                      v-else
                      class="ml-1 not-as-small text-white"
                    >
                      (Showing {{ pageStart }}&dash;{{ pageEnd }} of {{ totalFilteredAssetsCount }})
                    </span>
                  </div>
                  <div class="d-inline">
                    <div 
                      v-if="includedActions.length > 0 && selectedAssets.length > 0"
                      class="float-right"
                    >
                      <button
                        v-for="action in includedActions"
                        :key="action.name"
                        href="#"
                        class="btn btn-sm btn-secondary no-shadow ml-2"
                        @click.prevent="manageAction(action.name.toLowerCase())"
                      >
                        <span
                          :class="action.iconClass"
                          :data-tc-action-icon="action.name"
                        />
                        <span>{{ action.name }}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-auto d-flex align-items-center justify-content-end pagination-wrapper">
                <div class="text-right col-md-12">
                  <filter-button
                    @toggle-filter="toggleFilterMenu"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-themed-light px-4 pt-3 mb-n3">
          <div class="row justify-content-end m-0">
            <dismissible-container
              ref="dismissibleContainer"
              show-pointer
              class="mt-n4 mb-3 mr-1"
              @close-container="closeFilterMenu"
            >
              <asset-filters
                v-if="isFilterMenuOpen"
                class="bg-themed-box-bg p-1 rounded-pill border"
                @filters-updated="filtersUpdated"
              />
            </dismissible-container>
          </div>
          <div class="row justify-content-between align-items-start mb-1">
            <div class="d-flex mb-3 ml-4">
              <div
                v-if="!showLoading"
                class="d-flex align-items-center"
              >
                <search-input 
                  class="asset-search-bar" 
                  @fetch-assets="fetchAssets"
                />
              </div>
            </div>
            <div
              v-if="!loading"
              class="d-flex justify-content-end align-items-start mr-3 pagination-wrapper"
              :class="{ 'mb-2': isBulkSelecting }"
            >
              <div class="d-flex align-items-end">
                <span 
                  v-if="currentTabAssetsCount > 25"
                  class="small ml-2 d-flex align-items-center"
                >
                  <span class="text-muted align-text-bottom mr-2">
                    <span> Results per page </span>
                  </span>
                  <select
                    id="filtersPerPage"
                    class="form-control form-control-sm d-inline-block select-per-page-filter pt-0"
                    :input="pageSize"
                    :value="pageSize"
                    data-tc-filters-per-page
                    @input="changePageSize"
                  >
                    <option>25</option>
                    <option>50</option>
                    <option>100</option>
                  </select>
                </span>
                <div
                  v-if="pageCount > 1"
                  class="align-self-center ml-3"
                >
                  <paginate
                    ref="paginate"
                    :click-handler="pageSelected"
                    :container-class="'pagination pagination-sm mb-0'"
                    :next-class="'next-item'"
                    :next-link-class="'page-link'"
                    :next-text="'Next'"
                    :page-class="'page-item'"
                    :page-count="pageCount"
                    :page-link-class="'page-link'"
                    :prev-class="'prev-item'"
                    :prev-link-class="'page-link'"
                    :prev-text="'Prev'"
                    :selected="page"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div 
        v-if="selectedAssets.length > 0 && discoveredAssetStatus !== 'imported' && !assetsToImport"
        class="d-flex w-100 justify-content-end pt-3 mt-2 px-4 mb-n3 small bg-light"
      >
        <div class="box-width text-center text-secondary mb-0 rounded-bottom mt-1">
          <div>
            <span>{{ itemSelectionMessage }}</span>
            <button
              v-if="discoveredAssets.length < currentTabAssetsCount && !allAssetsSelected"
              class="btn btn-sm btn-link"
              data-tc-asset="select all"
              @click="selectAllAssetsRecord"
            >
              Select all {{ currentTabAssetsCount }} items
            </button>
          </div>
        </div>
        <div class="ml-2 d-flex select-box justify-content-end">
          <div class="d-flex align-items-center">
            <button
              v-if="selectedAssets.length > 0"
              class="btn btn-sm btn-link text-danger ml-2 rounded"
              @click="unSelectAssets"
            >
              Unselect all
            </button>

            <button
              v-if="selectedAssets.length < discoveredAssets.length"
              class="btn btn-sm btn-link ml-2"
              @click="selectAllAssetsOnPage"
            >
              Select {{ smallestOfAssetOrPageSize }} Assets on this page
            </button>
          </div>
        </div>
      </div>
      <div class="bg-themed-light px-5 rounded-bottom">
        <hr class="mb-2">
        <toggle-view
          v-if="assetsPresent && !loading"
          is-discovered-assets-module
          :view="'list'"
          :table-type="'scrollable'"
          :table-style="'table--spaced'"
          :table-class="'table--spaced'"
          :settings-style="`scrollable-table-settings--spaced`"
          :min-width="tableMinWidth(tableHeader.length)"
          :settings-path="'/settings/discovered_asset_table_data'"
          :show-settings-icon="!mspFlag"
          asset-list-view-table
        >
          <template slot="list">
            <thead>
              <table-header-row
                ref="tableHeaderRow"
                :table-header="tableHeader" 
                :sort-item="sortItem"
                :active-sort="activeSort"
                :draggable-header-preferences="discoveredAssetPreferences"
                :active-sort-direction="activeSortDirection"
                :list-table-layout-module-style-override="listTableLayoutModuleStyle"
                :show-select-all-checkbox-inline="showSelectAllCheckbox"
                :is-scroll-allow="false"
                :select-all-value="allPageAssetsSelected"
                @select-all="selectAllCheckboxClicked"
                @change="setSortCookies"
                @update-order="updateDiscoveredAssetListColumns"
              />
            </thead>
            <tbody>
              <asset-row
                v-for="asset in discoveredAssets"
                :key="asset.id"
                :asset="asset"
                :is-icon="false"
                :asset-preferences="discoveredAssetPreferences"
                :selected-asset-ids-arr="selectedAssets"
                :show-select-checkbox="showSelectAllCheckbox"
                :is-bulk-selected="isBulkSelecting"
                :is-discovered-asset="true"
                @select-asset="selectAsset"
              />
            </tbody>
          </template>
        </toggle-view>

        <div
          v-else-if="!loading && !assetsPresent"
          id="noAssetsPresentHeader"
          class="text-center p-4 mt-5"
        >
          <h4>Not tracking anything here, yet.</h4>
          <h6
            v-if="isWrite"
            class="text-secondary font-weight-normal"
          >
            Try clearing the search box, removing filters, or
            <router-link to="/discovery_tools/connectors">
              discover
            </router-link>
            or
            <router-link to="/new">
              add a new one
            </router-link>
          </h6>
        </div>
      </div>
      <div
        v-if="!loading && pageCount > 1 && assetsPresent"
        class="d-flex justify-content-end mt-4 mb-n5"
      >
        <paginate
          ref="paginate"
          :click-handler="pageSelected"
          :container-class="'pagination pagination-sm align-self-center mb-0'"
          :next-class="'next-item'"
          :next-link-class="'page-link'"
          :next-text="'Next'"
          :page-class="'page-item'"
          :page-count="pageCount"
          :page-link-class="'page-link'"
          :prev-class="'prev-item'"
          :prev-link-class="'page-link'"
          :prev-text="'Prev'"
          :selected="page"
          class="align-self-center mb-0 mt-1"
        />
      </div>
      <div class="more-info-modal">
        <sweet-modal
          ref="moreInfoModal"
          v-sweet-esc
        >
          <template slot="default">
            <div class="row">
              <div
                v-if="viewMoreInfoAsset"
                class="heading-area col-12"
              >
                <h4 class="mb-0 text-white modal-bg">
                  More information about <strong>{{ viewMoreInfoAsset.displayName }}</strong>
                </h4>
              </div>
              <div
                v-if="viewMoreInfoAsset"
                class="info-area col-md-12 col-sm-12"
              >
                <div class="row">
                  <div class="col-md-6 col-sm-12">
                    <p class="font-weight-bold mb-1">
                      <u>Basic Information</u>
                    </p>

                    <p
                      class="font-weight-bold mb-0"
                      style="word-wrap: break-word;"
                    >
                      IP Address
                    </p>
                    <p v-if="viewMoreInfoAsset.ipAddress">
                      {{ viewMoreInfoAsset.ipAddress }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      MAC Address
                    </p>
                    <p>
                      <span class="d-inline-block align-middle">
                        <ul class="mb-0">
                          <li
                            v-for="macAddress in viewMoreInfoAsset.macAddresses"
                            :key="macAddress"
                          >
                            {{ macAddress }}
                          </li>
                        </ul>
                      </span>
                    </p>

                    <p class="font-weight-bold mb-0">
                      Manufacturer
                    </p>
                    <p v-if="viewMoreInfoAsset.manufacturer">
                      {{ viewMoreInfoAsset.manufacturer }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p
                      class="font-weight-bold mb-0"
                      style="word-wrap: break-word;"
                    >
                      Asset Type
                    </p>
                    <p v-if="viewMoreInfoAsset.assetType">
                      {{ viewMoreInfoAsset.assetType }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      Memory
                    </p>
                    <p v-if="viewMoreInfoAsset.hardwareDetail && viewMoreInfoAsset.hardwareDetail.memory">
                      {{ viewMoreInfoAsset.hardwareDetail.memory }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">IMEI</p>
                    <p>{{ imei }}</p>

                    <p class="font-weight-bold mb-0">
                      Hard Disk
                    </p>
                    <p v-if="viewMoreInfoAsset.hardwareDetail && viewMoreInfoAsset.hardwareDetail.hardDrive">
                      {{ viewMoreInfoAsset.hardwareDetail.hardDrive }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      Machine Serial No.
                    </p>
                    <p v-if="viewMoreInfoAsset.machineSerialNo">
                      {{ viewMoreInfoAsset.machineSerialNo }}
                    </p>
                    <p v-else>
                      -
                    </p>
                  </div>

                  <div class="col-md-6 col-sm-12">
                    <p class="font-weight-bold mb-1">
                      <u>Processor Information</u>
                    </p>

                    <p class="font-weight-bold mb-0">
                      Name
                    </p>
                    <p v-if="viewMoreInfoAsset.hardwareDetail && viewMoreInfoAsset.hardwareDetail.processor">
                      {{ viewMoreInfoAsset.hardwareDetail.processor }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      Cores
                    </p>
                    <p v-if="viewMoreInfoAsset.hardwareDetail && viewMoreInfoAsset.hardwareDetail.processorCores">
                      {{ viewMoreInfoAsset.hardwareDetail.processorCores }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-1">
                      <u>OS Information</u>
                    </p>

                    <p class="font-weight-bold mb-0">
                      Operating System
                    </p>
                    <p v-if="viewMoreInfoAsset.os">
                      {{ viewMoreInfoAsset.os }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      Name
                    </p>
                    <p v-if="viewMoreInfoAsset.osName">
                      {{ viewMoreInfoAsset.osName }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      Version
                    </p>
                    <p v-if="viewMoreInfoAsset.osVersion">
                      {{ viewMoreInfoAsset.osVersion }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      OS Serial No.
                    </p>
                    <p v-if="viewMoreInfoAsset.osSerialNo">
                      {{ viewMoreInfoAsset.osSerialNo }}
                    </p>
                    <p v-else>
                      -
                    </p>

                    <p class="font-weight-bold mb-0">
                      Location
                    </p>
                    <p v-if="viewMoreInfoAsset.locationDescription">
                      {{ viewMoreInfoAsset.locationDescription }}
                    </p>
                    <p v-else>
                      -
                    </p>
                  </div>
                </div>

                <div class="text-center">
                  <a
                    :href="`/managed_assets/discovered_assets/${viewMoreInfoAsset.id}`"
                    target="_blank"
                    class="not-as-small"
                  >
                    View More Info
                    <i class="nulodgicon-external-link ml-1" />
                  </a>
                  <a
                    v-if="imported && $superAdminUser"
                    :href="`/managed_assets/${viewMoreInfoAsset.managedAssetId}`"
                    target="_blank"
                    class="d-block mt-1 not-as-small"
                  >
                    View Imported Asset
                    <i class="nulodgicon-external-link ml-1" />
                  </a>
                </div>
                <button
                  class="btn btn-link text-secondary float-right mr-3"
                  @click.stop="closeMoreInfoModal"
                >
                  Close
                </button>
              </div>
            </div>
          </template>
        </sweet-modal>
      </div>
    </div>

    <sweet-modal
      ref="ignoreModal"
      v-sweet-esc
      title="Before we proceed..."
    >
      <template slot="default">
        <div class="text-center">
          <h6 class="mb-3">
            Are you sure you want to ignore selected discovered assets?
          </h6>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeIgnore"
      >
        No, keep as is.
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click.stop="okIgnore"
      >
        Yes, ignore.
      </button>
    </sweet-modal>

    <sweet-modal
      ref="deleteModal"
      v-sweet-esc
      title="Before we proceed..."
    >
      <template slot="default">
        <div class="text-center">
          <h6 class="mb-3">
            Are you sure you want to permanently delete selected discovered assets?
          </h6>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeDelete"
      >
        No, keep as is.
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click.stop="okDelete"
      >
        Yes, delete.
      </button>
    </sweet-modal>

    <sweet-modal
      ref="unignoreModal"
      v-sweet-esc
      title="Before we proceed..."
    >
      <template slot="default">
        <div class="text-center">
          <h6 class="mb-3">
            Are you sure you want to unignore selected discovered assets?
          </h6>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeUnignore"
      >
        No, keep as is.
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click.stop="okUnignore"
      >
        Yes, unignore.
      </button>
    </sweet-modal>

    <sweet-modal
      ref="addAssetsModal"
      v-sweet-esc
      class="proceed-modal"
      :class="{ 'logs-modal': assetsToImport }"
      :title="addAssetsModalTitle"
      blocking
      width="80%"
      @close="closeAddAssetsForDuplicates"
    >
      <template slot="default">
        <div 
          v-if="hasDuplicateAssets"
          class="text-center text-error"
        >
          <h6>
            The following discovered assets have already been imported
          </h6>
          <p>
            Please manually ignore those duplicate assets from the list
          </p>
        </div>
        <div
          v-else
          class="text-center"
        >
          <h6 class="mb-2">
            Are you sure you want to add these to your assets?
          </h6>
          <p>
            If you choose to add them, you'll be able to see them in the
            <a
              href="/managed_assets"
              target="_blank"
              class="font-weight-bold"
            >Assets module</a>.
          </p>
        </div>
        <div>
          <div class="d-flex flex-column">
            <scrollable-table
              id="asset-list-table" 
              class="table mb-0 mt-4 text-left pb-6"
              :fade-right="false"
              :force-full-height="true"
              :show-settings-icon="false"
            >
              <tr>
                <th>Asset name</th>
                <th>Import As</th>
                <th>Location</th>
                <th>Used By</th>
                <th>Managed By</th>
                <th>Department</th>
                <th>Impact</th>
              </tr>
              <tr class="apply-to-all-row">
                <td>
                  <i class="font-weight-normal">Apply to all rows below:</i>
                </td>
                <td>
                  <select
                    id="assetType"
                    ref="assetTypeDropdown"
                    value="bulkSelectedDept"
                    class="form-control form-control-sm h-50 mt-1"
                    @change="changeAll($event)"
                  >
                    <option />
                    <option
                      v-for="type in assetTypes"
                      :key="type.id"
                      :value="type.name"
                    >
                      {{ type.name }}
                    </option>
                  </select>
                </td>
                <td>
                  <location-options
                    :multiple="false"
                    :discovered-asset-location-id="selectedLocation"
                    :module-type="'Discovered Asset'"
                    @input="(...args)=>setAllLocations(['locationId', ...args])"
                    @fetch-locations="fetchLocations"
                  />
                </td>
                <td>
                  <contributor-select
                    is-discovered-assets
                    add-new-user
                    :value="usedByContributor"
                    @remove="usedByContributor = null"
                    @select="(...args)=>changeAllContributors(['usedByContributorId', ...args])"
                  />
                </td>
                <td>
                  <contributor-select
                    is-discovered-assets
                    add-new-user
                    :value="managedByContributor"
                    @remove="managedByContributor = null"
                    @select="(...args)=>changeAllContributors(['managedByContributorId', ...args])"
                  />
                </td>
                <td>
                  <add-department
                    tag-placeholder="Add a new department"
                    placeholder="Select Department"
                    taggable
                    :selected-departments="bulkSelectedDept"
                    :multiple="false"
                    :show-whole-component="false"
                    :are-object-options="false"
                    :temp-options="tempOptions"
                    @input="(val) => changeAll('department', val)"
                    @remove="removeDepartment"
                    @tag="(...args) => addNewDepartment([null, ...args])"
                  />
                </td>
                <td>
                  <select
                    id="impact"
                    ref="impactDropdown"
                    class="form-control form-control-sm h-50 mt-1"
                    @change="changeAll($event)"
                  >
                    <option />
                    <option value="Low">
                      Low
                    </option>
                    <option value="Medium">
                      Medium
                    </option>
                    <option value="High">
                      High
                    </option>
                  </select>
                </td>
              </tr>
              <tr
                v-for="asset in importDiscoveredAssets"
                :key="asset.id"
                class="text-secondary"
              >
                <td>
                  <input
                    :ref="'asset-name-'+asset.id"
                    v-model="asset.displayName "
                    type="text"
                    class="form-control form-control-sm h-50"
                    @click.prevent="removeErrors(asset.id)"
                  >
                  <span
                    :ref="'asset-name-error-'+asset.id"
                    class="form-text small text-danger display-none"
                  >Please enter name</span>
                  <span
                    :ref="'asset-dup-error-'+asset.id"
                    class="form-text small text-danger display-none"
                  >Name must be unique</span>
                  <span
                    :ref="'mang-asset-dup-error-'+asset.id"
                    class="form-text small text-danger display-none"
                  >Duplicate asset</span>
                </td>
                <td>
                  <select
                    class="form-control form-control-sm h-50"
                    @change="changeAssetType($event, asset)"
                  >
                    <option
                      v-for="type in assetTypes"
                      :key="type.id"
                      :value="type.name"
                      :selected="type.name === recommendedType(asset)"
                    >
                      {{ type.name }}
                    </option>
                  </select>
                </td>
                <td>
                  <location-options
                    :multiple="false"
                    :discovered-asset-location-id="asset.locationId"
                    :module-type="'Discovered Asset'"
                    @input="(...args)=>setLocation([asset, ...args])"
                    @remove="(...args)=>removeLocation([asset, ...args])"
                    @fetch-locations="fetchLocations"
                  />
                </td>
                <td>
                  <contributor-select
                    is-discovered-assets
                    add-new-user
                    :value="usedByContributor || asset.usedByContributor"
                    @select="(...args)=>setUsedByContributor([asset, ...args])"
                  />
                </td>
                <td>
                  <contributor-select
                    is-discovered-assets
                    add-new-user
                    :value="managedByContributor"
                    @select="(...args)=>setManagedByContributor([asset, ...args])"
                  />
                </td>
                <td>
                  <add-department
                    placeholder="Select Department"
                    tag-placeholder="Add a new department"
                    taggable
                    :selected-departments="asset.department"
                    :multiple="false"
                    :show-whole-component="false"
                    :are-object-options="false"
                    :temp-options="tempOptions"
                    @input="(...args) => setDepartment([asset, ...args])"
                    @remove="(...args) => setDepartment([asset, ...args])"
                    @tag="(...args) => addNewDepartment([asset, ...args])"
                  />
                </td>
                <td>
                  <select
                    v-model="asset.impact"
                    class="form-control form-control-sm h-50"
                  >
                    <option value="Low">
                      Low
                    </option>
                    <option value="Medium">
                      Medium
                    </option>
                    <option value="High">
                      High
                    </option>
                  </select>
                </td>
              </tr>
              <span 
                v-if="loading && !assetsToImport"
                class="d-flex justify-content-center"
              >
                <h5 class="text-muted float-left">
                  Loading assets
                </h5>
                <sync-loader
                  :loading="loading"
                  class="ml-3 mt-1 d-inline"
                  color="#0d6efd"
                  size="0.3rem"
                />
              </span>
            </scrollable-table>
          </div>
          <div 
            v-if="!hasDuplicateAssets"
            class="sticky-btn-holder pt-1 pb-4 bg-lighter border-top border-light"
          >
            <div class="d-flex justify-content-center mb-2">
              <button
                v-if="false"
                class="btn px-3 py-1 rounded-pill"
                @click.stop.prevent="loadAllDiscoveredAssets"
              >
                Show More
              </button>
            </div>
            <div class="d-flex justify-content-center">
              <button
                slot="button"
                class="btn btn-link text-secondary"
                @click.stop="closeAddAssets"
              >
                No, cancel
              </button>
              <button
                slot="button"
                :disabled="(isSubmitting || loading) && !assetsToImport"
                class="btn btn-primary ml-2"
                @click.stop="okAddAssets"
              >
                Yes, add them
              </button>
            </div>
          </div>
        </div>
      </template>
    </sweet-modal>

    <sweet-modal
      ref="updateNotificationModal"
      v-sweet-esc
      title="Please wait, assets are being updated..."
    >
      <template slot="default">
        <div class="text-center">
          <h6 v-if="updateNotification">
            {{ updateNotification }}
          </h6>
          <h6 v-else>
            Your assets will be updated in a few minutes.
          </h6>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeUpdateNotificationModal"
      >
        Close
      </button>
    </sweet-modal>
  </div>
</template>
<script>
import dates from "mixins/dates";
import strings from "mixins/string";
import inflections from "mixins/inflections";
import assetImages from "mixins/asset_images";
import http from "common/http";
import { mapMutations, mapGetters, mapActions } from "vuex";
import Paginate from "vuejs-paginate";
import tableLayoutStyle from "mixins/table_layout_style";
import SyncLoader from "vue-spinner/src/SyncLoader.vue";
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import { SweetModal } from "sweet-modal-vue";
import sortHelper from "mixins/sorting_helper";
import ScrollableTable from 'components/shared/scrollable_table.vue';
import DismissibleContainer from "components/shared/dismissible_container.vue";

import _ from "lodash";
import permissionsHelper from "mixins/permissions_helper";
import pluralize from "pluralize/pluralize";
import activeSortHelper from "mixins/active_sort_helper";
import mspManagingHelper from "mixins/msp_managing_helper";
import mspHelper from "mixins/msp_helper";
import mspFilterLoader from "mixins/msp_filter_loader";
import filters from "mixins/filters";
import channelCleanup from "mixins/custom_forms/channel_cleanup";
import pageSizeHelper from "mixins/page_size_helper";
import DropDownClose from "mixins/dropdown_close_outside_click";
import AssetRow from "../asset_row.vue";
import ToggleView from "../../shared/toggle_view.vue";
import SearchInput from "../search_input.vue";
import AssetFilters from "./asset_filters.vue";

import FilterButton from "../../shared/filter_button.vue";
import TableHeaderRow from "../../shared/list_view_table_header.vue";
import LocationOptions from "../../shared/location_options.vue";
import addDepartment from '../../shared/add_department.vue';
import ContributorSelect from "../../shared/contributors_select.vue";

export default {
  components: {
    Paginate,
    AssetRow,
    SyncLoader,
    PulseLoader,
    SweetModal,
    ToggleView,
    SearchInput,
    AssetFilters,
    DismissibleContainer,
    FilterButton,
    TableHeaderRow,
    LocationOptions,
    addDepartment,
    ContributorSelect,
    ScrollableTable,
  },
  mixins: [
    dates,
    strings,
    inflections,
    assetImages,
    sortHelper,
    permissionsHelper,
    tableLayoutStyle,
    channelCleanup,
    pageSizeHelper,
    activeSortHelper,
    mspManagingHelper,
    mspHelper,
    mspFilterLoader,
    filters,
    DropDownClose,
  ],
  props: {
    work: {
      type: Object,
      default: null,
    },
    assetsToImport: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      showMore: false,
      isDeleting: false,
      selectedAssets: [],
      locationId: null,
      usedByContributor: null,
      usedByContributorId: null,
      managedByContributor: null,
      managedByContributorId: null,
      isFilterMenuOpen: false,
      allAssetsSelected: false,
      statusFilter: {},
      selectedAssetsArray: [],
      listTableLayoutModuleStyle: "managed_assets",
      isFilterRemoved: false,
      isDataFetched: false,
      offset: 0,
      perPage: 100,
      selectedLocation: null,
      hasDuplicateAssets: false,
      bulkSelectedDept: null,
      printQRRequest: false,
      viewMoreInfoAsset: null,
      isSubmitting: false,
      imported: false,
      displayName: null,
      assetType: null,
      machineSerialNo: null,
      manufacturer: null,
      model: null,
      locationDescription: null,
      selectAllAssets: false,
      loadAllAsset: false,
      updateNotification: null,
      checkedOffset: 0,
      updateAllAssetFields: {},
      locationsOffset: 0,
      bulkLocationId: 0,
      tempOptions: [],
      showMoreIndex: 0,
      height: 0,
    };
  },
  computed: {
    ...mapGetters([
      "pageCount",
      "discoveredAssets",
      "assetTypes",
      "pageIndex",
      "pageSize",
      "assetTags",
      "discoveredAssetStatus",
      "discoveredAssetPreferences",
      "activeFilters",
      "sortByOptions",
      "companyUsersAndGroups",
      "currentAsset",
      "viewType",
      "search",
      'totalReadyForImport',
      'totalIncomplete',
      'totalUnrecognized',
      'totalImported',
      'totalIgnored',
      'loading',
      'totalFilteredAssetsCount',
    ]),
    ...mapGetters("GlobalStore", ["locations"]),
    includedActions() {
      return this.headerActions.filter(a => a.include);
    },
    shouldShowButton() {
      return !(this.showMoreIndex + 1 === this.pageCount) && this.selectAllAssets === true && !this.loading;
    },
    importDiscoveredAssets() {
      return this.assetsToImport ? this.assetsToImport : this.discoveredAssets.filter(a => this.selectedAssets.includes(a.id));
    },
    allPageAssetsSelected() {
      return this.selectedAssets.length >= this.smallestOfAssetOrPageSize;
    },
    isBulkSelecting() {
      return (
        this.isWrite && this.assetsPresent && this.selectedAssets.length > 0
      );
    },
    itemSelectionMessage() {
      if (this.allAssetsSelected) {
        return `All ${this.currentTabAssetsCount} items are selected.`;
      }
      const hasMoreThanOne = this.selectAssetsCount > 1;
      const allItemsOnPage =
        this.selectAssetsCount === this.pageSize ||
        this.selectAssetsCount === this.discoveredAssets.length;

      return `${allItemsOnPage && hasMoreThanOne ? "All" : ""} ${
        this.selectAssetsCount
      }  ${pluralize("item", this.selectAssetsCount)} ${
        hasMoreThanOne ? "are" : "is"
      } selected.`;
    },
    smallestOfAssetOrPageSize() {
      return Math.min(this.discoveredAssets.length, this.pageSize);
    },
    assetsPresent() {
      return this.discoveredAssets && this.discoveredAssets.length > 0;
    },
    page() {
      return this.pageIndex;
    },
    selectedAssetArr() {
      return this.discoveredAssets.filter((a) => this.selectedAssets.includes(a.id));
    },
    showLoading() {
      return !this.discoveredAssets;
    },
    tableHeader() {
      return this.discoveredAssetPreferences.map((preference) => ({
        title: preference.friendlyName,
        label: preference.friendlyName,
        name: preference.friendlyName,
        fieldType: preference.name || '',
        sortBy: preference.name,
      }));
    },
    header() {
      return [
        "Id",
        "Name",
        "Machine Serial Number",
        "Ip Address",
        "Product Number",
        "Asset Tag",
        "Department",
        "Acquisition Date",
        "Warranty Expiration",
        "Install Date",
        "Model",
        "Used By Contributor Id",
        "Managed By Contributor Id",
        "Description",
        "Asset Type Id",
        "Custom Status Id",
      ];
    },
    filteredAssets() {
      const discoveredAssets = [];

      for (let index = 0; index < this.selectedAssetArr.length; index += 1) {
        discoveredAssets[index] = {};
        this.header.forEach((item) => {
          discoveredAssets[index][_.camelCase(item)] =
            this.selectedAssetArr[index][_.camelCase(item)];
        });
      }
      return discoveredAssets;
    },
    singleAssetSeleceted() {
      return this.selectedAssetArr.length === 1;
    },
    selectAssetsCount() {
      return this.selectedAssets.length;
    },
    hasAssociatedInfo() {
      return (
        this.selectedAssetArr[0].helpTickets.length ||
        this.selectedAssetArr[0].managedBy ||
        this.selectedAssetArr[0].usedBy ||
        this.selectedAssetArr[0].universalLinksCount
      );
    },
    showSelectAllCheckbox() {
      return this.discoveredAssetStatus !== 'imported';
    },
    modalTitle() {
      return this.isDeleting ? "delete" : "update";
    },
    selectedAllAssets() {
      return this.allAssetsSelected || this.selectedAssets.length === this.currentTabAssetsCount;
    },
    pageStart() {
      return Math.min(((this.pageIndex * this.pageSize) + 1), this.currentTabAssetsCount);
    },
    pageEnd() {
      return Math.min(
        (this.page * this.pageSize) + this.pageSize,
        this.currentTabAssetsCount
      );
    },
    ignoredStatus() {
      return (this.discoveredAssetStatus === 'ignored');
    },
    importedStatus() {
      return (this.discoveredAssetStatus === 'imported');
    },
    currentTabAssetsCount() {
      const totalAssetsCount = {
        ready_for_import: this.totalReadyForImport,
        incomplete: this.totalIncomplete,
        unrecognized: this.totalUnrecognized,
        imported: this.totalImported,
        ignored: this.totalIgnored,
      };
      return totalAssetsCount[this.discoveredAssetStatus];
    },
    headerActions() {
      const actions = [];

      const showImport = !this.importedStatus;
      const isIgnored = this.ignoredStatus;

      if (showImport) {
        actions.push({ name: 'Import', include: true, iconClass: 'nulodgicon-link' });
      }

      actions.push(
        { name: 'Ignore', include: !isIgnored && showImport, iconClass: 'nulodgicon-trash-b' },
        { name: 'Unignore', include: isIgnored && showImport, iconClass: 'nulodgicon-unlink' },
        { name: 'Delete', include: isIgnored && showImport, iconClass: 'nulodgicon-trash-b' }
      );

      return actions;
    },
    addAssetsModalTitle() {
      return this.hasDuplicateAssets ? 'Duplicate Assets' : 'Before we proceed...';
    },
  },
  watch: {
    assetTypes() {
      const typeId = this.$route.query.type;
      if (typeId && this.assetTypes) {
        const assetTypeId = Number(typeId);
        this.setAssetTypeById(assetTypeId);
      }
    },
    locations() {
      const locationId = this.$route.query.location_id;
      if (locationId && this.locations) {
        const assetLocationId = Number(locationId);
        this.setLocationByID(assetLocationId);
      }
    },
    discoveredAssetStatus() {
      this.selectedAssets = [];
    },
    discoveredAssets() {
      if (this.allAssetsSelected) {
        this.discoveredAssets.map((a) => {
          if (!this.selectedAssets.includes(a.id)) {
            this.selectedAssets.push(a.id);
          }
          return;
        });
      }
      this.setCheckedAssets([]);
      this.resetBulkActionsOptions();
      if (!this.companyUsersAndGroups?.length && this.discoveredAssets?.length) {
        this.$store.dispatch('fetchCompanyUserAndGroupsOptions');
      }
      if (this.discoveredAssets?.length && !this.locations?.length) {
        this.fetchLocations({ offset: this.locationsOffset, limit: this.perPage });
      }
    },
    selectedAssetsArray() {
      if (this.assetTags) {
        const updatedTags = this.assetTags.map((tag) => ({
          ...tag,
          checked: this.selectedAssetsArray.some((asset) =>
            asset.tags?.some((assetTag) => assetTag.name === tag.name)
          ),
        }));
        this.setAssetTags(updatedTags);
      }
    },
    activeSort: {
      handler(newValue, oldValue) { 
        this.setSortByOptions({
          activeSort: newValue,
          activeSortDirection: this.sortByOptions.activeSortDirection,
        });
        if (oldValue && oldValue.length) {
          this.fetchAssets();
        }
      },
    },
    activeSortDirection: {
      handler(newValue, oldValue) {
        this.setSortByOptions({
          activeSort: this.sortByOptions.activeSort,
          activeSortDirection: newValue,
        });
        if (oldValue && oldValue.length) {
          this.fetchAssets();
        }
      },
    },
    selectedAssetArr: {
      handler(newVal) {
        this.selectedAssetsArray = JSON.parse(JSON.stringify(newVal));
      },
      deep: true,
    },
    selectedAssets: {
      handler() {
        this.checkedAssets = this.importDiscoveredAssets;
      },
      deep: true,
    },
  },
  created() {
    this.$store.dispatch('fetchDiscoveredAssetPreferences');
  },
  beforeDestroy() {
    this.setDiscoveredAssets([]);
    this.setSortByOptions([]);
    this.setCurrentDiscoveredAsset(null);
    document.removeEventListener("click", this.handleOutsideClick);
    this.unbindAll();
  },
  methods: {
    ...mapMutations([
      "setAssetType",
      "setCurrentDiscoveredAsset",
      "setPageIndex",
      "setPageSize",
      "setSearchTerm",
      "setSearch",
      "setSortByOptions",
      "setCurrentLocation",
      "setPageIndex",
      "setAssetTags",
      "setActiveTab",
      'clearCheckedAssets',
      'removeCheckedAsset',
      'addCheckedAsset',
      'setCheckedAssets',
      'setDiscoveredAssetStatus',
      'setDiscoveredAssets',
      'setCurrentDiscoveredAsset',
    ]),
    ...mapActions("GlobalStore", ["fetchLocations"]),
    ...mapActions(["updateDiscoveredAssetListColumns"]),
    onWorkspaceChange() {
      this.$store.dispatch('fetchAssetTypes');
      this.$store.dispatch('fetchDiscoveredAssetPreferences').then(() => {
        this.checkSortCookies();
      });
      this.setSearchTerm(null);
      this.setSearch(null);
      this.setDiscoveredAssetStatus(this.$route.name);
      this.setPageIndex(0);
      this.checkPerPage("discovered-assets", this.setPageSize);
      this.$store.dispatch('fetchCompanyUserOptions');
      this.setCurrentDiscoveredAsset(null);
      this.setActiveTab(null);
      this.setSelectedColumnforSorting();
    },
    fetchAssets() {
      this.$store.dispatch("fetchDiscoveredAssets");
    },
    filtersUpdated() {
      this.$store.commit("setPageIndex", 0);
      this.fetchAssets();
      if (this.isFilterRemoved) {
        this.isFilterRemoved = false;
        this.allAssetsSelected = false;
      } else {
        this.selectedAssets = [];
      }
    },
    assignLocation(location) {
      this.selectedLocation = location;
      this.locationId = location.id;
    },
    setLocation(assetInfo) {
      this.selectedLocation = null;
      if (assetInfo && assetInfo[0] && assetInfo[1]) {
        const assetInformation = { ...assetInfo };
        assetInformation[0].locationId = assetInfo[1].id;
        assetInformation[0].locationDescription = assetInfo[1].name;
      }
    },
    removeLocation(assetInfo) {
      if (assetInfo && assetInfo[0]) {
       const assetInformation = { ...assetInfo };
       assetInformation[0].locationId = null;
       assetInformation[0].locationDescription = null;
      }
    },
    recommendedType(asset) {
      let match = false;
      let type = asset.assetType;

      for (let i = 0; i < this.assetTypes.length; i += 1) {
        if (this.assetTypes[i].name.toLowerCase() === asset.assetType.toLowerCase()) {
          type = this.assetTypes[i].name;
          match = true;
          break;
        }
      }

      if (!match) {
        for (let i = 0; i < this.assetTypes.length; i += 1) {
          if (asset.assetType.toLowerCase().includes(this.assetTypes[i].name.toLowerCase())) {
            type = this.assetTypes[i].name;
            match = true;
            break;
          }
        }
      }

      if (!match) {
        for (let i = 0; i < this.assetTypes.length; i += 1) {
          if (this.assetTypes[i].name.toLowerCase().includes(asset.assetType.toLowerCase())) {
            type = this.assetTypes[i].name;
            match = true;
            break;
          }
        }
      }

      if (!match) {
        type = this.assetTypes[0].name;
      }

      asset.assetType = type;
      return type;
    },

    changeAssetType(e, asset) {
      const modifiedAsset = asset;
      modifiedAsset.assetType = e.target.value;
    },

    pageSelected(p) {
      this.setPageIndex(p - 1);
      this.fetchAssets();
    },
    changePageSize(e) {
      this.setPageSize(e.currentTarget.value);
      this.setPageIndex(0);
      this.setPerPageInCookie("discoveredAssets", this.pageSize);
      this.fetchAssets();
    },
    setAssetTypeById(id) {
      this.assetTypes.forEach((t) => {
        if (t.id === id) {
          this.setAssetType(t);
        }
      });
    },
    selectAsset(assetId) {
      this.allAssetsSelected = false;
      const idx = this.selectedAssets.indexOf(assetId);
      if (idx > -1) {
        this.selectedAssets.splice(idx, 1);
      } else {
        this.selectedAssets.push(assetId);
      }
    },
    closeAddAssetsForDuplicates() {
      if (this.hasDuplicateAssets) {
        this.$store.dispatch('fetchDiscoveredAssets');
        this.$store.dispatch('fetchDiscoveredAssetsSummary');
        this.hasDuplicateAssets = false;
      }
    },
    changeAll(fieldOrEvent, value = null) {
      if (value === null && fieldOrEvent.target === undefined) {
        return this.importDiscoveredAssets;
      }

      let field;
      let val;

      if (value === null) {
        field = fieldOrEvent.currentTarget.id;
        val = fieldOrEvent.target.value;
      } else {
        field = fieldOrEvent;
        val = value;
        this.assignValue(field, val);
      }
      const params = { field, value: val };
      this.updateSelectedAssets(params);
      this.updateAllAssetFields[params.field] = params.value;
      return this.importDiscoveredAssets;
    },
    assignValue(field, val) {
      if (field === 'department') {
        this.bulkSelectedDept = [val];
      }
    },
    setAllLocations(assetInfo) {
      const field = assetInfo[0];
      const value = assetInfo[1]?.id;
      this.selectedLocation = value;
      const params = { field, value };
      this.updateAllAssetFields[field] = value;
      this.updateSelectedAssets(params);
      if (assetInfo[0] === 'locationId') {
        this.bulkLocationId = assetInfo[1]?.id;
      }
      return this.importDiscoveredAssets;
    },
    setUsedByContributor(assetInfo) {
      if (assetInfo && assetInfo[0]) {
        const assetInformation = { ...assetInfo };
        assetInformation[0].usedByContributorId = assetInfo[1].id;
      }
    },
    setManagedByContributor(assetInfo) {
      if (assetInfo && assetInfo[0]) {
        const assetInformation = { ...assetInfo };
        assetInformation[0].managedByContributorId = assetInfo[1].id;
      }
    },
    setDepartment(assetInfo) {
      if (assetInfo && assetInfo[0]) {
        const [asset, department] = assetInfo;
        asset.department = asset.department === department ? null : department;
      }
    },
    addNewDepartment(data) {
      this.tempOptions.push(data[1]);
  
      if (data[0]) {
        this.setDepartment(data);
      } else {
        this.changeAll('department', data[1]);
        this.bulkSelectedDept = [data[1]];
      }
    },
    removeDepartment() {
      this.changeAll('department', '');
      this.bulkSelectedDept = null;
    },
    removeErrors(asset) {
      if (this.$refs[`asset-name-${asset}`][0].classList.contains('is-invalid')) {
        this.$refs[`asset-name-${asset}`][0].classList.remove('is-invalid');
        this.$refs[`asset-name-error-${asset}`][0].classList.add('display-none');
        this.$refs[`asset-dup-error-${asset}`][0].classList.add('display-none');
        this.$refs[`mang-asset-dup-error-${asset}`][0].classList.add('display-none');
      }
    },
    showErrorsForDuplicateAssets(duplicateAssetIds) {
      this.hasDuplicateAssets = true;
      duplicateAssetIds.forEach(assetId => {
        this.$refs[`asset-name-${assetId}`][0].classList.add('is-invalid');
        this.$refs[`mang-asset-dup-error-${assetId}`][0].classList.remove('display-none');
      });
      const currentCheckedDuplicateAssets = this.importDiscoveredAssets.filter(asset => duplicateAssetIds.includes(asset.id));
      const insertedAssetsCount = this.importDiscoveredAssets.length - duplicateAssetIds.length;
      if (currentCheckedDuplicateAssets) {
        this.$store.commit('setDiscoveredAssets', this.discoveredAssets(currentCheckedDuplicateAssets));
      }
      if (insertedAssetsCount) {
        this.emitSuccess(`Successfully added ${insertedAssetsCount} discovered assets`);
      } else {
        this.emitError(`Sorry, there was an error moving assets. Please try again.`);
      }
    },
    changeAllContributors(assetInfo) {
      const field = assetInfo[0];
      const value = assetInfo[1].id;
      const params = { field, value };
      this.updateAllAssetFields[field] = value;
      this.updateSelectedAssets(params);
      this.loadContributor(assetInfo[0], assetInfo[1].id);
      return this.checkedAssets;
    },
    loadContributor(field, id) {
      const params = { includes: id };
      http
        .get('/contributor_options.json', { params })
        .then((res) => {
          if (field === 'usedByContributorId') {
            this.usedByContributor = res.data;
          } else {
            this.managedByContributor = res.data;
          }
        })
        .catch(() => {
          this.emitError(
            `Sorry, there was an error loading Users and Groups. Please try again later.`
          );
        });
    },
    okAddAssets() {
      this.isSubmitting = true;
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        const params = {};
        if (this.selectAllAssets) {
          params.status = this.discoveredAssetStatus;
          params.select_all = this.selectAllAssets;
          params.update_all_fields = this.updateAllAssetFields;
        }
        if (this.discoveredAssetSource) {
          params.source = this.discoveredAssetSource.id;
        }
        if (this.$store.getters.search) {
          params.search = this.$store.getters.search;
        }
        params.discovered_assets = this.importDiscoveredAssets;

        http
          .post("/discovered_assets/import_bulk.json", params)
          .then((res) => {
            if (res.data.message) {
              this.updateNotification = res.data.message;
              this.$refs.updateNotificationModal.open();
            } else if (res.data.duplicateAssetIds.length) {
              this.showErrorsForDuplicateAssets(res.data.duplicateAssetIds);
            }
            else {
              this.emitSuccess(`Successfully added discovered assets`);
            }
            if (!this.hasDuplicateAssets) {
              this.closeAddAssets();
              this.clearCheckedAssets();

              if (this.assetsToImport) {
                this.$store.dispatch('fetchDiscoveredAssetsLogs');
              } else {
                this.$store.dispatch('fetchDiscoveredAssets');
                this.$store.dispatch('fetchDiscoveredAssetsSummary');
              }
            }
            this.isSubmitting = false;
          }).catch(() => {
            this.emitError(`Sorry, there was an error moving assets. Please try again.`);
            this.isSubmitting = false;
          });
          this.selectAllAssets = false;
      } else {
        this.isSubmitting = false;
      }
      this.setupPusherListeners();
    },
    okIgnore() {
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        const params = {};
        if (this.selectAllAssets) {
          params.status = this.discoveredAssetStatus;
          params.select_all = this.selectAllAssets;
        }
        if (this.discoveredAssetSource) {
          params.source = this.discoveredAssetSource.id;
        }
        if (this.$store.getters.search) {
          params.search = this.$store.getters.search;
        }
        params.discovered_asset_ids = this.selectedAssets;
        
        http
          .post("/discovered_assets/bulk_archive.json", params )
          .then((res) => {
            if (res.data.message) {
              this.updateNotification = res.data.message;
              this.$refs.updateNotificationModal.open();
            } else {
              this.emitSuccess(`Successfully ignored discovered assets`);
            }
            this.closeIgnore();
            this.clearCheckedAssets();
            this.$store.dispatch('fetchDiscoveredAssets');
            this.$store.dispatch('fetchDiscoveredAssetsSummary');
          }).catch(() => {
            this.emitError(`Sorry, there was an error archiving assets. Please try again.`);
          });
      }
    },
    okDelete() {
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        const params = {};
        if (this.selectAllAssets) {
          params.status = this.discoveredAssetStatus;
          params.select_all = this.selectAllAssets;
        }
        if (this.discoveredAssetSource) {
          params.source = this.discoveredAssetSource.id;
        }
        if (this.$store.getters.search) {
          params.search = this.$store.getters.search;
        }
        params.discovered_asset_ids = this.selectedAssets;
        http
          .post("/discovered_assets/bulk_delete.json", params)
          .then((res) => {
            if (res.data.message) {
              this.updateNotification = res.data.message;
              this.$refs.updateNotificationModal.open();
            } else {
              this.emitSuccess(`Successfully deleted discovered assets`);
            }
            this.closeDelete();
            this.selectedAssets = [];
            this.$store.dispatch('fetchDiscoveredAssets');
            this.$store.dispatch('fetchDiscoveredAssetsSummary');
          }).catch(() => {
            this.emitError(`Sorry, there was an error archiving assets. Please try again.`);
          });
      }
    },
    okUnignore() {
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        const params = {};
        if (this.selectAllAssets) {
          params.status = this.discoveredAssetStatus;
          params.select_all = this.selectAllAssets;
        }
        if (this.discoveredAssetSource) {
          params.source = this.discoveredAssetSource.id;
        }
        if (this.$store.getters.search) {
          params.search = this.$store.getters.search;
        }
        params.discovered_asset_ids = this.selectedAssets;
        http
          .post("/discovered_assets/bulk_unarchive.json", params )
          .then((res) => {
            if (res.data.message) {
              this.updateNotification = res.data.message;
              this.$refs.updateNotificationModal.open();
            } else {
              this.emitSuccess(`Successfully unignored discovered assets`);
            }
            this.closeUnignore();
            this.selectedAssets = [];
            this.$store.dispatch('fetchDiscoveredAssets');
            this.$store.dispatch('fetchDiscoveredAssetsSummary');
          }).catch(() => {
            this.emitError(`Sorry, there was an error archiving assets. Please try again.`);
          });
      }
    },
    closeIgnore() {
      this.$refs.ignoreModal.close();
      this.selectedAssets = [];
    },
    closeDelete() {
      this.$refs.deleteModal.close();
      this.selectedAssets = [];
    },
    closeUnignore() {
      this.$refs.unignoreModal.close();
      this.selectedAssets = [];
    },
    closeAddAssets() {
      this.$refs.addAssetsModal.close();
      this.selectedAssets = [];
    },
    resetBulkActionsOptions(){
      this.usedByContributor = null;
      this.managedByContributor = null;
      this.bulkSelectedDept = null;
      this.selectedLocation = null;
      if (this.$refs.impactDropdown) {
        this.$refs.impactDropdown.value = null;
      }
      if(this.$refs.assetTypeDropdown){
        this.$refs.assetTypeDropdown.value = null;
      }
      this.updateAllAssetFields = {};
    },
    selectAllAssetsRecord() {
      this.selectedAssets = this.discoveredAssets.map(({ id }) => id);
      this.allAssetsSelected = true;
      this.selectAllAssets = true;
      this.findStatusFilter();
    },
    findStatusFilter() {
      this.statusFilter = {};
      this.activeFilters.forEach((filter) => {
        this.statusFilter[filter.filterName] = filter.filter.id;
      });
      if (this.search) {
        this.statusFilter.search = this.search;
      }
    },
    unSelectAssets() {
      this.allAssetsSelected = false;
      this.selectedAssets = [];
    },
    selectAllCheckboxClicked(selectAllValue) {
      selectAllValue ? this.selectedAssets = this.discoveredAssets.map(({ id }) => id) : this.unSelectAssets();
    },
    setSortCookies() {
      this.setActiveSortInCookie(
        "discovered-assets",
        this.activeSort,
        this.activeSortDirection
      );
    },

    toggleFilterMenu() {
      this.hideAllOtherMenus();
      this.isFilterMenuOpen = !this.isFilterMenuOpen;
      if (this.isFilterMenuOpen) {
        document.addEventListener("click", this.handleOutsideClick);
      } else {
        document.removeEventListener("click", this.handleOutsideClick);
      }
      this.$refs.dismissibleContainer.toggleOpen();
    },
    handleOutsideClick(e) {
      const ref = this.$refs.dismissibleContainer;
      if (ref) {
        const container = ref.$el;
        if (!container.contains(e.target) || e.target.className === 'dropdown d-inline-block') {
          this.closeFilterMenu();
        }
      }
    },
    closeFilterMenu() {
      this.isFilterMenuOpen = false;
      document.removeEventListener("click", this.handleOutsideClick);
      this.$refs.dismissibleContainer.forceClose();
    },
    setLocationByID(id) {
      const location = this.locations.find((loc) => loc.id === id);
      if (location) {
        this.setCurrentLocation(location);
      }
    },
    setSelectedColumnforSorting() {
      this.activeSort = this.sortByOptions.activeSort;
      this.activeSortDirection = this.sortByOptions.activeSortDirection;
    },
    setupPusherListeners() {
      if (this.$pusher) {
        const channel = this.$pusher.subscribe(`${this.$currentCompanyId}assets`);
        channel.bind('discovered-assets', () => {
          if (this.$refs.updateNotificationModal) {
            this.$refs.updateNotificationModal.close();
          }
        });  
      } 
    },
    fetchAssetsData() {
      this.$store.dispatch("fetchDiscoveredAssets", true).then(() => {
        const filteredArray = this.discoveredAssets.filter((newAssetData) =>
          this.selectedAssetsArray.some(
            (oldAssetData) => oldAssetData.id === newAssetData.id
          )
        );
        this.selectedAssetsArray = filteredArray;
        this.isDataFetched = true;
      });
    },
    checkSortCookies() {
      if (this.sortPresentInCookie()) {
        const cookieSortItem = this.getCookieValue('discovered-assets-sort-item');
        const cookieSortDirection = this.getCookieValue('discovered-assets-sort-direction');
        const preferences = this.discoveredAssetPreferences.map((n) => n.name);

        if (cookieSortItem && cookieSortDirection && preferences.includes(cookieSortItem)) {
          this.setSortByOptions({
            activeSort: cookieSortItem,
            activeSortDirection: cookieSortDirection,
          });
          this.setSelectedColumnforSorting();
        }
      }
    },
    manageAction(action) {
      if (action === 'import') {
        this.addToAssets();
      } else if (action === 'ignore') {
        this.ignoreAssets();
      } else if (action === 'unignore') {
        this.unignoreAssets();
      } else if (action === 'delete') {
        this.deleteAssets();
      } else {
        this.emitError(`Sorry, but action ${action} is not valid.`);
      }
    },
    setHeight(height) {
      this.height = height;
    },
    addToAssets() {
      const canRun = this.checkIfCanPerformAction();
      
      if (canRun) {
        if (this.height > 0) {
          this.$refs.addAssetsModal.$el.style.height = `${this.height}px`;
          this.$nextTick(() => {
            this.$refs.addAssetsModal.$el.scrollIntoView({ behavior: 'smooth', block: 'start' });
          });
        }
        this.$refs.addAssetsModal.open();
      }
    },
    ignoreAssets() {
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        this.$refs.ignoreModal.open();
      }
    },
    deleteAssets() {
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        this.$refs.deleteModal.open();
      }
    },
    unignoreAssets() {
      const canRun = this.checkIfCanPerformAction();
      if (canRun) {
        this.$refs.unignoreModal.open();
      }
    },
    checkIfCanPerformAction() {
      if (!this.isWrite) {
        this.emitError(`Sorry, you do not have permission to perform this action.`);
        return false;
      }
      if (this.selectedAssets.length === 0 && !this.assetsToImport) {
        this.emitError(`Please select at least one asset to perform this operation.`);
        return false;
      }
      return true;
    },
    selectAll(allAssetsSelected = true) {
      this.resetPagination();
      if (allAssetsSelected) {
        this.selectAllAssets = true;
        this.discoveredAssets.forEach(asset => {
          if (!this.importDiscoveredAssets.some(existingAsset => existingAsset.id === asset.id)) {
            this.addToCheckedAssetsArr(asset);
          }
        });
      } else {
        this.setCheckedAssets([]);
      }
    },
    addToCheckedAssetsArr(discoveredAsset) {
      const result = this.importDiscoveredAssets.map(a => a.id);
      const idx = result.indexOf(discoveredAsset.id);
      if (idx > -1) {
        this.filterCheckedAssets();
        this.removeCheckedAsset(idx);
        this.selectAllAssets = false;
        this.loadAllAsset = false;
      } else {
        this.addCheckedAsset({ ...discoveredAsset});
      }
    },
    filterCheckedAssets(){
      const discoveredAssetIds = this.discoveredAssets.map(a => a.id);
      const requiredAssets = this.checkedAssets.filter(checkedAsset => discoveredAssetIds.includes(checkedAsset.id) );
      this.setCheckedAssets(requiredAssets);
    },

    resetPagination() {
      this.showMoreIndex = 0;
      this.offset = 0;
      this.checkedOffset = 0;
    },
    loadAllDiscoveredAssets(isCheckedAssets = false) {
      const offsetKey = isCheckedAssets ? 'checkedOffset' : 'offset';
      this[offsetKey] += this.pageSize;
      const params = {
        offset: this[offsetKey],
        per_page: this.pageSize,
        status: this.discoveredAssetStatus,
        select_all_assets: this.selectAllAssets,
        include_filtered_assets: false,
      };
      if (this.$store.getters.search) {
        params.search = this.$store.getters.search;
      }
      if (this.discoveredAssetSource) {
        params.source = this.discoveredAssetSource.id;
      }
      http
        .get('/discovered_assets.json', { params })
        .then((res) => {
          this.loading = false;
          this.$store.commit('setDiscoveredAssets', this.discoveredAssets.concat(res.data.discoveredAssets));
          
          if (this.showMoreIndex + 1 < this.pageCount) {
            this.showMoreIndex += 1;
          }
          Object.keys(this.updateAllAssetFields).forEach((field) => {
            const object = { field, value: this.updateAllAssetFields[field] };
            this.updateSelectedAssets(object);
          });
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error loading all assets. Please try again.`);
          this.loading = false;
        });
    },
    updateSelectedAssets(object) {
      for (let idx = 0; idx < this.importDiscoveredAssets.length; idx += 1) {
        this.importDiscoveredAssets[idx][object.field] = object.value;
      }
    },
    selectAllAssetsOnPage() {
      this.selectedAssets = this.discoveredAssets.map(a => a.id);
    },
  },
};
</script>

<style scoped lang="scss">
  [type="checkbox"] {
    display: none;
  }

  :checked ~ .tag-option {
    background-color: color.adjust($teal, $lightness: -15%);
    border-color: color.adjust($teal, $lightness: -15%);
  }

  .select-all {
    cursor: pointer;
  }
  .bulk-modal :deep(.sweet-modal) {
    max-width: 80.375rem;
    top: 2rem;

    .sweet-modal-overlay {
      height: 100%;
      width: 100%;
    }

    .sweet-box-actions {
      right: 1.5rem;
      top: 1.875rem;
    }

    .sweet-content {
      padding: 2rem;

      .bulk-update-header {
        .modal-detail {
          font-size: 0.875rem;
        }
      }
    }
  }

  :deep(.merge-asset-modal .sweet-modal) {
    .sweet-content-content {
      width: 100%;
    }
  }

  .asset-bulk-edit-wrapper {
    border-top-left-radius: 0 !important;

    .nulodgicon-android-close {
      font-size: 22px;

      &:before {
        color: $themed-muted;
      }
    }
  }

  .asset-heading--bulk-selecting {
    z-index: map-get($zIndex, "above");
  }

  .badge {
    width: 1.0625rem;
    height: 1.0625rem;
  }

  .btn-view-more {
    z-index: 9;
    top: 0.313rem;
    min-width: 7.375rem;

    &:hover {
      background-color: #dae0e5;
      border: 1px solid rgba(33, 37, 41, 0.1);
    }
  }

  .asset-search-bar {
    width: 53.125rem;

    @media (max-width: 1390px) {
      width: 40rem;
    }
    @media (max-width: 1185px) {
      width: 25rem;
    }
    @media (max-width: 508px) {
      width: 20rem;
    }
  }

  @media (max-width: 940px) {
    .pagination-wrapper {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .apply-to-all-row {
    background: $themed-light;
  }
  
  .more-info-modal {
    :deep(.sweet-content) {
      padding: 0px !important;
  
      .heading-area {
        background-color: $themed-dark-drawer-bg;
        color: white;
        padding: 1rem;
        padding-left: 25px;
        text-align: left;
      }
  
      .info-area {
        padding: 1rem;
        padding-left: 35px;
        text-align: left;
      }
    }
  
    :deep(.sweet-modal .sweet-box-actions .sweet-action-close) {
      color: #fff;
    }
  }
  
  [type='checkbox'] {
    display: none;
  }
  .checkbox-data {
    width: 0.875em;
  }
  
  .checkbox-selected {
    background-color: $teal;
    border-color: $teal;
  }
  
  .discovered-assets-table th {
    border-top: 0px;
    border-bottom: 1px solid $themed-fair;
  }
  .asset-action {
    min-width: 5rem;
  }
  .display-none {
    display: none;
  }
  .table thead th {
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .align-middle {
      vertical-align: middle !important;
      max-width: 225px;
  }
  .tooltip {
    position: relative;
    display: inline-block;
  }
  .tooltip .tooltiptext {
    visibility: hidden;
    width: 150px;
    background-color: $themed-muted;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    position: absolute;
    z-index: 1;
    bottom: 150%;
    left: 50%;
    margin-left: -70px;
  }
  .tooltip:hover .tooltiptext {
    visibility: visible;
  }
  .tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: $gray-600 transparent transparent transparent;
  }
  .nulodgicon-external-link {
    font-size: 13px;
  }
  .truncate-data {
    display: block;
    max-width: 130px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .serial-container {
    max-width: 145px;
  }
  .source-icon {
    width: 25px;
    height: 25px;
  }
  .show-import-button {
    width: 90px;
  }
  
  :deep(.scrollable-table-outer-wrap) {
    max-height: calc(60vh - 3rem - 1rem);
    .simplebar-placeholder {
      width: 100% !important;
    }
  }
  .float-button {
    position: absolute;
    left: 50%;
    border-radius: 45px;
    font-size: 14px;
    color: #495057;
    transform: translateY(-123%) translateX(-50%);
  
    &:hover {
      background-color: #dfdfdf;
    }
  }
  .proceed-modal {
    z-index: 122;
  }
  .proceed-modal :deep(.sweet-modal) {
    max-height: 90%;
    overflow: hidden;
  }
  .logs-modal {
    left: -5rem
  }
  :deep(.logs-modal .sweet-title) {
    text-align: left;
  }
  :deep(.logs-modal .sweet-modal .sweet-box-actions .sweet-action-close:hover) {
    color: var(--themed-light) !important;
    }
  :deep(.logs-modal .sweet-modal .sweet-box-actions .sweet-action-close) {
    color: var(--themed-base) !important;
  }
  .box-width {
    width: 60%;
  }
  .select-box {
    width: 20%;
  }
  .modal-bg {
    line-height: 2rem;
  }
</style>
