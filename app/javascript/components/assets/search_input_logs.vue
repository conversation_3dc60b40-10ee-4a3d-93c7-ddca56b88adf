<template>
  <div>
    <input
      ref="searchTerms"
      class="form-control pl-5"
      placeholder="Search by name, mac address, ip address, asset type"
      :value="searchLogs"
      @input="updateSearch"
    >
    <i class="nulodgicon-ios-search-strong search-input-icon" />
    <span
      class="searchclear nulodgicon-ios-close-outline"
      @click="clearSearch"
    />
  </div>
</template>
  
<script>
  import _ from 'lodash';
  import { mapMutations, mapGetters } from 'vuex';
  
  export default {
    computed: {
      ...mapGetters(['searchLogs']),
    },
    methods: {
      ...mapMutations([
        'setSearchTermLogs',
        'setSearchLogs',
        'setPageIndex',
      ]),
  
      updateSearch: _.debounce(
        function update() {
          this.setPageIndex(0);
          if (this.$refs.searchTerms.value) {
            this.setSearchTermLogs('all');
            this.setSearchLogs(this.$refs.searchTerms.value);
          } else {
            this.setSearchTermLogs(null);
            this.setSearchLogs(null);
          }
          this.$store.dispatch('fetchDiscoveredAssetsLogs');
        },
        1000
      ),
      clearSearch() {
        this.setSearchTermLogs(null);
        this.setSearchLogs(null);
        this.$store.dispatch('fetchDiscoveredAssetsLogs');
      },
    },
  };
</script>
  
<style lang="scss" scoped>
  .searchclear {
    display: none;
  }
  input {
    &:not(:placeholder-shown) {
      + .searchclear {
        display: block;
        position: absolute;
        right: 25px;
        top: -12px;
        bottom: 0;
        height: 14px;
        margin: auto;
        font-size: 21px;
        cursor: pointer;
        color: #b5b7b8 !important;
      }
    }
  }
</style>
