<template>
  <div class="pr-2">
    <div class="float-right">
      <select
        id="filtersPerPage"
        data-tc-filters-per-page
        :input="pageSizeLogs"
        class="form-control d-inline-block select-per-page-filter"
        :value="pageSizeLogs"
        @input="changePageSize"
      >
        <option>25</option>
        <option>50</option>
        <option>100</option>
      </select>
    </div>
    <div class="float-right text-secondary btn-sm">
      Per Page
    </div>
  </div>
</template>
  
  <script>
  import { mapMutations, mapGetters } from 'vuex';
  import pageSizeHelper from '../../mixins/page_size_helper';
  
  export default {
    mixins: [pageSizeHelper],
    data() {
      return {
        moduleName: "ManagedAsset",
      };
    },
    computed: {
      ...mapGetters(['pageSizeLogs']),
    },
    methods: {
      ...mapMutations([
        'setPageSizeLogs',
        'setPageIndexLogs',
      ]),
      changePageSize(e) {
        this.setPageSizeLogs(e.currentTarget.value);
        this.setPageIndexLogs(0);
        this.setPerPageInCookie("discovered-assets-logs", parseInt(this.pageSize, 10));
        this.$store.dispatch("fetchDiscoveredAssetsLogs");
      },
    },
  };
  </script>
