<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">
    <div
      v-if="!showImportOption"
      class="row"
    >
      <div class="col-md-12">
        <!-- For now we will not show the steps -->
        <!-- <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <step-instruction-box
          step-number="1"
          step-title="Login to your Sophos account"
        >
          <p class="not-as-small mt-3">
            <ol>
              comment: Add steps to create Sophos creds
            </ol>
          </p>
        </step-instruction-box> -->

        <step-instruction-box
          step-number="1"
          step-title="Fill out the following information"
        >
          <form
            class="mt-4"
            @submit.prevent="submitSophosForm"
          >
            <p
              v-if="errors.length > 0"
              class="col-12 small text-danger"
            >
              <strong>Please correct the following error(s):</strong>
              <ul class="mb-0">
                <li
                  v-for="(error, index) in errors"
                  :key="`error-${index}`"
                >
                  {{ error }}
                </li>
              </ul>
            </p>
            <div class="row">
              <div class="form-group col-12">
                <label for="clientId">
                  Client ID
                </label>
                <input
                  id="clientId"
                  v-model="sophosData.clientId"
                  class="input form-control"
                  required
                  type="text"
                  @keydown.space.prevent
                >
              </div>
              <div class="form-group col-12">
                <label for="clientSecret">
                  Client Secret
                </label>
                <input
                  id="clientSecret"
                  v-model="sophosData.clientSecret"
                  class="input form-control"
                  required
                  type="password"
                  @keydown.space.prevent
                >
              </div>
            </div>
            <div class="form-group col-12 mb-0 text-right">
              <button
                v-if="!loading"
                class="btn btn-sm btn-link text-secondary mr-2"
                @click.prevent="close"
              >
                <span>Cancel</span>
              </button>
              <button
                :disabled="loading || !dataPresent"
                class="btn btn-sm btn-primary px-3"
              >
                Next
              </button>
            </div>
          </form>
        </step-instruction-box>
      </div>
    </div>
    <div v-if="showImportOption">
      <import-option-selector
        :integration-name="'Sophos'"
        @back-to-acc-details="backToAccDetails"
        @submit="startSophosInt"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import OnboardingTitle from 'components/shared/module_onboarding/onboarding_title.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import ImportOptionSelector from 'components/shared/module_onboarding/import_option_selector_modal.vue';

  export default {
    components: {
      OnboardingTitle,
      StepInstructionBox,
      ImportOptionSelector,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        header: 'Integrate Sophos',
        subHeader: 'Sync your existing asset services with Sophos',
        imageSrc: 'https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/logos/integrations-logos/sophos.png',
        integrationInstructions: `<li class='mt-3'> You must login with an account that has <b>Admin Privileges</b> to Sophos.</li>
                                  <li class='mt-1'>Syncing might take up to 15 minutes.</li>`,
        sophosData: {
          clientId: '',
          clientSecret: '',
          importType: 'discovered_asset',
        },
        errors: [],
        loading: false,
        showImportOption: false,
      };
    },
    computed: {
      dataPresent() {
        return Object.values(this.sophosData).every((val) => val);
      },
    },
    mounted() {
      this.$parent.$parent.setCondenseModal(true);
    },
    methods: {
      close() {
        this.$emit('close');
      },
      backToAccDetails() {
        this.showImportOption = false;
        this.loading = false;
      },
      startSophosInt(importType) {
        this.loading = true;
        this.errors = [];
        this.sophosData.importType = importType;
        http
          .post('/integrations/sophos/configs.json', { sophos_config: this.sophosData })
          .then(() => {
            this.loading = false;
            this.$store.dispatch('fetchCompanyIntegrations');
            this.close();
          })
          .catch(error => {
            this.loading = false;
            this.showImportOption = false;
            this.errors.push(error.response.data.message);
          });
      },
      submitSophosForm() {
        this.loading = true;
        if (this.dataPresent) {
          this.showImportOption = true;
        } else {
          this.showImportOption = false;
          this.showErrors();
          this.loading = false;
        }
      },
      showErrors() {
        if (!this.sophosData.clientId) {
          this.errors.push('Client ID is missing');
        }
        if (!this.sophosData.clientSecret) {
          this.errors.push('Client Secret is missing');
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .instance-input {
    border: 1px solid $gray-400;
    height: calc(2.25rem + 2px);
  }
  .instance-input input {
    height: auto;
  }
  .instance-input input:focus {
    box-shadow: none;
  }
  .instance-input label {
    color: #808080;
  }
</style>
