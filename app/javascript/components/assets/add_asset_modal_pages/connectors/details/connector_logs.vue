<template>
  <div class="mt-3 pb-3">
    <div
      v-if="connectorLogs.length >= 25"
      class="w-100 d-flex float-right justify-content-end mt-3 py-0"
    >
      <span class="small mr-2">
        <span class="text-muted mr-1">
          <span>Results per page</span>
        </span>
        <select
          id="filtersPerPage"
          class="form-control form-control-sm d-inline-block select-per-page-filter"
          :input="perPage"
          :value="perPage"
          @input="changePageSize"
        >
          <option>25</option>
          <option>50</option>
          <option>100</option>
        </select>
      </span>
      <div
        v-if="pageCount > 1"
        class="ml-3"
      >
        <paginate
          ref="paginate"
          :click-handler="pageSelected"
          :container-class="'pagination pagination-sm'"
          :next-class="'next-item'"
          :next-link-class="'page-link'"
          :next-text="'Next'"
          :page-class="'page-item'"
          :page-count="pageCount"
          :page-link-class="'page-link'"
          :prev-class="'prev-item'"
          :prev-link-class="'page-link'"
          :prev-text="'Prev'"
          :selected="indexPage"
        />
      </div>
    </div>
    <div
      v-if="isLoading"
      class="text-center mt-5 clear-both"
    >
      <h5 class="text-secondary font-weight-normal">
        Loading
        <span class="ml-3 d-inline-block">
          <sync-loader
            :loading="true"
            class="ml-3 mt-1"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h5>
    </div>
    <div
      v-else-if="connectorLogs && connectorLogs.length === 0"
      class="text-center mt-5 clear-both"
    >
      <h4>There are no logs present.</h4>
    </div>
    <div
      v-else-if="connectorLogs && connectorLogs.length > 0"
      class="box-border py-1 my-0"
    >
      <table class="log-table col-12 py-0 my-0">
        <thead>
          <tr>
            <th class="col-3">TIMESTAMP</th>
            <th class="col-3">DETAILS</th>
            <th class="col-3">STATUS</th>
            <th class="col-3">USER</th>
          </tr>
        </thead>
      </table>
      <div class="scrollable-tbody">
        <table class="table col-12 mb-0 log-table">
          <tbody>
            <tr
              v-for="log in connectorLogs"
              :key="log.id"
              class="text-secondary"
            >
              <td class="p-0">
                <div class="pr-sm-2 ml-3 pr-md-3 d-flex align-items-start flex-column">
                  <span class="text-secondary">{{ FormatDate(log.createdAt) }}</span>
                  <span class="text-secondary">{{ FormatTime(log.createdAt) }}</span>
                </div>
              </td>
              <td>
                <div class="pr-sm-2 pr-md-3 d-flex align-items-center">
                  <span
                    class="action-icon position-relative"
                    :class="`action-icon-${smartList(log.action)}`"
                  >
                    <i :class="smartList(log.action)"/>
                  </span>
                  <span class="text-capitalize ml-2">{{ toTitle(log.action) }}</span>
                </div>
              </td>
              <td>
                <span class="text-capitalize">{{ log.status }}</span>
              </td>
              <td>
                <div class="mt-sm-1 mt-md-0 text-capitalize text-align-start text-secondary text-muted p--responsive">
                  <span>{{ log.ownerName ? log.ownerName : log.userName }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<script>
  import moment from 'moment-timezone';
  import strings from 'mixins/string';
  import http from 'common/http';
  import Paginate from 'vuejs-paginate';
  import permissionsHelper from 'mixins/permissions_helper';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';

  export default {
    components:{
      SyncLoader,
      Paginate,
    },
    mixins: [permissionsHelper, strings],
    props: {
      connector: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        connectorLogs: [],
        perPage: 25,
        pageCount: 1,
        indexPage: 0,
        isLoading: true,
        title: null,
        connectorNames: [
          { name: "All", value: "all" },
          { name: "Cisco Meraki", value: "meraki" },
          { name: "Ubiquiti", value: "ubiquiti" },
          { name: "Microsoft Intune", value: "ms_intune" },
          { name: "Azure AD Devices", value: "azure_ad_devices"} ,
          { name: "Azure", value: "azure" },
          { name: "GCP", value: "google" },
          { name: "AWS", value: "aws" },
          { name: "Kaseya", value: "kaseya" },
          { name: "Kandji", value: "kandji" },
          { name: "Jamf Pro", value: "jamf_pro" },
          { name: "Probe", value: "probe" },
          { name: "Mosyle", value: "mosyle" },
          { name: "Sophos", value: "sophos" },
        ],
      };
    },
    mounted() {
      this.getConnectorsLogs();
    },
    methods: {
      FormatTime(date) {
        return moment.tz(new Date(date), Vue.prototype.$timezone).format('HH:mm A');
      },
      FormatDate(date) {
        return moment.tz(new Date(date), Vue.prototype.$timezone).format('MM/DD/YYYY');
      },
      changePageSize(e) {
        this.perPage = e.currentTarget.value;
        this.indexPage = 0;
        this.getConnectorsLogs();
      },
      getConnectorsLogs() {
        const params = {
          per_page: this.perPage,
          page: this.indexPage + 1,
          connector_name: this.connector,
        };
        http
          .get('/asset_connector_logs.json',{params})
          .then(res => {
            this.connectorLogs = res.data.assetConnectorLogs;
            this.pageCount = res.data.pageCount;
            this.isLoading = false;
          })
          .catch(e => {
            this.emitError(`Sorry, there was an error fetching logs. ${e.response.data.message}`);
            this.isLoading = false;
          });
      },
      pageSelected(p) {
        this.indexPage = p - 1;
        this.isLoading = true;
        this.connectorLogs = [];
        this.getConnectorsLogs();
      },
      createdAt(createdAt) {
        return this.timezoneMoment(createdAt, Vue.prototype.$timezone);
      },
      smartList(action) {
        const iconMap = {
          sync: "genuicon-refresh",
          resync: "genuicon-refresh",
          credentials_added: "nulodgicon-edit",
          credentials_updated: "nulodgicon-edit",
          deactivated: "nulodgicon-edit",
          activated: "nulodgicon-edit",
          deleted: "nulodgicon-trash-b",
        };
        return iconMap[action] || "";
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon-genuicon-refresh { background-color: $color-home; }
  .action-icon-nulodgicon-edit { background-color: $yellow; }
  .action-icon-nulodgicon-trash-b { background-color: $red; }

  .source-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
  .text-align-start {
    text-align: start;
  }
  .scrollable-tbody {
    max-height: 18.125rem;
    overflow-y: auto;

    thead, tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  
    tbody {
      display: block;
    }
  }

.log-table {
  border: none;
}

.log-table td {
  border: none;
}

.log-table tr {
  border: none;
}
.log-table th {
  border: none;
}
.log-table thead th{
  border: none;
}

.log-table tbody tr {
  border: none;
}
.box-border {
  border: 0.063rem solid $border-color;
  border-radius: $border-radius;
}
</style>
