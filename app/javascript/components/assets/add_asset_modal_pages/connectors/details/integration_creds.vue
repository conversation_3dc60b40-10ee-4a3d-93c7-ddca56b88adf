<template>
  <div>
    <form
      accept-charset="UTF-8"
      novalidate
    >
      <div class="form-row">
        <div
          v-for="data in valueData"
          :key="data.value"
          class="form-group mb-3 mt-2 col-md-6"
        >
          <label
            class="modal-fields-label"
            for="org_name"
          >
            {{ data.heading }}
          </label>
          <input
            :value="data.value"
            type="text"
            class="form-control"
            disabled
            name="name"
            placeholder="Enter"
          >
        </div>
      </div>
    </form>
  </div>
</template>
<script>

export default {
  props: {
    value: {
      type: Object,
      required: true,
    },
  },
  computed: {
    valueData() {
      switch (this.value.name) {
        case 'meraki':
          return [{heading: "Organization Name", value: this.value.userName}, {heading: "Token", value: this.value.token}];
        case 'kaseya':
          return [{heading: "Integrator Username", value: this.value.integratorUsername}];
        case 'ubiquiti':
           return [{heading: "Username", value: this.value.userName}, {heading: "Token", value: this.value.password}];
        case 'mosyle':
           return [{heading: "Integrator Username", value: this.value.userName}, {heading: "Token", value: this.value.token}];
        case 'kandji':
           return [{heading: "URL", value: this.value.apiUrl}, {heading: "Token", value: this.value.token}];
        case 'jamf_pro':
           return [{heading: "Username", value: this.value.userName}, {heading: "Password", value: this.value.password}];
        case 'sophos':
           return [{heading: "Client Id", value: this.value.clientId}, {heading: "Client Secret", value: this.value.clientSecret}];
        default:
          return '';
      }
    },
  },
};
</script>
<style scoped>
  .form-row {
    display: flex;
    gap: 1rem;
  }
  .form-group {
    flex: 1;
  }
  .modal-fields-label {
    display: flex;
    font-size: medium;
  }
</style>

