<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div 
      v-if="!showImportOption" 
      class="row"
    >
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <step-instruction-box
          step-number="1"
          step-title="Enter your Kaseya account"
        >
          <p class="not-as-small mt-3">
            <ol>
              <li>After sign up on kaseya you will receive login url through email e.g <i>'https://vsa_url/Authenticate/'</i></li>
              <li>Log in to Kaseya portal using an account with admin priviliges.</li>
              <li>
                Go to
                <strong> System </strong> &gt;
                <strong> User Security </strong> &gt;
                <strong> Users </strong> &gt;
                <strong> User </strong> &gt;
                <strong> Access Tokens </strong>
              </li>
              <li>Click <strong> New </strong> and create a token for your account.</li>
            </ol>
          </p>
        </step-instruction-box>

        <step-instruction-box
          step-number="2"
          step-title="Enter the following details"
        >
          <form
            class="row mt-4"
            @submit.prevent="submitKaseyaForm"
          >
            <p
              v-if="errors.length > 0"
              class="col-12 small text-danger"
            >
              <strong>Please correct the following error(s):</strong>
              <ul class="mb-0">
                <li
                  v-for="(error, index) in errors"
                  :key="`error-${index}`"
                >
                  {{ error }}
                </li>
              </ul>
            </p>
            <div class="form-group col-12">
              <label for="integratorUsername">
                Integrator Username
              </label>
              <input
                id="integratorUsername"
                v-model="kaseyaData.integratorUsername"
                class="input form-control"
                required
                type="text"
                @keydown.space.prevent
              >
            </div>
            <div class="form-group col-12">
              <label for="clientSecret">
                Client Secret
              </label>
              <input
                id="clientSecret"
                v-model="kaseyaData.clientSecret"
                class="input form-control"
                required
                type="text"
                @keydown.space.prevent
              >
            </div>
            <div class="form-group col-12">
              <label for="vsaUrl">
                VSA Url
              </label>
              <input
                id="vsaUrl"
                v-model="kaseyaData.vsaUrl"
                class="input form-control"
                required
                type="text"
                @keydown.space.prevent
              >
            </div>
            <div class="form-group col-12 mb-0 text-right">
              <button
                v-if="!loading"
                class="btn btn-sm btn-link text-secondary mr-2"
                @click.prevent="close"
              >
                <span>Cancel</span>
              </button>
              <button
                :disabled="loading || noData"
                class="btn btn-sm btn-primary px-3"
              >
                Next
              </button>
            </div>
          </form>
        </step-instruction-box>
      </div>
    </div>
    <div v-if="showImportOption">
      <import-option-selector
        :integration-name="'Kaseya'"
        @back-to-acc-details="backToAccDetail"
        @submit="startKaseyaInt"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import OnboardingTitle from 'components/shared/module_onboarding/onboarding_title.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import ImportOptionSelector from 'components/shared/module_onboarding/import_option_selector_modal.vue';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      OnboardingTitle,
      StepInstructionBox,
      ImportOptionSelector,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        imageSrc: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/kaseya.png',
        header: 'Sync your Kaseya account',
        subHeader: 'Sync your Kaseya account and pull in your assets.',
        loading: false,
        errors: [],
        kaseyaData: {
          integratorUsername: '',
          clientSecret: '',
          vsaUrl: '',
          importType: 'managed_asset',
        },
        showImportOption: false,
      };
    },
    computed: {
      noData() {
        return this.kaseyaData.integratorUsername === '' || this.kaseyaData.clientSecret === '' || this.kaseyaData.vsaUrl === '';
      },
    },
    methods: {
      onWorkspaceChange() {
        this.$parent.$parent.setCondenseModal(true);
      },
      backToAccDetail() {
        this.showImportOption = false;
        this.loading = false;
      },
      startKaseyaInt(importType) {
        this.kaseyaData.importType = importType;
        http
          .post('/integrations/kaseya/configs.json', { kaseya_config: this.kaseyaData })
          .then(() => {
            this.close();
            this.$store.dispatch('fetchCompanyIntegrations');
            this.emitSuccess("Retrieving data from Kaseya.");
          })
          .catch(error => {
            this.errors.push(error.response.data.message);
          })
          .finally(() => {
            this.loading = false;
          });
      },
      submitKaseyaForm() {
        this.loading = true;
        this.errors = [];
        let vsaUrl = '';
        if (this.kaseyaData.integratorUsername && this.kaseyaData.clientSecret && this.kaseyaData.vsaUrl) {
          vsaUrl = this.kaseyaData.vsaUrl;
          const [, , vsaUrlSplit] = this.kaseyaData.vsaUrl.split('/');
          this.kaseyaData.vsaUrl = vsaUrlSplit;
          if (!this.kaseyaData.vsaUrl || this.kaseyaData.vsaUrl && !this.kaseyaData.vsaUrl.includes('kaseya.net')) {
            this.loading = false;
            this.kaseyaData.vsaUrl = vsaUrl;
            return this.emitError('Please provide the valid VSA url.');
          }
          this.showImportOption = true;
        } else {
          if (this.kaseyaData.integratorUsername === '') {
            this.errors.push('Integrator Username is required.');
          }
          if (this.kaseyaData.clientSecret === '') {
            this.errors.push('Client Secret is required.');
          }
          if (this.kaseyaData.vsaUrl === '') {
            this.errors.push('Vsa Url is required.');
          }
          this.loading = false;
        }
        return false;
      },
      close() {
        this.$emit('close');
      },
    },
  };
</script>
