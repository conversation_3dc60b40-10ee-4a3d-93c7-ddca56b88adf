<template>
  <div class="summarized-sources mt-3">
    <template v-if="showSummarizedSources">
      <section
        v-for="sectionName in sectionNames"
        :key="sectionName"
        class="mb-3"
      >
        <div class="h6">
          {{ sectionName }} Details
        </div>
        <div class="box px-5">
          <template v-for="(attrData, index) in sectionData(sectionName)">
            <div
              v-if="showAttribute(attrData)"
              :key="index"
              class="card-width"
            >
              <div class="font-weight-bold mb-1">
                {{ titleizeKey(attrData.key) }}
              </div>
              <div class="not-as-small d-flex data-width">
                <img
                  v-if="valueExists(attrData.value) "
                  v-tooltip="friendlySourceName(attrData.source)"
                  class="source-icon mr-2"
                  :src="getSourceIcon(attrData.source)"
                >
                <span v-if="attrData.key == 'tags'">
                  {{ attrData.value }}
                </span>
                <span v-if="attrData.key == 'lastCheckInTime'">
                  {{ showDateTime(attrData.value) }}
                </span>
                <span v-else-if="isValueArray(attrData.value)">
                  {{ getArrayValue(attrData.value) }}
                </span>
                <span v-else-if="dateAttributes(attrData.key)">
                  {{ showDate(attrData.value) }}
                </span>
                <span v-else-if="attrData.key == 'status'">
                  {{ toTitle(attrData.value) }}
                </span>
                <span v-else-if="attrData.key == 'cost'">
                  <sup>$</sup>{{ attrData.value }}
                </span>
                <span v-else-if="attrData.key == 'systemUpTime'">
                  {{ toDaysAndHours(attrData.value) }}
                </span>
                <span v-else>{{ attrData.value }}</span>
              </div>
            </div>
          </template>
          <div
            v-if="dataNotPresent(sectionName)"
            class="missing-data text-muted h6"
          >
            No {{ sectionName.toLowerCase() }} details present for this asset.
          </div>
        </div>
      </section>
    </template>

    <div v-else>
      <span class="h6">Loading summary...</span>
      <sync-loader
        class="float-left mx-3 mt-1"
        color="#0d6efd"
        size="0.5rem"
        :loading="true"
      />
    </div>
  </div>
</template>

<script>
import sourcesHelper from "mixins/assets/sources_helper";
import assetImages from "mixins/asset_images";
import http from 'common/http';
import { mapGetters } from 'vuex';
import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
import permissionsHelper from 'mixins/permissions_helper';
import dates from "mixins/dates";

export default {
  components: {
    SyncLoader,
  },
  mixins: [sourcesHelper, assetImages, permissionsHelper, dates],
  props: {
    asset: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      sourcesSummary: null,
      isLoading: false,
      sectionNames: ['General'],
    };
  },
  computed: {
    ...mapGetters(['currentAsset']),
    hardwareAttrsArr() {
      return [
        'processor', 'processorName', 'processorCores', 'memory', 'harddrive', 'sku', 'ipAddress',
        'macAddresses', 'machineSerialNo', 'macAddressesInfo', 'secondaryMacAddresses',
      ];
    },
    showSummarizedSources() {
      return !this.isLoading && this.sourcesSummary;
    },
  },
  watch: {
    currentAsset() {
      if (this.currentAsset && this.currentAsset.id) {
        this.fetchSourcesSummary();
      }
    },
  },
  methods: {
    onWorkspaceChange() {
      this.fetchSourcesSummary();
    },
    showDateTime(date) {
      return moment(date.toString()).format('MMM DD, YYYY, hh:mm A');
    },
    valueExists(value) {
      if (typeof value === 'string' || Array.isArray(value)) {
        return value.length;
      } else if (typeof value === 'number') {
        return true;
      }
      if (value) {
        Object.keys(value).forEach((key) =>
        {
          if (hasOwnProperty.call(value, key)) {
            return true;
          }
          return false;
        });
      }
      return false;
    },
    showAttribute(attrData) {
      return attrData && this.valueExists(attrData.value) && this.filterAttribute(attrData.key);
    },
    fetchSourcesSummary() {
      if (this.currentAsset && this.currentAsset.id) {
        this.isLoading = true;
        http
          .get(`/managed_assets/${this.currentAsset.id}/sources_summary.json`)
          .then((res) => {
            this.sourcesSummary = res.data.data;
            this.isLoading = false;
          })
          .catch(() => {
            this.isLoading = false;
            this.emitError(`Sorry, there was an error loading asset summary.`);
          });
      }
    },
    sectionData(name) {
      if (name === 'General') {
        return this.sourcesSummary.filter((el) => !this.hardwareAttrsArr.includes(el.key));
      } 
      return [];
    },
    dataNotPresent(name) {
      return this.sectionData(name).filter(el => this.filterAttribute(el.key)).length === 0;
    },
    getArrayValue(val) {
      return Array.isArray(val) ? val.join(', ') : JSON.parse(val).join(', ');
    },
    isValueArray(val) {
      try {
        return Array.isArray(val) || Array.isArray(JSON.parse(val));
      } catch (e) {
        return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
  .summarized-sources {
    .box {
      align-items: unset;
      row-gap: 20px;
    }
    
    .source-icon {
      width: 19px;
      height: 19px;
    }

    .card-width {
      width: 50%;
    }

    .data-width {
      width: 75%;
    }

    .missing-data {
      margin-bottom: 0px;
      width: 100%;
      padding: 0.5rem;
      text-align: center;
    }

    @media (max-width: 600px) {
      .box {
        padding-left: 0.5rem !important;
        padding-right: 0.10rem !important;
      }

      .card-width {
        word-break: break-word;
        white-space: normal;
      }

      .data-width {
        width: 100% !important;
        word-break: break-word;
        white-space: normal;
      }
      
      .font-weight-bold {
        word-break: break-word;
        white-space: normal;
      }
    }
  }
</style>
