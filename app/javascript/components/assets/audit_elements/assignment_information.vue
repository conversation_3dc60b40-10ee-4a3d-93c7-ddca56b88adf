<template>
  <div
    v-if="auditedUsers.length > 0"
    class="row"
  >
    <template v-for="(change, item) in historyItem.auditedChanges">
      <div
        v-if="item != 'source'"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <div class="mobile-two-col">
          <div class="mobile-icon-col">
            <span class="action-icon mr-2">
              <i class="nulodgicon-person" />
            </span>

            <span v-tooltip="`Source: ${toTitle(source)}`">
              <img
                v-if="source"
                class="history-source-icon mb-2 mr-2"
                :src="getSourceIcon(source)"
              >
            </span>
          </div>
          <div class="mobile-content-col">
            <a
              :href="`/company/users/${historyItem.userId}`"
              target="_blank"
              class="text-secondary font-weight-bold"
            >{{ userName(historyItem.userId) }}</a>
            <span v-if="setItem(item) == 'used by' || setItem(item) == 'managed by'">
              {{ actionPerformed(item) }} the {{ setItem(item) }}
              <span class="float-right small text-muted">{{ actionDate }}</span>
              <div class="p-1 rounded ml-5 mt-2 col-9">
                <div class="row d-flex align-items-center">
                  <div
                    v-if="hasFromValue(item)"
                    class="bg-lighter col-5"
                  >
                    <user-info
                      show-default-view
                      :user="fromValue(item)"
                      :item="setItem(item)"
                    />
                  </div>
                  <i
                    v-if="hasFromValue(item) && hasToValue(item)"
                    class="col-1 nulodgicon-arrow-right-c h5 text-center"
                  />
                  <div
                    v-if="hasToValue(item)"
                    class="bg-lighter col-5"
                  >
                    <user-info
                      show-default-view
                      :user="hasToValue(item) ? toValue(item) : null"
                      :item="setItem(item)"
                    />
                  </div>
                </div>
              </div>
            </span>
          
            <span v-else>
              <span v-html="historyItemBody(item)" />
              <span class="float-right small text-muted">{{ actionDate }}</span>
            </span>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
  import audits from 'mixins/audits';
  import { mapGetters } from 'vuex';
  import assetImages from 'mixins/asset_images';
  import UserInfo from '../../shared/user_info.vue';

  export default {
    components: {
      UserInfo,
    },
    mixins: [audits, assetImages],
    props: ['historyItem'],
    computed: {
      ...mapGetters(['companyUsersAndGroups']),
      source() {
        return this.historyItem.auditedChanges.source;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $green;
  }
</style>
