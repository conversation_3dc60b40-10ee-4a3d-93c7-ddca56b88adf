<template>
  <div
    v-if="historyItem"
    class="row"
  >
    <template v-for="(change, item) in historyItem.auditedChanges">
      <div
        v-if="item != 'source'"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <div class="mobile-two-col">
          <div class="mobile-icon-col">
            <span class="action-icon mr-2">
              <i class="nulodgicon-dollar" />
            </span>

            <span v-tooltip="`Source: ${toTitle(source)}`">
              <img
                v-if="source"
                class="history-source-icon mb-2 mr-2"
                :src="getSourceIcon(source)"
              >
            </span>
          </div>
          <div class="mobile-content-col">
            <a
              :href="`/company/users/${historyItem.userId}`"
              target="_blank"
              class="text-secondary font-weight-bold"
            >
              {{ userName(historyItem.userId) }}
            </a>
            <span v-html="historyItemBody(item)" />
          </div>
        </div>
       
        <span class="float-right small text-muted">{{ actionDate }}</span>
      </div>
    </template>
  </div>
</template>


<script>
  import audits from 'mixins/audits';
  import { mapGetters } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';


  export default {
    mixins: [audits, permissionsHelper],
    props: ['historyItem'],
    computed: {
      ...mapGetters([
        'depreciations',
        'companyUsersAndGroups',
      ]),
    },
    methods: {
      onWorkspaceChange() {
        const source = this.historyItem.auditedChanges.updateBySourceAt;

        if (source !== undefined) {
          if (source[0] != null) {
            source[0] = moment(source[0]).format('MMM DD, YYYY, h:mm:ss a');
          }
          source[1] = moment(source[1]).format('MMM DD, YYYY, h:mm:ss a');
        }
      },
      showDollarSign(item) {
        return (item !== 'depreciationId') ? '<sup>$</sup>': '';
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $green;
  }
</style>
