<template>
  <div
    v-if="asset && assetTypes && companyLocations"
    class="row"
  >
    <template v-for="(change, item) in localAuditedChanges">
      <div
        v-if="isValidField(item)"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <div class="mobile-two-col">
          <div class="mobile-icon-col">
            <span class="action-icon mr-2">
              <i
                v-if="item == 'merged'"
                class="nulodgicon-link"
              />
              <i
                v-else
                class="nulodgicon-edit"
              />
            </span>

            <span v-tooltip="`Source: ${getSourceName}`">
              <img
                v-if="source"
                class="history-source-icon mb-2 mr-2"
                :src="getSourceIcon(source)"
              >
            </span>
          </div>
          <div class="mobile-content-col">
            <a
              target="_blank"
              class="text-secondary font-weight-bold"
              :href="`/company/users/${historyItem.userId}`"
            >{{ userName(historyItem.userId) }}</a>

            <span v-if="setItem(item) == 'location'">
              {{ actionPerformed(item) }} the {{ setItem(item) }}
              <span class="float-right small text-muted">{{ actionDate }}</span>
              <div class="p-1 rounded ml-5 mt-2 col-9">
                <div class="row d-flex align-items-center">
                  <div
                    v-if="hasFromValue(item)"
                    class="bg-lighter col-5"
                  >
                    <location-info :location="fromValue(item)" />
                  </div>
                  <i
                    v-if="hasFromValue(item) && hasToValue(item)"
                    class="col-1 nulodgicon-arrow-right-c h5 text-center"
                  />
                  <div
                    v-if="hasToValue(item)"
                    class="bg-lighter col-5"
                  >
                    <location-info :location="toValue(item)" />
                  </div>
                </div>
              </div>
            </span>

            <span v-else-if="setItem(item) == 'company asset type'">
              {{ hasFromValue(item) ? 'changed' : 'added' }} the asset type
              <span class="float-right small text-muted">{{ actionDate }}</span>
              <div class="col-8 p-0 ml-5 pl-4 mb-3">
                <div class="adjust-row-bottom row">
                  <div
                    v-if="change[0] && isAssetTypePresent(change[0])"
                    class="col-2 text-center"
                  >
                    <icon-badge
                      class="mx-auto mb-1"
                      :img-src="assetTypeSet(change[0])['image']"
                      :img-height-pixels="42"
                      :img-padding-pixels="6"
                      :background-color-class="`bg-lighter`"
                    />
                    <span class="mt-2"> {{ assetTypeSet(change[0])['type'] }} </span>
                  </div>
                  <div
                    v-else
                    class="col-2 text-center"
                    v-html="deletedItemBody(item)"
                  />

                  <i class="col-1 nulodgicon-arrow-right-c h5 mt-3 p-0 text-center" />
                  <div
                    v-if="change[1] && isAssetTypePresent(change[1])"
                    class="col-2 text-center"
                  >
                    <icon-badge
                      class="mx-auto mb-1"
                      :img-src="assetTypeSet(change[1])['image']"
                      :img-height-pixels="42"
                      :img-padding-pixels="6"
                      :background-color-class="`bg-lighter`"
                    />
                    <span class="mt-2"> {{ assetTypeSet(change[1])['type'] }} </span>
                  </div>
                  <div
                    v-else
                    class="col-2 text-center"
                    v-html="deletedItemBody(item)"
                  />
                </div>
              </div>
            </span>

            <span v-else-if="setItem(item) == 'archived'">
              <span
                v-if="toValue(item)"
                class="badge badge--archived align-middle mb-1"
              >
                <small
                  class="text-uppercase font-weight-semi-bold text-white"
                >Archived</small>
              </span>
              <span
                v-else
                class="badge badge--unarchived align-middle mb-1"
              >
                <small class="text-uppercase font-weight-semi-bold text-white">Unarchived</small>
              </span>
              this asset
              <span class="float-right small text-muted">{{ actionDate }}</span>
            </span>

            <span v-else-if="setItem(item) == 'product'">
              changed the product from <strong>{{ assetProductSet(change[0]) }}</strong> to <strong>{{ assetProductSet(change[1]) }}</strong>.
              <span class="float-right small text-muted">{{ actionDate }}</span>
            </span>

            <span v-else-if="setItem(item) == 'mac addresses'">
              {{ actionPerformed(item) }} the mac addresses
              <span class="float-right small text-muted">{{ actionDate }}</span>
              <div class="p-1 rounded ml-5 mt-2 col-9">
                <div class="row d-flex align-items-center">
                  <div
                    v-if="hasFromValue(item)"
                    class="bg-lighter col-5"
                  >
                    <p
                      v-for="address, index in fromValue(item)"
                      :key="index"
                      class="my-1 pl-2 py-0"
                    >{{ address }}</p>
                  </div>
                  <i
                    v-if="hasFromValue(item) && hasToValue(item)"
                    class="col-1 nulodgicon-arrow-right-c h5 text-center"
                  />
                  <div
                    v-if="hasToValue(item)"
                    class="bg-lighter col-5"
                  >
                    <p
                      v-for="address, index in toValue(item)"
                      :key="index"
                      class="my-1 pl-2 py-0"
                    >{{ address }}</p>
                  </div>
                </div>
              </div>
            </span>
            <span v-else-if="setItem(item) == 'merged'">
              has <strong>merged</strong> this asset into
              <span class="font-weight-semi-bold">
                <a
                  target="_blank"
                  :href="`/managed_assets/${combinedAssetId}`"
                >
                  {{ combinedAssetName }}.
                </a>
              </span>
              <span class="float-right small text-muted">{{ actionDate }}</span>
            </span>
            <span
              v-else
              class="asset-history"
            >
              <span 
                v-tooltip="{
                  content: historyItemBody(item),
                  boundariesElement: 'body',
                  show: (historyItemBody(item) && historyItemBody(item).length > 92 && hoveredIndex == historyItemBody(item)),
                  trigger: 'manual',
                }"
                class="text-truncate"
                @mouseover="hoveredIndex = historyItemBody(item)"
                @mouseleave="hoveredIndex = null" 
                v-html="historyItemBody(item)"
              />
              <span class="float-right small text-muted">{{ actionDate }}</span>
            </span>
          </div>

        </div>
      </div>
    </template>
  </div>
</template>

<script>
  import audits from 'mixins/audits';
  import IconBadge from 'components/shared/icon_badge.vue';
  import { mapGetters } from 'vuex';
  import _get from 'lodash/get';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import LocationInfo from '../../shared/location_info.vue';

  export default {
    components: {
      LocationInfo,
      IconBadge,
    },
    mixins: [audits, strings, permissionsHelper],
    props: ['historyItem', 'companyProducts', 'statusOptions', 'asset_images'],
    data() {
      return {
        asset: null,
        combinedAssetName: null,
        combinedAssetId: null,
        currentAssetType: null,
        actions: { create: 'added', destroy: 'removed' },
        hoveredIndex: null,
        localAuditedChanges: {},
      };
    },
    computed: {
      ...mapGetters([
        'assetTypes',
        'currentAsset',
        'companyUsersAndGroups',
      ]),
      getSourceName() {
        const probLocName = this.historyItem.auditedChanges.probeLocationName;
        return `${this.toTitle(this.source)} ${probLocName ? `(${probLocName})` : ''}`;
      },
    methods: {
      onWorkspaceChange() {
        if (this.currentAsset) {
          this.asset = this.currentAsset;
        }
        if (this.historyItem) {
          this.combinedAssetName = _get(this.historyItem, 'auditedChanges.merged.name', '');
          this.combinedAssetId = _get(this.historyItem, 'auditedChanges.merged.id', null);
          this.localAuditedChanges = JSON.parse(JSON.stringify(this.historyItem.auditedChanges));
        }
        if (this.localAuditedChanges.updateBySourceAt !== undefined) {
          if (this.localAuditedChanges.updateBySourceAt[0] != null) {
            this.localAuditedChanges.updateBySourceAt[0] = moment(this.localAuditedChanges.updateBySourceAt[0]).format('MMM DD, YYYY, h:mm:ss a');
          }

          this.localAuditedChanges.updateBySourceAt[1] = moment(this.localAuditedChanges.updateBySourceAt[1]).format('MMM DD, YYYY, h:mm:ss a');
        }
      },
      },
      isValidField(item) {
        return !(['source', 'probeLocationName'].includes(item));
      },
      assetTypeSet(assetTypeId) {
        if (!this.asset) {
          return null;
        } 
          let assetObject = {"type": null, "image": null};
          const localAsset = {...this.asset};
          const assetType = _get(this.assetTypes.filter(t => t.id === assetTypeId)[0], 'name', "");
          localAsset.assetType = this.toSnakecase(assetType);
          const assetTypeName = localAsset.assetType.charAt(0).toUpperCase() + localAsset.assetType.slice(1);
          assetObject = {"type": assetTypeName, "image": this.assetTypeImageSrc(localAsset)};
          return assetObject;
        
      },
      isAssetTypePresent(assetTypeId) {
        if (this.assetTypes) {
          const assetType = this.assetTypes.filter(type => type.id === assetTypeId);
          return assetType.length > 0;
        }
        return null;
      },
      assetProductSet(productId) {
        if (productId) {
          if (!this.asset) {
            return null;
          } 
            const assetProductName = this.companyProducts.filter(t => t.id === productId)[0].name;
            return assetProductName;
          
        } 
          return "nothing";
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $blue;
  }

  .high {
    color: $red;
  }

  .medium {
    color: $orange;
  }

  .low {
    color: $yellow;
  }
  .badge--archived {
    background-color: $color-accent;
    color: $white;
    bottom: 7px;
  }
  .badge--unarchived {
    background-color: $green;
    color: $white;
    bottom: 7px;
  }
  .set-image-height {
    min-height: 42px;
    margin-bottom: 5px;
  }
  .adjust-row-bottom {
    margin-bottom: -28px;
  }
  .asset-history {
    line-break: anywhere;
  }
  .action-col {
    flex: 0 0 4%;
    max-width: 4%;
    padding-left: 15px;
    padding-right: 15px;
  }
  .text-truncate {
    display: inline-block;
    max-width: 620px;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    @media($max: $medium) {
      max-width: 232px;
    }
  }
</style>
