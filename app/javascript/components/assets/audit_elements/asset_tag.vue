<template>
  <div class="row">
    <template v-for="(change, item) in filteredAuditedChanges">
      <div
        v-if="item != 'source'"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <div class="mobile-two-col">
          <div class="mobile-icon-col">
            <span
              :class="{'add-tag-color': historyItem.action == 'create', 'remove-tag-color': historyItem.action == 'destroy'}"
              class="action-icon mr-2"
            >
              <i class="nulodgicon-pricetag" />
            </span>

            <span v-tooltip="`Source: ${toTitle(source)}`">
              <img
                v-if="source"
                class="history-source-icon mb-2 mr-2"
                :src="getSourceIcon(source)"
              >
            </span>
          </div>
          <div class="mobile-content-col">
            <span>
              <a
                :href="`/company/users/${historyItem.userId}`"
                target="_blank"
                class="text-secondary font-weight-bold"
              >{{ userName(historyItem.userId) }}</a> {{ specifyAction }}
              <strong class="multiselect__tag small align-bottom px-2 ml-1">{{ change }}</strong>
            </span>
          </div>
        </div>
        <span class="float-right small text-muted">{{ actionDate }}</span>
      </div>
    </template>
  </div>
</template>

<script>
  import _ from 'lodash';
  import audits from 'mixins/audits';
  import { mapGetters } from 'vuex';

  export default {
    mixins: [audits],
    props: ['historyItem'],
    computed: {
      ...mapGetters(['companyUsersAndGroups']),
      specifyAction() {
        if (this.historyItem.action === "create") {
          return "added a new tag";
        } else if (this.historyItem.action === "destroy") {
          return "removed a tag";
        }
        return "";
      },
      filteredAuditedChanges() {
        return _.pick(this.historyItem.auditedChanges, "tag");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .remove-tag-color {
    background-color: $orange;
  }
  .add-tag-color {
    background-color: $gray-500;
  }
</style>
