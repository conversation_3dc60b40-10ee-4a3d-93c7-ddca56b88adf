<template>
  <div v-if="managedAsset">
    <div class="position-relative">
      <div class="row m-0 justify-content-between align-items-center flex-nowrap w-100 d-none d-md-flex">
        <div class="col-auto p-0">
          <span data-tc-view="asset type">
            <span :class="`assettype--${lowerCase(currentAsset.assetTypeName)}`">
              &bull;
            </span>
            {{ currentAsset.assetTypeName }}
          </span>
        </div>
        <div class="col-auto text-muted p-0 d-flex align-items-center">
          <router-link
            v-if="$route.params.id && $route.params.software_id"
            :to="`/${$route.params.id}/software`"
            class="position-relative align-top text-secondary mr-4"
            role="button"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span class="p--responsive">Back to <strong>all software</strong></span>
          </router-link>
          <router-link
            v-else
            :to="getPreviousRoute"
            class="position-relative align-top text-secondary mr-4"
            role="button"
            data-tc-link="back to all assets"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span>Back to <strong>all assets</strong></span>
          </router-link>
          <span
            v-if="isWrite && !mergedAsset"
            class="d-flex flex-wrap"
          >
            <a
              v-tooltip="'Clone'"
              class="btn btn-light btn-flat btn-icon-circle ml-1"
              href="#"
              role="button"
              @click.prevent="openClone"
            >
              <i class="genuicon-duplicate-contract base-font-size" />
            </a>
            <a
              v-if="currentAsset.archived"
              id="deleteAssetBtn"
              v-tooltip="'Delete'"
              href="#"
              role="button"
              data-tc-delete-asset-btn
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="permenantDeleteAssetCheck"
            >
              <i class="nulodgicon-trash-b" />
            </a>
            <a
              v-else
              id="archiveAssetBtn"
              v-tooltip="'Archive'"
              href="#"
              role="button"
              data-tc-archive-asset-btn
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="openArchiveModal"
            >
              <i class="nulodgicon-archive" />
            </a>
            <a
              v-if="currentAsset.archived"
              id="unArchiveAssetBtn"
              v-tooltip="'Unarchive'"
              href="#"
              role="button"
              data-tc-unarchive-asset-btn
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="unarchiveAsset"
            >
              <i class="nulodgicon-unarchive" />
            </a>
            <a
              v-else
              id="editAsset"
              v-tooltip="'Edit'"
              href="#"
              role="button"
              data-tc-edit-asset
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="goToEdit"
            >
              <i class="nulodgicon-edit mb-2" />
            </a>
            <a
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              href="#"
              role="button"
              @click.prevent="openQrCodeModal"
            >
              <i class="genuicon-qr-code mb-2" />
            </a>
          </span>
        </div>
      </div>
      <div class="row m-0 justify-content-between align-items-center w-100 d-flex d-md-none">
        <div class="col-auto p-0">
          <span data-tc-view="asset type">
            <span :class="`assettype--${lowerCase(currentAsset.assetTypeName)}`">
              &bull;
            </span>
            {{ currentAsset.assetTypeName }}
          </span>
        </div>
        <div class="col-auto text-muted p-0 d-flex align-items-center">
          <router-link
            v-if="$route.params.id && $route.params.software_id"
            :to="`/${$route.params.id}/software`"
            class="position-relative align-top text-secondary mr-4"
            role="button"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span class="p--responsive">Back to <strong>all software</strong></span>
          </router-link>
          <router-link
            v-else
            :to="getPreviousRoute"
            class="position-relative align-top text-secondary mr-4"
            role="button"
            data-tc-link="back to all assets"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span>Back to <strong>all assets</strong></span>
          </router-link>
        </div>
      </div>
      <div class="row m-0 w-100 d-flex d-md-none">
        <div class="col-12 text-muted p-0 d-flex align-items-center mt-2 justify-content-end text-right">
          <span
            v-if="isWrite && !mergedAsset"
            class="d-flex flex-wrap justify-content-end align-items-center w-100"
          >
            <a
              v-tooltip="'Clone'"
              class="btn btn-light btn-flat btn-icon-circle ml-1"
              href="#"
              role="button"
              @click.prevent="openClone"
            >
              <i class="genuicon-duplicate-contract base-font-size" />
            </a>
            <a
              v-if="currentAsset.archived"
              id="deleteAssetBtn"
              v-tooltip="'Delete'"
              href="#"
              role="button"
              data-tc-delete-asset-btn
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="permenantDeleteAssetCheck"
            >
              <i class="nulodgicon-trash-b" />
            </a>
            <a
              v-else
              id="archiveAssetBtn"
              v-tooltip="'Archive'"
              href="#"
              role="button"
              data-tc-archive-asset-btn
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="openArchiveModal"
            >
              <i class="nulodgicon-archive" />
            </a>
            <a
              v-if="currentAsset.archived"
              id="unArchiveAssetBtn"
              v-tooltip="'Unarchive'"
              href="#"
              role="button"
              data-tc-unarchive-asset-btn
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="unarchiveAsset"
            >
              <i class="nulodgicon-unarchive" />
            </a>
            <a
              v-else
              id="editAsset"
              v-tooltip="'Edit'"
              href="#"
              role="button"
              data-tc-edit-asset
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              @click.prevent="goToEdit"
            >
              <i class="nulodgicon-edit mb-2" />
            </a>
            <a
              class="ml-1 btn btn-light btn-flat btn-icon-circle"
              href="#"
              role="button"
              @click.prevent="openQrCodeModal"
            >
              <i class="genuicon-qr-code mb-2" />
            </a>
            <span class="ml-2 d-inline-block">
              <alert-date-modal :managed-asset="managedAsset" />
            </span>
          </span>
        </div>
      </div>
      <div class="row w-100">
        <div class="col-auto mt-1 d-flex align-items-center justify-content-center">
          <img
            v-tooltip="currentAsset.assetType"
            class="asset-image"
            :src="imageSrc"
          >
        </div>
        <div class="col mt-3">
          <div class="tag-base-class">
            <h1 class="d-inline-block mb-0 align-middle mr-2">
              <span
                class="font-weight-bold"
                data-tc-view-assets-name
              >
                {{ currentAsset.name || "No name present" }}
              </span>
            </h1>
            <span
              v-if="currentAsset.archived"
              class="badge badge--archived align-bottom mb-1"
            >
              <small
                class="text-uppercase font-weight-semi-bold text-white"
                data-tc-status-archived
              >
                Archived
              </small>
            </span>
            <span
              v-if="mergedAsset"
              class="badge badge--merged align-bottom mb-1"
              data-tc-merged-icon
            >
              <small
                class="text-uppercase font-weight-semi-bold text-white"
                data-tc-status-archived
              >
                Merged
              </small>
            </span>
            <tag
              v-for="tag in currentAsset.tags"
              :key="tag.id"
              class="mb-1 mr-1"
              :class="{'non-removeable': mergedAsset}"
              :data-tc-view-tags="tag.name"
            >
              {{ tag.name }}
              <i
                v-if="isWrite && !mergedAsset"
                aria-hidden="true"
                tabindex="1"
                class="multiselect__tag-icon"
                :data-tc-remove-icon="tag.name"
                @click="removeTagSelection(tag.name)"
              />
            </tag>
            <div class="add-tag-vendor-row">
              <a
                v-if="!mergedAsset"
                href="#"
                class="text-secondary small mb-1 d-inline-block align-bottom mx-1"
                data-tc-btn="add tag"
                @click.stop.prevent="openTagsModal"
              >
                + Add tag
              </a>
              <div
                v-if="currentAsset.vendor"
                class="vendor-inline"
              >
                <icon-link
                  :name="currentAsset.vendor.name"
                  :url="currentAsset.vendor.url"
                  :size="25"
                />
                <h6 class="d-inline-block mb-0 ms-1">
                  <a
                    :href="`/vendors/${currentAsset.vendor.id}`"
                    target="_blank"
                  >
                    {{ currentAsset.vendor.name }}
                  </a>
                </h6>
              </div>
            </div>
          </div>
          <header-action-bar
            :current-asset="currentAsset"
            class="mt-2"
          />
        </div>
      </div>

      <div class="not-as-small mt-3 d-flex align-items-center justify-content-between">
        <div>
          <span
            v-if="currentAsset.manufacturer"
            data-tc-view-manufacturer
          >
            {{ currentAsset.manufacturer }}
          </span>
          <span
            v-else
            class="text-muted"
          >No manufacturer provided</span>
          <span> / </span>
          <span
            v-if="currentAsset.model"
            data-tc-view-model
          >
            {{ currentAsset.model }}
          </span>
          <span
            v-else
            class="text-muted"
          >
            No model provided
          </span>
          <span> / </span>
          <span
            v-if="currentAsset.assetTag"
            v-tooltip="{
              content: currentAsset.assetTag,
              show: hoveredIndex == currentAsset.id && currentAsset.assetTag.length > 50,
              trigger: 'manual',
            }"
            data-tc-view-asset-tag
            @mouseover="hoveredIndex = currentAsset.id"
            @mouseleave="hoveredIndex = null"
          >
            <span>{{ truncate(currentAsset.assetTag, 50) }}</span>
          </span>
          <span
            v-else
            class="text-muted"
          >No asset tag</span>
          <span class="px-3 text-muted">&bull;</span>
          <span class="mt-1 small text-muted mb-0">
            <span v-if="currentAsset.sources">
              <span
                v-for="(source,index) in currentAsset.sources"
                :key="`source-${index}`"
                v-tooltip="sourceName(source)"
                :data-tc-source="sourceName(source)"
              >
                <img
                  class="source-icon mr-1"
                  :src="getSourceIcon(source)"
                >
              </span>
            </span>
            <span
              v-else
              v-tooltip="currentAsset.source"
            >
              <img
                class="source-icon mr-1"
                :src="getSourceIcon(currentAsset.source)"
                data-tc-merge-icon
              >
            </span>
          </span>
          <span class="px-3 text-muted">&bull;</span>
          <span v-if="currentAsset.impact">
            <ul
              id="impact-strength"
              v-tooltip.right="`${currentAsset.impact} impact`"
              class="mb-0 d-inline-block ml-2"
            >
              <li class="low"><div :class="{ 'active': impactLevel >= 1 }" /></li>
              <li class="medium"><div :class="{ 'active': impactLevel >= 2 }" /></li>
              <li class="high"><div :class="{ 'active': impactLevel >= 3 }" /></li>
            </ul>

          </span>
          <span
            v-else
            class="text-muted"
          >No impact provided
          </span>
          <v-popover
            v-if="isWrite && !mergedAsset"
            offset="5"
            class="d-inline"
            placement="bottom-center"
          >
            <i class="nulodgicon-help-circled text-muted font-weight-semi-bold clickable" />

            <template slot="popover">
              <p class="text-center mb-2">
                Update this asset impact
              </p>
              <impact-field
                :impact-value.sync="impactValue"
                :asset="currentAsset"
              />
              <hr>
              <div class="text-center">
                <button
                  v-close-popover
                  class="btn btn-primary btn-sm"
                  @click="submitNewImpact"
                >
                  Save
                </button>
              </div>
            </template>
          </v-popover>
          <span v-if="updatedAt">
            <span class="px-3 text-muted">&bull;</span>
            <span
              v-tooltip="`Updated at ${updatedAt}`"
              class="text-muted"
            >
              <i class="genuicon-calendar-o align-middle" />
              {{ updatedAt }}
            </span>
          </span>
        </div>
        <div class="d-none d-md-block">
          <alert-date-modal :managed-asset="managedAsset" />
        </div>
      </div>
    </div>
    <hr class="my-0 mb-3">

    <sweet-modal
      ref="qrCodeModal"
      v-sweet-esc
      title="Printable QR Code"
      class="qr-modal"
      width="40%"
    >
      <div>
        <h6>Customize the appearance of your QR codes by configuring the printing format in the
          <span
            class="btn-link cursor-pointer font-weight-bold"
            @click="routeToSettings"
          >
            settings.
          </span>
        </h6>
        <span >
          Note: For page settings to be applied, please download the QR code as a PDF.
        </span>
      </div>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click="printQRCode"
      >
        Download as PDF
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click="downloadQRCode"
      >
        Download as PNG
      </button>
    </sweet-modal>

    <sweet-modal
      ref="permenantDeleteAssetModal"
      v-sweet-esc
      title="Before you delete this asset..."
      class="qr-modal"
    >
      <template slot="default">
        <div>
          <warning-message
            v-if="currentAsset"
            ref="warningMessage"
            entity-type="ManagedAsset"
            :entity="currentAsset"
          />
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="cancelAssetPermenantRemove"
      >
        Cancel
      </button>
      <button
        id="modalDeleteAssetBtn"
        slot="button"
        data-tc-modal-delete-asset-btn
        class="btn btn-link text-danger"
        @click.stop="deleteAsset"
      >
        Delete
      </button>
    </sweet-modal>

    <asset-archive
      ref="assetArchive"
      :assets-arr="[currentAsset.id]"
    />

    <sweet-modal
      ref="tagsModal"
      v-sweet-esc
      title="Add a new tag"
      data-tc-title="tag"
      class="qr-modal"
    >
      <template slot="default">
        <form
          accept-charset="UTF-8"
          enctype="multipart/form-data"
        >
          <div class="form-group">
            <multi-select
              placeholder="Search for or type in a new tag"
              tag-placeholder="Add a tag"
              :allow-multiple="true"
              :multiple="true"
              :options="tagOptions"
              :taggable="true"
              :value="currentAsset.tagsArray"
              data-tc-tag-drop-down
              @tag="addNewTagSelection"
              @remove="removeTagSelection"
              @select="addNewTagSelection"
            />
          </div>
        </form>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        type="button"
        data-tc-btn="close tag modal"
        @click="closeTagsModal"
      >
        Close
      </button>
    </sweet-modal>
    <sweet-modal
      ref="cloneModal"
      v-sweet-esc
      title="Before we proceed..."
      class="qr-modal"
    >
      <template slot="default">
        <div class="text-center">
          <p class="clone-modal-heading">Basic asset info will be cloned. Choose the additional data you'd like to include:</p>
        </div>
        <div class="checkbox-group f-flex-col">
          <label 
            v-if="hasIntegratedSource"
            class="clickable d-flex clone-options"
            @click.stop.prevent="toggleOption('systemDetail')" 
          >
            <input 
              type="checkbox"
              class="d-none"
            >
            <i
              class="nulodgicon-checkmark checkbox mr-2 clone-checkbox"
              :class="{ 'checkbox-checked': systemDetail }"
            />
            System Details
          </label>
          <label 
            v-if="!hasIntegratedSource"
            class="clickable d-flex clone-options"
            @click.stop.prevent="toggleOption('customDetail')" 
          >
            <input 
              type="checkbox"
              class="d-none"
            >
            <i
              class="nulodgicon-checkmark checkbox mr-2 clone-checkbox"
              :class="{ 'checkbox-checked': customDetail }"
            />
            Custom Details
          </label>
          <label
            v-if="showCloudAssetAttrs"
            class="clickable d-flex clone-options"
            @click.stop.prevent="toggleOption('cloudDetail')"
          >
            <input
              type="checkbox"
              class="d-none"
            >
            <i
              class="nulodgicon-checkmark checkbox mr-2 clone-checkbox"
              :class="{ 'checkbox-checked': cloudDetail }"
            />
            Cloud Information
          </label>
          <label
            class="clickable d-flex clone-options"
            @click.stop.prevent="toggleOption('assetSoftwares')"
          >
            <input
              type="checkbox"
              class="d-none"
            >
            <i
              class="nulodgicon-checkmark checkbox mr-2 clone-checkbox"
              :class="{ 'checkbox-checked': assetSoftwares }"
            />
            All Software
          </label>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeClone"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click.stop="cloneAsset"
      >
        Yes, clone
      </button>
    </sweet-modal>
    <qr-code
      v-if="loadQR"
      ref="qrCodeComponent"
      class="qr-code"
      :class="{'hidden': !readyToDownloadQR}"
      :url="generateUrl"
      :asset-details="qrAssetDetails"
      :is-bulk-printing="false"
      :is-downloading-png="downloadAsPng"
      :file-name="currentAsset.name"
      :company-logo-url="companyLogoUrl"
    />
  </div>
</template>

<script>
  import { mapMutations, mapGetters } from 'vuex';
  import strings from 'mixins/string';
  import { SweetModal } from 'sweet-modal-vue';
  import assetImages from 'mixins/asset_images';
  import assetHelper from 'mixins/assets/asset_helper';
  import MultiSelect from 'vue-multiselect';
  import http from 'common/http';
  import permissionsHelper from 'mixins/permissions_helper';
  import _cloneDeep from 'lodash/cloneDeep';
  import IconLink from 'components/shared/icon_link';
  import ImpactField from './form_elements/impact_field.vue';
  import AssetArchive from './asset_archive.vue';
  import WarningMessage from '../shared/warning.vue';
  import Tag from "../shared/tag.vue";
  import alertDateModal from './alerts_date_modal.vue';
  import HeaderActionBar from './header_action_bar.vue';

  export default {
    components: {
      Tag,
      SweetModal,
      MultiSelect,
      ImpactField,
      AssetArchive,
      WarningMessage,
      QrCode: () => import('components/shared/qr_code.vue'),
      alertDateModal,
      IconLink,
      HeaderActionBar,
    },
    mixins: [strings, assetImages, permissionsHelper, assetHelper],
    data() {
      return {
        showStatusDropdown: false,
        statusOptions: [],
        hoveredIndex: null,
        impactValue: '',
        loadQR: false,
        downloadAsPng: false,
        readyToDownloadQR: false,
        managedAsset: {},
        assetSoftwares: false,
        customDetail: false,
        systemDetail: false,
        cloudDetail: false,
      };
    },
    computed: {
      ...mapGetters([
        'currentAsset',
        'assetTags',
        'associateTicketsHistory',
        'qrCodeSettings',
        'companyLogoUrl',
        'previousRoute',
        'integratedSources',
      ]),
      usedByAvatar() {
        return this.currentAsset.usedBy?.avatar;
      },
      tagOptions() {
        return this.assetTags.length > 0 ? this.assetTags.map((tag) => (tag.name)) : [];
      },
      imageSrc() {
        return this.currentAsset.imageMediumUrl || this.assetTypeImageSrc(this.currentAsset);
      },
      impactLevel() {
        if (this.currentAsset.impact === "Low") {
          return 1;
        } else if (this.currentAsset.impact === "Medium") {
          return 2;
        } else if (this.currentAsset.impact === "High") {
          return 3;
        }
        return 0;
      },
      currentStatus() {
        return this.currentAsset.status;
      },
      mergedAsset() {
        return this.currentAsset.merged;
      },
      hasAssociatedInfo() {
        return this.currentAsset.helpTickets.length
          || this.currentAsset.managedBy
          || this.currentAsset.usedBy
          || (this.currentAsset.universalLinks && this.currentAsset.universalLinks.length);
      },
      assetId() {
        return this.currentAsset.assetTag ? this.currentAsset.assetTag.toString() : this.currentAsset.id.toString();
      },
      qrAssetDetails() {
        const assetDetails = [];
        if (this.qrCodeSettings.printAssetTag) {
          assetDetails.push(this.assetId);
        }
        if (this.qrCodeSettings.printCompanyName) {
          assetDetails.push(this.qrCodeSettings.labelText ? this.qrCodeSettings.labelText : this.$companyName);
        }
        if (this.qrCodeSettings.printAssetName) {
          assetDetails.push(this.currentAsset.name);
        }
        return assetDetails;
      },
      generateUrl() {
        return `${window.location.origin}/managed_assets/${this.currentAsset.id}`;
      },
      getPreviousRoute() {
        return this.previousRoute || '/assets';
      },
      hasIntegratedSource() {
        return !!this.currentAsset.sources.find(source => this.integratedSources.includes(source));
      },
      showCloudAssetAttrs() {
        return ["aws", "azure", "google"].some(source => this.currentAsset.sources.includes(source));
      },
    },
    watch: {
      currentAsset() {
        this.managedAsset = _cloneDeep(this.currentAsset);
      },
    },
    methods: {
      ...mapMutations([
        'setCurrentAsset',
        'updateAsset',
      ]),
      onWorkspaceChange() {
        this.$store.dispatch('fetchCompanyAssetTags');
        this.$store.dispatch('fetchAssetPreferences');
        this.$store.dispatch('fetchCompanyLogoUrl');
        this.getStatusOptions();
      },
      openQrCodeModal() {
        this.$refs.qrCodeModal.open();
        this.loadQR = true;
      },
      closeQrCodeModal() {
        this.$refs.qrCodeModal.close();
      },
      openTagsModal() {
        if (!this.isWrite) {
          this.emitError(`Sorry, you do not have permissions to add new tags.`);
          return;
        }
        this.$refs.tagsModal.open();
      },
      closeTagsModal() {
        this.$refs.tagsModal.close();
      },
      removeTagSelection(tagName) {
        const tag = this.currentAsset.tags.find((t) => t.name === tagName);

        http
          .delete(`/managed_asset_tags/${tag.id}.json`)
          .then(() => {
            this.$store.dispatch("fetchAsset", this.currentAsset.id);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error deleting this tag (${error.response.data.message}).`);
          });
      },
      addNewTagSelection(newTag) {
        const tag = this.assetTags.find((t) => t.name === newTag);
        const params = { asset_id: this.currentAsset.id };
        if (tag) {
          params.company_tag_id = tag.id;
        } else {
          params.new_tag = newTag.toLowerCase();
        }

        http
          .post(`/managed_asset_tags.json`, params)
          .then(() => {
            this.$store.dispatch("fetchAsset", this.currentAsset.id);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error adding this tag (${error.response.data.message}).`);
          });
      },
      lowerCase(assetType) {
        if (!assetType) {
          return '';
        }
        return assetType.toLowerCase().split(' ').join('_');
      },
      goToEdit() {
        this.$router.push({ path: `/${this.currentAsset.id}/edit` });
      },
      toggleOption(option) {
        this[option] = !this[option];
      },
      openClone() {
        this.$refs.cloneModal.open();
      },
      closeClone() {
        this.$refs.cloneModal.close();
      },
      cloneAsset() {
        const parentAssetId = this.currentAsset.id;
        const parentAssetName = this.currentAsset.name;
        const cloneableObject = this.filteredAsset(this.currentAsset);
        this.$router.push({
          name: 'new',
          params: {
            parentAssetId,
            parentAssetName,
            assetSoftwares: this.assetSoftwares,
            cloudDetail: this.cloudDetail,
            customDetail: this.customDetail,
            systemDetail: this.systemDetail,
            cloneableObject,
          },
        });
      },
      filteredAsset(asset) {
        if (asset.alertDates) {
          asset.alertDates.map((date) => Object.assign(date, { id: "" }));
        }
        Object.assign(asset, { 
          id: null,
          name: `${asset.name} (Copy)`,
        });
        if (asset.costAttributes) {
          asset.costAttributes.id = null;
          asset.costAttributes.managedAssetId = null;
        }
        if (asset.assignmentInformationAttributes) {
          asset.assignmentInformationAttributes.id = null;
          asset.assignmentInformationAttributes.managedAssetId = null;
        }
        asset.source = null;
        return asset;
      },
      permenantDeleteAssetCheck() {
        this.$refs.warningMessage.open();
        this.$refs.permenantDeleteAssetModal.open();
      },
      openArchiveModal() {
        if (this.hasAssociatedInfo) {
          this.$refs.assetArchive.openAssociatedModal(this.currentAsset);
        } else {
          this.$refs.assetArchive.openSingleAssetArchiveModal();
        }
      },
      cancelAssetPermenantRemove() {
        this.$refs.permenantDeleteAssetModal.close();
      },
      unarchiveAsset() {
        this.$store.dispatch('unarchiveAsset', { asset: this.currentAsset, router: this.$router });
      },
      deleteAsset() {
        this.$store.dispatch('deleteAsset', { asset: this.currentAsset, router: this.$router });
        this.$refs.permenantDeleteAssetModal.close();
      },
      submitNewImpact() {
        this.currentAsset.impact = this.impactValue;
        http
          .put(`/managed_assets/${this.currentAsset.id}.json`, {managed_asset: this.currentAsset})
          .then(res => {
            this.emitSuccess("Asset impact was successfully updated");
            const updatedAsset = res.data.asset;
            this.setCurrentAsset(updatedAsset);
            this.updateAsset(updatedAsset);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error updating the asset impact. (${error.response.data.message}).`);
          });
      },
      getStatusOptions() {
        http
          .get('/managed_asset_statuses')
          .then(res => {
            this.statusOptions = res.data;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error getting asset status options. Please refresh the page and try again`);
          });
      },
      toggleStatusDropdown() {
        if (this.isWrite && !this.mergedAsset) {
          this.showStatusDropdown = !this.showStatusDropdown;
        } else {
          this.emitError(`Sorry, you do not have permissions to set status.`);
        }
      },
      changeStatus(status) {
        http
          .put(`/managed_asset_statuses/${this.currentAsset.id}.json?status_id=${status.id}`)
          .then(() => {
            this.showStatusDropdown = false;
            this.emitSuccess(`Successfully updated the asset status`);
            this.$store.dispatch("fetchAsset", this.currentAsset.id);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error updating this asset status ${error.response.data.message}`);
          });
      },
      sourceName(source) {
        const friendlyName = {
          'selfonboarding': 'Self-Onboarding',
          'merged_assets': 'Merged',
        };
        return friendlyName[source] ? friendlyName[source] : this.toTitle(source);
      },
      printQRCode() {
        this.readyToDownloadQR = true;
        requestAnimationFrame(() => {
          this.$refs.qrCodeComponent.printQRCode();
          this.loadQR = false;
          this.closeQrCodeModal();
        });
      },
      downloadQRCode() {
        this.readyToDownloadQR = true;
        this.downloadAsPng = true;
        requestAnimationFrame(() => {
          this.$refs.qrCodeComponent.downloadQRCode();
          this.loadQR = false;
          this.readyToDownloadQR = false;
          this.downloadAsPng = false;
          this.closeQrCodeModal();
        });
      },
      routeToSettings() {
        this.closeQrCodeModal();
        this.$router.push({ path: '/settings/qr_code' });
      },
      openAlertDatesModal() {
        this.$refs.alertModal.$refs.alertDateModal.open();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .badge--tag {
    background-color: green;
    color: white;
    text-transform: capitalize;
    bottom: 7px;
  }
  th {
    background-color:rgb(43, 40, 40);
    color:white;
  }
  .badge--archived {
    background-color: $color-accent;
    color: $white;
    padding: 4px 10px;
    border-radius: 5px;
  }

  #impact-strength {
    height: 20px;
    list-style: none;
    overflow: hidden;

    li {
      display: inline-block;
      width: 5px;
      float: left;
      height: 100%;
      margin-right: 1px;

      div {
        height: 100%;
        background: $themed-light;
      }
    }

    li.low {
      padding-top: 15px;
      .active {
        background: $yellow;
      }
    }

    li.medium {
      padding-top: 10px;
      .active {
        background: $orange;
      }
    }

    li.high {
      padding-top: 5px;
      .active {
        background: $red;
      }
    }
  }

  .dropdown-menu {
    display: block;
    left: auto;
    right: 0;
  }

  .source-icon {
    width: 25px;
    height: 25px;
  }

  .non-removeable {
    padding: 4px 10px 4px 10px !important;
  }

  .asset-image {
    display: block;
    max-width: 96px;
    max-height: 75px;
    width: auto;
    height: auto;
  }

  .checkbox-group {
    margin-left: 0.625rem;
    gap: 0.6875rem;
  }

  .checkbox-checked {
    background-color: $themed-link;
    border-color: $themed-link;
  }

  .clone-options {
    font-size: 1rem;
    font-weight: 500;
    max-width: 10rem;
  }

  .clone-modal-heading {
    font-size: 1rem;
  }

  .clone-checkbox {
    margin-top: 0.1875rem;
  }

  @media (min-width: 768px) {
    .tag-base-class {
      display: flex;
      align-items: end;
    }
  }

  @media (max-width: 600px) {
    .alert-col {
      display: none !important;
    }
    

    .btn-icon-circle {
      width: 32px !important;
      height: 32px !important;
      padding: 0 !important;
      font-size: 1rem !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .btn-icon-circle i {
      font-size: 1rem !important;
      margin: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
    
    .add-tag-vendor-row {
      display: block;
      gap: 0;
      margin-top: 0;
    }
    
    .vendor-inline {
      display: inline-block;
      margin-left: 0;
    }
  }
</style>
