<template>
  <div>
    <div 
      class="integration box d-flex flex-column justify-content-between"
      :class="isKaseya() ? 'box--with-hover' : ''"
      @click.stop="openAlertModal"
    >
      <div 
        class="d-flex pt-3 px-3 justify-content-between w-100"
        :class="disabled ? 'disabled-card' : ''"
      >
        <div class="d-flex pt-1">
          <div>
            <div class="logo">
              <img
                :src="integrationOption.imgPath"
                height="65"
              >
            </div>
          </div>
          <div>
            <h5 class="mb-2"> {{ integrationOption.name }} </h5>
            <div class="d-flex flex-wrap">
              <div
                v-tooltip="tooltipText"
                class="status" 
                :class ="statusClass()"
              >
                {{ mappedStatus }} 
              </div>
              <div 
                v-for="(alert, index) in alertsInfoList"
                :key="index"
                :class="alert.class"
                class="status"
              >
                {{ alert.message }}
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="d-flex align-items-center">
            <i
              class="genuicon-ellipsis-v btn btn-round btn-link text-dark my-n1 mr-n2.5 clickable"
              :class="{ 'disabled-toggle' : isIntegrationPending()}"
              @click.stop="toggleDropdown()"
            />
          </div>
          <div 
            v-if="isDropdownVisible"
          >
            <basic-dropdown
              class="dropdown-menu not-as-small dropdown-filter d-block"
              :show-dropdown="isDropdownVisible"
              :position="'bottom-right'"
              :right="((index + 1) % 3 === 0) ? 0 : -95"
              :max-width = "7.5"
              @on-blur="handleDropdownBlur()"
            >
              <div>
                <span
                  class="text-secondary action-dropdown-box"
                  @click.stop="resyncIntegration()"
                >
                  <i class="genuicon-refresh" />
                  <span class="pl-1">Resync</span>
                </span>
                <span
                  class="text-secondary action-dropdown-box"
                  @click.stop.prevent="openDeactivateModal()"
                >
                  <i class="genuicon-minus-circle" />
                  <span class="pl-1">Deactivate</span>
                </span>
                <span
                  class="text-secondary action-dropdown-box"
                  @click.stop.prevent="openDeleteModal()"
                >
                  <i class="nulodgicon-trash-b" />
                  <span class="pl-1">Delete</span>
                </span>
                <span
                  v-if="customizable"
                  class="text-secondary action-dropdown-box"
                  @click.stop.prevent="openSyncModal(true)"
                >
                  <i class="nulodgicon-edit" />
                  <span class="pl-1">Edit</span>
                </span>
                <span
                  class="text-secondary action-dropdown-box border-top"
                  @click.stop.prevent="showIntegrationDetails()"
                >
                  <i class="nulodgicon-eye" />
                  <span class="pl-1">Details</span>
                </span>
                <span
                  v-if="integrationOption.helpCenterLink"
                  class="text-secondary action-dropdown-box"
                  @click.stop="isDropdownVisible = false"
                >
                  <i class="nulodgicon-help-circled" />
                  <a 
                    class="pl-1 text-secondary"
                    :href="`${integrationOption.helpCenterLink}`"
                    target="_blank"
                    rel="noopener"
                  >
                    Help Center
                  </a>
                </span>
              </div>
            </basic-dropdown>
          </div>
        </div>
      </div>
      <div class="lower-border sync-text bg-lighter">
        {{ formmattedLastSyncedAt }}
        <material-toggle
          ref="syncToggle"
          v-tooltip="disabled ? 'Enable this integration' : 'Deactivate this integration'"
          class="pt-1"
          :class="{ 'disabled-toggle' : isIntegrationPending()}"
          :init-active="!disabled"
          @toggle-active-connector="toggleConnectorActivation"
        />
      </div>

      <add-item-modal
        ref="addItemModal"
        @close="resetActiveAddMethod"
      >
        <component
          :is="activeAddMethod"
          :customizing.sync="customizing"
          @close="closeSyncModal"
        />
      </add-item-modal>

      <sweet-modal
        ref="deleteIntegrationModal"
        v-sweet-esc
        class="mb-2"
        :title="`Delete ${integrationOption.searchableName} Integration`"
        :modal-theme="'wide'"
        @close="suppressAlertsModal"
      >
        <div>
          <h5 class="text-center my-3">
            Are you sure you want to delete this integration?
          </h5>
          <div class="d-flex align-items-center justify-content-end">
            <div 
              class="btn mx-2"
              @click.stop="closeDeleteModal()"
            >
              Cancel
            </div>
            <div 
              class="btn btn-primary"
              @click.stop="deleteIntegration()"
            >
              Confirm
            </div>
          </div>
        </div>
      </sweet-modal>

      <sweet-modal
        ref="deactivateIntegrationModal"
        v-sweet-esc
        class="mb-2"
        :title="`Deactivate ${integrationOption.searchableName} Integration`"
        :modal-theme="'wide'"
        @close="resetDeactivation"
      >
        <div>
          <h5 class="text-center my-3">
            Are you sure you want to deactivate this integration?
          </h5>
          <div class="d-flex align-items-center justify-content-end">
            <div 
              class="btn mx-2"
              @click.stop="closeDeactivateModal()"
            >
              Cancel
            </div>
            <div 
              class="btn btn-primary"
              @click.stop="deactivateIntegration()"
            >
              Confirm
            </div>
          </div>
        </div>
      </sweet-modal>

      <sweet-modal
        ref="detailsModal"
        v-sweet-esc
        :title="'Sync Account'"
        class="details-modal"
        width="75%"
        blocking
        @close="closeDetailsModal"
      >
        <details-modal
          v-if="showDetailsModal"
          ref="detailModalComponent"
          :header="`Integrate ${integrationOption.name}`"
          :sub-header="integrationOption.blurb"
          :image-src="integrationOption.imgPath"
          :intg-details="integrationDetail"
        />
      </sweet-modal>

      <assets-alert-modal
        v-if="isAlertModalVisible"
        :open-modal="isAlertModalVisible"
        :alerts-info="alertsInfo"
        :name="integrationOption.name"
        @close-modal="closeAlertModal"
        @resync="resyncIntegration"
      />
    </div>

    <div  
      v-if="isKaseya() && !disabled && alertsInfo && !isIntegrationPending()"
      class="d-flex flex-column align-items-end alert-container"
    >
      <connection-alert 
        v-for="(alert) in dismissableAlertsList"
        :key="alert.type"
        :alert-type="alert.type"
        :asset-count="alert.count"
        :source="integrationOption.name"
        :integration-id="integrationDetail.id"
      >
        {{ alert.message }}
      </connection-alert>
      <connection-alert
        v-if="showConnectionAlert()"
        :alert-type="'failed'"
        :source="integrationOption.name"
        :integration-id="integrationDetail.id"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import MomentTimezone from 'mixins/moment-timezone';
  import MaterialToggle from '../../shared/material_toggle.vue';
  import common from '../../shared/module_onboarding/common';
  import AddItemModal from '../../shared/module_onboarding/add_item_modal.vue';
  import * as addAssetModalPages from "../add_asset_modal_pages/index";
  import onboardingOptions from '../onboarding_options';
  import BasicDropdown from '../../shared/basic_dropdown.vue';
  import DetailsModal from '../add_asset_modal_pages/connectors/details/details_modal.vue';
  import assetsAlertModal from '../assets_alert_modal.vue';
  import connectionAlert from '../discovery_tools/connection_alert.vue';

  export default {
    components: {
      MaterialToggle,
      AddItemModal,
      ...addAssetModalPages,
      BasicDropdown,
      SweetModal,
      DetailsModal,
      assetsAlertModal,
      connectionAlert,
    },
    mixins: [MomentTimezone, common, onboardingOptions],
    props: {
      integrationDetail: {
        type: Object,
        required: true,
      },
      integrationOption: {
        type: Object,
        required: true,
      },
      disabled: {
        type: Boolean,
        required: true,
      },
      index: {
        type: Number,
        default: 0,
      },
      customizable: {
        type: Boolean,
        default: false,
      },
      selectedMenu: {
        type: String,
        default: null,
      },
    },
    data() {
      return {
        statusesMapping: {
          successful: "In-Sync",
          pending: "Syncing...",
          failed: "Sync Error",
          in_progress: "Syncing",
        },
        activeAddMethod: null,
        customizing: false,
        isDropdownVisible: false,
        syncToggleValue: !this.disabled,
        showDetailsModal: false,
        isAlertModalVisible: false,
        alertsInfo: null,
      };
    },
    computed: {
      ...mapGetters([
        'deletingIntegrations',
        'integrationsWithAlerts',
      ]),
      formmattedLastSyncedAt() {
        const timeToFormat = this.disabled ? this.integrationDetail.updatedAt : this.integrationDetail.lastSyncedAt;
        const timePassed = this.$moment.tz(timeToFormat, Vue.prototype.$timezone).fromNow();

        if (timePassed === "Invalid date") {
          return 'No last synced date present';
        }

        return this.disabled ? `Last active ${timePassed}` : `Last synced ${timePassed}`;
      },
      mappedStatus() {
        return this.disabled ? 'Disabled' : this.statusesMapping[this.integrationDetail.syncStatus];
      },
      tooltipText() {
        return this.integrationDetail.syncStatus === "failed" ? this.integrationDetail.errorMessage : '';
      },
      alertsInfoList() {
        if (!this.alertsInfo || this.disabled || this.isIntegrationPending()) {
          return [ { message: '', class: '' }];
        }
        
        const { notReporting, checkAssets, newAssets } = this.alertsInfo;
        const pluralize = (count, singular, plural) => count === 1 ? singular : plural;

        const notReportingMessage = `${notReporting} ${pluralize(notReporting, 'Asset', 'Assets')} Not Reporting`;
        const checkAssetsMessage = `${checkAssets} ${pluralize(checkAssets, 'Asset', 'Assets')} Out-of-Sync`;
        const newAssetsMessage = `${newAssets} New ${pluralize(newAssets, 'Asset', 'Assets')}`;

        return [
          { message: notReportingMessage, count: notReporting,  class: "not-reporting" },
          { message: newAssetsMessage, count: newAssets, class: "new-assets" },  
          { message: checkAssetsMessage, count: checkAssets, class: "check-assets" }, 
        ].filter(alert => alert.count > 0);
      },
      dismissableAlertsList() {
        const { notReporting, checkAssets, newAssets } = this.alertsInfo;
        const dismissedInfo = this.integrationDetail.alertInfo;

        return [
          { count: newAssets, type:"newAsset", dismissed: dismissedInfo.newAsset },
          { count: checkAssets, type:"outOfSync", dismissed: dismissedInfo.outOfSync },
          { count: notReporting, type:"notReporting", dismissed: dismissedInfo.notReporting },
        ].filter(alert => (alert.count > 0 && !alert.dismissed));
      },
    },
    mounted() {
      this.fetchAlertsInfo();
      window.addEventListener('scroll', this.handleScroll, true);
    },
    beforeUnmount() {
      window.removeEventListener('scroll', this.handleScroll, true);
    },
    methods: {
      ...mapMutations([
        'setDeletingIntegrations',
        'setIntegrationsWithAlerts',
      ]),
      statusClass() {
        return this.disabled ? 'disabled' : this.integrationDetail.syncStatus;
      },
      openSyncModal(customizing = false) {
        this.isDropdownVisible = false;
        this.customizing = customizing;
        this.activeAddMethod = this.integrationOption.openModalStr;
        this.$refs.addItemModal.open();
      },
      resetDeactivation() {
        this.suppressAlertsModal();
        if (!this.disabled) {
          this.$refs.syncToggle.enableToggle();
        }
      },
      resetActiveAddMethod() {
        if (this.disabled) {
          this.$refs.syncToggle.revertToggle();
        }
        this.activeAddMethod = null;
        this.customizing = false;
      },
      closeSyncModal() {
        this.$refs.addItemModal.close();
      },
      toggleConnectorActivation(isActive) {
        isActive ? this.openSyncModal() : this.openDeactivateModal();
      },
      toggleDropdown() {
        this.isDropdownVisible = this.disabled ? false : !this.isDropdownVisible;
      },
      handleDropdownBlur() {
        setTimeout(() => {
          this.isDropdownVisible = false;
        }, 10);
      },
      deactivateIntegration() {
        this.closeDeactivateModal();
        http
          .post(`/integrations/${this.integrationDetail.name}/configs/${this.integrationDetail.configId}/deactivate.json`)
          .then(() => {
            this.$store.dispatch('fetchCompanyIntegrations');
            this.setSyncingConnectors(this.syncingConnectors.filter(connector => connector.shortName !== this.integrationDetail.name));
            this.emitSuccess('Successfully deactivated integration');
            })
          .catch(error => {
            this.$refs.syncToggle.enableToggle();
            this.emitError(`Sorry, there was an error while deactivating this integration. ${error.response.data.message}`);
          });
      },
      resyncIntegration() { 
        this.isDropdownVisible = false;
        this.closeAlertModal();
        const params = { id: this.integrationDetail.configId, integration_name: this.integrationDetail.name, is_resyncing: true };
        http
          .post(`/integrations/${this.integrationDetail.name}/resync.json`, params)
          .then(() => {
            this.$store.dispatch('fetchCompanyIntegrations');
            this.setSyncingConnectors(this.syncingConnectors.filter(connector => connector.shortName !== this.integrationDetail.name));
            this.$emit('set-resync-pusher', this.integrationDetail.name);
            this.emitSuccess('Integration re-sync started');
            })
          .catch(error => {
            this.close();
            this.emitError(`Sorry, there was an error while resyncing this integration. ${error}`);
          });
      },
      deleteIntegration() {
        this.emitSuccess(`Started ${this.integrationOption.searchableName} deletion`);
        this.setNewDeletingIntegrations();
        this.closeDeleteModal();
        http
          .delete(`/integrations/${this.integrationDetail.name}/configs/${this.integrationDetail.configId}.json`)
          .then(() => {
            this.emitSuccess('Successfully deleted integration');
            this.$store.dispatch('fetchCompanyIntegrations');
            this.$store.dispatch('fetchDiscoveredAssetsSummary');
            this.setSyncingConnectors(this.syncingConnectors.filter(connector => connector.shortName !== this.integrationDetail.name));
            })
          .catch(error => {
            this.emitError(`Sorry, there was an error while deleting this integration. ${error.response.data.message}`);
          });
      },
      fetchAlertsInfo() {
        const params = {
          source: this.integrationOption.filterName ? this.integrationOption.filterName : this.integrationOption.searchableName,
        };

        http
          .get('/discovered_assets/discovered_asset_updates.json', { params}).then((res) => {
            this.alertsInfo = res.data;
          });
      },
      showIntegrationDetails() {
        this.isDropdownVisible = false;
	      this.showDetailsModal = true;
	      this.$nextTick(() => {
            this.$refs.detailModalComponent.getAssetCount();
            this.$refs.detailsModal.open();
          }
        );
	    },
      closeDetailsModal() {
        this.showDetailsModal = false;
        this.suppressAlertsModal();
      },
      getDisabled() {
        return !this.disabled;
      },
      isIntegrationPending() {
        return this.integrationDetail.syncStatus === 'pending';
      },
      openDeleteModal() {
        this.isDropdownVisible = false;
        this.$refs.deleteIntegrationModal.open();
      },
      closeDeleteModal() {
        this.$refs.deleteIntegrationModal.close();
      },
      openDeactivateModal() {
        this.isDropdownVisible = false;
        this.$refs.deactivateIntegrationModal.open();
      },
      closeDeactivateModal() {
        this.$refs.deactivateIntegrationModal.close();
      },
      isPlaidIntegration() {
        return this.integrationDetail.name === "plaid";
      },
      setNewDeletingIntegrations() {
        const deletions = this.deletingIntegrations;
        this.setDeletingIntegrations([...deletions, this.integrationOption.openModalStr]);
      },
      handleScroll() {
        if (this.isDropdownVisible) {
          this.isDropdownVisible = false;
        }
      },
      isKaseya() {
        return this.integrationOption.name === 'Kaseya' && this.integrationDetail.syncStatus !== "pending";
      },
      openAlertModal() {
        if (this.isKaseya() && !this.showDetailsModal) {
          this.isAlertModalVisible = true;
        }
      },
      closeAlertModal() {
        this.isAlertModalVisible = false;
      },
      showConnectionAlert() {
        return !this.integrationDetail.alertInfo.failed && this.integrationDetail.syncStatus === "failed";
      },
      hasAlerts() {
        return this.dismissableAlertsList.length > 0 || this.showConnectionAlert() ? this.integrationOption.searchableName : null;
      },
      suppressAlertsModal() {
        setTimeout(() => {
          this.isAlertModalVisible = false;
        }, 100);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .integration {
    align-items: start !important;
    padding: 0;
    overflow: hidden;
  }

  .integration {
    height: 11.5rem;

    @media (max-width: 1480px) {
      width: 20.5rem;
      height: 14.5rem;
    }
  }

  .lower-border {
    display: flex;
    width: 100%;
    padding: 0.75rem 1.2rem;
    padding-right: 0.25rem;
    border-top: #d3d3d3 0.1rem solid;
    align-items: center;
    justify-content: space-between;
  }

  :deep(.dropdown-menu) {
    z-index: 102;
    min-width: 6.5rem !important;
  }

  .dropdown-menu.not-as-small.dropdown-filter.d-block {
    min-width: 6.5rem !important;
  }

  .action-dropdown-box {
    display: block;
    padding: 0.25rem 0rem;
    cursor: pointer;
    color: #000;
    padding-left: 0.5rem;
    padding-right: 0.9rem;
  }

  .border-top {
    border-top: 0.05rem solid #a4a4a4;
  }

  .action-dropdown-box:hover {
    background-color: var(--themed-light);
    color: var(--themed-base);
  }

  .logo {
    min-width: 7rem;
    display: flex;
    justify-content: center;
    margin-top: 0.25rem;
  }

  .sync-text {
    color: $themed-secondary;
    font-size: smaller;
  }

  .not-reporting {
    background: var(--themed-orange-subtle);
    color: var(--themed-asset-not-reporting);
  }

  .check-assets {
    background: var(--themed-yellow-subtle);
    color: var(--themed-asset-check);
  }

  .new-assets {
    background: var(--themed-blue-subtle);
    color: var(--themed-blue-dark);
  }

  .successful {
    background-color: var(--themed-green-subtle);
    color: var(--themed-green-dark);
  }

  .pending {
    background-color: var(--themed-cyan-subtle);
    color: var(--themed-asset-syncing);
  }

  .failed {
    background-color: #e14143;
    color: #ffffff;
  }

  .in_progress {
    background-color: #dcf1f4;
    color: #1a8494;
  }

  .disabled {
    background-color: #e9e9e9;
    color: #6b6b6b;
  }

  .status {
    border-radius: 1rem;
    display: inline;
    width: fit-content;
    padding: 0rem 0.6rem;
    margin: 0.3rem;
    margin-left: 0rem;
    margin-top: 0rem;
    font-size: 0.9rem;
  }

  .disabled-card {
    opacity: 0.4;
  }

  .disabled-toggle {
    pointer-events: none;
    opacity: 0.6;
  }

  .details-modal {
    :deep {
      .sweet-title {
        background-color: $themed-dark-drawer-bg;
        color: white;
        font-size: 1rem;
        height: 3.5rem;

      }

      .sweet-modal {
        margin-top: 0.3rem;
      }

      .sweet-action-close {
        color: white !important;
      }

      .sweet-content-content {
        width: 100%;
      }
      .sweet-content {
        padding-top: 0.8rem;
        padding-bottom: 0rem;
      }
    }
  }

  .alert-container {
    z-index: 100;
    position: fixed;
    right: 2.5rem;
    bottom: 4.25rem;
  }
</style>
