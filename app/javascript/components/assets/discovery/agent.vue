<template>
  <div class="clearfix my-3 ml-4">
    <div class="mx-auto">
      <div class="row mt-3">
        <div class="col-xs-12 col-md-8">
          <h5 class="font-weight-normal mb-4">
            Discovery Agent
          </h5>
          <div class="text-secondary row mt-1">
            <div class="col-md-1 text-center">
              <img
                src="https://nulodgic-static-assets.s3.amazonaws.com/images/discovery-agent.svg"
                width="50"
                class="mt-2"
              >
            </div>
            <div class="col-md-10">
              Keep your device info up-to-date, hassle-free.  The Discovery Agent is a
              password-protected application that runs in the background and scans your
              device for up-to-date information to update the system.
              <span class="font-weight-bold">
                The Agent can be deployed on a stand-alone basis or it can be deployed 
                silently via GPO, Intune, or Command Prompt.
              </span>
            </div>
          </div>
          <div class="text-secondary row mt-4">
            <div class="col-md-12">
              <h6>
                Step 1: To get started, first download the Discovery Agent.
              </h6>
              <p>
                The Discovery Agent retrieves system information directly from the asset it was
                installed on.  The agent performs periodic scans to keep that asset information
                up to date.
              </p>
            </div>
          </div>
          <div class="row">
            <div
              v-if="isWrite"
              class="col-md-6"
            >
              <button
                v-if="!loadingUrl"
                class="btn btn-primary px-5"
                @click.prevent="downloadNow"
              >
                <i class="nulodgicon-cloud-download mr-2" /> Download for Windows
              </button>
              <span v-else>
                <sync-loader
                  class="ml-3 mt-1 mr-3 float-left"
                  color="#0d6efd"
                  size="0.5rem"
                /><h5 class="float-left">Prepping download</h5>
              </span>
              <a
                id="windows-download"
                download
              />
            </div>
            <div
              v-if="isWrite || isScoped"
              class="col-md-6"
            >
              <button
                :disabled="macUrl == ''"
                class="btn btn-primary px-5"
                @click.prevent="downloadMacNow"
              >
                <i class="nulodgicon-cloud-download mr-2" /> Download for OSX
              </button>

              <div 
                v-if="macUrl != ''"
                class="small text-muted login-code mt-1"
              >
                Click to copy the code
                <toggle-copy-item
                  v-if="loginCode.name"
                  class="ml-2"
                  :item="loginCode"
                  :show-custom-icon="true"
                />
              </div>
            </div>
          </div>

          <div class="text-secondary row mt-5">
            <div class="col-md-12">
              <h6>
                Step 2: Install and run the application.
              </h6>
              <p>
                When prompted with the login screen, simply enter your Genuity username
                and password or copy the login code from above to authenticate
                Periodically, the Discovery Agent will send the latest device
                data to Genuity.  You can sign out by clicking "Log out" and manually sync
                data by clicking "sync".
              </p>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-12">
              <h6>
                Step 3: Verify
              </h6>
              <p class="text-secondary">
                We highly recommend going through them to verify all the information is
                correct and adding in any missing fields.  Once that is done, you can
                treat yourself to a nice lunch.
              </p>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-12">
              <router-link
                to="/discovered_assets/ready_for_import"
                class="btn btn-primary px-5"
              >
                Take a look at Discovered Assets
              </router-link>
            </div>
          </div>
        </div>

        <div class="col-xs-12 col-md-4">
          <sub-menu />
          <div class="box box--natural-height p-5">
            <div class="box__inner">
              <h6>
                How does it work?
              </h6>
              <p>
                After installation, the Discovery Agent collects your machine's hardware and
                software info and periodically updates your Genuity account with your asset
                information.
              </p>
              <h6 class="mt-5">
                Helpful Tip:
              </h6>
              <p>
                This tool was built to help you collect data about the device it is installed
                on.  We highly recommend using this tool as well as the other tools we've
                provided to collect data on your company's other assets as well.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import SyncLoader from 'vue-spinner/src/SyncLoader';
import ToggleCopyItem from 'components/shared/toggle_copy_item.vue';
import permissionsHelper from "mixins/permissions_helper";
import SubMenu from './sub_menu.vue';

export default {
  components: {
    SyncLoader,
    SubMenu,
    ToggleCopyItem,
  },
  mixins: [permissionsHelper],
  data() {
    return {
      loadingUrl: false,
      url: '',
      macUrl: '',
      loginCode: {},
      ssoUser: false,
    };
  },
  methods: {
    onWorkspaceChange() {
      this.downloadWindowsAgent();
      this.getMacAgentUrl();
    },
    downloadWindowsAgent() {
      http
        .post('/download_windows_agents.json')
        .then((res) => {
          if (res.data.status) {
            this.url = res.data.url;
          }
        })
        .catch(() => {
          this.emitError('Sorry, Discovery Agent might not be present.');
        });
    },
    getMacAgentUrl() {
      http
        .get('/download_mac_agent.json')
        .then((res) => {
          if (res.data.status) {
            this.macUrl = res.data.url;
            this.loginCode = { name: res.data.loginCode};
            this.ssoUser = res.data.ssoUser;
          }
        })
        .catch(() => {
          this.emitError('Sorry, Mac Discovery Agent might not be present.');
        });
    },
    downloadNow() {
      this.loadingUrl = true;
      http
        .post('/download_windows_agents.json')
        .then((res) => {
          if (res.data.url) {
            this.loadingUrl = false;
            this.url = res.data.url;
            this.url = this.url.replace(/"/g, '');
            const downloadLink = document.getElementById('windows-download');
            downloadLink.setAttribute('href', this.url);
            downloadLink.click();
          } else {
            setTimeout(() => {
              this.downloadNow();
            }, 1000);
          }
        })
        .catch(() => {
          this.emitError('Sorry, Discovery Agent might not be present.');
        });
    },
    downloadMacNow() {
      const url = this.macUrl;
      window.open(url, '_blank');
    },
  },
};
</script>

<style scoped lang="scss">
.login-code :deep(.item-name) {
  display: none;
}

.login-code :deep(.ml-12) {
  margin-left: 80px;
}

</style>
