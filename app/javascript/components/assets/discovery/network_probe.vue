<template>
  <div class="clearfix my-3 ml-4">
    <div class="mx-auto">
      <div class="row mt-3">
        <div class="col-xs-12 col-md-8">
          <h5 class="font-weight-normal mb-4">
            Network Probe
          </h5>
          <div class="text-secondary row mt-1">
            <div class="col-md-1 text-center">
              <img
                src="https://nulodgic-static-assets.s3.amazonaws.com/images/network-probe.svg"
                width="50"
                class="mt-2"
              >
            </div>
            <div class="col-md-10">
              Easily scan your entire network for hardware assets to link to.  Simply using your
              company's network, the Network Probe is able to discover assets in your workplace
              and automatically add them to the system.
            </div>
          </div>
          <div class="text-secondary row mt-4">
            <div class="col-md-12">
              <h6>
                Step 1: To get started, first download the Network Probe.
              </h6>
              <p>
                The Probe uses WMI queries to fetch data from Windows, SSH
                for Linux and Mac, and SNMP to fetch devices like printers,
                routers, firewalls, and switches.
              </p>
            </div>
          </div>
          <div class="row">
            <div
              v-if="isWrite"
              class="col-md-6"
            >
              <button
                v-if="!loadingUrl"
                class="btn btn-primary px-5 mt-3"
                @click.prevent="downloadNow"
              >
                <i class="nulodgicon-cloud-download mr-2" /> Download Windows Probe
              </button>
              <div v-else>
                <sync-loader 
                  class="ml-3 mt-1 mr-3 float-left" 
                  color="#0d6efd" 
                  size="0.5rem"
                />
                <br>
                <h5 class="float-left">
                  Prepping download
                </h5><br>
              </div>
              <a
                id="windows-download"
                download
              />
            </div>
            <div class="col-md-6 text-info pt-4">
              A Mac probe is coming soon!
            </div>
          </div>

          <div class="text-secondary row mt-5">
            <div class="col-md-12">
              <h6>
                Step 2: Install and run the application.
              </h6>
              <p>
                Probe configuration has three basic steps:
                <ol class="probe-steps">
                  <li> Probe will detect the subnets from the network which have been enabled by default. You can edit or add new subnets. </li>
                  <li> Configure various protocols i.e WMI, SSH, SNMP. </li>
                  <li> Set the schedule of the probe to re-scan the assets connected to the network.</li>
                </ol>
                <span>
                  Once the configuration is completed, click "Next" to start scanning all the assets according to the configurations.
                </span>
              </p>
              <p class="mt-2">
                That's it!  These assets will show up in the Genuity system.
              </p>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-12">
              <h6>
                Step 3: Verify
              </h6>
              <p class="text-secondary">
                We highly recommend going through them to verify all the
                information is correct and adding in any missing fields.
                Once that is done, you can do a happy dance in your workspace
                (granted nobody is watching).
              </p>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-12">
              <router-link
                to="/discovered_assets/ready_for_import"
                class="btn btn-primary px-5"
              >
                Take a look at Discovered Assets
              </router-link>
            </div>
          </div>
        </div>

        <div class="col-xs-12 col-md-4">
          <sub-menu />
          <div class="box box--natural-height p-5">
            <div class="box__inner">
              <h6>
                How does it work?
              </h6>
              <p>
                Our Windows application can be installed on any workstation
                or server on your network running a Windows OS.  Once installed,
                it will scan any and every type of devices tied to the same
                network and sync that information with your account.
              </p>
              <h6 class="mt-5">
                Helpful Tip:
              </h6>
              <p>
                This tool was built to help you find assets that are a part
                of your network.  There are undoubtedly assets that aren't a
                part of your network that can be beneficial to add.  We highly
                recommend using this tool as well as the other tools we've
                provided to ensure the highest degree of accuracy.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import SyncLoader from 'vue-spinner/src/SyncLoader';
import permissionsHelper from "mixins/permissions_helper";
import SubMenu from './sub_menu.vue';

export default {
  components: {
    SyncLoader,
    SubMenu,
  },
  mixins: [permissionsHelper],
  data() {
    return {
      loadingUrl: false,
      url: '',
    };
  },
  methods: {
    onWorkspaceChange() {
      this.downloadWindowsAgent();
    },
    downloadWindowsAgent() {
      http
        .post('/download_windows_probes.json')
        .then((res) => {
          if (res.data.status) {
            this.url = res.data.url;
          }
        })
        .catch(() => {
          this.emitError('Sorry, Windows Agent might not be present.');
        });
    },
    downloadNow() {
      this.loadingUrl = true;
      http
        .post('/download_windows_probes.json')
        .then((res) => {
          if (res.data.url) {
            this.loadingUrl = false;
            this.url = res.data.url;
            this.url = this.url.replace(/"/g, '');
            const downloadLink = document.getElementById('windows-download');
            downloadLink.setAttribute('href', this.url);
            downloadLink.click();
          } else {
            setTimeout(() => {
              this.downloadNow();
            }, 1000);
          }
        })
        .catch(() => {
          this.emitError('Sorry, Windows Probe might not be present.');
        });
    },
    downloadMacTool() {
      const url = '/download_mac_tool';
      window.open(url, '_blank');
    },
  },
};
</script>
<style scoped>
  .probe-steps {
    list-style-type: disc;
    margin-bottom: 0;
  }
</style>
