<template>
  <div class="container-fluid py-4 px-3 px-md-4">
    <div v-if="currentAsset && Object.keys(currentAsset).length">
      <show-header />

      <div>
        <div
          v-if="currentAsset.helpTickets.length > 0"
          class="mt-1 alert alert-basic-warning p-3 clearfix d-inline-block position-relative"
          role="alert"
        >
          <span v-if="currentAsset.helpTickets.length > 1">
            <strong>There are help tickets for {{ currentAsset.name }}.</strong>
            <a
              class="alert-link ml-1"
              target="_blank"
              :href="`/help_tickets/all?managed_asset_id=${currentAsset.id}&managed_asset_name=${currentAsset.name}`"
              @click.stop=""
            >
              Click here
            </a>
            to view the tickets.
          </span>

          <span v-if="currentAsset.helpTickets.length == 1">
            <strong>There is a help ticket for {{ currentAsset.name }}.</strong>
            <a
              class="alert-link ml-1"
              target="_blank"
              :href="`/help_tickets/${currentAsset.helpTickets[0]}/general`"
              @click.stop=""
            >
              Click here
            </a>
            to view the ticket.
          </span>
        </div>
        <div
          v-if="showWarrantyAlert(currentAsset)"
          class="mt-3 alert alert-basic-warning p-3 clearfix d-inline-block position-relative"
          role="alert"
        >
          <strong>Your warranty has expired.</strong>
        </div>
        <div
          v-if="isManualWarrantyLookup"
          class="mt-3 alert alert-basic-info p-3 clearfix d-inline-block position-relative"
          role="alert"
        >
          <strong>You can look up your warranty expiration date.</strong>
          <a
            class="alert-link ml-1"
            :href="warrantyLookupUrl"
            target="_blank"
          >
            Click here
          </a>
          to view it.
        </div>
      </div>

      <div class="responsive-main-content row">
        <div class="col-12 col-md-auto mb-3 mb-md-0">
          <asset-sub-menu :asset="currentAsset" />
        </div>
        <div class="col-12 col-md overflow-auto pb-1">
          <router-view :key="$route.fullPath" />
        </div>
      </div>
    </div>

    <div
      v-else
      class="row"
    >
      <h5 class="ml-4 text-muted">
        Loading asset
      </h5>
      <sync-loader
        :loading="true"
        class="float-left ml-3"
        color="#0d6efd"
        size="0.5rem"
      />
    </div>
  </div>
</template>

<script>
import dates from 'mixins/dates';
import strings from 'mixins/string';
import assetWarranty from 'mixins/asset_warranty';
import { mapGetters } from 'vuex';
import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
import companyChannel from 'mixins/company_channel';
import permissionsHelper from 'mixins/permissions_helper';
import ShowHeader from './show_header.vue';
import AssetSubMenu from './asset_sub_menu.vue';

export default {
  components: {
    AssetSubMenu,
    ShowHeader,
    SyncLoader,
  },
  mixins: [dates, strings, assetWarranty, companyChannel, permissionsHelper],

  computed: {
    ...mapGetters(['currentAsset']),
    sourceName() {
      if (this.currentAsset.source === 'selfonboarding') {
        return 'Self Onboarding';
      }
      return this.toTitle(this.currentAsset.source);
    },
  },
  methods: {
    onWorkspaceChange() {
      this.$store.dispatch('fetchAsset', this.$route.params.id);
      this.$store.dispatch('fetchPermissions');
    },
    goToEdit() {
      this.$router.push({ path: `/${this.currentAsset.id}/edit` });
    },
    showWarrantyAlert(asset) {
      return (asset.warrantyExpiration != null && this.getDaysLeft(asset) < 1);
    },
    getDaysLeft(asset) {
      const start = moment();
      const end = this.toISOstring(asset.warrantyExpiration).add(1, 'd');
      const currentDatesDiff = end.diff(start, 'days');

      return currentDatesDiff;
    },
    goToStore() {
      const url = `/store_accesses/?redirect_to=store&asset_type=${
        this.currentAsset.name
      }&manufacturer=${
        this.currentAsset.manufacturer}`;
      window.open(url, '_blank');
    },
    archiveAsset() {
      this.$store.dispatch('archiveAsset', { asset: this.currentAsset, router: this.$router });
    },
    unarchiveAsset() {
      this.$store.dispatch('unarchiveAsset', { asset: this.currentAsset, router: this.$router });
    },
    deleteAsset() {
      this.$store.dispatch('deleteAsset', { asset: this.currentAsset, router: this.$router });
      this.$refs.permenantDeleteAssetModal.close();
    },
  },
};
</script>

<style scoped>
  .alert {
    word-break: break-word;
    font-size: 1rem;
    border-radius: 0.75rem;
  }

  .responsive-main-content {
    width: 100%;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  @media (min-width: 992px) {
    .responsive-main-content {
      flex-direction: row;
      align-items: flex-start;
    }

    .responsive-main-content > .col-md-auto {
      max-width: 260px;
      min-width: 180px;
      flex: 0 0 auto;
    }
    
    .responsive-main-content > .col-md {
      flex: 1 1 0%;
      min-width: 0;
    }
  }

  @media (max-width: 991.98px) {
    .responsive-main-content {
      flex-direction: column;
    }

    .responsive-main-content > .col-12 {
      max-width: 100%;
      flex: 0 0 100%;
    }
  }
</style>
