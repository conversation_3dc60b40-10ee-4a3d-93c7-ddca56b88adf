<template>
  <div>
    <h5 class="font-weight-normal mb-3 d-inline-block">Usage Status</h5>
    <span
      v-if="showEditBtn"
      v-tooltip="'Edit Details'"
      class="d-inline-block float-right btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm"
      data-tc-icon="edit usage status"
      @click.stop.prevent="openLocationAndUsageModal"
    >
      <i class="nulodgicon-edit clickable h6" />
    </span>
    <div class="box p-3 box--with-title pb-5">
      <div
        class="box__inner"
        data-tc-box="usage status"
      >
        <div class="subpage-menu">
          <a
            v-for="(tab, index) in tabs"
            :key="index"
            href="#"
            class="subpage-menu__item"
            :class="{ 'router-link-exact-active router-link-active': activeTab === tab.id }"
            @click.prevent="switchTab(tab.id)"
          >
            {{ tab.label }}
          </a>
        </div>
        <div
          v-if="activeTab === 'history'"
          class="d-flex flex-column flex-md-row justify-content-end align-items-stretch align-items-md-center mt-4"
        >
          <span class="small mb-2 mb-md-0 mr-0 mr-md-3 w-100 w-md-auto d-flex flex-column flex-md-row align-items-stretch align-items-md-center">
            <span class="text-muted align-text-bottom mt-2 mt-md-0 mr-0 mr-md-2 mb-1 mb-md-0">Results per page</span>
            <select
              id="filtersPerPage"
              class="form-control form-control-sm d-inline-block select-per-page-filter pt-0 w-100 w-md-auto"
              :input="perPage"
              :value="perPage"
              @input="changePageSize"
            >
              <option>10</option>
              <option>25</option>
              <option>50</option>
              <option>100</option>
            </select>
          </span>
          <span
            v-if="pageCount > 1"
            class="w-100 w-md-auto"
          >
            <paginate
              ref="paginate"
              :click-handler="pageSelected"
              :container-class="'pagination pagination-sm justify-content-center justify-content-md-start'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="indexPage"
            />
          </span>
        </div>

        <div class="mt-5">
          <div v-if="activeTab === 'currentStatus'">
            <div class="border-b-300 d-flex flex-column flex-md-row justify-content-between">
              <div class="d-flex flex-column flex-md-row">
                <div class="mr-4 mb-2">
                  <assets-status-dropdown
                    ref="statusDropdown"
                    :current-asset="managedAsset"
                    @change-status="changeStatus"
                  />
                </div>
                <span
                  v-if="currentStateSince"
                  class="d-flex align-items-end mb-2"
                >
                  <span class="text-secondary true-small">Since&nbsp;<span class="text-dark not-as-big font-weight-bold"> {{ currentStateSince }}</span></span>
                </span>
              </div>
              <div
                v-if="expectedCheckIn"
                class="d-flex align-items-end mb-2 mt-2 mt-md-0"
              >
                <span class="small text-muted">Expected Check-in&nbsp;<span class="text-dark base-font-size"> {{ expectedCheckIn }}</span></span>
              </div>
            </div>

            <div class="d-flex flex-wrap mt-3">
              <div class="d-flex flex-column horizontal-box">
                <div class="bg-lighter rounded px-2">
                  <user-info
                    v-if="managedAsset.usedBy"
                    :user="managedAsset.usedBy"
                    :show-contact-info="true"
                  />
                  <user-info-empty-state
                    v-else-if="isWrite && !mergedAsset"
                    @open-modal="openLocationAndUsageModal"
                  >
                    <span class="text-muted " />
                    <a href="#"> Set Teammate </a>
                  </user-info-empty-state>
                </div>
                <usage-key-value-element
                  title="Managed by"
                  class="mt-3"
                  :has-value-element="true"
                  :has-not-permission="!isWrite || mergedAsset"
                  @open-usage-modal="openLocationAndUsageModal"
                >
                  <template #valueElement>
                    <a
                      v-if="managedAsset.managedBy"
                      :href="managedByHref"
                      target="_blank"
                      class="text-secondary"
                      data-tc-view-managed-user
                    >
                      <div class="d-flex align-items-center">
                        <avatar
                          :size="25"
                          :src="managedAsset.managedBy.avatar"
                          :username="managedAsset.managedBy.fullName || managedAsset.managedBy.email"
                          class="logo-outline"
                        />
                        <div class="ml-2 font-weight-bold"> {{ managedAsset.managedBy.fullName }} </div>
                      </div>
                    </a>
                  </template>
                </usage-key-value-element>
              </div>
              <div class="mr-5 mt-3"/>
              <div class="d-flex flex-column horizontal-box">
                <div class="bg-lighter rounded px-2">
                  <location-info
                    v-if="managedAsset.location"
                    :location="managedAsset.location"
                    data-tc-view-assets-location
                  />
                  <cloud-location
                    v-else-if="managedAsset.cloudLocation"
                    :location="managedAsset.cloudLocation"
                  />
                  <location-info-empty-state
                    v-else-if="isWrite && !mergedAsset"
                    @open-modal="openLocationAndUsageModal"
                  >
                    <a
                      href="#"
                      data-tc-link-btn="location"
                    > Set Location
                    </a>
                  </location-info-empty-state>
                </div>
                <div class="mt-3">
                  <usage-key-value-element
                    v-if="isPhoneMobType"
                    title="Phone Number"
                    :value="formatNumber"
                    :has-not-permission="!isWrite || mergedAsset"
                    @open-usage-modal="openLocationAndUsageModal"
                  />
                  <usage-key-value-element
                    title="Department"
                    :department="selectedDepartment"
                    :has-not-permission="!isWrite || mergedAsset"
                    @open-usage-modal="openLocationAndUsageModal"
                  />
                </div>
              </div>
            </div>
          </div>

          <usage-history
            v-else
            :managed-asset="managedAsset"
            :usage-history="usageHistory"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapActions } from 'vuex';
  import { Avatar } from 'vue-avatar';
  import Paginate from 'vuejs-paginate';
  import dates from 'mixins/dates';
  import permissionsHelper from 'mixins/permissions_helper';
  import phoneFormatter from 'mixins/phone_number_formatter';
  import UserInfo from '../../shared/user_info.vue';
  import LocationInfo from '../../shared/location_info.vue';
  import CloudLocation from '../../shared/cloud_location.vue';
  import UserInfoEmptyState from '../../shared/user_info_empty_state.vue';
  import LocationInfoEmptyState from '../../shared/location_info_empty_state.vue';
  import AssetsStatusDropdown from '../../shared/assets_status_dropdown.vue';
  import UsageHistory from './usage_history.vue';
  import UsageKeyValueElement from './usage_key_value_element.vue';

  export default {
    components: {
      Avatar,
      Paginate,
      UserInfo,
      LocationInfo,
      CloudLocation,
      UserInfoEmptyState,
      LocationInfoEmptyState,
      AssetsStatusDropdown,
      UsageHistory,
      UsageKeyValueElement,
    },
    mixins: [permissionsHelper, phoneFormatter, dates],
    props: {
      managedAsset: {
        type: Object,
        default: () => {},
      },
      mergedAsset: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        activeTab: 'currentStatus',
        tabs: [
          {
            id: 'currentStatus',
            label: 'Current Status',
          },
          {
            id: 'history',
            label: 'History',
          },
        ],
        usageHistory: [],
        perPage: 10,
        pageCount: 1,
        indexPage: 0,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['departments']),
      showEditBtn() {
        return this.isWrite && !this.mergedAsset && this.activeTab === 'currentStatus';
      },
      managedByHref() {
        if (this.managedAsset.managedBy.type === "CompanyUser") {
          return `/company/users/${this.managedAsset.managedBy.rootId}`;
        }
        return `/company/groups/${this.managedAsset.managedBy.rootId}/edit`;
      },
      isPhoneMobType() {
        return ['Mobile', 'Phone'].includes(this.managedAsset.assetType);
      },
      formatNumber() {
        return this.processPhoneNumber(
          this.managedAsset.assignmentInformationAttributes.phoneNumber,
          this.managedAsset.assignmentInformationAttributes.phoneNumberCountryCode,
          this.managedAsset.assignmentInformationAttributes.phoneNumberCountryCodeNumber
        );
      },
      currentStateSince() {
        return this.showFullDate(this.managedAsset.assignmentInformationAttributes.currentStateSince);
      },
      expectedCheckIn() {
        return this.showFullDate(this.managedAsset.assignmentInformationAttributes.expectedCheckIn);
      },
      selectedDepartment() {
        const { departmentId } = this.managedAsset.assignmentInformationAttributes;
        if (!departmentId || !this.departments.length) return [];
        return this.departments.filter(dept => dept.id === departmentId);
      },
    },
    created() {
      this.fetchDepartments();
    },
    methods: {
      ...mapActions('GlobalStore', ['fetchDepartments']),
      openLocationAndUsageModal() {
        this.$refs.statusDropdown.onCloseDropdown();
        this.$emit('open-usage-modal');
      },
      changePageSize(e) {
        this.perPage = e.currentTarget.value;
        this.indexPage = 0;
        this.fetchUsageHistory();
      },
      pageSelected(p) {
        this.indexPage = p - 1;
        this.usageHistory = [];
        this.fetchUsageHistory();
      },
      fetchUsageHistory() {
        const params = {
          per_page: this.perPage,
          page: this.indexPage + 1,
        };

        http
          .get(`/managed_assets/${this.managedAsset.id}/usage_history.json`, { params })
          .then((response) => {
            this.usageHistory = response.data.usageHistory;
            this.pageCount = response.data.pageCount;
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error fetching usage history ${error.response.data.message}`);
          });
      },
      switchTab(tabId) {
        this.activeTab = tabId;
        if (tabId === 'history') {
          this.fetchUsageHistory();
        }
      },
      changeStatus(status) {
        http
          .put(`/managed_asset_statuses/${this.managedAsset.id}.json?status_id=${status.id}`)
          .then(() => {
            this.emitSuccess(`Successfully updated the asset status`);
            this.$store.dispatch("fetchAsset", this.managedAsset.id);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error updating this asset status ${error.response.data.message}`);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .horizontal-box {
    @media($min: $medium) {
      width: 20rem;
    }
    @media($max: 1097px) {
      width: 100%;
    }
  }
  .select-per-page-filter {
    font-size: 0.875rem;
    padding: 0;
    padding-left: 0.3125rem;
    position: relative;
    z-index: 2;
    min-height: 1.875rem;
    margin-left: auto;
    margin-right: auto;
    display: block;
  }
</style>
