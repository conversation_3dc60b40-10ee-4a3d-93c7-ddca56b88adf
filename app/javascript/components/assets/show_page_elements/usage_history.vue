<template>
  <div>
    <div class="table-responsive-mobile">
      <table
        v-if="usageHistory.length"
        class="table not-as-small mb-0"
      >
        <thead>
          <tr>
            <th class="font-weight-bold p-2">
              Managed By
            </th>
            <th class="font-weight-bold p-2">
              Used By
            </th>
            <th class="font-weight-bold p-2">
              Status
            </th>
            <th class="font-weight-bold p-2">
              Location
            </th>
            <th class="font-weight-bold p-2">
              Department
            </th>
            <th
              v-if="isPhoneMobType"
              class="font-weight-bold p-2"
            >
              Phone Number
            </th>
            <th class="font-weight-bold p-2">
              Assigned On
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(history, index) in usageHistory"
            :key="index"
            class="apps-rows"
          >
            <td class="px-sm-1 p-2">
              {{ history.managedBy }}
            </td>
            <td class="p-2">
              {{ history.usedBy }}
            </td>
            <td class="p-2 d-flex">
              <asset-status-pill
                :name="history.statusName"
                :icon="history.statusIcon"
              />
              <span
                v-if="history.currentStateSince"
                class="ml-2 align-self-center"
              >
                <span class="text-muted true-small">Since&nbsp;<span class="text-dark"> {{ showFullDate(history.currentStateSince) }}</span></span>
              </span>
            </td>
            <td class="p-2">
              {{ history.location }}
            </td>
            <td class="p-2">
              {{ history.department }}
            </td>
            <td
              v-if="isPhoneMobType"
              class="p-2"
            >
              {{ formatNumber(history) }}
            </td>
            <td class="p-2">
              {{ showFullDate(history.createdAt) }}
            </td>
          </tr>
        </tbody>
      </table>
      <div
        v-else
        class="text-secondary not-as-small text-center w-50 mx-auto"
      >
        No data available.
      </div>
    </div>
  </div>
</template>

<script>
  import dates from 'mixins/dates';
  import permissionsHelper from 'mixins/permissions_helper';
  import phoneFormatter from 'mixins/phone_number_formatter';
  import AssetStatusPill from 'components/shared/asset_status_pill.vue';

  export default {
    components: {
      AssetStatusPill,
    },
    mixins: [permissionsHelper, phoneFormatter, dates],
    props: {
      managedAsset: {
        type: Object,
        required: true,
      },
      usageHistory: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      isPhoneMobType() {
        return ['Mobile', 'Phone'].includes(this.managedAsset.assetType);
      },
    },
    methods: {
      formatNumber(history) {
        return this.processPhoneNumber(
          history.phoneNumber,
          history.phoneNumberCountryCode,
          history.phoneNumberCountryCodeNumber
        );
      },
    },
  };
</script>
