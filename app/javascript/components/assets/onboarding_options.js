export default {
  data() {
    return {
      onboardingOptions: [
        {
          name: "Discovery Agent",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/discovery_agent_mac.svg",
          subName: "Mac",
          blurb: "Automatically add your own Mac device info",
          openModalStr: "DiscoveryAgentMac",
          helpCenterLink: 'https://docs.gogenuity.com/docs/genuity-asset-mac-agents',
          thirdPartyConnector: false,
          searchableName: null,
          filterName: 'agent',
        },
        {
          name: "Discovery Agent",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/discovery_agent_windows.svg",
          subName: "Windows",
          blurb: "Automatically add your own Windows device info",
          openModalStr: "DiscoveryAgentWindows",
          helpCenterLink: null,
          thirdPartyConnector: false,
          searchableName: null,
          filterName: 'agent',
        },
        {
          name: "Network Probe",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/network-probe.svg",
          subName: null,
          blurb: "Securely scan your whole network and add multiple devices",
          openModalStr: "NetworkProbe",
          helpCenterLink: null,
          thirdPartyConnector: false,
          searchableName: null,
          filterName: 'probe',
        },
        {
          name: "Self-Onboarding",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/self-onboarding.svg",
          subName: "Windows + Mac",
          blurb: "Upload your own or your teammates' assets with ease.",
          openModalStr: "SelfOnboarding",
          helpCenterLink: null,
          thirdPartyConnector: false,
          searchableName: null,
          filterName: 'selfonboarding',
        },
        {
          name: "Asset Import",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/asset_import_icon.svg",
          subName: null,
          blurb: "Quickly import one or more devices with a spreadsheet",
          openModalStr: "ImportAssets",
          helpCenterLink: null,
          thirdPartyConnector: false,
          searchableName: null,
          filterName: null,
        },
        {
          name: "Manual Entry",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/asset_manual_entry_icon.svg",
          subName: null,
          blurb: "Enter your own device data in a form",
          openModalStr: "ManualEntry",
          helpCenterLink: null,
          thirdPartyConnector: false,
          searchableName: null,
          filterName: null,
        },
        {
          name: "Cisco Meraki",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/meraki.png",
          subName: null,
          blurb: "Sync your existing asset services with Cisco Meraki",
          openModalStr: "Meraki",
          helpCenterLink: "https://docs.gogenuity.com/docs/cisco-meraki",
          thirdPartyConnector: true,
          searchableName: "meraki",
        },
        {
          name: "Ubiquiti",
          imgPath: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/Ubiquiti_Networks_logo.png',
          subName: null,
          blurb: "Sync your existing asset services with Ubiquiti",
          openModalStr: "Ubiquiti",
          helpCenterLink: "https://docs.gogenuity.com/docs/ubiquiti-networks-unifi-controller",
          thirdPartyConnector: true,
          searchableName: "ubiquiti",
        },
        {
          name: "Microsoft Intune",
          imgPath: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/intune_logo.png',
          subName: null,
          blurb: "Sync your existing asset services with Microsoft Intune",
          openModalStr: "Intune",
          helpCenterLink: "https://docs.gogenuity.com/v1/docs/microsoft-intune-devices",
          thirdPartyConnector: true,
          searchableName: "ms_intune_assets",
          filterName: "ms_intune",
        },
        {
          name: "Azure AD Devices",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/azure_ad.png",
          subName: null,
          blurb: "Sync your existing asset services with Azure AD",
          openModalStr: "AzureAdAssets",
          helpCenterLink: "https://docs.gogenuity.com/v1/docs/microsoft-azure-devices",
          thirdPartyConnector: true,
          searchableName: "azure_ad_assets",
          filterName: "azure_ad_devices",
        },
        {
          name: "Kaseya",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/kaseya.png",
          subName: null,
          blurb: "Sync your existing asset services with Kaseya",
          openModalStr: "Kaseya",
          helpCenterLink: 'https://docs.gogenuity.com/docs/kaseya',
          thirdPartyConnector: true,
          searchableName: "kaseya",
        },
        {
          name: 'Kandji',
          imgPath: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/kandji.png',
          subName: null,
          blurb: 'Sync your existing asset services with Kandji',
          openModalStr: 'Kandji',
          helpCenterLink: 'https://docs.gogenuity.com/docs/kandji-asset-connector',
          thirdPartyConnector: true,
          searchableName: 'kandji',
        },
        {
          name: "Jamf Pro",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/jamf_pro.png",
          subName: null,
          blurb: "Sync your existing asset services with Jamf Pro",
          openModalStr: "JamfPro",
          helpCenterLink: "https://docs.gogenuity.com/v1/docs/jamf-pro",
          thirdPartyConnector: true,
          searchableName: "jamf_pro",
        },
        {
          name: "Google Workspace",
          imgPath: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/google-workspace-devices.png",
          subName: null,
          blurb: "Sync your existing asset services with Google Workspace",
          openModalStr: "GoogleWorkspace",
          helpCenterLink: "https://docs.gogenuity.com/docs/google-workspace-devices",
          thirdPartyConnector: true,
          searchableName: "google_workspace",
        },
        {
          name: "AWS",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/aws_logo.png",
          subName: null,
          blurb: "Sync your existing asset services with AWS",
          openModalStr: "Aws",
          helpCenterLink: "https://docs.gogenuity.com/docs/aws-asset-connector",
          thirdPartyConnector: true,
          searchableName: "aws_assets",
          filterName: "aws",
        },
        {
          name: "Azure",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/azure_logo.png",
          subName: null,
          blurb: "Sync your existing asset services with Azure",
          openModalStr: "Azure",
          helpCenterLink: "https://docs.gogenuity.com/docs/microsoft-azure",
          thirdPartyConnector: true,
          searchableName: "azure_assets",
          filterName: "azure",
        },
        {
          name: "GCP",
          imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/google_logo.png",
          subName: null,
          blurb: "Sync your existing asset services with GCP",
          openModalStr: "Gcp",
          helpCenterLink: null,
          thirdPartyConnector: true,
          searchableName: "google_assets",
          filterName: "google",
        },
        {
          name: "Mosyle",
          imgPath: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/logos/integrations-logos/mosyle.png",
          subName: null,
          blurb: "Sync your existing asset services with Mosyle",
          openModalStr: "Mosyle",
          helpCenterLink: "https://docs.gogenuity.com/docs/mosyle",
          thirdPartyConnector: true,
          searchableName: "mosyle",
        },
        {
          name: "Sophos",
          imgPath: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/logos/integrations-logos/sophos.png",
          subName: null,
          blurb: "Sync your existing asset services with Sophos",
          openModalStr: "Sophos",
          helpCenterLink: null,
          thirdPartyConnector: true,
          searchableName: "sophos",
        },
      ],
    };
  },
};
