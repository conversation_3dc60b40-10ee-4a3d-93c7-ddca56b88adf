<template>
  <div
    id="assetsSideNav"
    ref="assetMenu"
    class="d-flex d-md-block mb-4 mb-md-0 overflow-auto pr-2"
  >
    <div
      class="position-relative"
      :style="{ width: assetMenuWidth }"
    >
      <div class="position-static w-100">
        <div
          class="staff-menu"
          :class="{'position-fixed': stickyAssetMenu}"
          :style="{ 'top': stickyAssetMenu ? `${marginToScroll}px` : null }"
        >
          <a
            id="location-usage-menu"
            href="#"
            class="side-menu-item is-active d-flex align-items-center"
            @click.prevent="goToItem({section: 'location-usage'})"
          >
            <i class="icon genuicon-location mr-2 mt-1"/>
            Usage Status
          </a>
          <a
            v-if="showSystemDetails"
            id="summary-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'asset-summary'})"
          >
            <i class="icon genuicon-summary mr-2 mt-1"/>
            Summary
          </a>
          <a
            id="asset-hardware-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'asset-hardware'})"
          >
            <i class="icon genuicon-hardware mr-2 mt-1"/>
            System Details
          </a>
          <a
            id="software-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            data-tc-software-link
            @click.prevent="goToItem({section: 'asset-software'})"
          >
            <i class="icon genuicon-software mr-2 mt-1"/>
            Software
          </a>
          <a
            id="cost-depreciation-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'cost-depreciation'})"
          >
            <i class="icon genuicon-cost mr-2 mt-1"/>
            Cost Depreciation
          </a>
          <a
            id="warranty-acquisition-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'warranty-acquisition'})"
          >
            <i class="icon genuicon-warranty mr-2 mt-1"/>
            Warranty & Acquisition
          </a>
          <a
            id="asset-related-items-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'asset-related-items'})"
          >
            <i class="icon genuicon-cost mr-2 mt-1"/>
            Related Items
          </a>
          <a
            v-if="showCloudAssetAttrs"
            id="cloud-information-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'cloud-information'})"
          >
            <i class="icon genuicon-cloud mr-2 mt-1"/>
            Cloud Information
          </a>
          <a
            id="asset-details-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            data-tc-details-link
            @click.prevent="goToItem({section: 'asset-details'})"
          >
            <i class="icon genuicon-hardware mr-2 mt-1"/>
            {{ hasIntegratedSource ? 'Details' : 'Custom Details' }}
          </a>
          <a
            id="asset-notes-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            data-tc-notes-link
            @click.prevent="goToItem({section: 'asset-notes'})"
          >
            <i class="icon genuicon-notes mr-2 mt-1"/>
            Notes
          </a>
          <a
            id="asset-alerts-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            data-tc-details-link
            @click.prevent="goToItem({section: 'asset-alerts'})"
          >
            <i class="icon genuicon-alerts mr-2 mt-1"/>
            Alerts
            <span
              v-if="isAlertRecipient() && dangerAlertsCount > 0"
              class="btn-icon-circle btn-icon-circle-xxs icon--line-height alert-badge bg-danger-light ml-2 text-center"
            >
              <span
                class="font-weight-bold small"
                style="position: relative;top: -1px;"
              >
                {{ dangerAlertsCount }}
              </span>
            </span>
          </a>
          <a
            id="asset-sources-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            data-tc-asset-sources
            @click.prevent="goToItem({section: 'asset-sources'})"
          >
            <i class="icon genuicon-sources mr-2 mt-1"/>
            Sources
          </a>
          <a
            v-if="vendorsPermission"
            id="asset-history-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'asset-history'})"
          >
            <i class="icon genuicon-history mr-2 mt-1"/>
            History
          </a>
          <a
            v-if="asset.managedAssetAttachments.length"
            id="asset-attachment-menu"
            href="#"
            class="side-menu-item d-flex align-items-center"
            @click.prevent="goToItem({section: 'asset-attachments'})"
          >
            <i class="icon genuicon-history mr-2 mt-1"/>
            Attachments
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';
  import assetHelper from 'mixins/assets/asset_helper';
  import assetAlerts from 'mixins/assets/asset_alerts';

  const MARGIN_TO_SCROLL = 60;
  export default {
    mixins: [ permissionsHelper, assetHelper, assetAlerts ],
    props: ['asset'],
    data() {
      return {
        activeTab: 'summary',
        stickyAssetMenu: false,
        marginToScroll: MARGIN_TO_SCROLL,
        assetMenuWidth: null,
        assetMenuMapping: [
          {
            section: 'location-usage',
            menu: 'location-usage-menu',
          },
          {
            section: 'asset-summary',
            menu: 'summary-menu',
          },
          {
            section: 'asset-hardware',
            menu: 'asset-hardware-menu',
          },
          {
            section: 'asset-software',
            menu: 'software-menu',
          },
          {
            section: 'cost-depreciation',
            menu: 'cost-depreciation-menu',
          },
          {
            section: 'warranty-acquisition',
            menu: 'warranty-acquisition-menu',
          },
          {
            section: 'asset-related-items',
            menu: 'asset-related-items-menu',
          },
          {
            section: 'cloud-information',
            menu: 'cloud-information-menu',
          },
          {
            section: 'asset-details',
            menu: 'asset-details-menu',
          },
          {
            section: 'asset-notes',
            menu: 'asset-notes-menu',
          },
          {
            section: 'asset-alerts',
            menu: 'asset-alerts-menu',
          },
          {
            section: 'asset-sources',
            menu: 'asset-sources-menu',
          },
          {
            section: 'asset-history',
            menu: 'asset-history-menu',
          },
          {
            section: 'asset-attachments',
            menu: 'asset-attachment-menu',
          },
        ],
      };
    },
    computed: {
      ...mapGetters(['integratedSources']),

      vendorsPermission() {
        return this.isSpecificPermission('read', "Vendor")
          || this.isSpecificPermission('write', "Vendor");
      },
    },
    beforeDestroy() {
      $SiteScroll
        .getScrollElement()
        .removeEventListener("scroll", this.checkSticky, true);
      $SiteScroll
        .getScrollElement()
        .removeEventListener("scroll", this.checkMenuActive, true);
    },
    methods: {
      onWorkspaceChange() {
        $SiteScroll
          .getScrollElement()
          .addEventListener("scroll", this.checkSticky, true);
        $SiteScroll
          .getScrollElement()
          .addEventListener("scroll", this.checkMenuActive, true);
      },
      isCurrentPath(path, includeChildren) {
        if (includeChildren) {
          return this.$route.path.indexOf(path) >= 0;
        }
        return this.$route.path === path || this.$route.path === `${path}/`;
      },
      inactiveAllMenuItems() {
        this.assetMenuMapping.forEach(({ menu }) => {
          const itemMenu = document.getElementById(menu);
          if (itemMenu) {
            itemMenu.classList.remove('is-active');
          }
        });
      },
      checkMenuActive() {
        this.assetMenuMapping.forEach(({ menu, section }) => {
          const itemMenu = document.getElementById(menu);
          const itemSection = document.getElementById(section);

          if (itemMenu && itemSection) {
            const position = itemSection.getBoundingClientRect().y;
            if (position < MARGIN_TO_SCROLL) {
              this.inactiveAllMenuItems();
              itemMenu.classList.add('is-active');
              if (section === "asset-alerts") {
                this.updateSeenAlert();
              }
            }
          }
        });
      },
      goToItem({ section }) {
        if (section) {
          document.getElementById(section)?.scrollIntoView({ block: "start", behavior: "smooth" });
          if (section === "asset-alerts") {
            this.updateSeenAlert();
          }
        }
      },
      checkSticky() {
        const { assetMenu } = this.$refs;
        if (assetMenu) {
          if (!this.stickyAssetMenu && this.$refs.assetMenu && !this.assetMenuWidth) {
            this.assetMenuWidth = `${this.$refs.assetMenu.offsetWidth}px`;
          }
          const distanceToTop = assetMenu.getBoundingClientRect().top - MARGIN_TO_SCROLL;
          this.stickyAssetMenu = distanceToTop < 0;

          const alertsSection = document.getElementById('asset-alerts');
          if (alertsSection) {
            const alertsSectionBottom = alertsSection.getBoundingClientRect().bottom;
            if (alertsSectionBottom < 0) {
              this.updateSeenAlert();
            }
          }
        }
      },
    },
  };
</script>

<style scoped>
  @media (max-width: 991.98px) {
    #assetsSideNav {
      flex-direction: row !important;
      overflow-x: auto !important;
      overflow-y: hidden !important;
      white-space: nowrap;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
      border-bottom: 1px solid #e5e5e5;
    }
    
    .staff-menu {
      display: flex !important;
      flex-direction: row !important;
      min-width: unset;
      width: max-content;
      position: static !important;
      top: unset !important;
    }

    .side-menu-item {
      display: inline-flex !important;
      align-items: center;
      margin-right: 0.5rem;
      margin-bottom: 0 !important;
      font-size: 0.98rem;
      padding: 0.6rem 0.7rem;
      min-width: 120px;
      border-radius: 0.5rem;
      white-space: nowrap;
    }
  }
</style>
