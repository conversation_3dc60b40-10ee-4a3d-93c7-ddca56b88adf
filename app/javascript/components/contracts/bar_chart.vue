<script>
  import { Bar, mixins } from 'vue-chartjs';
  import { themedLegendColor } from 'common/chart_colors';
  import permissionsHelper from 'mixins/permissions_helper';
  import chartYAxisHandler from '../../mixins/chart_y_axis_handler';

  export default {
    extends: Bar,
    mixins: [mixins.reactiveProp, chartYAxisHandler, permissionsHelper],
    props: { customOpts: Object },
    watch: {
      chartData: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            this.$nextTick(() => {
              this.renderCustomChart();
            });
          }
        },
      },
    },
    mounted() {
      this.renderCustomChart();
    },
    methods: {
      renderCustomChart() {
        const yAxisTicks = this.getYAxisTicks(this.chartData, this.toCurrencyNoDecimals);
        this.renderChart(this.chartData, {
          scales: {
            xAxes: [{
              barPercentage: 0.4,
              gridLines: { display: true },
              barThickness: 24,
              maxBarThickness: 24,
              ticks: {
                fontColor: themedLegendColor,
                callback(value) {
                  if (typeof value === 'string' && value.length > 15) {
                    return `${value.substring(0, 15)}...`;
                  }
                  return value;
                },
              },
            }],
            yAxes: [{
              gridLines: {
                display: true,
                fontColor: themedLegendColor,
              },
              ticks: yAxisTicks,
              scaleLabel: {
                display: true,
                labelString: "Total value (USD)",
                fontSize: 14,
                fontColor: themedLegendColor,
              },
            }],
          },
          legend: false,
          tooltips: {
            callbacks: {
              label: (tooltipItem, data) => {
                const tipIndex = !data.datasets[0].data[tooltipItem.index] ? tooltipItem.index - 1 : tooltipItem.index;
                const value = (data.datasets[0].data[tipIndex]).toFixed(2);
                return ` Total value: $${this.formatToUnits(value, 2)}`;
              },
              title(data) {
                const tooltip = data[0].label;
                if (tooltip.length > 40) {
                  return tooltip.match(/[\s\S]{1,40}\S*/g);
                }
                return tooltip;
              },
            },
          },
        });
      },
    },
  };
</script>
