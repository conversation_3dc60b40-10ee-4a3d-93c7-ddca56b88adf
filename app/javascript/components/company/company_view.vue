<template>
  <div class="col">
    <div class="container">
      <people-sub-menu v-if="isPeoplePath" />
      <div
        v-if="isAllUsersPath"
        v-tooltip="alertMessage()"
      >
        <notification-button
          label="People Changes"
          :show-notification-badge="showNotificationBadge"
          @show-discovered="openAlertModal"
        />
      </div>
    </div>
    <people-alert-modal
      v-if="isAlertModalVisible"
      :open-modal="isAlertModalVisible"
      @close-modal="closeAlertModal"
    />
    <router-view />
  </div>
</template>

<script>
  import http from 'common/http';
  import permissionsHelper from 'mixins/permissions_helper';
  import PeopleSubMenu from './people_sub_menu.vue';
  import PeopleAlertModal from './people_alert_modal.vue';
  import NotificationButton from './notification_button.vue';

  export default {
    components: {
      PeopleSubMenu,
      PeopleAlertModal,
      NotificationButton,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        showNotificationBadge: false,
        isAlertModalVisible: false,
        usersCount: null,
      };
    },
    computed: {
      isPeoplePath() {
        const peoplePaths = [
          '/users',
          '/users/sync_and_download',
          '/users/ready_for_import',
          '/users/imported',
          '/users/ignored',
        ];

        return peoplePaths.includes(this.$route.path);
      },
      isAllUsersPath() {
        if (this.$route.path === '/users') {
          this.getUserCount();
          return true;
        }
        return false;
      },
    },
    methods: {
      openAlertModal() {
        this.isAlertModalVisible = true;
        this.showNotificationBadge = false;
      },
      closeAlertModal() {
        this.isAlertModalVisible = false;
      },
      getUserCount() {
        http
          .get('/discovered_users_summary/count')
          .then((res) => {
            this.usersCount = res.data.userCount;
            if (this.usersCount && this.usersCount !== Number(localStorage.getItem('discUsersCount'))) {
              localStorage.setItem('discUsersCount', this.usersCount);
              this.showNotificationBadge = true;
            }
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error loading the Discoverd Users count (${e.message})`);
          });
      },
      alertMessage() {
        if (this.usersCount) {
          return `${this.usersCount} New Users found`;
        }
        return 'No New User available';
      },
    },
  };
</script>

<style scoped lang="scss">
.container {
  display: flex !important;
  justify-content: space-between;
  padding: 0px;
}
</style>
