<template>
  <div>
    <div v-if="usersInAutomatedTask">
      <span>
        <p v-if="bulkArchive">
          Some teammates are being referred to the following automated tasks. You can remove them by clicking on the automated tasks' links below:
        </p>
        <p v-else-if="userArchive">
          <strong>{{ currentUser.user.fullName }}</strong> is being referred to the following automated tasks. You can remove them by clicking on the automated tasks' links below:
        </p>
        <p v-else>
          <strong>{{ currentUser.user.fullName }}</strong> is being referred to the following automated tasks:
        </p>
        <div>
          <div
            v-for="(userTasks, userId) in groupedTasks"
            :key="userId"
          >
            <span v-if="bulkArchive">
              {{ getUserFullName(userId) }}'s automated task(s):
            </span>
            <span
              v-for="(automatedTask, index) in userTasks"
              :key="index"
            >
              <span>Helpdesk Automated Task -</span>
              <a
                :href="`/help_tickets/automated_tasks/${automatedTask.id}/edit`"
                class="font-weight-bold"
                target="_blank"
              >
                #{{ automatedTask.id }}
              </a>
              <span v-if="index < userTasks.length - 1">, </span>
            </span>
          </div>
          <span
            v-for="(automatedTask, index) in userAssetsTasks"
            :key="index"
          >
            <span>Assets Automated Task -</span>
            <a
              :href="`/managed_assets/automated_tasks`"
              class="font-weight-bold"
              target="_blank"
            >
              #{{ automatedTask.automatedTasks.id }}
            </a>
            <span v-if="index < userAssetsTasks.length - 1">, </span>
          </span>
        </div>
      </span>
    </div>
    <div v-if="userArchive || bulkArchive">
      <p class="mt-3">
        Are you sure you want to archive these people? You can unarchive them any time later.
      </p>
    </div>
  </div>
</template>

<script>
import companyModule from 'mixins/company_module';

export default {
  mixins: [companyModule],
  props: {
    dependentAutomatedTasks: {
      type: Array,
      default: () => [],
    },
    userAssetsTasks: {
      type: Array,
      default: () => [],
    },
    companyUsers: {
      type: Array,
      default: () => [],
    },
    currentUser: {
      type: Object,
      default: () => {},
    },
    bulkArchive: {
      type: Boolean,
      default: false,
    },
    userArchive: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    usersInAutomatedTask() {
      return this.dependentAutomatedTasks?.length || this.userAssetsTasks?.length;
    },
    groupedTasks() {
      return this.groupTasksByUser(this.dependentAutomatedTasks);
    },
  },
  methods: {
    groupTasksByUser(tasks) {
      const groupedTasks = {};
      tasks.forEach(task => {
        const userIds = task.user;
        userIds.forEach(userId => {
          if (!groupedTasks[userId]) {
            groupedTasks[userId] = [];
          }
          groupedTasks[userId].push(task.automatedTasks);
        });
      });
      return groupedTasks;
    },
    getUserFullName(userId) {
      const user = this.companyUsers.find(companyUser => String(companyUser.id) === userId);
      return user ? user.user.fullName : '';
    },
  },
};
</script>
