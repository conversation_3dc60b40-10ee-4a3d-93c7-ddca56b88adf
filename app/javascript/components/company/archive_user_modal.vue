<template>
  <sweet-modal
    v-if="companyUser"
    ref="archiveCompanyUserModal"
    v-sweet-esc
    title="Before you archive this teammate..."
  >
    <template slot="default">
      <div v-if="!companyUserRelatedData">
        Please wait while we process your request...
      </div>
      <div v-if="usersInAutomatedTask && !isCaution">
        <task-list
          :current-user="companyUser"
          :dependent-automated-tasks="dependentTasks"
          :user-assets-tasks="assetDependentTasks"
          :user-archive="true"
        />
      </div>
      <div v-else-if="companyUser.user">
        <div v-if="isCaution">
          <div class="text-center mb-2">
            <img
              src="https://nulodgic-static-assets.s3.amazonaws.com/images/caution.svg"
              style="width:4rem;"
            >
          </div>
          <div
            v-if="companyUserRelatedData.usedAssetsLength > 0"
            class="d-inline-block my-3 w-100"
          >
            <reassignable
              attr="used_by_contributor_id"
              type="managed_assets"
              :delete-modal-company-user="companyUser"
              :reassignable-items="companyUserRelatedData.usedAssets"
              @reassigned-data="reassignOrRemoveData"
            />
          </div>
          <div
            v-if="companyUserRelatedData.managedAssetsLength > 0"
            class="d-inline-block my-3 w-100"
          >
            <reassignable
              attr="managed_by_contributor_id"
              type="managed_assets"
              :delete-modal-company-user="companyUser"
              :reassignable-items="companyUserRelatedData.managedAssets"
              @reassigned-data="reassignOrRemoveData"
            />
          </div>
          <div
            v-if="companyUserRelatedData.contractsLength > 0"
            class="d-inline-block my-3 w-100"
          >
            <reassignable
              attr="company_user_cont_id"
              type="contracts"
              :delete-modal-company-user="companyUser"
              :reassignable-items="companyUserRelatedData.contracts"
              @reassigned-data="reassignOrRemoveData"
            />
          </div>
          <div
            v-if="companyUserRelatedData.createdTicketsLength > 0"
            class="d-inline-block my-3 w-100"
          >
            <reassignable
              attr="created_by"
              type="help_tickets"
              :delete-modal-company-user="companyUser"
              :reassignable-items="companyUserRelatedData.createdTickets"
              :tickets-length="ticketLength"
              @reassigned-data="reassignOrRemoveData"
            />
          </div>
          <div
            v-if="companyUserRelatedData.assignedTicketsLength > 0"
            class="d-inline-block my-3 w-100"
          >
            <reassignable
              attr="assigned_to"
              type="help_tickets"
              :delete-modal-company-user="companyUser"
              :reassignable-items="companyUserRelatedData.assignedTickets"
              :tickets-length="ticketLength"
              @reassigned-data="reassignOrRemoveData"
            />
          </div>
          <hr>
          <div v-if="usersInAutomatedTask && isCaution">
            <task-list
              :current-user="companyUser"
              :dependent-automated-tasks="dependentTasks"
              :user-assets-tasks="assetDependentTasks"
              :user-archive="true"
            />
          </div>
        </div>
        <div v-if="this.isRelatedItemsPresent">
          <p>
            Please note that this teammate is present in the <span class="font-weight-bold">Related Items</span> field of below modules and will be removed when archived.
          </p>
          <ul>
            <li 
              v-for="(type, index) in this.relatedItemsLinkableTypes" 
              :key="index"
            >
              <universal-link-icon
                class="mr-2"
                :item="{ linkableType: type === 'People / Group' ? 'Group' : type }"
                :size="16"
              />
              <span class="item-name d-inline-block align-bottom">
                {{ type }}
              </span>
            </li>
          </ul>
        </div>
        <vendor-lists
          v-if="subscriptionConditions || hasVendorManagementSubscription"
          :selected-company-user-vendors="selectedCompanyUserVendors" 
        />
        <div v-if="!usersInAutomatedTask">
          <p>
            Are you sure you want to archive this teammate? You can unarchive them any time later.
          </p>
        </div>
      </div>
    </template>
    <template v-if="companyUser.user">
      <button
        slot="button"
        class="btn btn-link text-secondary"
        :data-tc-cancel-archive="companyUser.user.fullName"
        @click.stop="cancelArchive"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-link text-danger"
        :class="['btn', 'btn-link', 'text-danger', { 'disabled': !companyUserRelatedData }]"
        :disabled="isSubmitting"
        :data-tc-archive-user-modal-btn="companyUser.user.fullName"
        @click.stop="archiveCompanyUser"
      >
        {{ archiveStateIndicator }}
      </button>
    </template>
  </sweet-modal>
</template>

<script>
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import companyModule from 'mixins/company_module';
  import Reassignable from './reassignable.vue';
  import TaskList from "./task_list.vue";
  import UniversalLinkIcon from '../shared/universal_link/universal_link_icon.vue';
  import VendorLists from './vendor_lists.vue';

  export default {
    components: {
      SweetModal,
      Reassignable,
      TaskList,
      UniversalLinkIcon,
      VendorLists,
    },
    mixins: [permissionsHelper, companyModule],
    props: ['userId', 'companyUser', 'companyUserRelatedData', 'subscriptionConditions', 'hasVendorManagementSubscription'],
    data() {
      return {
        isSubmitting: false,
        itemsToReassign: [],
      };
    },
    computed: {
      isRelatedItemsPresent() {
        return this.companyUser.universalLinks.length;
      },
      relatedItemsLinkableTypes() {
        if (!this.companyUser.universalLinks) {
          return [];
        }
        const linkableTypes = new Set();
        this.companyUser.universalLinks.forEach(link => {
          if (link.target?.linkableType) {
            let type = link.target.linkableType;
            if (type === "Group" || type === "CompanyUser") {
              type = "People / Group";
            }
            linkableTypes.add(type);
          }
        });
        return Array.from(linkableTypes);
      },
      ticketLength() {
        if (this.companyUserRelatedData) {
          return {
            'created_by': this.companyUserRelatedData.createdTicketsLength,
            'assigned_to': this.companyUserRelatedData.assignedTicketsLength,
          };
        }
        return null;
      },
      isCaution() {
        if (
          !this.companyUserRelatedData ||
          !this.companyUserRelatedData.contracts ||
          !this.companyUserRelatedData.usedAssets ||
          !this.companyUserRelatedData.managedAssets ||
          !this.companyUserRelatedData.createdTickets ||
          !this.companyUserRelatedData.assignedTickets
        ) {
          return false;
        }
        return (
          this.companyUserRelatedData.contractsLength > 0 ||
          this.companyUserRelatedData.usedAssetsLength > 0 ||
          this.companyUserRelatedData.managedAssetsLength > 0 ||
          this.companyUserRelatedData.createdTicketsLength > 0 ||
          this.companyUserRelatedData.assignedTicketsLength > 0
        );
      },
      archiveStateIndicator() {
        return this.isSubmitting ? 'Archiving' : 'Archive';
      },
    },
    methods: {
      onWorkspaceChange() {
        if (this.openModal) {
          this.$refs.archiveCompanyUserModal.open();
        } else {
          this.cancelArchive();
        }
      },
      open() {
        this.checkUserDependencies();
        this.fetchUserVendors();
        this.$refs.archiveCompanyUserModal.open();
      },
      close() {
        this.$refs.archiveCompanyUserModal.close();
      },
      cancelArchive() {
        if (this.$refs.archiveCompanyUserModal) {
          this.$refs.archiveCompanyUserModal.close();
        }
      },
      archiveCompanyUser() {
        this.isSubmitting = true;
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          this.performReassignmentAndArchival();
        }
      },
      performReassignmentAndArchival() {
        if (this.itemsToReassign && this.itemsToReassign.length > 0) {
          http
            .post('/entity_reassignments.json', { entity_reassignments: this.itemsToReassign })
            .then(() => {
              this.isSubmitting = false;
              this.performCompanyUserArchival();
            })
            .catch((error) => {
              this.isSubmitting = false;
              if (error.response?.data?.itemName) {
                this.emitError(
                  `Sorry, there was an error reassigning this item. ${error.response.data.message}. 
                  Details: ${error.response.data.itemName} -> ${error.response.data.userName}`
                );
              } else {
                this.emitError(`Sorry, there was an error reassigning this item. ${error.response?.data?.message}`);
              }
            });
        } else {
          this.performCompanyUserArchival();
        }
      },
      performCompanyUserArchival() {
        http
          .put(`/company_user_archivals/${this.companyUser.id}.json`)
          .then(() => {
            this.cancelArchive();
            this.$emit('refresh-users');
            this.emitSuccess('Teammate successfully archived');
            this.isSubmitting = false;
          })
          .catch(error => {
            this.cancelArchive();
            this.isSubmitting = false;
            this.emitError(`Sorry, there was an error archiving the teammate. ${error.response.data.message}`);
          });
      },
      checkIfCanPerformAction() {
        if (!this.isWrite) {
          this.emitError(`Sorry, you do not have permission to perform this action.`);
          return false;
        }
        return true;
      },
      removeItemFromReassignList(entityType, itemId) {
        const foundItem = this.itemsToReassign.find((item) => item.entity_type === entityType && item.params.item_id === itemId);
        if (foundItem) {
          const index = this.itemsToReassign.indexOf(foundItem);
          this.itemsToReassign.splice(index, 1);
        }
      },
      reassignOrRemoveData(data) {
        this.removeItemFromReassignList(data.entity_type, data.params.item_id);

        this.itemsToReassign.push(data);
      },
      removeAssignedUser(user) {
        this.removeItemFromReassignList(user.entity_type, user.item_id);
      },
    },
  };
</script>
