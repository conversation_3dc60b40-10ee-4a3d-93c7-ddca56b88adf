<template>
  <sweet-modal
    ref="deleteCompanyUserModal"
    v-sweet-esc
    title="Before you delete this teammate..."
  >
    <template slot="default">
      <div v-if="helpdeskDependentTasks.length >= 1 || assetDependentTasks.length >= 1">
        <h5 class="text-danger">
          Note:
        </h5>
        <p>
          Looks like the user is being used in a few automated tasks. If you continue the related automated tasks will be altered/deleted.
        </p>
      </div>
      <div v-else>
        <p>
          Are you sure you want to delete this teammate? This action cannot be undone.
        </p>
      </div>
    </template>
    <button
      slot="button"
      class="btn btn-sm text-secondary btn-link font-weight-bold mr-2"
      data.tc.cancel-delete-staff-member
      @click.stop="cancelDelete"
    >
      Cancel
    </button>
    <button
      slot="button"
      class="btn btn-sm mr-1 text-danger btn-link font-weight-bold"
      data-tc-delete-staff-member
      @click.stop="deleteCompanyUser"
    >
      Delete teammate
    </button>
  </sweet-modal>
</template>

<script>
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      SweetModal,
    },
    mixins: [permissionsHelper],
    props: ['companyUser', 'openModal'],
    data() {
      return {
        hasReassignables: false,
        reassignableUsers: [],
        helpdeskDependentTasks: [],
        assetDependentTasks: [],
      };
    },
    methods: {
      onWorkspaceChange() {
        if (this.openModal) {
          this.$refs.deleteCompanyUserModal.open();
        } else {
          this.$refs.deleteCompanyUserModal.close();
        }
      },
      cancelDelete() {
        this.$refs.deleteCompanyUserModal.close();
        this.$emit('close-modal', true);
      },
      open() {
        this.checkUserDependencies();
        this.$refs.deleteCompanyUserModal.open();
      },
      checkUserDependencies() {
        http
          .get(`/company_users/${this.companyUser.id}/user_dependent_automated_task.json`)
          .then(res => {
            this.helpdeskDependentTasks = res.data.tasks.dependantTasks;
            this.assetDependentTasks = res.data.tasks.assetsDependantTasks;
          });
      },
      deleteCompanyUser() {
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          http
            .delete(`/company_users/${this.companyUser.id}.json`)
            .then(() => {
              this.cancelDelete();
              this.$router.push('/users');
              this.emitSuccess('Teammate successfully deleted.');
            })
            .catch(error => {
              this.cancelDelete();
              this.emitError(`Sorry, there was an error deleting this user. ${error.response.data.message}`);
            });
          }
      },
      checkIfCanPerformAction() {
        if (!this.isWrite) {
          this.emitError(`Sorry, you do not have permission to perform this action.`);
          return false;
        }
        return true;
      },
    },
  };
</script>
