<template>
  <div class="mt-5">
    <div
      v-if="!isLoaded"
      class="clearfix"
    >
      <h5 class="mb-3 font-weight-normal d-inline ml-3">
        Loading teammate
      </h5>
      <sync-loader
        :loading="true"
        class="float-left"
        color="#0d6efd"
        size="0.5rem"
      />
    </div>

    <div v-else-if="permissions">
      <div>
        <div
          v-if="isWrite"
          class="float-right"
        >
          <a
            v-if="!currentCompanyUser.archivedAt"
            class="text-secondary edit-delete-btn archive mr-3"
            href="#"
            data-tc-archive-user-btn
            role="button"
            @click.prevent="archiveCompanyUserCheck"
          >
            <i class="nulodgicon-archive" />
          </a>
          <a
            v-else
            class="text-secondary edit-delete-btn delete mr-3"
            href="#"
            data-tc-archive-user-btn
            role="button"
            @click.prevent="deleteCompanyUserCheck"
          >
            <i class="nulodgicon-trash-b" />
          </a>
          <a
            v-if="currentCompanyUser.archivedAt"
            class="text-secondary edit-delete-btn unarchive mr-3"
            href="#"
            data-tc-unarchive-user-btn
            role="button"
            @click.prevent="restoreCompanyUser"
          >
            <i class="nulodgicon-unarchive" />
          </a>
          <router-link
            :to="{ path: `/users/${currentCompanyUser.id}/edit`, query: { formId: currentCompanyUser.customFormId }}"
            class="edit-delete-btn edit text-secondary mr-3"
            data-tc-edit-usr-btn
          >
            <i class="nulodgicon-edit mb-2" />
          </router-link>
          <div
            v-if="customFormOptions && customFormOptions.length > 1"
            class="edit-delete-btn move text-secondary mr-3"
            @click.prevent="openMoveTeammateModal"
          >
            <i
              class="nulodgicon-switch"
              height="18"
            />
          </div>
        </div>
        <div class="float-right">
          <router-link
            to="/users"
            class="back-to-link text-secondary"
            :class="{'mr-4': isWrite}"
          >
            <i
              class="nulodgicon-arrow-left-c white mr-2"
              data-tc-back-staff-btn
            />
            <span>Back to all <strong>people</strong></span>
          </router-link>
        </div>
        <div class="d-flex align-items-center">
          <div class="user-avatar-holder">
            <div 
              v-if="currentCompanyUser.avatarThumbUrl"
              class="avatar-image"
              :style="{ backgroundImage: `url('${currentCompanyUser.avatarThumbUrl}')`}"
            />
            <div
              v-else-if="userValid && currentCompanyUser.user.avatarUrl"
              class="avatar-image"
              :style="{ backgroundImage: `url('${currentCompanyUser.user.avatarUrl}')`}"
            />
            <avatar
              v-else-if="fullName"
              class="d-inline-block"
              :size="96"
              :username="fullName"
            />
          </div>
          <div class="ml-4">
            <h1
              class="mb-0"
              data-tc-user-full-name
            >
              {{ fullName }}
            </h1>
            <h6
              v-if="userValid"
              class="text-muted mt-1 mb-0"
            >
              <a
                v-if="userEmail != 'Not provided'"
                :href="`mailto:${userEmail}`"
                data-tc-email
              >
                {{ userEmail }}
              </a>
              <span v-else>{{ userEmail }}</span>
              <span
                v-if="isAdmin && currentCompanyUser.mfaSecretKey"
                class="text-primary clickable"
                @click.prevent="openResetAuthenticatorConfirmModal"
              >
                - Reset authenticator app
              </span>
            </h6>
            <span class="text-muted p-0 mt-1">
              {{ additionalInfo }}
            </span>
          </div>
        </div>
      </div>
      <div
        ref="staffMenu"
        class="row mt-5"
      >
        <div class="col-auto">
          <div
            ref="menuContainer"
            :style="{ width: staffMenuWidth }"
            class="position-relative"
          >
            <div
              class="position-static"
              :class="{'w-100': !stickyStaffMenu}"
            >
              <div
                class="staff-menu"
                :class="{'position-fixed': stickyStaffMenu}"
                :style="{
                  'top': stickyStaffMenu ? `${marginToScroll}px` : null,
                }"
              >
                <a
                  id="staff-profile-menu"
                  href="#"
                  class="side-menu-item is-active"
                  @click.prevent="goToItem({section: 'staff-profile'})"
                >
                  <i class="icon nulodgicon-person mr-2 mt-1"/>
                  Profile
                </a>
                <a
                  id="staff-permissions-menu"
                  href="#"
                  class="side-menu-item"
                  @click.prevent="goToItem({section: 'staff-permissions'})"
                >
                  <i class="icon genuicon-person-stalker mr-2 mt-1"/>
                  Groups & Permissions
                </a>
                <a
                  v-if="(subscriptionConditions || hasAssetManagementSubscription)"
                  id="staff-assets-menu"
                  href="#"
                  class="side-menu-item d-flex justify-content-center"
                  @click.prevent="goToItem({section: 'staff-assets'})"
                >
                  <i class="icon genuicon-module-assets mr-2 mt-1"/>
                  <span>Assets</span>
                  <div class="ml-auto">
                    <span class="badge bg-light">
                      {{ totalAssets }}
                    </span>
                  </div>
                </a>
                <a
                  v-if="(subscriptionConditions || hasVendorManagementSubscription)"
                  id="staff-contracts-menu"
                  href="#"
                  class="side-menu-item d-flex"
                  @click.prevent="goToItem({section: 'staff-contracts'})"
                >
                  <i class="icon genuicon-module-contracts mr-2 mt-1"/>
                  Contracts
                  <div class="ml-auto">
                    <span class="badge bg-light">
                      {{ currentCompanyUser.contractsLength }}
                    </span>
                  </div>
                </a>
                <a
                  v-if="(subscriptionConditions || hasVendorManagementSubscription)"
                  id="staff-apps-menu"
                  href="#"
                  class="side-menu-item d-flex"
                  @click.prevent="goToItem({section: 'staff-apps'})"
                >
                  <i class="icon genuicon-module-vendors mr-2 mt-1"/>
                  Connected Apps
                  <div class="ml-auto">
                    <span class="badge bg-light">
                      {{ currentCompanyUser.apps.length }}
                    </span>
                  </div>
                </a>
                <a
                  v-if="subscriptionConditions || hasAssetManagementSubscription"
                  id="staff-usage-history-menu"
                  href="#"
                  class="side-menu-item d-flex"
                  @click.prevent="goToItem({section: 'staff-usage-history'})"
                >
                  <i class="icon genuicon-history mr-2 mt-1"/>
                  <span> Usage History </span>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="col pl-5 mt-n5">
          <user-profile
            :company-user="currentCompanyUser"
            :can-manage="isWrite"
            @load-company-user="loadCompanyUser"
          >
            <template #header>
              <div
                v-if="isWrite"
                class="mb-4"
              >
                <div
                  v-if="invitable"
                  class="row bg-blue-oblivion p-2 rounded mx-0"
                >
                  <span class="col-lg-8 mb-0 not-as-small align-self-center text-themed-fair mb-sm-3 mb-lg-0 px-1">
                    <i class="genuicon-ban text-danger-light position-relative icon--nudge-top"/>
                    <span class="text-capitalize font-weight-semi-bold"> {{ firstName }} </span> does not have access to Genuity.
                  </span>
                  <span class="col-lg-4 px-1 text-right">
                    <button
                      class="btn btn-sm btn-primary pl-3 pr-2"
                      data-tc-invite-user
                      @click.stop="inviteCompanyUser"
                    >
                      Invite teammate
                      <i class="icon genuicon-paper-plane ml-1 position-relative icon--nudge-top"/>
                    </button>
                  </span>
                </div>
                <div
                  v-else-if="lockable"
                  class="row bg-blue-oblivion p-2 rounded mx-0"
                >
                  <span class="col-lg-6 mb-0 not-as-small align-self-center text-themed-fair mb-sm-3 mb-lg-0 px-1">
                    <i class="nulodgicon-android-checkmark-circle"/>
                    <span class="text-capitalize font-weight-semi-bold"> {{ firstName }} </span> has access to Genuity.
                  </span>
                  <span class="col-lg-6 px-1 text-right">
                    <button
                      class="btn btn-sm btn-danger px-3"
                      @click.stop="openRevokeUserAccessModal"
                    >
                      Revoke permissions
                    </button>
                  </span>
                </div>
                <div
                  v-else-if="reinvitable"
                  class="row bg-blue-oblivion text-themed-fair p-2 rounded mx-0"
                >
                  <span class="col-lg-6 mb-0 not-as-small align-self-center mb-sm-3 mb-lg-0 p-0">
                    <i class="genuicon-ban text-danger-light position-relative icon--nudge-top"/>
                    <span class="text-capitalize font-weight-semi-bold"> {{ firstName }} </span> does not have access to Genuity.
                  </span>
                  <span class="col-lg-6 px-1 text-right not-as-small align-self-center">
                    Invite Sent -<button
                      v-tooltip="'Resend invite'"
                      class="btn btn-sm ml-2 pr-2 btn-success"
                      @click.stop="inviteCompanyUser"
                    >
                      Resend
                      <i class="icon genuicon-paper-plane ml-1 position-relative icon--nudge-top"/>
                    </button>
                  </span>
                </div>
              </div>
            </template>
          </user-profile>
          <div class="mt-5">
            <h5
              id="staff-permissions"
              class="font-weight-normal mb-3"
            >
              Groups & Permissions
            </h5>
            <div class="box">
              <div class="box__inner">
                <user-groups
                  class="mt-n3 mb-2"
                  :groups="currentCompanyUserGroups"
                  :can-manage="isWrite"
                  :is-saving="isSaving"
                  :selected-group="selectedGroupForAdd"
                  @add-to-group="addToGroup"
                  @open-remove-modal="openRemoveModal"
                  @group-selected="groupSelected"
                  @group-removed="groupRemoved"
                />
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="pl-0">
              <div class="box">
                <div class="container px-0 permissions">
                  <div class="row">
                    <div class="col-12">
                      <user-permissions
                        :key="permissionsUpdateKey"
                        :value="currentCompanyUser.permissions"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="(hasAssetManagementSubscription || subscriptionConditions)"
            class="mt-5"
          >
            <user-profile-assets :company-user="currentCompanyUser"/>
          </div>
          <div
            v-if="(hasVendorManagementSubscription || subscriptionConditions)"
            class="mt-5"
          >
            <user-profile-contracts :company-user="currentCompanyUser"/>
          </div>
          <div
            v-if="(hasVendorManagementSubscription || subscriptionConditions)"
            class="mt-5"
          >
            <user-profile-apps :company-user="currentCompanyUser"/>
          </div>
          <div
            v-if="hasAssetManagementSubscription || hasVendorManagementSubscription || subscriptionConditions"
            class="mt-5"
          >
            <user-profile-usage-history
              :company-user="currentCompanyUser"
              :has-asset-management-subscription="hasAssetManagementSubscription"
              :subscription-conditions="subscriptionConditions"
              :has-vendor-management-subscription="hasVendorManagementSubscription"
            />
          </div>
          <display-universal-links
            :key="linkRefreshKey"
            class="mt-5 mb-5 pl-0"
            :entity="currentCompanyUser"
            :entity-type="'CompanyUser'"
            @updateRelatedItems="updateRelatedItems"
          />
        </div>
      </div>

      <archive-user-modal
        v-if="currentCompanyUser"
        ref="archiveUserModal"
        :company-user="currentCompanyUser"
        :company-user-related-data="companyUserRelatedData"
        :subscription-conditions="subscriptionConditions"
        :has-vendor-management-subscription="hasVendorManagementSubscription"
        @refresh-users="$router.push('/users')"
      />
      <delete-user-modal
        v-if="currentCompanyUser"
        ref="deleteUserModal"
        :company-user="currentCompanyUser"
        @refresh-users="$router.push('/users')"
      />
      <move-teammate-modal
        v-if="currentCompanyUser && (customFormOptions && customFormOptions.length > 1)"
        ref="moveTeammateModal"
        class="move-modal"
        :forms="customFormOptions"
        :company-user="currentCompanyUser"
      />
      <sweet-modal
        ref="removeUserFromGroupModal"
        v-sweet-esc
        title="Before you remove"
      >
        <template v-if="selectedGroupForRemoval && selectedGroupForRemoval.id">
          <div>
            <h6>
              Are you absolutely sure you want to remove {{ currentCompanyUser.user.fullName }} from {{ selectedGroupForRemoval.name }}?
            </h6>
            <p class="text-secondary mt-3">
              <i>
                <strong>Note</strong>: This may result in {{ currentCompanyUser.user.fullName }} losing permissions granted to this group.
              </i>
            </p>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-text mr-2"
          @click.prevent="closeRemoveModal"
        >
          No, keep it
        </button>
        <button
          slot="button"
          class="btn btn-danger"
          :disabled="isRemoving"
          @click.prevent="removeFromGroup"
        >
          Yes, I'm sure
        </button>
      </sweet-modal>
      <sweet-modal
        ref="resetAuthenticatorConfirmModal"
        v-sweet-esc
        title="Are you sure..."
      >
        <template>
          <div>
            <h6>
              Are you absolutely sure you want to reset {{ currentCompanyUser.user.fullName }} authenticator app?
            </h6>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-text mr-2"
          @click.prevent="closeResetAuthenticatorConfirmModal"
        >
          Cancel
        </button>
        <button
          slot="button"
          class="btn btn-danger"
          @click.prevent="resetAuthenticatorApp"
        >
          Yes, I'm sure
        </button>
      </sweet-modal>
      <revoke-user-access-modal
        ref="revokeUserAccessModal"
        :company-user="currentCompanyUser"
        @refresh-users="loadCompanyUser"
      />
    </div>
  </div>
</template>

<script>
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import http from 'common/http';
  import PhoneFormatter from 'mixins/phone_number_formatter';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import { SweetModal } from 'sweet-modal-vue';
  import companyChannel from 'mixins/company_channel';
  import _findIndex from 'lodash/findIndex';
  import _get from 'lodash/get';
  import _sortBy from 'lodash/sortBy';
  import permissionsHelper from 'mixins/permissions_helper';
  import Pusher from 'common/pusher';
  import customFormHelper from "mixins/custom_form_helper";
  import { Avatar } from 'vue-avatar';
  import adminUser from 'mixins/admin_specific';
  import subscription from 'stores/mixins/subscription';
  import DisplayUniversalLinks from '../shared/universal_link/display_universal_links.vue';
  import ArchiveUserModal from './archive_user_modal.vue';
  import DeleteUserModal from './delete_user_modal.vue';
  import UserGroups from '../shared/user_groups.vue';
  import UserProfile from '../shared/user_profile.vue';
  import UserPermissions from './company_user_permissions.vue';
  import RevokeUserAccessModal from './revoke_user_access_modal.vue';
  import UserProfileAssets from "../shared/user_profile_assets.vue";
  import UserProfileUsageHistory from "../shared/user_profile_usage_history.vue";
  import UserProfileContracts from "../shared/user_profile_contracts.vue";
  import UserProfileApps from "../shared/user_profile_apps.vue";
  import MoveTeammateModal from './move_teammate_modal.vue';

  const MARGIN_TO_SCROLL = 60;
  export default {
    components: {
      UserProfileApps,
      UserProfileContracts,
      UserProfileAssets,
      UserProfileUsageHistory,
      UserPermissions,
      UserProfile,
      SyncLoader,
      ArchiveUserModal,
      DeleteUserModal,
      SweetModal,
      UserGroups,
      DisplayUniversalLinks,
      Avatar,
      RevokeUserAccessModal,
      MoveTeammateModal,
    },
    mixins: [PhoneFormatter, companyChannel, permissionsHelper, customFormHelper, adminUser, subscription],
    data() {
      return {
        linkRefreshKey: false,
        selectedGroupForAdd: {},
        selectedGroupForRemoval: {},
        isSaving: false,
        isRemoving: false,
        permissionsUpdateKey: false,
        listenerUp: false,
        companyUserRelatedData: null,
        usedAssets: [],
        managedAssets: [],
        contracts: [],
        apps: [],
        formFields: [],
        locations: [],
        supervisors: [],
        stickyStaffMenu: false,
        marginToScroll: MARGIN_TO_SCROLL,
        staffMenuWidth: null,
        staffMenuMapping: [
          {
            section: 'staff-profile',
            menu: 'staff-profile-menu',
          },
          {
            section: 'staff-permissions',
            menu: 'staff-permissions-menu',
          },
          {
            section: 'staff-assets',
            menu: 'staff-assets-menu',
          },
          {
            section: 'staff-contracts',
            menu: 'staff-contracts-menu',
          },
          {
            section: 'staff-apps',
            menu: 'staff-apps-menu',
          },
          {
            section: 'staff-usage-history',
            menu: 'staff-usage-history-menu',
          },
        ],
      };
    },
    computed: {
      ...mapGetters({
        currentCompanyUser: 'companyUser',
      }),
      ...mapGetters('GlobalStore', ['customFormOptions']),

      isLoaded() {
        return !!this.currentCompanyUser;
      },
      invitable() {
        return this.currentCompanyUser.inviteToken == null && this.currentCompanyUser.grantedAccessAt == null;
      },
      reinvitable() {
        return this.currentCompanyUser.inviteToken != null && !this.lockable;
      },
      userValid() {
        return this.currentCompanyUser && this.currentCompanyUser.user;
      },
      lockable() {
        return this.currentCompanyUser.deletedAt == null && this.currentCompanyUser.grantedAccessAt != null;
      },
      firstName() {
        if (this.userValid) {
          return this.currentCompanyUser.user.firstName;
        }
        return "Teammate";
      },
      fullName() {
        if (this.userValid) {
          return this.currentCompanyUser.user.fullName || "Name Missing";
        }
        return "Name missing";
      },
      userEmail() {
        return this.currentCompanyUser?.user?.email ?? "Not provided";
      },
      customFormName() {
        return _get(this, 'currentCompanyUser.customForm.name', null);
      },
      form() {
        return _get(this, 'currentCompanyUser.customForm', null);
      },
      rightFields() {
        if (this.form) {
          return _sortBy(this.form.formFields, 'orderPosition');
        }
        return [];
      },
      additionalInfo() {
        return this.customFormName ?? this.currentCompanyUser.mobilePhone ?? this.currentCompanyUser.workPhone;
      },
      totalAssets() {
        return this.currentCompanyUser.usedAssetsLength + this.currentCompanyUser.managedAssetsLength;
      },
      currentCompanyUserGroups() {
        return this.currentCompanyUser.groups.filter(op => op.name !== 'Everyone');
      },
    },
    watch: {
      "$pusher": function () {
        this.setupPusherListeners();
      },
    },
    beforeDestroy() {
      this.unsubscribePusher();
    },
    destroyed() {
      $SiteScroll
        .getScrollElement()
        .removeEventListener("scroll", this.checkSticky, true);
      $SiteScroll
        .getScrollElement()
        .removeEventListener("scroll", this.checkMenuActive, true);
    },
    methods: {
      ...mapMutations([
        'updateCompanyUser', 
        'updateCurrentUser', 
        'setCompanyChannelKey',
        'setFormValUpdate',
        'setFormValCreate',
        'setFormValDelete',
        'setCompanyUser',
      ]),
      ...mapActions('GlobalStore', ['fetchCustomFormOptions']),
      onWorkspaceChange() {
        this.loadCompanyUser();
        this.setupPusherListeners();
        $SiteScroll
          .getScrollElement()
          .addEventListener("scroll", this.checkSticky, true);
        $SiteScroll
          .getScrollElement()
          .addEventListener("scroll", this.checkMenuActive, true);
        this.getCustomFormOptions();
      },
      getCustomFormOptions() {
        const params = {
          company_module: 'company_user',
          active: true,
        };
        this.fetchCustomFormOptions(params);
      },
      openMoveTeammateModal() {
        this.$refs.moveTeammateModal.open();
      },
      openResetAuthenticatorConfirmModal() {
        this.$refs.resetAuthenticatorConfirmModal.open();
      },
      closeResetAuthenticatorConfirmModal() {
        this.$refs.resetAuthenticatorConfirmModal.close();
      },
      resetAuthenticatorApp() {
        http
          .put(`/company_users/${this.currentCompanyUser.id}/reset_authenticator_app.json`)
          .then(() => {
            this.currentCompanyUser.mfaSecretKey = null;
            this.closeResetAuthenticatorConfirmModal();
            this.emitSuccess('Reset successful for this teammate.');
          })
          .catch((error) => {
            const message = error.response?.data?.message || 'Sorry, there was an error resetting this teammate authenticator app.';
            this.emitError(message);
          });
      },
      inviteCompanyUser() {
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          http
            .get(`/company_user_invites/${this.currentCompanyUser.id}.json`)
            .then(() => {
              this.loadCompanyUser();
              this.emitSuccess('Teammate invited successfully');
            })
            .catch(error => {
              this.emitError(`Sorry, there was an error inviting the teammate. ${error.response.data.errors}`);
            });
          }
      },
      inactiveAllMenuItems() {
        this.staffMenuMapping.forEach(({ menu }) => {
          const itemMenu = document.getElementById(menu);
          if (itemMenu) {
            itemMenu.classList.remove('is-active');
          }
        });
      },
      checkMenuActive() {
        this.staffMenuMapping.forEach(({ menu, section }) => {
          const itemMenu = document.getElementById(menu);
          const itemSection = document.getElementById(section);

          if (itemMenu && itemSection) {
            const position = itemSection.getBoundingClientRect().y;
            if (position < MARGIN_TO_SCROLL) {
              this.inactiveAllMenuItems();
              itemMenu.classList.add('is-active');
            }
          }
        });
      },
      goToItem({ section }) {
        if (section) {
          document.getElementById(section)?.scrollIntoView({ block: "start", behavior: "smooth" });
        }
      },
      checkSticky() {
        const { staffMenu } = this.$refs;
        if (staffMenu) {
          if (!this.stickyStaffMenu && this.$refs.menuContainer && !this.staffMenuWidth) {
            this.staffMenuWidth = `${this.$refs.menuContainer.offsetWidth}px`;
          }
          const distanceToTop = staffMenu.getBoundingClientRect().top - MARGIN_TO_SCROLL;
          this.stickyStaffMenu = distanceToTop < 0;
        }
      },
      async loadCompanyUser() {
        if (!this.$route.params.id) return;
        http
          .get(`/company_users/${this.$route.params.id}.json`)
          .then((
            { data,
              data: { company: { guid }},
            }) => {
              this.updateCompanyUser(data);
              this.isLoading = false;
              this.linkRefreshKey = !this.linkRefreshKey;
              this.setCompanyChannelKey(guid);
          })
          .catch((error) => {
            this.isLoading = false;
            this.emitError(`Sorry, there was an error loading the teammate (${error.response.data.message}).`);
          });
      },

      restoreCompanyUser() {
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          http
            .delete(`/company_user_archivals/${this.currentCompanyUser.id}.json`)
            .then(() => {
              this.$router.push('/users');
              this.emitSuccess('Teammate access successfully restored');
            })
            .catch(error => {
              this.emitError(`Sorry, there was an error restoring the teammate. ${error.response.data.errors}`);
            });
          }
      },
      openArchiveModal() {
        this.$refs.archiveUserModal?.open();
      },
      openDeleteModal() {
        this.$refs.deleteUserModal?.open();
      },
      archiveCompanyUserCheck() {
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          this.fetchCompanyUserRelatedData();
          this.openArchiveModal();
        }
      },
      fetchCompanyUserRelatedData() {
        http
          .get('/company_user_related_entities.json', { params: { contributor_id: this.currentCompanyUser.contributorId } })
          .then(({ data: { relatedData }}) => {
            this.companyUserRelatedData = relatedData;
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error. ${error.response.data.message}`);
          });
      },
      deleteCompanyUserCheck() {
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          this.openDeleteModal();
        }
      },
      checkIfCanPerformAction() {
        if (!this.isWrite) {
          this.emitError(`Sorry, you do not have permission to perform this action.`);
          return false;
        }
        return true;
      },
      openRemoveModal(group) {
        this.selectedGroupForRemoval = group;
        this.$refs.removeUserFromGroupModal.open();
      },
      removeFromGroup() {
        const groupIndex = _findIndex(this.currentCompanyUser.groups, { id: this.selectedGroupForRemoval.id });
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          this.isRemoving = true;
          http
            .delete(`/group/${this.selectedGroupForRemoval.id}/group_members/${this.currentCompanyUser.contributorId}`)
            .then(({ data: { permissions }}) => {
              this.emitSuccess(`Teammate successfully removed from ${this.selectedGroupForRemoval.name}`);
              this.currentCompanyUser.groups.splice(groupIndex, 1);
              this.currentCompanyUser.permissions = permissions;
              this.permissionsUpdateKey = !this.permissionsUpdateKey;
              this.closeRemoveModal();
            })
            .catch((error) => {
              const message = error.response.data.message ? error.response.data.message : error.message;
              this.emitError(`Sorry, there was an error removing teammate from group ${this.selectedGroupForRemoval.name}. ${message}`);
              this.closeRemoveModal();
            });
        }
      },
      closeRemoveModal() {
        this.isRemoving = false;
        this.selectedGroupForRemoval = {};
        this.$refs.removeUserFromGroupModal.close(); 
      },
      addToGroup() {
        const canRun = this.checkIfCanPerformAction();
        if (canRun) {
          const params = { companyUserContributorId: this.currentCompanyUser.contributorId, id: this.selectedGroupForAdd.id };
          this.isSaving = true;
          http
            .post(`/group_members.json`, params)
            .then(({ data: { permissions, group }}) => {
              this.emitSuccess(`Teammate successfully added to ${this.selectedGroupForAdd.name}`);
              this.currentCompanyUser.groups.push(group);
              this.currentCompanyUser.permissions = permissions;
              this.permissionsUpdateKey = !this.permissionsUpdateKey;
              this.selectedGroupForAdd = {};
              this.isSaving = false;
            })
            .catch(() => {
              this.emitError(`Sorry, there was an error adding teammate to group ${this.selectedGroupForAdd.name}`);
              this.selectedGroupForAdd = {};
              this.isSaving = false;
            });
        }
      },
      groupSelected(group) {
        this.selectedGroupForAdd = group;
      },
      groupRemoved() {
        this.selectedGroupForAdd = {};
      },
      updateRelatedItems(links) {
        this.currentCompanyUser.universalLinks = links;
        this.updateCompanyUser(this.currentCompanyUser);
      },
      setupPusherListeners() {
        Pusher.then(() => {
          if (this.$pusher && this.currentCompanyUser && !this.listenerUp) {
            this.listenerUp = true;
            const channelId = `company_user=${this.currentCompanyUser.guid}`;
            const channel = this.$pusher.subscribe(channelId);
            channel.bind(`form-field`, data => {
              if (!data || !this.currentCompanyUser) {
                return;
              }
              http
                .get(`/company_users/${this.currentCompanyUser.id}/custom_form_values.json`, { params: { custom_form_field_id: data.id } })
                .then(res => {
                  const obj = { ...this.currentCompanyUser };
                  obj.values = obj.values.filter(v => v.customFormFieldId !== data.id);
                  res.data.forEach(r => obj.values.push(r));
                  this.setCompanyUser({ ...obj });
                })
                .catch((error) => {
                  const msg = _get(error, "response.data.message", "");
                  this.emitError(`Sorry, there was an error loading teammate information. ${msg}`);
                });
            }, this);

            const groupsChannel = this.$pusher.subscribe(`${this.currentCompanyUser.company.guid}`);
            groupsChannel.bind('removed-from-group', data => {
              data.forEach(group => {
                const groupIndex = _findIndex(this.currentCompanyUser.groups, { id: group.id });
                this.currentCompanyUser.groups.splice(groupIndex, 1);
              });
            }, this);
          }
        });
      },
      unsubscribePusher() {
        if (this.$pusher) {
          const channelId = `company_user=${this.currentCompanyUser.guid}`;
          this.$pusher.unsubscribe(channelId);
        }
      },

      openRevokeUserAccessModal() {
        this.$refs.revokeUserAccessModal.open();
      },
    },
  };
</script>

<style scoped lang="scss">
  .avatar-image {
    height: 6rem;
    width: 6rem;
  }

  .badge {
    line-height: unset;
  }

  .name-modal {
    :deep(.sweet-modal) {
      width: 450px;
    }
  }

  .permissions {
    z-index: unset;
  }

  .icon--nudge-top {
    top: 0.125rem;
  }

  .move-modal {
    :deep(.sweet-modal) {
      overflow: unset;
    }
  }
</style>
