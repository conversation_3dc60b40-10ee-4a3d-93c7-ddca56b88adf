<template>
  <div class="d-inline-flex">
    <button
      class="mt-2 btn btn-lighter btn-flat btn-sm btn-pill"
      @click="$emit('show-discovered')"
    >
      <div class="d-flex">
        <i 
          class="genuicon-alerts sub-menu-item__icon"
          :class="{ 'sub-menu-separator tooltip-top': showNotificationBadge }"
        />
        <span class="mt-1 text-themed-base">{{ label }}</span>
      </div>
    </button>
  </div>
</template>

<script>
  export default {
    props: ['label','showNotificationBadge'],
  };
</script>

<style scoped lang="scss">
.tooltip-top {
  top: 0.1rem;
  margin-right: 1rem;
}
</style>
