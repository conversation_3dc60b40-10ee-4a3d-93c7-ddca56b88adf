<template>
  <div
    class="w-100 position-relative"
    :data-tc-ticket="getSubjectValue"
    :class="{'font-weight-bold': isBold && isAgent}"
  >
    <label
      v-if="!mspFlag && !isRead"
      class="box-checkholder mb-0"
      @click.stop.prevent="selectTicket"
    >
      <input
        type="checkbox"
        class="d-none"
      >
      <i
        class="d-inline-flex nulodgicon-checkmark box-checkbox"
        :class="{ 'box-checkbox--checked': isSelected }"
        :data-tc-ticket-checkbox="getSubjectValue"
      />
    </label>

    <ticket-badge
      :class="{ 'badge--left-0 badge--top-0': isSplitPane }"
      :ticket="ticket"
    />

    <a
      :href="`/help_tickets/${ticket.id}`"
      class="box"
      :class="{
        'split-pane-box border-bottom p-2.5': isSplitPane, 
        'box--with-hover': !isSplitPane, 
        'box--selected': isSelected || isOpenedTicket
      }"
      @click.stop.prevent="routeOrSelect"
    >
      <div class="box__inner align-self-start d-flex flex-column h-100">
        <div class="row align-items-center mb-3 mt-n0.5">
          <div class="col-auto text-secondary font-weight-normal align-self-start pr-2.5 ml-n0.5">
            <people-list
              :ticket="ticket"
              :field-name="'createdBy'"
              :avatar-size="36"
              ticket-view-type="Grid"
            />
          </div>
          <div
            class="col pl-0 truncate-subject my-n1"
            :class="{'subject-width': cardSubjectWidth}"
            data-tc-ticket-subject
            :data-tc-view-subject="getSubjectValue"
          >
            <div class="reduced">{{ getSubjectValue }}</div>
          </div>
        </div>

        <div class="ticket-state-info-wrap mt-auto mx-n0.5 mb-n1">
          <div class="ticket-state-info ticket-state-info--status px-0.5 mb-1">
            <status-dropdown
              v-if="ticketFields.status && ticketFields.status.visible"
              v-tooltip="statusToolTip"
              :value="ticketFields.status.value"
              :object="ticket"
              :color="ticketFields.status.color"
              :disabled="mspFlag || disabledStatus"
              is-grid-view
              lazy
              :data-tc-ticket-status-icon="ticketFields.status.value"
              @input="checkRequiredFields"
            />
          </div>
          <div class="ticket-state-info ticket-state-info--priority px-0.5 mb-1">
            <priority-dropdown
              v-if="ticketFields.priority && ticketFields.priority.visible"
              v-tooltip="priorityTooltip"
              :value="ticketFields.priority.value"
              :object="ticket"
              :color="ticketFields.priority.color"
              :disabled="mspFlag || disabledPriority"
              use-css-truncatation
              is-grid-view
              show-text
              lazy
            />
          </div>
          <div class="ticket-state-info ticket-state-info--assignment true-small px-0.5 mt-n1">
            <span v-if="$currentCompanyAdminUser || $superAdminUser">
              <inline-assigned-dropdown
                :object="ticket"
                :field-name="'assignedTo'"
                :avatar-size="24"
                display-text
                truncate-names
                :disabled="mspFlag"
              />
            </span>
            <span v-else>
              <inline-assigned-dropdown
                v-if="ticket.assignedTo && ticket.assignedTo[0].visible"
                :object="ticket"
                :field-name="'assignedTo'"
                :disabled="mspFlag || isBasicEndUser"
                :avatar-size="24"
                display-text
                truncate-names
              />
            </span>
          </div>
        </div>

        <div class="bg-themed-lighter text-secondary smallest font-weight-semi-bold rounded mt-2.5 mb-n2 mx-n2 py-1 px-2">
          <div class="separated-columns-wrap">
            <div class="separated-column my-0.5">
              <span
                v-tooltip="{
                  content: ticket.ticketNumber,
                  show: (ticket.ticketNumber.length > 6 && hoveredIndex == ticket.ticketNumber),
                  trigger: 'manual'
                }"
                class="separated-column__content"
                :class="{ 'multiple-assignees': ticket.assigneeCount > 1 }"
                @mouseover="hoveredIndex = ticket.ticketNumber"
                @mouseleave="hoveredIndex = null"
              >
                <i class="genuicon-nav-tickets reduced mr-1" />
                <span
                  class="nowrap"
                  data-tc-ticket-index
                >
                  #{{ ticketNumber }}
                </span>
              </span>
            </div>

            <div class="separated-column my-0.5">
              <span
                v-tooltip="lastCommentAtVerbose"
                class="separated-column__content"
              >
                <i class="genuicon-comment-o reduced mr-1" />
                <span v-if="ticket.commentCount > 100">100+</span>
                <span
                  v-else
                  class="nowrap"
                >
                  {{ ticket.commentCount }}
                </span>
              </span>
            </div>

            <div class="separated-column my-0.5">
              <span
                v-tooltip="`Last updated ${lastUpdatedVerbose}`"
                class="separated-column__content"
              >
                <i class="genuicon-android-time reduced mr-1" />
                <span class="nowrap">{{ lastUpdated }}</span>
              </span>
            </div>

            <div class="separated-column my-0.5">
              <span
                v-tooltip="`${ticketSourceInfo.tooltip}`"
                class="separated-column__content"
              >
                <img 
                  :src="ticketSourceInfo.img"
                  class="ticket-source-image position-relative"
                  :class="{
                    'ticket-source-image--manual': ticket.source === 'manually_added',
                    'ticket-source-image--auto': ticket.source === 'auto_generated',
                  }"
                  height="16"
                  width="16"
                >
              </span>
            </div>

            <div class="separated-column separated-column--company-info my-0.5">
              <div class="separated-column__content">
                <span
                  v-if="showCompanyTag"
                  v-tooltip="{
                    content: ticket.company.name,
                    show: (ticket.company.name.length > nameWidth && hoveredIndex == ticket.company.name),
                    trigger: 'manual'
                  }"
                  class="d-flex align-items-center"
                  @mouseover="hoveredIndex = ticket.company.name"
                  @mouseleave="hoveredIndex = null"
                >
                  <i class="genuicon-company reduced mr-1" />
                  <span class="nowrap">
                    {{ companyName }}
                  </span>
                </span>
                <span 
                  v-if="showCompanyTag"
                  class="separated-column__separator" 
                >&#8226;</span>
                <span 
                  v-tooltip="{
                    content: ticket.workspace,
                    show: (ticket.workspace.length > nameWidth && hoveredIndex == ticket.workspace),
                    trigger: 'manual'
                  }"
                  @mouseover="hoveredIndex = ticket.workspace"
                  @mouseleave="hoveredIndex = null"
                >
                  <i class="genuicon-workspace reduced mr-1" />
                  <span class="nowrap">
                    {{ workspaceName }}
                  </span>
                </span>
              </div>
            </div>

          </div>
        </div>
      </div>
    </a>
  </div>
</template>

<script>
  import moment from 'moment-timezone';
  import { timespan } from 'common/timespan';
  import { mapGetters, mapMutations } from 'vuex';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import ticketHelper  from 'mixins/ticket_helper';
  import { managementToCompany } from 'mixins/msp_helper';
  import peopleList from './people_list.vue';
  import PriorityDropdown from '../shared/priority_dropdown.vue';
  import StatusDropdown from '../shared/status_dropdown.vue';
  import InlineAssignedDropdown from '../shared/inline_assigned_dropdown.vue';
  import TicketBadge from './ticket_badge.vue';

  const TICKET_SOURCES_INFO = {
    manually_added: {
      img: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-manual.svg",
      tooltip: "Manually created.",
    }, 
    email: {
      img: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-email.svg",
      tooltip: "Created via email.",
    }, 
    slack: {
      img: "https://nulodgic-static-assets.s3.amazonaws.com/images/slack.svg",
      tooltip: "Created via Slack.",
    }, 
    auto_generated: {
      img: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-auto-generated.svg",
      tooltip: "Created via an automation.",
    }, 
    ms_teams: {
      img: "https://nulodgic-static-assets.s3.amazonaws.com/images/ms_teams.svg",
      tooltip: "Created via Microsoft Teams.",
    }, 
    splitted: {
      img: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-split.svg",
      tooltip: "Created from a split ticket.",
    }, 
    mobile_app: {
      img: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-genuity-app.svg",
      tooltip: "Created via the Genuity Help Desk Mobile App.",
    }, 
    desktop_app: {
      img: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-genuity-app.svg",
      tooltip: "Created via the Genuity Help Desk Desktop App.",
    },
  };

  export default {
    components: {
      peopleList,
      PriorityDropdown,
      StatusDropdown,
      TicketBadge,
      InlineAssignedDropdown,
    },
    mixins: [ strings, permissionsHelper, ticketHelper ],
    props: {
      value: {
        type: Object,
        default: () => {},
      },
      selectedTickets: {
        type: Array,
        default: () => [],
      },
      showCompanyTag: {
        type: Boolean,
        default: false,
      },
      isSplitPane: {
        type: Boolean,
        default: false,
        required: false,
      },
      isOpenedTicket: {
        type: Boolean,
        default: false,
        required: false,
      },
      cardSubjectWidth: {
        type: Boolean,
        default: false,
        required: false,
      },
      mspFlag: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        hoveredIndex: null,
        nameWidth: 0,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      ticket() {
        return this.value;
      },
      ticketFields() {
        return this.value?.fields;
      },
      ticketNumber() {
        return this.ticket.ticketNumber.length > this.nameWidth 
          ? this.truncateText(this.ticket.ticketNumber) 
          : this.ticket.ticketNumber;
      },
      workspaceName() {
        return this.ticket.workspace.length > this.nameWidth
          ? this.truncateText(this.ticket.workspace, "workspace") 
          : this.ticket.workspace;
      },
      companyName() {
        return this.ticket.company.name.length > this.nameWidth
          ? this.truncateText(this.ticket.company.name, "company") 
          : this.ticket.company.name;
      },
      lastCommentAtVerbose() {
        if (this.ticket.lastCommentAt) {
          return `Last comment at ${this.lastCommentAt}`;
        }
        return "No comments available";
      },
      lastCommentAt() {
        if (this.ticket.lastCommentAt) {
          return moment.utc(this.ticket.lastCommentAt).local().format('YYYY-MM-DD HH:mm:ss');;
        }
        return null;
      },
      isSelected() {
        return this.selectedTickets.find(({id}) => this.ticket.id === id );
      },
      lastUpdatedVerbose() {
        const updatedAt = this.ticket.updatedAt ? moment.utc(this.ticket.updatedAt) : null;
        const lastCommentAt = this.ticket.lastCommentAt ? moment.utc(this.ticket.lastCommentAt) : null;
        if (!updatedAt) {
          return moment.tz(lastCommentAt, Vue.prototype.$timezone).format('lll');
        } else if (!lastCommentAt) {
          return moment.tz(updatedAt, Vue.prototype.$timezone).format('lll');
        } else if (updatedAt > lastCommentAt) {
          return moment.tz(updatedAt, Vue.prototype.$timezone).format('lll');
        }
        return moment.tz(lastCommentAt, Vue.prototype.$timezone).format('lll');
      },
      lastUpdated() {
        const updatedAt = this.ticket.updatedAt ? moment.utc(this.ticket.updatedAt) : null;
        const lastCommentAt = this.ticket.lastCommentAt ? moment.utc(this.ticket.lastCommentAt) : null;
        return timespan(updatedAt, lastCommentAt).replace(/\s/g, "");
      },
      statusToolTip() {
        return `Status: ${this.ticketFields.status.value}`;
      },
      priorityTooltip() {
        return `Priority: ${this.titleize(this.ticketFields.priority.value)}`;
      },
      disabledStatus() {
        if (this.ticketFields.status.disabled) {
          return true;
        } else if (this.isWrite || this.ticket.canWrite) {
          return false;
        }
        return true;
      },
      disabledPriority() {
        if (this.ticketFields.priority.disabled) {
          return true;
        } else if (this.isWrite || this.ticket.canWrite) {
          return false;
        }
        return true;
      },
      isBasicEndUser() {
        return this.ticket.assignedTo[0].disabled || !(this.ticket.canWrite);
      },
      getSubjectValue() {
        return this.ticketFields.subject?.value;
      },
      ticketSourceInfo() {
        let ticketSourceInfo = TICKET_SOURCES_INFO[this.ticket.source];
        if (!ticketSourceInfo) {
          ticketSourceInfo = TICKET_SOURCES_INFO.manually_added;
        }

        return ticketSourceInfo;
      },
    },
    mounted() {
      if (this.showCompanyTag) {
        this.nameWidth = this.isSplitPane ? 35 : 18;
      } else {
        this.nameWidth = 10;
      }
    },
    methods: {
      truncateText(value, type) {
        const len = this.showCompanyTag ? 8 : 4;

        return (type === "company" || type === "workspace")
          ? `${value.substring(0, len)}..${value.slice(-len)}`
          : `${value.substring(0, 2)}..${value.slice(-3)}`;
      },
      ...mapMutations(['setCurrentHelpTicket']),
      routeOrSelect(e) {
        if (this.isSplitPane) {
          this.handleSwitchToCompanyView();
          if (!this.isOpenedTicket) {
            if (this.ticket.company.id !== getCompanyFromStorage().id) {
              this.$emit('child-ticket-opener', this.ticket);
            } else {
              this.$emit('open-quick-view', this.ticket.id);
            }
          } else {
            this.$emit('close-quick-view');
          }
        } else {
          const isModalOpen = document.getElementById('helpTicketCloseModal').classList.contains('is-visible');
          const elements = ['multiselect__tags', 'multiselect__select'];
          const ticketUrl = `/help_tickets/${this.ticket.id}`;

          if (e.shiftKey) {
            this.selectTicket();
          } else if (e.ctrlKey || e.metaKey) {
            window.open(ticketUrl, '_blank');
          } else if (isModalOpen || elements.includes(e.srcElement.className)) {
            return;
          } else if (this.ticket.company && this.ticket.company.id !== getCompanyFromStorage().id) {
              this.$emit('child-ticket-opener', this.ticket);
          } else if (this.ticket.workspaceId && this.ticket.workspaceId !== getWorkspaceFromStorage().id) {
            const url = `/help_tickets/${this.ticket.id}?workspace_id=${this.ticket.workspaceId}`;
            window.open(url, '_blank');
          } else {
            this.isInsights ? window.open(ticketUrl, '_blank') : this.$router.push({ path: `/${this.ticket.id}` });
          }
        }
      },
      selectTicket() {
        this.$emit('select-ticket', this.ticket);
      },
      checkRequiredFields(params) {
        this.$emit('input', params);
      },
      handleSwitchToCompanyView() {
        managementToCompany();
      },
    },
  };
</script>

<style scoped lang="scss">
.truncate-subject {
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 3rem;
  padding-right: 3rem;

  @media($max: $small) {
    font-size: 0.8rem;
    margin-bottom: 1.1rem !important;
  }
}

.subject-width {
  width: 20rem;
}

.box--with-hover {
  transition-property: box-shadow;

  @media($max: $medium) {
    padding: 0.75rem;
  }

  &:hover {
    z-index: 1;
  }
}

.multiple-assignees {
  margin-left: -0.625rem !important;
  padding-left: 0;
}

.genuicon-refresh {
  color: $gray-500;
  font-size: 0.75rem;
  position: relative;
  top: 2px;
}

.split-pane-box {
  border-radius: 0;
  transition: $transition-base;
  transition-property: background-color, box-shadow;

  .help-ticket-grid-item:first-of-type & {
    border-top-left-radius: $border-radius;
  }

  .help-ticket-grid-item:last-of-type & {
    border-bottom: 0 !important;
    border-bottom-left-radius: $border-radius;
  }
}

.split-pane-box:not(.box--selected) {
  box-shadow: none;
}

$separated-column-spacing: 1.5rem;
.separated-columns-wrap {
  clip-path: inset(0 $separated-column-spacing 0 0);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-right: -$separated-column-spacing;
}

.separated-column {
  align-items: center;
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
}

.separated-column:not(:last-of-type):after,
.separated-column__separator {
  color: $themed-very-muted;
  content: "\2022";
  text-align: center;
  width: $separated-column-spacing;
}

.separated-column__content {
  align-items: center;
  display: flex;
  flex: 1;
  justify-content: center;
}
.separated-column--company-info {
  margin-left: auto;
  margin-right: auto;
  max-width: 18rem;

  .separated-column__content {
    justify-content: space-around;
    margin-right: $separated-column-spacing;
  }
}

.ticket-source-image {
  object-fit: cover;
}

.ticket-source-image--manual,
.ticket-source-image--auto {
  top: -2px; 
  transform: scale(1.25);
}

.ticket-state-info-wrap {
  align-items: flex-start;
  display: flex;
  flex-flow: row wrap; 
}

.ticket-state-info {
  flex: 1 1 auto;
  min-width: 0;
  max-width: max-content;
}

/* The flex-basis values are set to allow for shrinking of content to fit the container, 
 * while the parent flex-wrap value with wrap elements once there isn't enough space for any element to be unreadable.
 * While the min-widths are set to ensure no long content is ever too cut off, the flex-basis works in such a way
 * that the elements won't _actually_ be the min-width if the inner content is smaller.
 */
.ticket-state-info--status {
  // 7rem accomodates enough length for discerning status even when cut off
  flex-basis: 7rem;

  :deep(.dropdown) {
    max-width: 100%;
  }
}

.ticket-state-info--priority {
  // 5rem is the necesary width to always show at least 4 characters
  flex-basis: 5.5rem;

  :deep(.dropdown) {
    max-width: 100%;
  }
}

.ticket-state-info--assignment {
  margin-left: auto;
  // 7rem keeps "Help Desk Agents" to only two wrapped lines
  flex-basis: 7rem;

  :deep(.avatar-holder) {
    margin-right: 0.375rem;
  }
}
</style>
