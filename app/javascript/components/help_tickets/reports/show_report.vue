<template>
  <div 
    v-if="currentReport"
    class="mt-4"
    :class="{ 'is-customizing': isDrawerVisible }"
  >
    <div 
      class="primary-dashboard"
      :style="{ transform: `scale(${isDrawerVisible ? scaleFactor : 1}) translate3d(0, 0, 0)` }"
    >
      <div class="row col-12 justify-content-between align-items-center">
        <div class="d-flex align-items-center mb-1">
          <div class="p-2 icon-background">
            <img
              :src="reportIcon"
              height="50"
              style="filter: invert(100%) brightness(100%);"
            >
            <button
              v-if="isEditMode"
              href="#"
              role="button"
              class="btn btn-round btn-sm shadow-above edit-report-btn"
              @click.stop.prevent="openIconsDropdown"
            >
              <i class="nulodgicon-edit" />
            </button>
            <basic-dropdown
              ref="dropdown"
              class="dropdown-menu not-as-small dropdown-filter"
              :show-dropdown="isIconDropdownOpen"
              @on-blur="onBlur"
            >
              <div class="report-grid-container p-2">
                <a
                  v-for="(icon, index) in iconsList"
                  :key="index"
                  href="#"
                  class="p-2 rounded border bg-light"
                  :class="{
                    'border border-primary' : reportIcon == icon,
                    'border-white' : reportIcon != icon,
                  }"
                  @click.stop.prevent="selectIcon(icon)"
                >
                  <img
                    :src="icon"
                    height="25"
                  >
                </a>
              </div>
            </basic-dropdown>
          </div>
          <div v-if="!isEditMode">
            <h4 class="ml-3 mb-n1 mt-1">{{ currentReport.name }}</h4>
            <p class="not-as-small text-muted ml-3 mt-2">{{ currentReport.description || 'No description' }}</p>
          </div>
          <div
            v-if="isEditMode" 
            class="col pr-4"
          >
            <div>
              <input
                v-model="currentReport.name"
                v-validate="'required|max:25'"
                name="name"
                placeholder="Report Title"
                class="form-control report-title-input font-weight-bold bg-transparent border-right-0 border-left-0 border-top-0 rounded-0 shadow-none form-control-sm px-0 py-0"
                :class="{ 'is-invalid': errors.has('name') }"
                :style="{ width: `calc(${(currentReport.name.length || 12)}ch - 0.5ch)` }"
              >
              <span
                v-if="errors.has('name')"
                class="help text-danger small"
              >
                {{ errors.first('name') }}
              </span>
            </div>
            <div>
              <input
                v-model="currentReport.description"
                v-validate="'max:50'"
                name="description"
                placeholder="Enter a description (optional)"
                class="form-control report-description-input bg-transparent border-right-0 border-left-0 border-top-0 rounded-0 shadow-none form-control-sm px-0 py-0"
                :class="{ 'is-invalid': errors.has('description') }"
                :style="{ width: `calc(${(currentReport.description.length || 20)}ch - 0.6ch)` }"
              >
              <span
                v-if="errors.has('description')"
                class="help text-danger small"
              >
                {{ errors.first('description') }}
              </span>
            </div>
          </div>

        </div>
        <div>
          <a
            class="text-secondary mr-4"
            :href="backLink"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span class="p--responsive">Back to <strong>{{ backLabel }}</strong></span>
          </a>
          <a
            v-if="isReportTemplate || isEditMode"
            href="#"
            class="btn btn-primary"
            @click="saveReport"
          >
            <i class="genuicon-checkmark" />
            {{ isReportTemplate || isTemplate ? 'Save Template' : 'Save Report' }}
          </a>
          <a
            v-else-if="(isWrite && templateShow) || isTemplate"
            href="#"
            class="btn edit-btn"
            @click="openEditMode"
          >
            <i class="nulodgicon-edit" />
            {{ isTemplate ? 'Edit Template' : 'Edit Report' }}
          </a>
        </div>
      </div>
      <hr class="mt-1">
      <div :class="{ 'd-flex justify-content-center': currentViewMode === 'printable' }"> 
        <div 
          class="box box--with-heading box--flat mt-2 transformation"
          :style="{ width: currentViewMode === 'printable' ? '7in' : 'auto' }"
          :class="{ 'printable-mode': currentViewMode === 'printable' }"
        >
          <div class="box__heading d-flex justify-content-between">
            <div 
              class="d-flex"
              :class="{ 'align-items-center': currentViewMode === 'dashboard' }"
            >
              <div
                class="applied-filters-container"
                :class="{ 'mt-1': currentViewMode === 'printable' }"
              >
                <div
                  v-if="filtersApplied || timeframe"
                  class="applied-filters ml-2"
                >
                  <timeframe-dropdown
                    v-click-outside="closeDropdown"
                    id-label="id"
                    label="Timeframe"
                    name="name"
                    :options="dynamicTimeframes"
                    @selected="filterTimeframe"
                  />
                  <span
                    v-for="filter in reportActiveFilters()"
                    v-if="currentViewMode === 'dashboard'"
                    :key="`${filter.filterName}_${filter.filter.name}`"
                    class="align-self-center"
                  >
                    <report-active-filter
                      :filter="filter"
                      @clear-filter="clearFilter"
                    />
                  </span>
                  <!-- <div class="col-md-12 d-flex justify-content-end mt-3">
                  </div> -->
                  <div
                    v-if="!isReportTemplate && !isTemplate && currentViewMode !== 'printable'"
                    class="w-60"
                  >
                    <a
                      v-tooltip="'Add Filters'"
                      href="#"
                      class="d-flex border rounded-pill w-200 filter-btn"
                      :class="{ 'w-120': currentViewMode === 'printable' }"
                      @click="openFilters"
                    >
                      <span 
                        class="pill not-as-small d-flex mr-1 ml-1"
                        @click.stop.prevent="toggleFilterMenu"
                      >
                        <i class="genuicon-android-funnel align-text-bottom mr-1" />
                        <span class="ml-1">+ Add Filters</span>
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div 
              class="d-flex"
              :class="{ 'adjust-view': currentViewMode === 'printable' , 'align-items-center': currentViewMode === 'dashboard' }"
            >
              <div
                v-if="isPrintable && !isTemplate && !isReportTemplate"
                class="d-flex border rounded-pill"
              >
                <span
                  class="pill not-as-small"
                  :class="{ 'active-pill': currentViewMode === 'dashboard' }"
                  @click="setViewMode('dashboard')"
                >
                  Dashboard View
                </span>
                <span
                  id="printable-view-toggle"
                  class="pill not-as-small"
                  :class="{ 'active-pill': currentViewMode === 'printable' }"
                  @click="setViewMode('printable')"
                >
                  Printable View
                </span>
              </div>

              <div
                v-if="isPrintable && !isTemplate && !isReportTemplate && currentViewMode === 'printable'"
                class="d-flex justify-content-between w-100"
              >
                <div>
                  <button
                    type="button"
                    class="btn btn-primary px-3 ml-2 export-btn position-relative"
                    :class="{ 'mt-3': currentViewMode === 'printable' }"
                    @click.stop.prevent="togglePrintableEdit"
                  >
                    {{ isEditMode ? 'Save Layout' : 'Edit Layout' }}
                  </button>
                  <sup
                    v-tooltip.top="'Please adjust your report layout for the PDF preview.'"
                    class="genuicon-info-circled text-faded-light true-small ml-1"
                  />
                </div>
                <button
                  id="report_preview_btn"
                  type="button"
                  class="btn btn-primary px-3 export-btn ml-2 position-relative"
                  :class="{ 'mt-3': currentViewMode === 'printable' }"
                  :disabled="isEditMode"
                  @click.stop.prevent="exportReport"
                >
                  <i class="nulodgicon-cloud-download mr-1" />
                  Export PDF
                </button>
              </div>
  
              <a
                class="btn-group ml-2 position-relative"
              >
                <button
                  v-if="isScheduleReport && !isTemplate"
                  id="schedule_report_btn"
                  type="button"
                  class="btn btn-primary px-3 export-btn mr-1"
                  @click.stop.prevent="openScheduleReportModal"
                >
                  Schedule Report
                </button>
                <button
                  v-if="isTemplateReportPreview"
                  id="schedule_report_btn"
                  type="button"
                  class="btn btn-primary px-3 export-btn mr-1"
                  @click.stop.prevent="cloneReport"
                >
                  <i class="genuicon-edit-copy mr-1" />
                  Edit as a copy
                </button>

                <a
                  v-if="isEditMode && !isDrawerVisible && currentViewMode !== 'printable'"
                  class="btn-group ml-2 position-relative"
                >
                  <button
                    type="button"
                    class="btn btn-primary px-3 export-btn"
                    @click="toggleDrawer()"
                  >
                    <i class="nulodgicon-plus-round mr-1" />
                    Add Charts
                  </button>
                </a>

                <a
                  v-if="isEditMode && isDrawerVisible"
                  class="btn-group ml-2 position-relative"
                >
                  <button
                    type="button"
                    class="btn btn-primary px-3 export-btn"
                    @click="toggleDrawer()"
                  >
                    <i class="nulodgicon-arrow-left-c mr-1" />
                    Hide Charts
                  </button>
                </a>
              </a>
            </div>
          </div>

          <div class="box__inner p-0">
            <div
              v-if="selectedWidgets"
              id="cards-grid"
              class="grid-container"
              :class="{ 'edit-grid-container': isEditMode }"
            >
              <div
                v-if="!reportCards.length"
                class="text-center pt-5 pb-5"
              >
                <h4>
                  Get started by
                </h4>
                <h5 class="text-secondary font-weight-normal">
                  <a
                    href="#"
                    @click.stop.prevent="toggleDrawer() "
                  >
                    + adding charts
                  </a>
                  to the report
                </h5>
              </div>
              <div class="d-flex ml-3">
                <dismissible-container
                  ref="dismissibleContainer"
                  show-pointer
                  container-classes="dismissible-container--right mb-2"
                  @show-hide-container="toggleFilterMenu"
                  @close-container="toggleFilterMenu"
                >
                  <filters @filters-updated="filtersUpdate"/>
                </dismissible-container>
                <div 
                  v-if="currentViewMode === 'printable'" 
                  class="mr-3"
                >
                  <filters-preview :filters="activeFilters"/>
                </div>
              </div>
              <div v-if="isEditMode || currentViewMode === 'printable'">
                <div
                  v-for="(y, index) in pageBreakLines()" 
                  :key="index"
                  class="page-break-line"
                  :style="{ top: `${y}px` }"
                />
              </div>
              <grid-layout
                ref="gridlayout"
                :layout.sync="activeLayout"
                :col-num="gridWidth"
                :row-height="30"
                :is-draggable="isEditMode"
                :is-resizable="isEditMode"
                :is-mirrored="false"
                :vertical-compact="currentViewMode === 'dashboard'"
                :margin="[20, 20]"
                :use-css-transforms="true"
                :transform-scale="gridScale"
                @layout-updated="layoutUpdatedEvent"
              >
  
                <grid-item
                  v-for="item in reportCards"
                  :key="itemKey(item)"
                  :x="getAttrs(item).x"
                  :y="getAttrs(item).y"
                  :w="getAttrs(item).w"
                  :h="getAttrs(item).h"
                  :i="getAttrs(item).i"
                  :min-w="getMinWidth(item.chartType)"
                  :min-h="getMinHeight(item.chartType)"
                >
                  <report-widget-card
                    :widget-id="item.cardAttributes.i"
                    :name="item.name"
                    :data="item.data"
                    :widget="item"
                    :is-edit-mode="isEditMode"
                    :is-loading="isLoadingCards[item.cardAttributes.i]"
                    :view-mode="currentViewMode"
                    @remove-card="removeCard(item.cardAttributes.i)"
                    @finalize-widget-edit="handleFinalWidgetEdit"
                    @unselected-fields="updateUnselectedFields"
                  />
                </grid-item>
              </grid-layout>
            </div>
          </div>
        </div>
      </div>
    </div>

    <add-widgets-drawer
      v-if="isDrawerVisible"
      @drag-widget="drag"
      @dragend-widget="dragend"
      @close-report-popout="toggleDrawer()"
    />

    <Teleport to="body">
      <sweet-modal
        ref="editWidgetModal"
        v-sweet-esc
        :title="'Customze Your Chart'"
        modal-theme="right theme-dark-header theme-sticky-footer"
        :width="'75%'"
      >
        <div>
          <customize-widget-card
            ref="customizeWidget"
            :widget="newCard"
            :modal-opened="customizeModalOpened"
            :index="newCardIndex"
            @finish-editing="handleFinalWidgetEdit"
            @update-unselected-fields="updateUnselectedFields"
          />
        </div>
      </sweet-modal>
    </Teleport>

    <preview
      ref="previewModal"
      :current-report="currentReport"
      :preview-modal="previewModal"
      @close-preview-modal="closePreviewModal"
    />
    <save-report-modal
      ref="saveReportModal"
      :saved-report="currentReport"
      :is-new-schedule-report="!currentReport.hasScheduledReport"
      :scheduled-report="currentReport.scheduledReport"
      @refresh-scheduled-report="refreshReport"
    />
  
  </div>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';
  import http from 'common/http';
  import vClickOutside from 'v-click-outside';
  import VueGridLayout from 'vue-grid-layout';
  import { selectedWidgets, initReport, dynamicTimeframes, allMetrics } from 'mixins/hd_analytics_helper';
  import permissionsHelper from 'mixins/permissions_helper';
  import BasicDropdown from 'components/shared/basic_dropdown.vue';
  import dates from 'mixins/dates';
  import dropdownClose from 'mixins/dropdown_close_outside_click';
  import Filters from 'components/help_tickets/report/in_depth/filters.vue';
  import DismissibleContainer from 'components/shared/dismissible_container.vue';
  import { mapGetters, mapMutations } from 'vuex';
  import TimeframeDropdown from 'components/help_tickets/report/in_depth/timeframe_dropdown.vue';
  import SaveReportModal from './save_report_modal.vue';
  import ReportWidgetCard from './report_widget_card.vue';
  import AddWidgetsDrawer from './add_widgets_drawer.vue';
  import CustomizeWidgetCard from './customize_widget_card.vue';
  import ReportActiveFilter from './report_active_filter.vue';
  import Preview from './export/index.vue';
  import FiltersPreview from './filters_preview.vue';

  const mouseXY = {"x": null, "y": null};
  const DragPos = {"x": null, "y": null, "w": 1, "h": 1, "i": null};

  export default {
    $_veeValidate: {
      validator: 'new',
    },
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      GridLayout: VueGridLayout.GridLayout,
      GridItem: VueGridLayout.GridItem,
      ReportWidgetCard,
      AddWidgetsDrawer,
      CustomizeWidgetCard,
      SweetModal,
      BasicDropdown,
      Filters,
      DismissibleContainer,
      ReportActiveFilter,
      TimeframeDropdown,
      SaveReportModal,
      Preview,
      FiltersPreview,
    },
    mixins: [permissionsHelper, dates, dropdownClose],
    data() {
      return {
        customizeModalOpened: false,
        selectedWidgets,
        initReport,
        currentReport: null,
        isEditMode: false,
        isEditingName: false,
        isEditingDescription: false,
        isLoadingCards: [],
        isDrawerVisible: false,
        scaleFactor: 0.8,
        resizeObserver: null,
        newlyAddedCard: null,
        newlyAddedCardIndex: null,
        isIconDropdownOpen: false,
        timeframeFilter: null,
        iconsList: [
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-tickets.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-agents.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-tags.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-timing.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-priority.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-timing.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-people.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-health.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-satisfaction.svg",
          "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/reports-pipeline.svg",
        ],
        dynamicTimeframes,
        allMetrics,
        currentViewMode: 'dashboard',
        gridHeight: 0,
        previewModal: false,
      };
    },
    computed: {
      ...mapGetters('inDepthTicketReport', [
        'activeFilters',
        'priority',
        'source',
        'category',
        'tag',
        'agent',
        'group',
        'status',
        'timePeriod',
        'timeframe',
      ]),
      newCard() {
        return this.newlyAddedCard || {};
      },
      newCardIndex() {
        return this.newlyAddedCardIndex || null;
      },
      cardAttributes() {
        return this.reportCards?.map((x) => x.cardAttributes) || [];
      },
      reportCards() {
        return this.currentReport?.cards;
      },
      gridWidth() {
        return 16;
      },
      gridScale() {
        if (this.isDrawerVisible) {
          return 0.8;
        }
        return 1;
      },
      reportIcon() {
        return this.currentReport?.iconClass;
      },
      filtersApplied() {
        return this.activeFilters?.length;
      },
      isReportTemplate() {
        return this.$route.path === '/report_templates/new';
      },
      backLink() {
        return this.isReportTemplate || this.isTemplate ? '/report_templates' : '/help_tickets/reports/saved';
      },
      backLabel() {
        return this.isReportTemplate || this.isTemplate ? 'all templates' : 'all reports';
      },
      isTemplate() {
        const {path} = this.$route;
        return path.includes('/report_templates') && !path.includes('/new');
      },
      isTemplateReportPreview() {
        return this.$route.query.template === 'true' || this.$route.query.template === true;
      },
      isTemplateView() {
        const {path} = this.$route;
        return path.includes('/report_templates');
      },
      templateShow() {
        return !this.$route.query.template;
      },
      isReportId() {
        return this.currentReport?.id;
      },
      isPrintable() {
        return this.templateShow && !this.$route.query.addReport;
      },
      isScheduleReport() {
        return this.isReportId && this.isPrintable && this.currentViewMode !== 'printable';
      },
      activeLayout() {
        return this.currentReport.cards.map(card => this.currentViewMode === 'printable'
            ? card.previewAttributes
            : card.cardAttributes);
      },
    },
    mounted() {
      if (this.isWrite && this.$route.path.endsWith('/edit')) {
        this.isEditMode = true;
      }
      if (this.$route.query.template || (this.$route.query.addReport && this.$route.path !== '/reports/new')) {
        this.setReport();
        if (this.$route.query.addReport) {
          this.isEditMode = true;
        }
      }
      else if (this.$route.path === '/reports/new' || this.isReportTemplate) {
        this.isEditMode = true;
        this.currentReport = this.initReport;
        this.currentReport.cards = [];
        const defaultIcon = this.iconsList[0];
        this.currentReport.iconClass = defaultIcon;
        this.setTimeframe("Previous Week");
      } else {
        this.$route.path.includes('/report_templates') ? this.fetchTemplate() : this.fetchReport();
      }
      document.addEventListener("dragover", (e) => {
        mouseXY.x = e.clientX;
        mouseXY.y = e.clientY;
      }, false);
      this.observeResize();
      this.updateScaleFactor();
      this.updateGridHeight();
      window.addEventListener("resize", this.updateGridHeight);
    },
    beforeUnmount() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
      }
    },
    methods: {
      ...mapMutations('inDepthTicketReport', [
        'clearReportFilter',
        'clearAllFilters',
        'setPriority',
        'setStatus',
        'setSource',
        'setCategory',
        'setTag',
        'setAgent',
        'setGroup',
        'setTimeframe',
      ]),
      onWorkspaceChange() {
        if (this.currentReport && this.currentReport.workspaceId !== getWorkspaceFromStorage().id) {
          this.$router.push("/reports/saved");
        }
      },
      setReport() {
        if (this.$route.params.report) {
          this.currentReport = this.$route.params.report;
          this.applyFilters();
          this.fetchCardsData();
          return;
        }
          this.fetchTemplate();
      },
      getAttrs(item) {
        if (this.currentViewMode === 'printable') {
          if (!item.previewAttributes) {
            item.previewAttributes = JSON.parse(JSON.stringify(item.cardAttributes));
          }
          return item.previewAttributes;
        } 
          return item.cardAttributes;
      },
      refreshReport() {
        this.fetchReport();
      },
      itemKey(item) {
        return this.getAttrs(item)?.i || item.id || item.name;
      },
      toggleFilterMenu() {
        this.$refs.dismissibleContainer.toggleOpen();
      },
      openEditMode() {
        this.isEditMode = true;
      },
      saveReport() {
        this.$validator.validateAll().then((result) => {
          if (result) {
            if (this.currentReport.cards.length === 0) {
              this.emitError('Please add at least one card to your report.');
            } else {
              this.isEditMode = false;
              this.isEditName = false;
              this.isEditingDescription = false;
              this.isDrawerVisible = false;
              if (!this.currentReport.id || this.$route.query.addReport) {
                this.isReportTemplate ? this.createTemplate() : this.createReport();
              } else {
                this.isTemplate ? this.updateTemplate() : this.updateReport();
              }
            }
          } else {
            this.emitError(`Please correct the highlighted errors before submitting.`);
          }
        });
      },
      cloneReport() {
        const routeParams = {
          name: 'show-report',
          params: {
            id: this.currentReport.id,
            report: this.currentReport,
          },
          query: {},
        };
        if (this.currentReport) {
          routeParams.query.addReport = true;
        }
        this.$router.push(routeParams);
        this.isEditMode = true;
      },
      createReport() {
        const params = this.getReportParams();
        http
          .post('/help_tickets/reports', { report: params })
          .then(() => {
            this.emitSuccess("Created report successfully!");
            this.$router.push({ path: "/reports/saved" });
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while creating report. ${e.response.data.message}`);
          });
      },
      updateReport() {
        const params = this.getReportParams();
        http
          .put(`/help_tickets/reports/${this.currentReport.id}`, { report: params, viewMode: this.currentViewMode })
          .then(() => {
            if (this.currentViewMode !== 'printable') {
              this.emitSuccess("Updated report successfully!");
            }
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while updating report. ${e.response.data.message}`);
          });
      },
      fetchReport() {
        http
          .get(`/help_desk/reports/${this.$route.params.id}`)
          .then((res) => {
            this.currentReport = res.data;
            this.applyFilters();
            this.fetchCardsData();
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while fetching report. ${e.response.data.message}`);
          });
      },
      fetchTemplate() {
        let routeId = null;
        if (this.$route.params.id) {
          routeId = this.$route.params.id - 0;
        }
        http
          .get(`/report_templates/${routeId}.json`)
          .then((res) => {
            this.currentReport = res.data;
            this.applyFilters();
            this.fetchCardsData();
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while fetching report template. ${e.response.data.message}`);
          });
      },
      createTemplate() {
        const params = this.getReportParams();
        http
          .post('/report_templates', { template: params, isReportTemplate: true})
          .then(() => {
            this.emitSuccess("Created template successfully!");
            this.$router.push({ path: "/report_templates" });
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while creating template. ${e.response.data.message}`);
          });
      },
      updateTemplate() {
        const params = this.getReportParams();
        http
          .put(`/report_templates/${this.currentReport.id}`, { template: params })
          .then(() => {
            this.emitSuccess("Updated template successfully!");
            this.$router.push({ path: "/report_templates" });
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while updating template. ${e.response.data.message}`);
          });
      },
      applyFilters() {
        this.clearAllFilters();

        const filters = this.currentReport?.filters || [];

        const priorityFilter = filters.find(filter => filter.key === "priority") || null;
        const statusFilter = filters.find(filter => filter.key === "status") || null;
        const sourceFilter = filters.find(filter => filter.key === "source") || null;
        const categoryFilter = filters.find(filter => filter.key === "category") || null;
        const tagFilter = filters.find(filter => filter.key === "tag") || null;
        const agentFilter = filters.find(filter => filter.key === "agent") || null;
        const groupFilter = filters.find(filter => filter.key === "group") || null;
        const timeframeFilter = filters.find(filter => filter.key === "timeframe") || null;

        if (priorityFilter) {
          this.setPriority(priorityFilter.value);
        };
        if (statusFilter) {
          this.setStatus(statusFilter.value);
        };
        if (sourceFilter) {
          this.setSource(sourceFilter.value);
        };
        if (categoryFilter) {
          this.setCategory(categoryFilter.value);
        };
        if (tagFilter) {
          this.setTag(tagFilter.value);
        };
        if (agentFilter) {
          this.setAgent(agentFilter.value);
        };
        if (groupFilter) {
          this.setGroup(groupFilter.value);
        };
        if (timeframeFilter) {
          this.setTimeframe(timeframeFilter.value);
        } else {
          this.setTimeframe("Previous Week");
        };
      },
      removeCard(widgetIndex) {
        const idx = this.reportCards.findIndex(card => card.cardAttributes.i === widgetIndex);
        if (idx > -1) {
          this.reportCards.splice(idx, 1);
        }
      },
      getReportParams() {
        return this.currentReport;
      },
      openFilters() {},
      openTimePeriodFilters() {},
      toggleEditingName() {
        this.isEditingName = !this.isEditingName;
      },
      toggleEditingDescription() {
        this.isEditingDescription = !this.isEditingDescription;
      },
      layoutUpdatedEvent(layout) {
        this.updateGridHeight();
        this.currentReport.cards.forEach((card, idx) => {
          if (this.currentViewMode === 'printable') {
            card.previewAttributes = layout[idx];
          } else {
            card.cardAttributes = layout[idx];
          }
        });

        if (this.currentViewMode !== 'printable') return;

        const sortedCards = [...this.currentReport.cards].sort(
          (a, b) => a.previewAttributes.y - b.previewAttributes.y
        );
        const pageBreakLines = this.pageBreakLines();
        const gridBreaks = pageBreakLines.map(line => this.convertPxToGridY(line));
        let shiftUnits = 0;

        sortedCards.forEach((card) => {
          const originalCard = this.currentReport.cards.find(c => c.id === card.id);
          card.previewAttributes.y += shiftUnits;

          gridBreaks.some(gridValue => {
            const cardTop = card.previewAttributes.y;
            const cardBottom = card.previewAttributes.y + card.previewAttributes.h;

            if (cardTop <= gridValue && cardBottom > gridValue) {
              if (originalCard.previewAttributes.y === gridValue) {
                return true;
              }

              const previousY = card.previewAttributes.y;
              card.previewAttributes.y = gridValue;

              const addedShift = card.previewAttributes.y - previousY;
              shiftUnits += addedShift;

              return true;
            }

            return false;
          });

          if (originalCard) {
            originalCard.previewAttributes.y = card.previewAttributes.y;
          }
        });
      },
      convertPxToGridY(pixelValue) {
        const rowHeight = 30;
        const verticalMargin = 20;
        const totalRowSpace = rowHeight + verticalMargin;
        return Math.ceil(pixelValue / totalRowSpace);
      },
      updateGridHeight() {
          if (this.$refs.gridlayout?.$el) {
            this.gridHeight = this.$refs.gridlayout.$el.offsetHeight;
          }
      },
      handleFinalWidgetEdit({ widgetName, widgetDescription, chartType, metrics, index, displayLabels, comparePreviousTimeperiod }) {
        const cardIndex = this.currentReport.cards.findIndex(card => card.cardAttributes.i === index);
        const cardToUpdate = this.currentReport.cards[cardIndex];
        if (cardToUpdate) {
          cardToUpdate.name = widgetName;
          cardToUpdate.description = widgetDescription;
          cardToUpdate.chartType = chartType;
          cardToUpdate.metrics = metrics;
          cardToUpdate.displayLabels = displayLabels;
          cardToUpdate.comparePreviousTimeperiod = comparePreviousTimeperiod;
          this.fetchCardInsights(cardToUpdate, cardIndex);
        }
        this.customizeModalOpened = false;
        this.$refs.editWidgetModal.close();
        this.newlyAddedCard = null;
        this.newlyAddedCardIndex = null;
      },
      getMinWidth(chartType) {
        if (this.currentViewMode === 'printable') {
          return 10;
        }
        if(chartType === 'pie' || chartType === 'donut') {
          return 4;
        } else if (chartType === 'numeric') {
          return 3;
        }
        return 4;
      },
      getMinHeight(chartType) {
        if(chartType === 'pie' || chartType === 'donut') {
          return 7;
        } else if (chartType === 'numeric') {
          return 4;
        }
        return 7;
      },
      toggleDrawer() {
        if (this.currentViewMode === 'printable') {
          this.setViewMode('dashboard');
        }
        this.isDrawerVisible = !this.isDrawerVisible;
        this.updateScaleFactor();
      },
      updateScaleFactor() {
        const zoomLevel = Math.round(window.devicePixelRatio * 100);
        let scaleFactor = 0.8;

        if (zoomLevel === 80) {
          scaleFactor = 0.83;
        } else if (zoomLevel === 90) {
          scaleFactor = 0.82;
        } else if (zoomLevel >= 110 && zoomLevel < 125) {
          scaleFactor = 0.77;
        } else if (zoomLevel >= 125 && zoomLevel < 150) {
          scaleFactor = 0.75;
        } else if (zoomLevel >= 150) {
          scaleFactor = 0.7;
        }

        this.scaleFactor = scaleFactor;
      },
      observeResize() {
        this.resizeObserver = new ResizeObserver(() => {
          this.updateScaleFactor();
        });
        this.resizeObserver.observe(document.body);
      },
      fetchCardsData() {
        this.reportCards.forEach((card, idx) => {
          this.fetchCardInsights(card, idx);
        });
      },
      getFilterParams(isCompareTimePeriod) {
        const params = { values: [] };
        if (!this.isTemplate && !this.isReportTemplate) {
          params.companyId = Vue.prototype.$currentCompanyId;
          params.workspaceId = $workspace.id;
        }

        const appliedFilters = [];

        if (this.timeframe) {
          let filterDates = null;
          if (isCompareTimePeriod) {
            filterDates = this.previousTimeperiodDates(this.timeframe);
          } else {
            filterDates = this.timeframeDates(this.timeframe);
          }
          this.timeframeFilter = {
            name: this.timeframe.name || this.timeframe,
            startDate: filterDates.startDate,
            endDate: filterDates.endDate,
          };
          params.values.push({
            name: 'custom_date',
            value: {
              durationType: "createdAt",
              startDate: filterDates.startDate,
              endDate: filterDates.endDate,
              filter: 'custom_date',
            },
          });
          appliedFilters.push({
            key: 'timeframe',
            value: this.timeframe,
          });
        }

        if (this.priority) {
          params.values.push({
            name: 'priority',
            value: this.priority.id,
          });
          appliedFilters.push({
            key: 'priority',
            value: this.priority,
          });
        }

        if (this.source) {
          params.values.push({
            name: 'source',
            value: this.source.index,
          });
          appliedFilters.push({
            key: 'source',
            value: this.source,
          });
        }

        if (this.category) {
          params.values.push({
            name: 'category',
            value: this.category.name,
          });
          appliedFilters.push({
            key: 'category',
            value: this.category,
          });
        }

        if (this.tag) {
          params.values.push({
            name: 'tag',
            value: this.tag.name,
          });
          appliedFilters.push({
            key: 'tag',
            value: this.tag,
          });
        }

        if (this.agent) {
          params.values.push({
            name: 'agent',
            value: this.agent.contributorId,
          });
          appliedFilters.push({
            key: 'agent',
            value: this.agent,
          });
        }

        if (this.group) {
          params.values.push({
            name: 'group',
            value: this.group.contributorId,
          });
          appliedFilters.push({
            key: 'group',
            value: this.group,
          });
        }

        if (this.status) {
          params.values.push({
            name: 'status',
            value: this.status.name,
            with_survey: false,
          });
          appliedFilters.push({
            key: 'status',
            value: this.status,
          });
        }

        this.currentReport.filters = appliedFilters;
        return params;
      },
      fetchCardInsights(card) {
        const cardId = card.cardAttributes.i;
        this.$set(this.isLoadingCards, cardId, true);
        const idx = this.reportCards.findIndex((x) => x.cardAttributes.i === card.cardAttributes.i);
        // this.$set(this.isLoadingCards, idx, true);

        const metrics = card.metrics || [];
        const metricPromises = metrics.map((metric) => {
          const params = this.getFilterParams(false);
          params.widgetId = card.id;
          params.type = metric.type;

        if (this.isReportTemplate || this.isTemplate) {
          const matchingMetric = this.allMetrics.find(m => m.type === metric.type);

          if (matchingMetric) {
            this.$set(metric, 'previewCategories', matchingMetric.previewCategories || []);
            
            if (card.comparePreviousTimeperiod) {
              this.$set(metric, 'previewData', matchingMetric.previewData || []);
            } else {
              const currentOnly = matchingMetric.previewData?.length
                ? [matchingMetric.previewData[0]]
                : [];
              this.$set(metric, 'previewData', currentOnly);
            }
          }

          return Promise.resolve();
        }


          return http
            .get("/insights.json", { params: { params } })
            .then((res) => {
              const currentData = res.data[metric.type];
              const friendlyName = metric.friendlyName || metric.title;

              metric.rawCurrentData = currentData;
              metric.previewCategories = currentData.map(x => x.name);
              metric.previewData = [{
                name: friendlyName,
                data: currentData.map(x => x.value),
              }];

              if (card.comparePreviousTimeperiod && this.timeframe?.name !== "custom_date_range") {
                return this.fetchTimeperiodData(card, metric, currentData);
              }
              return null;
            });
        });

        Promise.all(metricPromises)
          .then(() => {
            const newCard = JSON.parse(JSON.stringify(card));
            this.$set(this.currentReport.cards, idx, newCard);
            this.$set(this.isLoadingCards, cardId, false);
          })
          .catch(() => {
            this.$set(this.isLoadingCards, cardId, false);
            this.emitError(`There was an error while fetching reporting data. Please reload page and try again.`);
          });
      },
      fetchTimeperiodData(card, metric, currentData) {
        const params = this.getFilterParams(true);
        params.widgetId = card.id;
        params.type = metric.type;

        return http
          .get("/insights.json", { params: { params } })
          .then((res) => {
            const previousData = res.data[metric.type];
            const allCategories = Array.from(new Set([
              ...currentData.map(x => x.name),
              ...previousData.map(x => x.name),
            ]));

            const createValueMap = (data) => {
              const map = {};
              data.forEach(item => {
                map[item.name] = item.value;
              });
              return map;
            };

            const currentMap = createValueMap(currentData);
            const previousMap = createValueMap(previousData);

            const currentValues = allCategories.map(name => currentMap[name] ?? 0);
            const previousValues = allCategories.map(name => previousMap[name] ?? 0);

            metric.previewCategories = allCategories;
            metric.previewData = [
              {
                name: metric.friendlyName || metric.title,
                data: currentValues,
              },
              {
                name: `${metric.friendlyName || metric.title} Comparison`,
                data: previousValues,
              },
            ];
          });
      },
      filtersUpdate() {
        this.fetchCardsData();
      },
      reportActiveFilters() {
        return this.activeFilters;
      },
      clearFilter(filter) {
        this.clearReportFilter(filter);
        this.filterRemoved();
      },
      clearTimeframeFilter() {
        this.timeframeFilter = null;
        this.setTimeframe(null);
        this.filterRemoved();
      },
      filterRemoved() {
        this.filtersUpdate();
      },
      drag(width, height) {
        const parentRect = document.getElementById('cards-grid').getBoundingClientRect();
        let mouseInGrid = false;
        if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
            mouseInGrid = true;
        }
        if (mouseInGrid === true && !(this.reportCards.find(card => card.cardAttributes.i === 'drop'))) {
          const newCard = {
            id: null,
            name: "Temp",
            description: "",
            chartType: "bar",
            viewBy: null,
            cardAttributes: {
              x: (this.reportCards.length * 2) % (this.colNum || 12),
              y: (this.reportCards.length * 2) + (this.colNum || 12),
              w: width,
              h: height,
              i: 'drop',
              moved: false,
            },
            previewAttributes: {
              x: (this.reportCards.length * 2) % (this.colNum || 12),
              y: (this.reportCards.length * 2) + (this.colNum || 12),
              w: width,
              h: height,
              i: 'drop',
              moved: false,
            },
            metrics: [],
            reportsId: this.currentReport.id,
          };
          this.reportCards.push(newCard);
        }
        const index = this.reportCards.findIndex(card => card.cardAttributes.i === 'drop');
        if (index !== -1) {
            // try {
            //     this.$refs.gridlayout.$children[this.reportCards.length].$refs.item.style.display = "none";
            // } catch {
            //   console.log("Error occured");
            // }
            const el = this.$refs.gridlayout.$children[index];
            el.dragging = {"top": mouseXY.y - parentRect.top, "left": mouseXY.x - parentRect.left};
            const newPos = el.calcXY(mouseXY.y - parentRect.top, mouseXY.x - parentRect.left);
            if (mouseInGrid === true) {
                this.$refs.gridlayout.dragEvent('dragstart', 'drop', newPos.x, newPos.y, height, width);
                DragPos.i = String(index);
                DragPos.x = this.reportCards[index].cardAttributes.x;
                DragPos.y = this.reportCards[index].cardAttributes.y;
            }
            if (mouseInGrid === false) {
                const idx = this.reportCards.findIndex(card => String(card.cardAttributes.i).trim() === 'drop');
                if (idx !== -1) {
                  this.reportCards.splice(index, 1);
                }
                this.$refs.gridlayout.dragEvent('dragend', 'drop', newPos.x, newPos.y, height, width);
            }
        }
      },
      dragend(widget, widgetTitle, type, width, height) {
        const parentRect = document.getElementById('cards-grid').getBoundingClientRect();
        let mouseInGrid = false;
        if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
            mouseInGrid = true;
        }
        if (mouseInGrid === true) {
          this.$refs.gridlayout.dragEvent('dragend', 'drop', DragPos.x, DragPos.y, height, width);
          const index = this.reportCards.findIndex(card => card.cardAttributes.i === 'drop');
          if (index !== -1) {
            const highestIndex = Math.max(
                ...this.reportCards
                    .map(card => (typeof card.cardAttributes.i === 'number' ? card.cardAttributes.i : 0))
            );
            this.reportCards[index].previewAttributes.x = DragPos.x;
            this.reportCards[index].previewAttributes.y = DragPos.y;
            this.reportCards[index].previewAttributes.w = this.gridWidth;
            this.reportCards[index].previewAttributes.h = height;
            this.reportCards[index].previewAttributes.i = highestIndex + 1;
            this.reportCards[index].cardAttributes.x = DragPos.x;
            this.reportCards[index].cardAttributes.y = DragPos.y;
            this.reportCards[index].cardAttributes.w = width;
            this.reportCards[index].cardAttributes.h = height;
            this.reportCards[index].cardAttributes.i = highestIndex + 1;
            this.reportCards[index].name = widgetTitle;
            this.reportCards[index].cardAttributes.moved = true;
            this.reportCards[index].metrics[0] = widget;
            
            this.newlyAddedCardIndex = highestIndex + 1;
          };
          this.newlyAddedCard = { ...this.reportCards[index] };
          this.$refs.editWidgetModal.open();

          if (!this.isTemplateView) {
            this.customizeModalOpened = true;
          }
          this.$nextTick(() => {
            if (!this.isTemplateView) {
              this.$refs.customizeWidget?.initializeData?.();
            } else {
              this.$refs.customizeWidget?.setSelectedMetric?.();
            }
          });
        };
      },
      openIconsDropdown() {
        this.isIconDropdownOpen = true;
      },
      onBlur() {
        this.isIconDropdownOpen = false;
      },
      selectIcon(icon) {
        this.currentReport.iconClass = icon;
        this.isIconDropdownOpen = false;
      },
      filterTimeframe(timeframe, startDate, endDate) {
        if (timeframe === "custom_date_range") {
          this.setTimeframe({
            name: timeframe,
            startDate,
            endDate,
          });
        } else {
          this.setTimeframe(timeframe);
        }
        this.filtersUpdate();
      },
      closeDropdown() {
        this.hideAllOtherMenus();
      },
      handleModalClose() {
        const index = this.reportCards.findIndex(card => card.cardAttributes.i === this.newlyAddedCardIndex);
        if (index !== -1) {
          this.reportCards.splice(index, 1);
        }
        this.newlyAddedCardIndex = null;
      },
      exportReport() {
        this.updateReport();
        this.openPreviewModal();
      },
      togglePrintableEdit() {
        this.isEditMode = !this.isEditMode;
      },
      openPreviewModal() {
        this.previewModal = true;
        this.toggleFullPreview();
      },
      closePreviewModal() {
        this.previewModal = false;
        this.toggleFullPreview();
        document.getElementById('full_screen_preview').innerHTML = '';
      },
      toggleFullPreview() {
        this.$refs.previewModal.toggleShowPreviewModal();
        document.body.classList.toggle('full-screen-preview');
      },
      openScheduleReportModal() {
        this.$refs.saveReportModal.open();
      },
      setViewMode(mode) {
        this.currentViewMode = mode;
        this.isEditMode = false;
      },
      pageBreakLines() {
        if (this.currentViewMode !== 'printable') {
          return [];
        }

        this.updateGridHeight();
        const firstInterval = 950;
        const repeatInterval = 1000;

        const lines = [];
        let y = firstInterval;
        lines.push(y);

        while (y + repeatInterval < this.gridHeight + repeatInterval) {
          y += repeatInterval;
          lines.push(y);
        }

        return lines;
      },
      updateUnselectedFields(index, unselectedFields) {
        const cardIndex = this.currentReport.cards.findIndex(card => card.cardAttributes.i === index);
        const cardToUpdate = this.currentReport.cards[cardIndex];
        if (cardToUpdate) {
          cardToUpdate.unselectedFields = unselectedFields;
          this.$set(this.currentReport.cards, cardIndex, cardToUpdate);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .grid-container {
    user-select: none;
    border: 1px solid lightgray;
    background-color: var(--themed-box-bg);
    min-height: 45rem;
    position: relative;
    padding-bottom: 1rem;
  }

  .edit-grid-container {
    background-color: $gray-100;
    background-image: radial-gradient(circle, $gray-300 1px, rgba(0, 0, 0, 0) 1px);
    background-size: 0.75rem 0.75rem;
    padding-bottom: 5rem;
  }

  .page-break-line {
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    border-top: 3px dotted #928d8d;
    z-index: 2;
    pointer-events: none;
    overflow: hidden;
  }

  .filter-btn {
    color: #f8f9fa;
  }

  .edit-btn {
    background-color: var(--themed-light);
    color: var(--themed-dark);
    background-color: var(--themed-light);
    font-weight: 500;
  }

  .time-period-filter {
    width: 17rem;
    background-color: #5c6c80;
  }

  .date-text-color {
    color: var(--themed-very-fair);
  }

  .export-btn {
    background-color: #19427c;
    border-color: #19427c;
  }

  .name-field-width {
    width: 27rem;
  }

  .description-field-width {
    width: 40rem;
  }

  .checkmark-icon-color {
    color: green;
  }

  .primary-dashboard {
    position: relative;
    transform: scale(1) translate3d(0, 0, 0);
    transform-origin: left top;
    transition: 0.3s ;
  }

  .icon-background {
    background-color: var(--themed-dark-drawer-alt-bg);
    border-radius: 0.8rem;
  }

  .edit-report-btn {
    position: absolute;
    margin-top: 2.4rem;
    margin-left: -0.3rem;
  }

  .report-grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 4 items per row */
    gap: 1rem; /* Optional: add space between items */
  }
  .dismissible-container {
    max-width: 550px;
    background-color: white !important;
  }

  .applied-filters-container {
    display: flex;
    flex-direction: row;
  }

  .applied-filters {
    display: flex;
    flex-direction: row;
    max-width: 64rem;
    flex-wrap: wrap;
    row-gap: 10px,
  }

  .selected-btn{
    background-color: #19427c;
    color: var(--themed-light);

  }

  .btn-outline{
    background-color: var(--themed-muted);
    color: var(--themed-very-muted);
  }

  .transformation {
    transition: 0.3s ease-in-out;
  }

  .pill {
    border-radius: 2rem;
    margin: 5px;
    padding: 0px 10.5px;
    font-weight: bold;
    cursor: pointer;
  }

  .active-pill {
    background-color: $color-helpdesk;
  }

/* scoped or global */
.printable-mode .box__heading {
  flex-direction: column;
  gap: 0.75rem;
}

.printable-mode .box__heading > .d-flex.align-items-center {
  flex-direction: column;
  align-items: flex-start;
}

.printable-mode .btn-group,
.printable-mode .border.rounded-pill {
  display: flex;
  justify-content: flex-start;
}

.printable-mode .pill {
  display: inline-block;
}

.printable-mode .export-btn {
  margin-bottom: 0.3rem;
}

.adjust-view {
  align-items: flex-end;
  flex-direction: column;
  margin-top: -2.8rem;
}

.report-title-input {
  border-bottom: 1px dashed var(--themed-fair);
  font-size: 1.5rem;
  min-width: 10rem;
  max-width: 30rem;
}

.report-description-input {
  border-bottom: 1px dashed var(--themed-fair);
  font-size: 0.8rem;
  min-width: 15rem;
  max-width: 30rem;
}

</style>
<style lang="scss">
  .vue-grid-item.vue-grid-placeholder {
    background: gray;
    border-radius: 0.5rem;
  }
  .vue-grid-item>.vue-resizable-handle {
    touch-action: none;
    background-size: 0.6rem;
    padding: 0 6px 6px 0;
  }
  .vue-resizable,
  .vue-grid-item {
    touch-action: none;
  }
</style>
