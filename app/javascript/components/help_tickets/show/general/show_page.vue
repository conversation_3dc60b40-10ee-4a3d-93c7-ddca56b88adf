<template>
  <div>
    <component
      :is="selectedComponent"
      v-if="isLoaded"
    />
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapActions } from 'vuex';
import ModernHelpTicketShow from './modern_show.vue';
import HelpTicketShow from './show.vue';
import modernViewsHelper from '../../../../mixins/modern_views_helper';

export default {
  components: {
    ModernHelpTicketShow,
    HelpTicketShow,
  },
  mixins: [modernViewsHelper],
  data() {
    return {
      isModernHelpTicket: true,
      isLoaded: false,
    };
  },
  computed: {
    ...mapGetters(['isModernView', 'isModernHelpTicketViewEnabled']),
    selectedComponent() {
      if (!this.isModernHelpTicketViewEnabled) {
        return 'HelpTicketShow';
      } 
      return this.isModernHelpTicket ? 'ModernHelpTicketShow' : 'HelpTicketShow';
    },
  },
  watch: {
    isModernView(newValue) {
      this.isModernHelpTicket = newValue;
      this.setHtModernView(newValue);
    },
  },
  async created() {
    await this.isHtModernViewEnabled();
    const htStoredValue = localStorage.getItem('is-modern-help-ticket');
    if (htStoredValue === null && this.isModernHelpTicketViewEnabled && this.shouldApplyModernFeatures()) {
      localStorage.setItem('is-modern-help-ticket', String(this.isModernHelpTicket));
    } else {
      this.isModernHelpTicket = htStoredValue === 'true';
    }
    this.setHtModernView(this.isModernHelpTicket);
    this.isLoaded = true;
  },
  methods: {
    ...mapMutations(['setHtModernView']),
    ...mapActions(['isHtModernViewEnabled']),
  },
};
</script>

<style scoped>
.loading {
  text-align: center;
  padding: 1.25rem;
}
</style>
