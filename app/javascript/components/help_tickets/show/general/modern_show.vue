<template>
  <div
    :key="$route.path"
    :class="ticketShowStyles"
    class="use-temporary-modern-show-ticket-padding"
  >
    <div>
      <sweet-modal
        ref="disableTicketModal"
        v-sweet-esc
        title="Before you make any changes..."
        @close="clearCommentId"
      >
        <template #default>
          <disable-ticket-overlay
            :creator-name="creatorName"
            @go-to-comment="goToComment"
          />
        </template>
      </sweet-modal>
    </div>
    <modern-show-header
      :is-split-pane-view="isSplitpaneView"
      :is-quick-view="isQuickView && !isSplitpaneView"
      :app-sessions="appSessions"
      :avatar-session="avatarSession"
      :quick-view-ticket-id="ticketId"
      @set-active-component="setActiveComponent"
      @session="setSession"
      @all-sessions="setAllSessions"
      @close-quick-view="$emit('close-quick-view')"
    />
    <div v-if="loadingTicket">
      <help-ticket-detail-skeleton :is-quick-view="isQuickView" />
    </div>
    <div
      v-if="currentHelpTicket && !loadingTicket"
      class="row d-flex-column--medium"
      :class="{'mb-7 pb-4': isQuickView }"
    >
      <div
        v-if="!isQuickView"
        class="arrows-holder position-relative d-lg-block d-none"
      >
        <div
          v-tooltip="!showPreviousTicketBtn ? 'No more Tickets': ''"
          class="position-fixed left-arrow"
          :class="leftPositionClass"
        >
          <router-link
            :to="previousTicketUrl"
            class="text-secondary p-2 not-as-small previous"
            :class="{ 'disable-buttons': !showPreviousTicketBtn }"
            role="button"
            @click.native="onPreviousTicketChange"
          >
            <i
              v-tooltip="!previousTicket ? 'Load More Tickets': ''"
              class="nulodgicon-chevron-left"
            />
            <next-previous-ticket-preview
              v-if="previousTicket"
              :ticket="previousTicket"
              :previous-ticket="true"
            />
          </router-link>
        </div>
        <div
          v-tooltip="!showNextTicketBtn ? 'No more Tickets': ''"
          class="position-fixed right-arrow"
        >
          <router-link
            :to="nextTicketUrl"
            class="text-secondary mr-4 p-2 not-as-small next"
            :class="{ 'disable-buttons': !showNextTicketBtn }"
            role="button"
            @click.native="onNextTicketChange"
          >
            <i
              v-tooltip="!nextTicket ? 'Load More Tickets': ''"
              class="nulodgicon-chevron-right"
            />
            <next-previous-ticket-preview
              v-if="nextTicket"
              :ticket="nextTicket"
              :next-ticket="true"
            />
          </router-link>
        </div>
      </div>
      <div
        class="col-sm-12 h-100"
        :class="{ 'col-lg-8': !isQuickView }"
        :style="{
          position: !isQuickView ? 'sticky' : undefined,
        }"
      >
        <div
          class="mr-sm-0 mt-2 p-0"
          :class="{ 'box': !isQuickView }"
        >
          <div class="box_inner w-100">
            <div
              class="lower-section"
              :class="{'box p-0 mt-5 mb-3': isQuickView}"
            >
              <div
                id="active-component-nav"
                class="mt-sm-3"
                :class="{'w-100': isQuickView, 'mt-md-4': !isQuickView}"
              >
                <div
                  class="sub-menu clearfix d-flex justify-content-between"
                  :class="{ 'px-3': !isQuickView, 'pl-3': isQuickView }"
                >
                  <div class="float-left module-sub-tabs">
                    <a
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'ModernComments' }"
                      href="#"
                      @click.stop.prevent="activeComponent = 'ModernComments'"
                    >
                      Conversation
                    </a>
                    <a
                      v-if="!isBasicAccess"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'Tasks'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'Tasks'"
                    >
                      Tasks
                    </a>
                    <a
                      v-if="isWriteAny"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'TimeSpent'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'TimeSpent'"
                    >
                      Time Spent
                    </a>
                    <a
                      v-if="attachmentFields.length > 0"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'Attachments'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'Attachments'"
                    >
                      Attachments
                    </a>
                    <a
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'History'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'History'"
                    >
                      History
                    </a>
                    <a
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'ScheduledComments'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'ScheduledComments'"
                    >
                      Scheduled Comments
                    </a>
                  </div>
                  <div
                    v-if="!isQuickView"
                    class="d-flex ml-auto mb-1 align-time-box"
                  >
                    <div
                      v-if="isWriteAny && timeSpentExist"
                      v-tooltip="{ content: isQuickView ? `Time Spent: ${ticket.totalTimeSpent}`: '' }"
                      :class="{
                        'btn-link smallest text-muted px-2 border-radius-4 mr-2 clickable d-flex align-items-center ml-4 icon-button-align': !isQuickView, 
                        'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
                      }"
                      @click="openTimeSpentTab"
                    >
                      <i
                        class="genuicon-android-time align-middle"
                        :class="{ 'mr-1 mt-0.5': !isQuickView, 'not-as-small': isQuickView }"
                      />
                      <span v-if="!isQuickView">
                        <span>Time Spent:</span>
                        <span class="font-weight-semi-bold">
                          <span>{{ totalTimeSpent }}</span>
                        </span>
                      </span>
                    </div>

                    <div
                      v-if="showTimer"
                      class="smallest text-muted pl-2 pr-1 py-1 border-radius-4 bg-themed-lighter icon-button-align"
                      :class="{ 'bg-themed-lighter': !isQuickView, 'bg-themed-light': isQuickView }"
                    >
                      <div
                        class="timepicker-holder"
                        :class="{'small': isQuickView}"
                      >
                        <vue-timepicker
                          v-model="stopWatchTimer"
                          lazy
                          hide-clear-button
                          input-class="timepicker-input border-0 small timepicker-disabled-input"
                          placeholder="00:00"
                          disabled
                          :format="isRunning? 'HH:mm:ss' : 'HH:mm'"
                          @change="calculateDifference($event)"
                        />
                        <img
                          v-tooltip="'Reset stopwatch'"
                          src="https://nulodgic-static-assets.s3.amazonaws.com/images/reset_icon.png"
                          height="16"
                          class="clickable ml-3"
                          style="filter: var(--themed-light-icon-filter); opacity: 0.8"
                          @click="resetWatch"
                        >
                        <img
                          v-if="!isRunning"
                          v-tooltip="'Start timer'"
                          src="https://nulodgic-static-assets.s3.amazonaws.com/images/play_icon.png"
                          height="18"
                          class="clickable ml-1"
                          @click="toggleRunning"
                        >
                        <img
                          v-else
                          v-tooltip="'Stop timer'"
                          src="https://nulodgic-static-assets.s3.amazonaws.com/images/stop_icon.png"
                          height="18"
                          class="clickable ml-1"
                          @click="toggleRunning"
                        >
                      </div>
                    </div>

                    <div
                      v-if="!showTimer && isWriteOrScopedPermission"
                      :class="{
                        'btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm bg-gray text-white d-flex align-items-center justify-content-center mt-n0.5  w-100 px-2 icon-button-align button-width': !isQuickView,
                        'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs bg-gray text-white d-inline-flex justify-content-center align-items-center ml-1': isQuickView
                      }"
                      @click="toggleTimer"
                    >
                      <img
                        v-tooltip="'Run stopwatch to record time spent'"
                        src="https://nulodgic-static-assets.s3.amazonaws.com/images/stopwatch_icon.png"
                        :class="{ 'mr-1' : !isQuickView }"
                        :height="isQuickView ? 14 : 18"
                        class="filter-white"
                      >
                      <span class="text-white">Track Time</span>
                    </div>
                    <div
                      :class="{
                        'btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary resource-button-height': !isQuickView, 
                        'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
                      }"
                      @click="openArticleModal"
                    >
                      <img
                        v-tooltip="'Resources'"
                        src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/genuity-resources.svg"
                        class="light-icon-filter"
                        :class="{ 'mb-1': !isQuickView }"
                        :width="isQuickView ? 16 : 18"
                      >
                    </div>
                    <div
                      v-if="displayAiSummary"
                      class="btn btn-link btn-flat btn-icon-circle text-secondary ml-2"
                      :class="ticketSummaryClasses"
                      @click="openAiModal"
                    >
                      <img
                        v-tooltip="'Generate Ticket Summary via AI'"
                        src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/ai_images/ai-tooling.svg"
                        :class="{
                          'ml-0.5 mb-1': !isQuickView,
                          'light-icon-filter': isDarkMode,
                        }"
                        :width="isQuickView ? 16 : 20"
                      >
                    </div>
                    <span
                      v-if="displayAiSummary && !isTicketSummarySeen"
                      class="notification-indicator"
                      :class="{ 'indicator-position' : !isQuickView }"
                    />
                    <resource-select ref="articleSelect" />
                    <ticket-summary
                      v-if="displayAiSummary"
                      ref="ticketSummaryModal"
                      :ticket-id="currentHelpTicket.id"
                    />
                  </div>
                </div>
              </div>

              <div
                class="p-3 bg-themed-box-bg rounded-bottom" 
                :class="{
                  'box_inner w-100': isQuickView,
                  'show-section': hideHeader && activeComponent !== 'ModernComments',
                  'adjust-section': !hideHeader && activeComponent !== 'ModernComments',
                  'dynamic-container': activeComponent !== 'ModernComments'
                }"
              >
                <component
                  :is="activeComponent"
                  :new-comment-id="activeComponent == 'ModernComments' ? newCommentId : null"
                  :scheduled-comment-id="activeComponent === 'ScheduledComments' ? scheduledCommentID : null"
                  :quick-view-ticket-id="ticketId"
                  :is-help-ticket-show="true"
                  :is-quick-view="isQuickView && !isSplitpaneView"
                  @show-scheduled-comments="activeComponent = 'ScheduledComments'"
                  @go-to-comment="goToComment"
                  @open-modal="openModal"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="col-sm-12 h-100 mt-1 overflow-auto"
        :class="{ 
          'col-lg-4 pl-5': !isQuickView,
        }"
        :style="sectionStyle"
      >
        <div class="pb-1 position-relative bg-light mt-2 rounded border">
          <div class="d-flex justify-content-between bg-biscay-blue rounded-top text-white px-4 py-2">
            <span
              class="p-0"
            >
              Ticket #{{ currentHelpTicket.ticketNumber }}
            </span>
            <div class="d-flex align-items-end smallest">
              <span class="p-0">
                Created
                <span class="font-weight-semi-bold">
                  <span>{{ dateTimeFormatting('createdAt')[1] }}</span>
                </span>
              </span>
            </div>
          </div>
          <div class="px-4 pt-2">
            <survey-field
              v-if="showSurveyField"
              :ticket="currentHelpTicket"
            />

            <div
              v-if="mergeParent"
              class="row mt-2"
            >
              <div class="col-auto">
                <i
                  class="genuicon-loop-alt3 text-muted"
                  style="font-size: 1.5rem"
                />
              </div>
              <div class="col pl-0 text-secondary not-as-small">
                <label class="mb-0 align-sub">
                  Merged with:
                </label>
                <div
                  class="hoverable align-sub ml-1 font-weight-semi-bold"
                  @click="openMergedParentTicket"
                >
                  <span>#{{ mergeParent.ticketNumber }}</span>
                  <span>{{ mergeParent.subject }}</span>
                </div>
              </div>
            </div>

            <merged-ticket
              :is-quick-view="isQuickView"
              :merged-tickets="mergedTickets"
              :is-help-ticket-show="true"
              class="my-2"
              @unmerge="unmergeTicket"
              @set-quick-view-ticket-id="setQuickViewTicket"
            />

            <ul v-if="currentHelpTicket.slaResponse">
              <li
                v-if="currentHelpTicket.firstResponseTime && timePassed(currentHelpTicket.firstResponseTime)"
                class="mb-3 text-secondary not-as-small"
              >
                <span
                  class="custom-legend bullet-color"
                  :class="{ 'bullet-green' : currentHelpTicket.firstResponseReceived }"
                />
                First Response due
                {{ showDateTime(currentHelpTicket.firstResponseTime) }}
              </li>
              <li
                v-if="currentHelpTicket.resolutionTime && timePassed(currentHelpTicket.resolutionTime)"
                class="text-secondary not-as-small"
              >
                <span 
                  class="custom-legend bullet-color"
                  :class="{ 'bullet-green' : currentHelpTicket.ticketResolved }"
                />
                Resolution due
                {{ showDateTime(currentHelpTicket.resolutionTime) }}
              </li>
            </ul>
            <div
              v-for="(formField, idx) in basicFields"
              :key="`field-${idx}`"
            >
              <div
                v-if="formField.label === 'Assigned To'"
              >
                <div>
                  <field-renderer
                    class="mb-5"
                    :form-field="formField"
                    :object="currentHelpTicket"
                    :active-dropdown="activeDropdown"
                    :is-click-allowed="true"
                    :is-help-ticket-show="true"
                    show-label
                    @update-field="updateFormField"
                    @dropdown-toggled="setActiveDropdown"
                  />
                </div>
              </div>
              <div v-else>
                <field-renderer
                  class="mb-3"
                  :form-field="formField"
                  :object="currentHelpTicket"
                  :active-dropdown="activeDropdown"
                  :is-click-allowed="true"
                  :is-help-ticket-show="true"
                  show-label
                  @update-field="updateFormField"
                  @dropdown-toggled="setActiveDropdown"
                />
              </div>
            </div>
            <div class="d-flex p-0 col-12 mb-3 mb-md-3 mb-sm-2">
              <span class="py-2 px-0 col-4 text-secondary not-as-small">
                Created On
              </span>
              <span class="p-2 col-md-8 rounded-pill pills-bg font-weight-semi-bold not-as-small">
                <span>
                  {{ dateTimeFormatting('createdAt')[0] }}
                  <span 
                    v-if="currentHelpTicket['createdAt']"
                    class="text-secondary smallest"
                  > 
                    • {{ dateTimeFormatting('createdAt')[1] }}
                  </span>
                </span>
              </span>
            </div>

            <div class="d-flex p-0 col-12 mb-3 mb-md-3 mb-sm-2">
              <span class="py-2 px-0 col-4 text-secondary not-as-small">
                First Response
              </span>
              <span class="p-2 col-md-8 rounded-pill pills-bg font-weight-semi-bold not-as-small">
                <span>
                  {{ dateTimeFormatting('firstResponseAt')[0] }}
                  <span
                    v-if="currentHelpTicket['firstResponseAt']"
                    class="text-secondary  smallest"
                  > 
                    • {{ dateTimeFormatting('firstResponseAt')[1] }}
                  </span>
                </span>
              </span>
            </div>

            <div class="d-flex p-0 col-12 mb-3 mb-md-3 mb-sm-2">
              <span class="py-2 px-0 col-4 text-secondary not-as-small">
                Source
              </span>
              <span class="p-2 col-md-8 rounded-pill pills-bg font-weight-semi-bold not-as-small">
                <span>{{ snakeToHumanize(currentHelpTicket.source) }}</span>
              </span>
            </div>

            <div class="container-fields pills-bg rounded px-2 py-1 mb-3">
              <div class="d-flex state-align flex-wrap">
                <div
                  v-for="(formField, idx) in flagFields"
                  :key="`field-${idx}`"
                >
                  <field-renderer
                    class="rounded ml-1 mt-1"
                    :class="formField.label === 'Status' ? 'bg-green-subtle' : 'pills-bg'"
                    :form-field="formField"
                    :object="currentHelpTicket"
                    :active-dropdown="activeDropdown"
                    :is-click-allowed="true"
                    :show-label="false"
                    :object-class="''"
                    :is-help-ticket-show="true"
                    @update-field="updateFormField"
                    @dropdown-toggled="setActiveDropdown"
                  />
                </div>
              </div>
              <div class="status-container">
                <span class="rounded bg-cyan-subtle font-weight-semi-bold not-as-small status-padding">
                  <img 
                    src="https://cdn-icons-png.flaticon.com/512/2961/2961948.png"
                    height="16"
                    class="mr-1"
                  >
                  <span class="text-color-black">Updated {{ dateTimeFormatting('updatedAt')[0] }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="pb-1 position-relative mt-4 rounded border px-4 pt-2">
          <label class="d-flex border-bottom my-3 pb-1 align-sub">
            Additional Ticket Info
          </label>
          <div
            class="form-style text-secondary module-breadcrumb small rounded-pill bg-themed-vertical-nav-hover-link"
            @click.stop="openCustomFormModal"
          >
            <span>
              <i
                class="genuicon-build-custom-forms mr-1"
              />
              <span class="font-weight-semi-bold">{{ form.formName }} </span></span>
            <span class="material-symbols-outlined true-small font-weight-bold ml-2">
              <i class="genuicon-up-down-arrow"/>
            </span>
          </div>
          <div
            v-for="(formField, idx) in additionalRightFields"
            :key="`field-${idx}`"
          >
            <field-renderer
              class="mb-3"
              :form-field="formField"
              :object="currentHelpTicket"
              :active-dropdown="activeDropdown"
              :is-click-allowed="true"
              :is-help-ticket-show="true"
              :is-modern-additional-field="true"
              show-label
              @update-field="updateFormField"
              @dropdown-toggled="setActiveDropdown"
            />
          </div>

          <universal-links
            v-if="form"
            class="mt-4"
            :custom-form="form"
            :universal-links="universalLinks"
            :label-classes="['text-secondary not-as-small']"
            :is-scope-creator="isScopeCreator || isScopeAssign"
            :source-id="currentHelpTicket.id"
            @remove="removeLink"
            @add="addLink"
          />
          <move-ticket-modal
            ref="moveTicketModal"
            class="ticket-modal"
            :is-custom-form-fields="true"
            :forms="customForms"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import _cloneDeep from 'lodash/cloneDeep';
  import Pusher from 'common/pusher';
  import _debounce from 'lodash/debounce';
  import _get from 'lodash/get';
  import _sortBy from 'lodash/sortBy';
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';

  import channelCleanup from "mixins/custom_forms/channel_cleanup";
  import customForms from 'mixins/custom_forms';
  import universalLink from "mixins/universal_link";
  import companyChannel from 'mixins/company_channel';
  import suggestions from 'mixins/automated_tasks/suggestions';
  import inflections from "mixins/inflections";
  import customFormFields from 'mixins/custom_forms/fields';
  import permissionsHelper from "mixins/permissions_helper";
  import sortHelper from 'mixins/sorting_helper';
  import helpTickets from 'mixins/help_ticket';
  import MomentTimezone from 'mixins/moment-timezone';
  import string from 'mixins/string';
  import VueTimepicker from 'vue2-timepicker';

  import customFormHelper from "mixins/custom_form_helper";
  import dates from 'mixins/dates';
  import http from "common/http";
  import ticketHelper  from 'mixins/ticket_helper';
  import { removeUnusedAttachments } from 'mixins/trix_vue_helper';
  import subscription from '../../../../stores/mixins/subscription';
  import ModernShowHeader from '../modern_show_header.vue';
  import History from '../history/index.vue';
  import HelpTicketDetailSkeleton from '../../loading_states/help_ticket_detail_skeleton.vue';
  import ModernComments from '../comments/modern_index.vue';
  import Tasks from '../project_tasks/index.vue';
  import TimeSpent from '../time_spent/index.vue';
  import Attachments from '../attachments/index.vue';
  import NextPreviousTicketPreview from './next_previous_ticket_preview.vue';
  import MergedTicket from './merged_ticket.vue';
  import FieldRenderer from '../../../shared/custom_forms/renderer.vue';
  import InlineAssignedDropdown from '../../../shared/inline_assigned_dropdown.vue';
  import UniversalLinks from '../../../shared/universal_link/universal_links.vue';
  import SurveyField from '../../closing_surveys/field.vue';
  import DisableTicketOverlay from '../disable_ticket_overlay.vue';
  import peopleList from "../../../shared/custom_forms/field_renderer/people_list.vue";
  import MoveTicketModal from '../move_ticket_modal.vue';
  import ResourceSelect from '../resource_select.vue';
  import ArticleSelect from '../../../article_select.vue';
  import ScheduledComments from '../comments/scheduled_comments.vue';
  import TicketSummary from '../ticket_summary.vue';


  const ADDITIONAL_SORT_COLUMNS = ["ticket_number", "comment_count", "created_at", "total_time_spent", "updated_at", "workspace_name"];

  export default {
    components: {
      Attachments,
      ModernComments,
      Tasks,
      History,
      TimeSpent,
      FieldRenderer,
      MergedTicket,
      ModernShowHeader,
      SurveyField,
      UniversalLinks,
      SweetModal,
      DisableTicketOverlay,
      HelpTicketDetailSkeleton,
      NextPreviousTicketPreview,
      VueTimepicker,
      InlineAssignedDropdown,
      peopleList,
      MoveTicketModal,
      ResourceSelect,
      ArticleSelect,
      ScheduledComments,
      TicketSummary,
    },
    mixins: [
      subscription,
      channelCleanup,
      companyChannel,
      customForms,
      customFormFields,
      inflections,
      permissionsHelper,
      sortHelper,
      customFormHelper,
      suggestions,
      universalLink,
      dates,
      ticketHelper,
      removeUnusedAttachments,
      helpTickets,
      MomentTimezone,
      string,
    ],
    provide: {
      showUniversalLinkBeforeMenu: false,
    },
    props: {
      isQuickView: {
        type: Boolean,
        default: false,
      },
      isSplitpaneView: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        activeComponent: "ModernComments",
        listenerUp: false,
        newCommentId: null,
        creatorName: '',
        nextTicket: null,
        previousTicket: null,
        previousChange: false,
        nextChange: false,
        activeDropdown: '',
        subscribedChannels: [],
        appSessions: [],
        avatarSession: null,
        showTimer: false,
        isRunning: false,
        startTime: null,
        stopWatchTimer: null,
        timer: null,
        elapsedTime: 0,
        stopWatch: null,
        windowWidth: window.innerWidth,
        scheduledCommentID: null,
        isTicketSummarySeen: false,
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicket',
        'currentHelpTicketDraft',
        'enableTicketDrafts',
        'isTicketDraftEmpty',
        'tickets',
        'loadingTicket',
        'showPreviousTicketBtn',
        'showNextTicketBtn',
        'pageCount',
        'page',
        'loadingStatus',
        'shouldUserRedirectToTickets',
        'currentWorkspace',
        'displayAiSummary',
        'hideHeader',
      ]),
      ...mapGetters('customForms', ['customForms']),
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      sectionStyle() {
        const width = this.windowWidth;
        let baseVH = 100;

        if (width >= 2100) {
          baseVH = 113;
        } else if (width >= 1900) {
          baseVH = 124;
        } else if (width >= 1675) {
          baseVH = 128;
        } else if (width >= 1450) {
          baseVH = 132;
        } else if (width >= 1300) {
          baseVH = 134;
        } else if (width >= 1200) {
          baseVH = 138;
        } else {
          baseVH = 130;
        }

        if (this.hideHeader) {
          baseVH += 7;
        }

        return {
          maxHeight: `calc(${baseVH}vh - 30rem)`,
        };
      },
      mergedTickets() {
        return this.currentHelpTicket.mergedTickets;
      },
      totalTimeSpent() {
        return this.ticket?.totalTimeSpent ?? ''; 
      },
      ticketShowStyles() {
        return `${!this.isQuickView ? 'mt-3 ' : ''}${this.isOnlyBasicRead ? 'mt-6 px-6' : ''}`;
      },
      nextTicketUrl() {
        if (!(this.tickets && this.tickets.length) || !(this.currentHelpTicket && this.currentHelpTicket.id)) {
          return "";
        }
        const index = this.currentTicketIndex;
        if (index < (this.tickets.length - 1)) {
          this.setNextTicket(this.tickets[index + 1]);
        } else {
          this.setNextTicket(null);
        }
        return "";
      },
      previousTicketUrl() {
        if (!(this.tickets && this.tickets.length) || !(this.currentHelpTicket && this.currentHelpTicket.id)) {
          return "";
        }
        const index = this.currentTicketIndex;
        if (index > 0) {
          this.setPreviousTicket(this.tickets[index - 1]);
        } else {
          this.setPreviousTicket(null);
        }
        return "";
      },
      showSurveyField() {
        const collectClosingSurvey = _get(this, 'form.moduleForm.collectClosingSurvey');
        if (!collectClosingSurvey) {
          return false;
        }
        let values = this. getValuesForName(this.currentHelpTicket, 'status');
        if (!values || values.length === 0) {
          return false;
        }
        if (values[0].valueStr !== 'Closed') {
          return false;
        }
        values = this. getValuesForName(this.currentHelpTicket, 'created_by');
        if (!values || values.length === 0) {
          return false;
        }
        if (values[0].valueInt !== this.$currentContributorId) {
          return false;
        }
        return true;
      },

      mergeParent() {
        const parentId = _get(this, 'currentHelpTicket.mergeParent.id');
        if (parentId) {
          return this.currentHelpTicket.mergeParent;
        }
        return null;
      },
      universalLinks() {
        return this.currentHelpTicket.universalLinks;
      },
      form() {
        if (this.currentHelpTicket) {
          return this.currentHelpTicket.customForm;
        }
        return null;
      },
      leftFields() {
        return this.fields('left').filter(
          (field) => !['Description'].includes(field.label)
        );
      },
      additionalRightFields() {
        const excludedFields = ['Priority', 'Status', 'Created By', 'Assigned To'];

        const allFields = this.fields('left')
          .concat(this.fields('right'))
          .filter(field => field.label !== 'Description');

        return allFields.filter(field => !excludedFields.includes(field.label));
      },
      basicFields() {
        const includedFields = ['Created By', 'Assigned To'];
        return this.fields('right')
          .filter((field) => includedFields.includes(field.label))
          .sort(
            (a, b) =>
              includedFields.indexOf(a.label) - includedFields.indexOf(b.label)
          );
      },
      flagFields() {
        const includedFields = ['Status', 'Priority'];

        return this.fields('right')
          .filter((field) => includedFields.includes(field.label))
          .sort(
            (a, b) =>
              includedFields.indexOf(a.label) - includedFields.indexOf(b.label)
          );
      },
      attachmentFields() {
        return this.getFieldsByType(this.currentHelpTicket, "attachment");
      },
      isScopeCreator() {
        const values = this.getValuesForName(this.currentHelpTicket, 'created_by');
        if (values[0] && values[0].valueInt === this.$currentContributorId) {
          return true;
        };
        return false;
      },
      selectedTicketId() {
        return this.isQuickView ? this.$route.query?.selectedTicket : this.$route.params?.id;
      },
      isScopeAssign() {
        const values = this.getValuesForName(this.currentHelpTicket, 'assigned_to');
        let assigned = false;
        values.forEach(assignedUser => {
          if (assignedUser.valueInt === this.$currentContributorId) {
            assigned = true;
          };
        });
        return assigned;
      },
      currentTicketIndex() {
        return this.tickets.findIndex(x => x.id === this.currentHelpTicket.id);
      },
      timeSpentExist() {
        return this.ticket.totalTimeSpent !== '0 hr 0 min';
      },
      isWriteOrScopedPermission() {
        return this.isWrite || this.isSpecificPermission('scoped');
      },
      ticket() {
        return this.currentHelpTicket;
      },
      ticketSummaryClasses() {
        if (!this.isQuickView) {
          return 'btn-icon-circle-sm resource-button-height';
        }
        return 'btn-icon-circle-xs ml-1 d-inline-flex justify-content-center align-items-center';
      },
    },
    watch: {
      elapsedTime() {
        this.stopWatchTimer = new Date(this.elapsedTime).toISOString().substr(11, 8);
      },
    },
    beforeMount(){
      this.setLoadingTicket(true);
    },
    mounted() {
      window.addEventListener('beforeunload', this.beforeWindowUnload);
      this.setupPusherListeners();
    },
    updated() {
      if (this.currentHelpTicket) {
        const index = this.currentTicketIndex;
        if (!this.loadingStatus && this.previousChange) {
          this.changeTicket(this.tickets.length - 1);
          this.previousChange = false;
        }
        if (!this.loadingStatus && this.nextChange) {
          this.changeTicket(0);
          this.nextChange = false;
        }
        if (index !== -1) {
          this.checkTicketindex(index);
        }
      }
    },
    beforeDestroy() {
      if (this.enableTicketDrafts) {
        this.$store.dispatch("handleTicketDraft");
      }
      window.removeEventListener('beforeunload', this.beforeWindowUnload);
      this.handleEmptyDraft();
      if (this.avatarSession || !this.$superAdminUser || this.$currentUserId) {
        this.destroyTicketSession();
      }
      this.unbindAll(this.subscribedChannels);
      this.subscribedChannels = [];
      window.removeEventListener('resize', this.updateWindowWidth);
    },
    created() {
      window.addEventListener('resize', this.updateWindowWidth);
    },
    methods: {
      ...mapMutations([
        'addUniversalLinkToTicket',
        'removeComment',
        'removeUniversalLinkToTicket',
        'setActionSuggestion',
        'setCurrentHelpTicket',
        'setCurrentHelpTicketDraft',
        'setSortColumn',
        'setSortDirection',
        'setSortType',
        'setLoadingTicket',
        'setPreviousBtn',
        'setNextBtn',
        'setPage',
        'setWorkspaceFilter',
        'setShouldUserRedirectToTickets',
        'setQuickViewTicketId',
      ]),
      ...mapActions([
        'createWindowSession',
        'fetchComment',
        'fetchFormValue',
        'fetchCustomEmails',
        'destroyTicketSession',
        'fetchCompanyUserOptions',
      ]),
      ...mapActions('GlobalStore', ['fetchCurrentCompanyUser']),

      dateTimeFormatting(value) {
        if (this.currentHelpTicket && this.currentHelpTicket[value]) {
          return [moment(this.currentHelpTicket[value]).fromNow(), this.timezoneDatetimeMoment(this.currentHelpTicket[value], Vue.prototype.$timezone)];
        }
        return ['No record'];
      },
      onWorkspaceChange() {
        window.addEventListener('resize', this.updateWindowWidth);
        if (!this.ticketId) {
          return;
        }
        const commentId = this.$route.query.comment_id;
        if (commentId) {
          this.handleScheduledComment(commentId);
        }
        this.setDefaultSort();
        this.fetchCompanyUserOptions({ archived_users_only : true });
        this.setCurrentHelpTicket(null);
        this.refreshTicket().then(() => {
          if (this.tickets.length === 0) {
            this.$store.dispatch('fetchTickets');
          };
          if (!this.currentCompanyUser) {
            this.fetchCurrentCompanyUser({ fetch_permission: false });
          };
          if (this.currentHelpTicket) {
            this.updateTicketSeen();
          };
        });
        this.loadDraft();
        this.createWindowSession();
        this.isTicketSummarySeen = this.$hasSeenModuleWalkthrough.ticket_ai_summary;

        this.$store.dispatch("fetchAgentCheck");
        this.$store
          .dispatch("fetchTasks", this.ticketId)
          .catch(err => {
            this.emitError(`Sorry, there was an error loading tasks. ${err.response.data.message}`);
          });
        this.setActionSuggestion(null);
        this.fetchCustomEmails();
        this.populateStopWatch();
        if (!this.$superAdminUser) {
          this.setupListeners();
        }
      },
      setupListeners() {
        if (this.$pusher && this.selectedTicketId) {
          const channel = this.$pusher.subscribe(this.selectedTicketId);
          channel.bind(`app-session-deleted`, data => {
            this.removeAppSession(data);
          }, this);
          channel.bind(`app-session-created`, data => {
            this.addAppSession(data);
          }, this);
        }
      },
      handleScheduledComment(commentId) {
        this.activeComponent = 'ScheduledComments';
        this.scheduledCommentID = parseInt(commentId, 10);
      },
      onPreviousTicketChange() {
        const index = this.currentTicketIndex;
        if (this.enableTicketDrafts) {
          this.$store.dispatch("handleTicketDraft");
        }
        if (index === 0 && this.page > 0 && !this.previousChange) {
          this.setPage(this.page - 1);
          this.loadDraft();
          this.$store.dispatch('fetchTickets');
          this.previousChange = true;
        } else if (this.tickets[index - 1]) {
          this.$router.replace(`/${this.tickets[index - 1].id}`);
          this.loadDraft();
          this.loadHelpTicket();
        }
        this.handleEmptyDraft();
      },
      setActiveDropdown(field) {
        this.activeDropdown = field;
      },
      setNextTicket(ticket) {
        this.nextTicket = ticket;
      },
      setPreviousTicket(ticket) {
        this.previousTicket = ticket;
      },
      onNextTicketChange() {
        const index = this.currentTicketIndex;
        if (this.enableTicketDrafts) {
          this.$store.dispatch("handleTicketDraft");
        }
        if (index === (this.tickets.length - 1) && this.page < this.pageCount - 1 && !this.nextChange) {
          this.setPage(this.page + 1);
          this.loadDraft();
          this.$store.dispatch('fetchTickets');
          this.nextChange = true;
        } else if (this.tickets[index + 1]) {
          this.$router.replace(`/${this.tickets[index + 1].id}`);
          this.loadDraft();
          this.loadHelpTicket();
        }
        this.handleEmptyDraft();
      },
      openMergedParentTicket() {
        if (this.isQuickView) {
          this.setQuickViewTicket(this.mergeParent.id);
        } else {
          window.open(`/help_tickets/${this.mergeParent.id}`);
        }
      },
      setQuickViewTicket(ticketId) {
        this.setQuickViewTicketId(ticketId);
        this.loadHelpTicket();
      },
      loadHelpTicket() {
        this.setCurrentHelpTicket(null);
        this.setLoadingTicket(true);
        this.$store.dispatch("fetchTicket", this.ticketId);
      },
      loadDraft() {
        this.setCurrentHelpTicketDraft({
          helpTicketId: this.ticketId,
          workspaceId: null,
          companyId: null,
          companyUserId: this.currentCompanyUser?.id || this.$currentCompanyUserId,
          fieldsData: {},
          comments: {},
          timeSpents: {},
          tasksData: {},
        });
        this.$store.dispatch("fetchTicketDraft", this.ticketId);
      },
      beforeWindowUnload() {
        if (this.enableTicketDrafts) {
          this.$store.dispatch("handleTicketDraft");
        }
      },
      checkTicketindex(idx) {
        if ((this.tickets.length) === 1 && this.pageCount === 1) {
          this.setPreviousBtn(false);
          this.setNextBtn(false);
        } else if (idx === 0 && this.page === 0) {
          this.setPreviousBtn(false);
          this.setNextBtn(true);
        } else if (idx === (this.tickets.length - 1) && this.page === this.pageCount - 1) {
          this.setNextBtn(false);
          this.setPreviousBtn(true);
        } else if (idx >= 0 && idx < this.tickets.length) {
          this.setPreviousBtn(true);
          this.setNextBtn(true);
        }
      },
      changeTicket(idx) {
        this.$router.push(`/${this.tickets[idx].id}`);
        this.loadHelpTicket();
      },
      setDefaultSort() {
        this.activeSort = JSON.parse(localStorage.getItem('active_sort'));
        if (this.activeSort && !ADDITIONAL_SORT_COLUMNS.includes(this.activeSort)) {
          this.activeSortType = JSON.parse(localStorage.getItem('active_sort_type'));
        }
        if (!this.activeSort) {
          this.activeSort = 'priority';
          this.activeSortType = 'priority';
        }
        this.activeSortDirection = JSON.parse(localStorage.getItem('active_sort_direction'));
        if (!this.activeSortDirection) this.activeSortDirection = 'desc';

        this.setSortColumn(this.activeSort);
        this.setSortDirection(this.activeSortDirection);
        this.setSortType(this.activeSortType);
      },
      loadTickets() {
        if (this.tickets.length === 0) {
          this.refreshTickets();
        }
      },
      resetWatch() {
        if (this.elapsedTime) {
          this.isRunning = false;
          this.startTime = null;
          this.stopWatchTimer = null;
          this.elapsedTime = 0;
          clearInterval(this.timer);
          this.isSamePage = !this.isSamePage;
          if (this.stopWatch) {
            http
              .delete(`/tickets/${this.currentHelpTicket.id}/stop_watch_timers/${this.stopWatch.id}`)
              .catch(() => {
                this.emitError(`Sorry, an error occured, try again.`);
              });
          }
        };
      },
      populateStopWatch() {
        const params = {
          company_id: this.currentCompany,
        };
        http.get(`/tickets/${this.ticketId}/stop_watch_timers.json`, { params })
          .then(res => {
            this.stopWatch = res.data;
            if (this.stopWatch && !this.isSamePage) {
              this.setTimeAttributes();
            }
          });
      },
      calculateDifference(event) {
        const hours = Number(event.data.HH);
        const minutes = Number(event.data.mm);
        const seconds = Number(event.data.ss);
        this.calculateElapsedTime(hours, minutes, seconds);
      },
      setTimeAttributes() {
        if (this.$currentCompanyUserId === this.stopWatch.companyUserId) {
          this.isSamePage = !this.isSamePage;
          this.isRunning = this.stopWatch.isStartTime;
          this.startTime = this.stopWatch.startTime;
          this.showTimer = true;
          this.endTime = moment(new Date()).format('HH:mm:ss');

          const timeDifference = moment.utc(moment(this.endTime, "HH:mm:ss").diff(moment(this.startTime, "HH:mm:ss"))).format("HH:mm:ss").split(':');

          const hours = timeDifference[0];
          const minutes = timeDifference[1];
          const seconds = timeDifference[2];

          this.calculateElapsedTime(hours, minutes, seconds);
          this.startInterval();
        }
      },
      toggleRunning() {
        this.isRunning = !this.isRunning;

        if (this.isRunning) {
          this.isSamePage = !this.isSamePage;
          this.setStartedTime();
          this.startInterval();
        } else {
          this.saveComment();
        };
      },
      startInterval() {
        this.timer = setInterval(() => {
          this.elapsedTime += 1000;
        }, 1000);
      },
      setStartedTime() {
        this.startTime = moment(new Date()).format('HH:mm:ss');
        const stopWatch = { company_user_id: this.$currentCompanyUserId, start_time: this.startTime, end_time: null, is_start_time: true };

        http
          .post(`/tickets/${this.currentHelpTicket.id}/stop_watch_timers.json`, { stopWatch, company_id: this.currentHelpTicket.company.id })
          .then(() => {
            this.populateStopWatch();
          })
          .catch(() => {
            this.emitError(`Sorry, an error occured. Try Again.`);
          });
      },
      calculateElapsedTime(hours, minutes, seconds) {
        this.elapsedTime = (hours * 60 * 60 * 1000) + (minutes * 60 * 1000) + (seconds * 1000);
      },
      toggleTimer() {
        this.showTimer = !this.showTimer;
      },
      openTimeSpentTab() {
        this.$emit('set-active-component', 'TimeSpent');
      },
      saveComment() {
        const params = {
          help_ticket_comment:
          {
            commentBody: null, help_ticket_id: this.ticket.id, contributorId: this.$currentContributorId,
            timeSpent: {
              id: null,
              companyUserId: this.$currentCompanyUserId,
              endTime: moment(new Date()).format('HH:mm'),
              startTime: this.startTime.substr(0,5),
              timeSpent: moment.utc(moment(moment(new Date()).format('HH:mm:ss'), "HH:mm:ss").diff(moment(this.startTime, "HH:mm:ss"))).format("HH:mm:ss"),
              startedAt: new Date(),
            },
          },
          company_id: this.ticket.company.id,
        };
        http
          .post(`/ticket_comments.json`, params)
          .then(() => {
            this.resetWatch();
            this.emitSuccess(`Time spent entry added`);
            this.$store.dispatch('fetchTimeSpents', this.ticketId);
            this.$store.dispatch('fetchTicket', this.ticketId);
            this.$store.dispatch('fetchComments', this.ticketId);
          })
          .catch(() => {
            this.emitError('Sorry, there was an error adding the comment.');
          });
      },
      setupPusherListeners() {
        Pusher.then(() => {
          if (this.$pusher && this.currentHelpTicket && !this.listenerUp) {
            this.listenerUp = true;

            const channelId = `help_ticket=${this.currentHelpTicket.guid}`;
            const channel = this.$pusher.subscribe(channelId);
            this.subscribedChannels.push(channel);

            // Originally we were passing the values via pusher, BUT...
            // there is a limit on the size of the message you can send via Pusher (4k)
            // so we have to just pass the id's and get the values here
            channel.bind(`form-field`, data => {
              if (this.shouldUserRedirectToTickets) {
                this.$router.push("/");
                this.setShouldUserRedirectToTickets(false);
                return;
              }

              if (!data || !this.currentHelpTicket ) {
                return;
              }

              this.fetchFormValue(data.id).then(() => this.refreshActivities());
            }, this);
            channel.bind(`ticket-due-time`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.currentHelpTicket.resolutionTime = data.resolution_time;
              this.currentHelpTicket.firstResponseTime = data.first_response_time;
              this.currentHelpTicket.firstResponseReceived = data.first_response_received;
              this.currentHelpTicket.ticketResolved = data.ticket_resolved;
            }, this);

            channel.bind(`comment-added`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              if (this.currentHelpTicket.id === data.helpTicketId) {
                if (data.creator_ids && !data.creator_ids.includes(this.$currentContributorId)) {
                  this.newCommentId = data.id;
                  this.creatorName = data.creator_name;
                  if (this.activeComponent !== 'ModernComments') {
                    this.$refs.disableTicketModal.open();
                  }
                }
                this.fetchComment(data);
              }
            }, this);

            channel.bind(`comment-deleted`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.removeComment(data);
            }, this);

            channel.bind(`universal-link-added`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.addUniversalLinkToTicket(data);
              this.refreshActivities();
            }, this);

            channel.bind(`universal-link-deleted`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.removeUniversalLinkToTicket(data);
              this.refreshActivities();
            }, this);
          }
        });
      },
      removeAppSession(data) {
        const storage = window.sessionStorage;
        if (data.helpTicketId.toString() !== this.selectedTicketId) {
          return;
        }
        const appSession = this.appSessions.find(s => s.requestSessionId === data.requestSessionId);
        if (appSession && appSession.windowGuid !== storage.getItem("windowGuid")) {
          const idx = this.appSessions.indexOf(appSession);
          if (idx > -1) {
            this.appSessions.splice(idx, 1);
          }
        }
      },
      addAppSession(data) {
        if (data.helpTicketId.toString() !== this.selectedTicketId) {
          return;
        }
        const oldValue = this.appSessions.find(s => s.companyUserId === data.companyUserId && s.helpTicketId === data.helpTicketId);
        if (oldValue && oldValue.sessionId !== data.sessionId) {
          const idx = this.appSessions.indexOf(oldValue);
          this.appSessions.splice(idx, 1);
        } else if (!oldValue) {
          this.appSessions.push(data);
        }
        if (data.sessionId === this.$sessionId) {
          this.avatarSession = data;
        }
      },
      setAllSessions(sessions) {
        this.appSessions = sessions;
      },
      setSession(session) {
        this.avatarSession = session;
      },
      updateTicketSeen() {
        if (this.isBold || !this.currentCompanyUser) {
          const params = {
            help_ticket: this.currentHelpTicket.id,
            company_id: this.currentHelpTicket.company.id,
            user_matched: this.isBold,
          };
          http
            .put(`/tickets/${this.currentHelpTicket.id}/update_ticket_seen_status.json`, params)
            .catch(() => {
              this.emitError('An error occurred while updating ticket seen status.');
            });
        }
      },
      refreshTicket() {
        return this.$store.dispatch("fetchTicket", this.ticketId, true)
          .then(() => {
            this.setupPusherListeners();
            if (!this.currentWorkspace) {
              const workspace = getWorkspaceFromStorage();
              if (this.currentHelpTicket && workspace && workspace.id !== this.currentHelpTicket.workspaceId) {
                if ($workspaceSelectedFromDropdown) {
                  this.$router.push("/");
                } else {
                  setWorkspaceToStorage(this.currentHelpTicket.workspaceJson);
                  this.setWorkspaceFilter(this.currentHelpTicket.workspaceJson);
                  if (this.workspaceFilter) {
                    const storage = window.sessionStorage;
                    storage.setItem("workspaceFilter", JSON.stringify(this.workspaceFilter));
                  }
                }
              }
            }
          });
      },
      refreshActivities: _debounce(
        function refreshActivitiesFunction() {
          if (this.currentHelpTicket) {
            this.$store.dispatch("fetchActivities", this.currentHelpTicket.id);
          } else if (this.ticketId) {
            this.$store.dispatch("fetchActivities", this.ticketId);
          }
        }, 1000),
      fields(section) {
        if (this.form) {
          const fields = this.form.formFields.filter(field => field.fieldPosition && field.fieldPosition.position === section);
          return _sortBy(fields, 'orderPosition');
        }
        return [];
      },
      addLink(link) {
        if (this.currentHelpTicket.linkableId !== link.linkableId) {
          const params = {
            source_id: this.currentHelpTicket.linkableId,
            target_id: link.id,
            company_id: this.currentHelpTicket.company.id,
          };
          this.addUniversalLink(params);
        } else {
          this.emitError(`Sorry, you cannot link an item with itself.`);
        }
      },
      removeLink(link) {
        if (link && link.id) {
          this.removeUniversalLink(link.id);
        }
      },
      updateFormField(params) {
        this.updateField(params)
          .then((res) => {
            this.setFieldInTicket(res.data);
          });
      },
      setActiveComponent(component) {
        this.activeComponent = component;
        document.querySelector('#active-component-nav').scrollIntoView({behavior: "smooth", block: "center"});
      },
      goToComment() {
        if (this.activeComponent !== 'ModernComments') {
          this.activeComponent = 'ModernComments';
          this.$refs.disableTicketModal.close();
        } else {
          const comment = document.querySelector(`[data-comment-id="${this.newCommentId}"]`);
          if (comment) {
            comment.scrollIntoView({behavior: 'smooth', block: 'center'});
            comment.classList.add("highlight-comment");
            setTimeout(() => {
              comment.classList.remove("highlight-comment");
            }, 5000);
          }
          this.$refs.disableTicketModal.close();
          this.newCommentId = null;
        }
      },
      openModal() {
        this.$refs.disableTicketModal.open();
      },
      clearCommentId() {
        this.newCommentId = null;
      },
      unmergeTicket(ticket) {
        const newTicket = _cloneDeep(this.currentHelpTicket);
        const idx = newTicket.mergedTickets.indexOf(ticket);
        if (idx) {
          newTicket.mergedTickets.splice(idx, 1);
          this.setCurrentHelpTicket(newTicket);
        }
      },
      timePassed(time) {
        return Date.parse(time) > new Date();
      },
      isDescriptionAndQuickView(field) {
        return field.name === 'description' && this.isQuickView;
      },
      openCustomFormModal() {
        this.$refs.moveTicketModal.open();
      },
      openArticleModal() {
        this.$refs.articleSelect.open();
      },
      handleEmptyDraft() {
        if (this.isTicketDraftEmpty && this.currentHelpTicketDraft?.id) {
          this.$store.dispatch('deleteTicketDraft', this.currentHelpTicketDraft.id);
        }
      },
      updateWindowWidth() {
        this.windowWidth = window.innerWidth;
      },
      setAISummarySeen() {
        http
          .put('/check_out_my_module_onboarding', { module_name: 'ticket_ai_summary' })
          .then(() => {
            this.isTicketSummarySeen = true;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error marking AI summary as seen');
          });
      },
      openAiModal() {
        this.$refs.ticketSummaryModal.open();
        if (!this.isTicketSummarySeen) {
          this.setAISummarySeen();
        }
        this.$refs.ticketSummaryModal.requestSummary();
      },
    },
  };
</script>

<style lang="scss">
.module.no-footer-content .main-page-content:has(.use-temporary-modern-show-ticket-padding) {
  padding-bottom: 0 !important;
  overflow: hidden !important;
  margin-bottom: 0 !important;
}
</style>

<style lang="scss" scoped>


.arrows-holder {
  z-index: 999;
  .left-arrow,
  .right-arrow {
    top: 50vh;
    z-index: 9999;
  }
  .right-arrow {
    right: -2rem;
  }
}

.hoverable {
  color: $themed-muted;
  &:hover {
    color: #000;
    font-weight: bold;
  }
}

.disable-buttons {
  opacity: 0.3;
  pointer-events: none;
}

.nulodgicon-chevron-left,
.nulodgicon-chevron-right {
  font-size: 2.25rem;
}

.next {
  &:hover {
    .next-button-box {
      transform: translate(-72%);
    }
  }
}

.previous {
  &:hover {
    .previous-button-box {
      transform: translate(2%);
    }
  }
}
.bullet-color {
  background-color: #CC3333;
}

.bullet-green {
  background-color: #47e047;
}
.arrows-holder {
  .basic-access-left-arrow {
    left: 0rem;
  }
}
.filter-white {
  filter: brightness(0) invert(1);
}
.sidebar-class {
  overflow: hidden !important;
  position: static !important;
}
.margin-right {
  max-height: 30vh !important;
}
.status-align {
  max-width: 42% !important;
  text-align: center;
  justify-content: end;
}
.overflow-height {
  overflow: auto;
  max-height: 500px !important;
}
.form-style {
  cursor: pointer;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  margin-left: 0;
  justify-content: space-between;
  padding: 0.25rem 0.75rem;
}
.form-style:hover {
  background-color: #d8d8d8;
  transform: scale(1.02);
}

.dynamic-container,
.show-section,
.adjust-section {
  overflow-x: hidden;
  overflow-y: auto;
  overscroll-behavior: contain;
  min-height: 10rem;
}

.ticket-modal {
  margin-top: 2rem;
  :deep(.sweet-modal) {
    overflow: unset;
  }
}

.container-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 0.313rem;
  justify-content: space-between;
}

.status-container {
  text-align: right;
  margin-bottom: 0.5rem;
}

.status-padding {
  padding: 0.5rem !important;
  vertical-align: sub;
  padding-bottom: 0.25rem;
}

.show-section {
  max-height: calc(140vh - 30rem);
}

.adjust-section {
  max-height: calc(130vh - 30rem);
}

.pills-bg {
  background-color: $themed-lighter;
}

.pills-bg-green {
  background-color: $green-subtle;
}

.text-color-black {
  color: #495057;
}

.icon-button-align{
  margin-right: 0.5rem;
  margin-top: -0.8rem;
}

.resource-button-height {
  line-height: -1;
  margin-top: -0.8rem;
}

.light-icon-filter {
  filter: var(--themed-light-icon-filter);
}

.align-time-box {
  flex-grow: 1;
  justify-content: end;
}
.button-width {
  max-width: 8rem !important;
}
@media (min-width: 1200px) {
  .show-section {
    max-height: calc(140vh - 30rem);
  }
  .adjust-section {
    height: calc(130vh - 30rem);
  }
}

@media (min-width: 1300px) {
  .show-section {
    max-height: calc(135vh - 30rem);
  }
  .adjust-section {
    height: calc(125vh - 30rem);
  }
}

@media (min-width: 1450px) {
  .show-section {
    max-height: calc(130vh - 30rem);
  }
  .adjust-section {
    height: calc(120vh - 30rem);
  }
}

@media (min-width: 1600px) {
  .show-section {
    max-height: calc(125vh - 30rem);
  }
  .adjust-section {
    height: calc(115vh - 30rem);
  }
}

.indicator-position {
  margin-top: 0.9rem;
  margin-right: 0.3rem;
}

.sidebar-left-open {
  left: 4rem;
}

.sidebar-left-closed {
  left: 0;
}
</style>
