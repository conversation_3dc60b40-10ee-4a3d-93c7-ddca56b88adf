<template>
  <div 
    class="row-font-size position-relative"
    :class="{ 'my-n1': isPaddedField }"
  >
    <div v-if="preference.name == 'priority' && !isInsights">
      <priority-dropdown
        v-if="ticket.fields.priority && ticket.fields.priority.visible"
        :is-bold="isBold"
        :value="ticket.fields.priority.value"
        :object="ticket"
        :color="ticket.fields.priority.color"
        :disabled="mspFlag || ticket.fields.priority.disabled || !(ticket.canWrite)"
        show-text
        lazy
      />
    </div>
    <div v-else-if="preference.name == 'status' && !isInsights">
      <status-dropdown
        v-if="ticket.fields.status && ticket.fields.status.visible"
        class="status"
        :is-bold="isBold"
        :value="ticket.fields.status.value"
        :object="ticket"
        :color="ticket.fields.status.color"
        :disabled="mspFlag || ticket.fields.status.disabled || !(ticket.canWrite)"
        @input="checkRequiredFields"
      />
    </div>
    <div v-else-if="fieldTypeCheck == 'people_list' && preference.name != 'assigned_to'">
      <people-list
        class="align-items-center pr-1"
        :ticket="ticket"
        :field-name="preference.name"
        :not-set-text="peopleListNotSetText"
        ticket-view-type="List"
        show-name
      />
    </div>
    <div v-else-if="preference.name == 'assigned_to' && !isInsights">
      <div v-if="$currentCompanyAdminUser || $superAdminUser">
        <inline-assigned-dropdown
          :object="ticket"
          :field-name="preference.name"
          :disabled="mspFlag"
        />
      </div>
      <div v-else>
        <inline-assigned-dropdown
          v-if="ticket.assignedTo && ticket.assignedTo[0].visible"
          :object="ticket"
          :field-name="preference.name"
          :disabled="mspFlag || ticket.assignedTo[0].disabled || !(ticket.canWrite)"
        />
      </div>
    </div>
    <div v-else-if="preference.name == 'comment_count'">
      <!-- Using v-if because v-tooltip conditional doesn't seem to work -->
      <span
        v-if="lastCommentAt"
        v-tooltip="`Last comment at ${ lastCommentAt }`"
      >
        <i class="genuicon-comment-o mb-0 clickable mx-1" />
        {{ ticket['commentCount'] }}
      </span>
      <span v-else>
        <i class="genuicon-comment-o mb-0 clickable mx-1" />
        {{ ticket['commentCount'] }}
      </span>
    </div>

    <div v-else-if="typeCheck == 'createdAt'">
      {{ createdAt }}
    </div>
    <div v-else-if="fieldTypeCheck == 'date'">
      {{ convertDate }}
    </div>
    <div v-else-if="typeCheck == 'updatedAt'">
      {{ updatedAt }}
    </div>
    <div v-else-if="typeCheck == 'dateClosed'">
      {{ dateClosed }}
    </div>
    <div v-else-if="typeCheck == 'workspaceName'">
      {{ getWorkspaceName }}
    </div>
    <div v-else-if="typeCheck == 'formUsed'">
      {{ getformName }}
    </div>
    <div v-else-if="typeCheck == 'attachments'">
      {{ attachmentsCount }}
    </div>
    <div v-else-if="smartList && formValue">
      <div v-if="formValue.split(',').length < 3">
        {{ formValue }}
      </div>
      <div v-else>
        <div v-if="!readMore">
          {{ formValue.split(',').slice(0,2).join(",") }}...
          <a
            href="#"
            class="small py-3 cursor-pointer text-center"
            @click.stop.prevent="toggleShow"
          >
            Show more
          </a>
        </div>
        <div v-else>
          {{ formValue }}
          <a
            href="#"
            class="small py-3 cursor-pointer text-center"
            @click.stop.prevent="toggleShow"
          >
            Show less
          </a>
        </div>
      </div>
    </div>
    <div v-else-if="fieldTypeCheck === 'rich_text'">
      {{ stripHTML(formValue) }}
    </div>
    <div v-else-if="formValue && formValue['value']">
      {{ formValue["value"] }}
    </div>
    <div v-else-if="preference.name == 'ticket_number'">
      <div 
        class="d-flex align-items-center"
        :style="{ 'max-width': ticketNumberColumnWidth }"
      >
        <label
          v-if="!isInsights && !mspFlag"
          class="clickable m-0"
          @click.stop.prevent="selectTicket"
        >
          <input
            type="checkbox"
            class="d-none"
          >
          <i
            class="nulodgicon-checkmark checkbox mr-2.5"
            :class="{ 'checkbox-checked': isTicketSelected }"
          />
        </label>
        <div
          v-tooltip="ticketNumber"
          class="ticket-number true-small text-spaced nowrap truncate"
          data-tc-ticket-number
          :data-tc-view-number="ticketNumber"
        >
          {{ ticketNumber }}
        </div>
      </div>
    </div>
    <div v-else-if="preference.name == 'total_time_spent'">
      {{ totalTimeSpent }}
    </div>
    <div
      v-else
      data-tc-listed-note
      :data-tc-listed-value="formValue"
      :class="{ 'font-weight-semi-bold': preference.name === 'subject' }"
    >
      {{ formValue }}
    </div>
  </div>
</template>

<script>
  import _get from 'lodash/get';
  import _map from 'lodash/map';
  import _camelCase from 'lodash/camelCase';
  import MomentTimezone from 'mixins/moment-timezone';
  import { mapGetters } from 'vuex';
  import mspHelper from 'mixins/msp_helper';

  import permissionsHelper from 'mixins/permissions_helper';
  import ticketHelper  from 'mixins/ticket_helper';
  import vClickOutside from 'v-click-outside';
  import PriorityDropdown from '../shared/priority_dropdown.vue';
  import StatusDropdown from '../shared/status_dropdown.vue';
  import InlineAssignedDropdown from '../shared/inline_assigned_dropdown.vue'; 
  import peopleList from './people_list.vue';

  const FIELD_TYPES = ['created_by', 'assigned_to', 'status', 'priority'];
  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      PriorityDropdown,
      StatusDropdown,
      InlineAssignedDropdown,
      peopleList,
    },
    mixins: [ permissionsHelper, MomentTimezone, ticketHelper, mspHelper ],
    props: ['ticket', 'preference', 'selectedTickets', 'isInsights', 'mspFlag'],
    data() {
      return {
        readMore: false,
        hoveredIndex: null,
        showTooltip: false,
      };
    },
    computed: {
      ...mapGetters(['ticketNumberColumnWidth']),
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      isTicketSelected() {
        return !!this.selectedTickets.find(ticket => ticket.id === this.ticket.id);
      },
      fieldName() {
        return _camelCase(this.preference.name);
      },
      fieldLabel() {
        return _camelCase(this.preference.label);
      },
      attachmentsCount() {
        if (this.ticket.fields.attachments) {
          return this.ticket.fields.attachments.value;
        } 
        return 0;
      },
      typeCheck() {
        if (FIELD_TYPES.includes(this.preference.name)) {
          return false;
        } else if (this.preference.fieldType === 'location_list') {
          return 'locations';
        }
        return _camelCase(this.preference.name);
      },
      fieldTypeCheck() {
        return this.preference.fieldType;
      },
      getWorkspaceName() {
        return this.ticket?.workspace;
      },
      getformName() {
        return this.ticket?.formUsed;
      },
      lastCommentAt() {
        if (this.ticket.lastCommentAt) {
          return this.timezoneMoment(this.ticket.lastCommentAt, Vue.prototype.$timezone);
        } 
        return null;
      },
      createdAt() {
        if (this.ticket.createdAt) {
          return this.timezoneDatetime(this.ticket.createdAt, Vue.prototype.$timezone);
        }
        return null;
      },
      updatedAt() {
        if (this.ticket.updatedAt) {
          return this.timezoneDatetime(this.ticket.updatedAt, Vue.prototype.$timezone);
        }
        return null;
      },
      dateClosed() {
        if (this.ticket.closedAt) {
          return this.timezoneDatetime(this.ticket.closedAt, Vue.prototype.$timezone);
        }
        return null;
      },
      convertDate() {
        if (this.ticket.fields[this.fieldName] && this.ticket.fields[this.fieldName].value) {
          return this.timezoneDate(this.ticket.fields[this.fieldName].value, this.currentTz);
        }
        return null;
      },
      smartList() {
        return ['location_list', 'contract_list', 'asset_list', 'vendor_list', 'telecom_list', 'tag'].includes(this.fieldTypeCheck);
      },
      ticketNumber() {
        return this.ticket.ticketNumber;
      },
      totalTimeSpent() {
        return this.ticket?.totalTimeSpent ?? '';
      },
      formValue() {
        if (!this.ticket || !this.ticket.fields) {
          return '';
        }
        if (this.fieldName === "companyName") {
          return this.ticket.company.name;
        }
        let fieldData = this.ticket.fields[this.fieldName];
        if (!fieldData) {
          fieldData = this.ticket.fields[this.fieldLabel];
        }
        if (fieldData && (this.additionalFieldType || this.fieldTypeCheck === fieldData.fieldType || (fieldData.length && this.fieldTypeCheck === fieldData[0].fieldType))) {
          if (Array.isArray(fieldData)) {
            return _map(fieldData, (v) => _get(v, 'name'))
              .filter(Boolean)
              .join(", ");
          } else if (typeof fieldData === 'string') {
            return fieldData;
          } else if (fieldData?.value != null) {
            return fieldData.value;
          } else if (this.fieldName !== 'phone' && this.ticket.fields[this.fieldName]) {
            return this.ticket.fields[this.fieldName];
          }
          return '';
        }
        return '';
      },
      additionalFieldType() {
        return this.fieldTypeCheck === '';
      },
      isPaddedField() {
        // A number of elements have a vertical padding for clickability (or have a large inline element), which means they take up extra vertical space on the table.
        // This will return true for those elements so that we can adjust the margins to give more vertical visual consistency.
        return ["priority", "status", "assigned_to", "followers"].includes(this.preference.name);
      },
      peopleListNotSetText() {
        if (this.preference.name === "raise_this_request_on_behalf_of") {
          return "Not set";
        } else if (this.preference.name === "followers") {
          return "No followers";
        } 
        return "";
      },
    },
    methods: {
      toggleShow() {
        this.readMore = !this.readMore;
      },
      checkRequiredFields(params) {
        this.$emit('input', params);
      },
      selectTicket() {
        this.$emit('select');
      },
      stripHTML(richText) {
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = richText;
        return tempDiv.textContent || tempDiv.innerText || "";
      },
    },
  };
</script>

<style scoped lang="scss">
  .table tr td .status .dropdown-toggle {
    display: inline;
    margin-left: -0.25rem;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    position: relative;
    text-align: left;
    width: auto;
  }

  .status .dropdown-toggle {
    /* Vertical align is often weird, and this is one of those times. 
       This ensures vertical alignment of priority and status dropdowns, 
       but there is probably a better way to fix it.
     */
    top: -1px;
  }

  .status .dropdown-toggle span {
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    padding-right: 0.125rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
    vertical-align: middle;
  }

  .checkbox-checked {
    background-color: $themed-link;
    border-color: $themed-link;
  }

  .badge {
    left: -0.75rem;
    top: -1.5rem;
    border-radius: 0 0 4px 0;
    font-size: 12px;
    line-height: 11px;
  }
</style>
