<template>
  <div>
    <div class="col-12 d-flex justify-content-between align-items-center mt-2">
      <h5 class="mb-0">FAQ</h5>
      <div class="d-flex align-items-center">
        <router-link 
          to="/faqs" 
          class="text-secondary back-to-link d-flex align-items-center mr-3"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span>Back to <strong>FAQs</strong></span>
        </router-link>
        <a
          v-if="isWrite"
          v-tooltip="'Edit'"
          href="#"
          class="edit ml-1 btn btn-light btn-flat btn-icon-circle has-tooltip"
          role="button"
          data-tc-faq-edit
          @click.stop.prevent="$router.push(`/faqs/${$route.params.id}/edit`)"
        >
          <i class="nulodgicon-edit" />
        </a>
        <a
          v-if="isWrite"
          v-tooltip="'Delete'"
          href="#"
          class="delete ml-1 btn btn-light btn-flat btn-icon-circle has-tooltip"
          role="button"
          data-tc-faq-delete
          @click.stop.prevent="$refs.deleteModal.open()"
        >
          <i class="nulodgicon-trash-b" />
        </a>
      </div>
    </div>
    <div class="mt-4">
      <div class="box p-4">
        <div
          v-if="faq"
          class="mx-auto w-100"
        >
          <div class="row mt-3 align-items-center">
            <div class="col">
              <span class="badge badge-secondary px-2">
                {{ categoryName() }}
              </span>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col-md-10">
              <strong>
                <trix-render 
                  type="faq" 
                  :value="faq.questionBody" 
                />
              </strong>
              <p class="mt-3">
                <trix-render 
                  type="faq"
                  :value="faq.answerBody" 
                />
              </p>
            </div>
          </div>
          <delete-modal 
            ref="deleteModal" 
            :value="faq" 
            @deleted="gotoIndex" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import permissionsHelper from "mixins/permissions_helper";
import { mapGetters, mapMutations, mapActions } from 'vuex';
import _find from 'lodash/find';
import DeleteModal from "./delete_modal.vue";
import TrixRender from "../../trix_render.vue";

export default {
  components: {
    TrixRender,
    DeleteModal,
  },
  mixins: [permissionsHelper],
  computed: {
    ...mapGetters([
      'faq',
      'categories',
    ]),
    categoriesPresent() {
      return this.categories.length > 0;
    },
  },
  methods: {
    ...mapMutations([
      'setLoading',
      'setLoadingStatus',
    ]),
    ...mapActions([
      'fetchFaq',
      'fetchCategories',
    ]),


    onWorkspaceChange() {
      if (!this.categoriesPresent) {
        this.fetchCategories();
      }
      this.fetchFaq(this.$route.params.id)
        .catch(() => {
          this.emitError(`Sorry, there was an issue loading the FAQ.  Please refresh the page and try again`);
        })
        .finally(() => {
          this.setLoadingStatus(false);
        });
    },
    createQuestion(newFaq) {
      this.$store.dispatch("createQuestion", newFaq)
        .then(() => {
          this.myFaq = { };
          this.$router.push("/faqs");
          this.emitSuccess("Question/answer created");
        })
        .catch(() => {
          this.emitError(`Sorry, there was an issue adding FAQ questions. Please refresh the page and try again.`);
        });
    },
    categoryName() {
      const category = _find(this.categories, {id: this.faq.categoryId});
      if (category)
        return category.name;
      return null;
    },
    gotoIndex() {
      this.$router.push('/faqs');
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-delete-btn {
  color: black;
  background-color: $themed-light;

  &:hover {
    background-color: $themed-muted;
    color: $themed-light;
  }
}
</style>
