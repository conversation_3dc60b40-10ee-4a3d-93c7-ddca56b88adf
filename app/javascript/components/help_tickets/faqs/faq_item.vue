<template>
  <div v-if="faq">
    <div
      class="faq-item clickable box pl-5"
      :class="[backgroundCss, isWrite ? 'box--with-hover' : 'disable-cursor']"
      data-tc-edit-faq
      @mouseover="hoverIn"
      @mouseout="hoverOut"
      @click="gotoFaq"
    >
      <div class="drag-overlay">
        <span class="handle genuicon-draggable"/>
      </div>
      <span
        v-show="hover"
        class="position-absolute overlay"
      >
        <a
          v-if="!isRead"
          href="#"
          @click.stop.prevent="editItem"
        >
          <i
            class="nulodgicon-edit h5 mb-0 clickable mx-3"
            data-tc-edit-faq
          />
        </a>
        <a
          v-if="!isRead"
          href="#"
          @click.stop.prevent="$refs.deleteModal.open()"
        >
          <i
            class="nulodgicon-trash-b h5 mb-0 clickable"
            data-tc-delete-faq
          />
        </a>
      </span>

      <span
        class="badge badge-secondary mb-3"
        :data-tc-view-category="faq.categoryName"
      >
        {{ faq.categoryName }}
      </span>

      <div
        v-if="faq.public"
        v-tooltip="'Viewable in your public Help Center'"
        class="badge box-badge bg-muted text-white"
        data-tc-article-is-public
      >
        Public
      </div>

      <span
        class="text-truncate font-weight-bold w-100 h6--responsive"
        :title="decodeHTML(faq.questionBody)"
      >
        {{ decodeHTML(faq.questionBody) }}
      </span>
      <p
        class="p--responsive text-truncate"
        :title="decodeHTML(faq.answerBody)"
      >
        {{ decodeHTML(faq.answerBody) }}
      </p>
    </div>

    <delete-modal
      ref="deleteModal"
      :value="faq"
      @deleted="deleteFaq"
    />
  </div>
</template>

<script>
import strings from 'mixins/string';
import { mapGetters, mapMutations } from 'vuex';
import permissionsHelper from "mixins/permissions_helper";
import _ from "lodash";
import DeleteModal from './delete_modal.vue';


export default {
  components: {
    DeleteModal,
  },
  mixins: [permissionsHelper, strings],
  props: ['faq', 'categories'],
  data() {
    return {
      hover: false,
    };
  },
  computed: {
    ...mapGetters(['faqs']),
    backgroundCss() {
      if (this.hover) {
        return { "faq-item-hovered": true };
      } 
        return { };
      
    },
    categoryName() {
      if (this.faq.categoryId) {
        const category = _.find(this.categories, {id: this.faq.categoryId});
        if (category) {
          return category.name;
        }
      }
      return "Uncategorized";
    },
  },
  methods: {
    ...mapMutations(['setFaq', 'setFaqs']),
    hoverIn() {
      this.hover = true;
    },
    hoverOut() {
      this.hover = false;
    },
    gotoFaq() {
      this.$router.push(`/faqs/${this.faq.id}`);
    },
    editItem() {
      this.setFaq(this.faq);
      this.$router.push(`/faqs/${this.faq.id}/edit`);
    },
    deleteFaq() {
      const index = this.faqs.indexOf(this.faq);
      this.faqs.splice(index, 1);
      this.setFaqs(this.faqs);
    },
  },
};
</script>

<style lang="scss" scoped>
  .overlay {
    transition: 1s ease;
    top: 1.5rem;
    right: -1rem;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    a {
      color: #888;
    }
    a:hover {
      color: #636262;
    }
  }

  .box--with-hover {
    padding: 1.25rem;

    &:hover {
      .drag-overlay {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .disable-cursor {
    cursor: auto;
  }

  .handle {
    cursor: move;
    font-size: 1.125rem;
    position: absolute;
    top: 40%;
  }

  .drag-overlay {    
    background-color: rgba(33, 37, 41, 0.1);
    border-radius: 0.25rem 0 0 0.25rem;
    color: $themed-base;
    content: "";
    cursor: move;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    visibility: hidden;
    width: 1.25rem;
    z-index: 2;

    &:hover {
      background-color: rgba(33, 37, 41, 0.3);
    }
  }

  div.faq-item {
    padding: 10px;
    background-color: $themed-box-bg;
    border-radius: 0.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .faq-item-hovered {
    transition: all 0.2s ease-in-out;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23) !important;
  }
  
  .nulodgicon-edit, .nulodgicon-trash-b {
    @media($max: $medium) {
      font-size: 0.85rem;
    }
  }
</style>
