<template>
  <div class="mt-5">
    <router-link
      to="/faqs"
      class="text-secondary back-to-link mr-4"
      data-tc-back-faq
      role="button"
    >
      <i class="nulodgicon-arrow-left-c white mr-2" />
      <span>Back to <strong>FAQs</strong></span>
    </router-link>
    <h4 
      class="mt-5 mb-3 font-weight-normal h4--responsive" 
      data-tc-view-header="edit faq" 
    >
      Edit FAQ
    </h4>
    <faq-form
      v-if="myFaq"
      :value="myFaq"
      @input="saveFaq"
    />
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import permissionsHelper from 'mixins/permissions_helper';
import FaqForm from "./form.vue";

export default {
  components: {
    FaqForm,
  },
  mixins: [permissionsHelper],
  data() {
    return {
      myFaq: null,
    };
  },
  computed: {
    ...mapGetters(['faq']),
    id() {
      return this.$route.params.id;
    },
  },
  methods: {
    ...mapMutations(['setFaq']),

    onWorkspaceChange() {
      if (!this.faq && this.id) {
        this.$store.dispatch("fetchFaq", this.id).then(() => {
          this.myFaq = { ...this.faq };
        });
      } else if (this.faq) {
        this.myFaq = { ...this.faq };
      } else {
        this.emitError("Sorry, but this FAQ no longer exist.");
        this.$router.push("/faqs");
      }
    },
    saveFaq(updatedFaq) {
      this.$store.dispatch("saveQuestion", updatedFaq)
        .then(() => {
          this.$router.push("/faqs");
          this.myFaq = null;
          this.emitSuccess(`Question/answer updated`);
        })
        .catch(() => {
          this.emitError(`Sorry, there was an issue editing this FAQ question. Please refresh the page and try again.`);
        });
    },
  },
};
</script>
