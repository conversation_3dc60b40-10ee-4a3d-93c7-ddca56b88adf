<template>
  <div>
    <resources-menu active="faq" />
    <div>
      <h5
        class="mt-sm-2 mt-md-4 font-weight-normal h5--responsive"
        data-tc-result
      >
        Frequently Asked Questions
      </h5>
      <expandable-description class="mb-4">
        <template #short>
          <span class="p--responsive">FAQs are searchable questions and answers that can be accessed by users.</span>
        </template>
        <template #long>
          <span class="p--responsive">FAQs are searchable questions and answers that can be accessed by users to help solve common issues.  The FAQ's are accessible outside of the system.</span>
        </template>
      </expandable-description>
      <div class="mt-3 mb-3">
        <div class="search-wrap">
          <input
            ref="searchInput"
            v-model="search"
            class="form-control search-input readable-length"
            placeholder="Search FAQ"
            data-tc-faq-search
            @keyup="fetchResults"
          >
          <i
            class="mt-2 nulodgicon-ios-search-strong search-input-icon"
            @click.prevent="$refs.searchInput.focus"
          />
        </div>
        <div
          v-if="showResultCounts"
          class="float-right mt-1 not-as-small text-muted"
          data-tc-no-faq
        >
          {{ faqs.length }} {{ faqsLabel }} shown
        </div>
      </div>
      <div
        v-if="isLoading"
        class="d-flex"
      >
        <h5 class="text-dark d-inline-block mb-0">
          Loading faqs...
        </h5>
        <span class="d-inline-block align-top ml-3">
          <sync-loader
            :loading="true"
            class="ml-3 mt-1"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </div>
      <div
        v-if="faqs && faqs.length > 0 && categoriesPresent"
        class="mt-4 row" 
      >
        <draggable
          v-if="okToSort"
          class="list-group faq-section w-100"
          group="faqsList"
          :value="faqs"
          @input="updateOrder"
        >
          <faq-item
            v-for="(question) in faqs"
            :key="question.id"
            class="faq-preview col-sm-12 col-lg-4 mt-4"
            :faq="question"
            :categories="categories"
            data-tc-question
          />
        </draggable>
        <faq-item
          v-for="(question) in faqs"
          v-else
          :key="question.id"
          class="faq-preview col-sm-12 col-lg-4 mt-4"
          :categories="categories"
          :faq="question"
        />
        <h4
          v-if="noSearchResults"
          class="text-muted my-5"
        >
          No search results found.
        </h4>
      </div>
      <div
        v-if="faqs && faqs.length == 0"
        class="text-center mt-5 pt-4"
      >
        <h4 data-tc-no-faq-found>No FAQ's exist, yet.</h4>
        <h5
          v-if="!isScopedAny"
          class="text-secondary font-weight-normal"
        >
          It'd be great to
          <router-link to="/faqs/new">
            add some now
          </router-link>.
        </h5>
      </div>
      <nav v-if="faqsPageCount > 1">
        <paginate
          ref="paginate"
          class="my-3 px-2 justify-content-center"
          :click-handler="pageSelected"
          :container-class="'pagination pagination-sm'"
          :next-class="'next-item'"
          :next-link-class="'page-link'"
          :next-text="'Next'"
          :page-class="'page-item'"
          :page-count="faqsPageCount"
          :page-link-class="'page-link'"
          :prev-class="'prev-item'"
          :prev-link-class="'page-link'"
          :prev-text="'Prev'"
          :selected="faqsPage"
        />
      </nav>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import _debounce from 'lodash/debounce';
  import Draggable from 'vuedraggable';
  import Paginate from 'vuejs-paginate';
  import permissionsHelper from "mixins/permissions_helper";
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import FaqItem from './faq_item.vue';
  import ResourcesMenu from '../resources_menu.vue';
  import ExpandableDescription from '../../shared/expandable_description.vue';

  export default {
    components: {
      Draggable,
      ExpandableDescription,
      FaqItem,
      Paginate,
      ResourcesMenu,
      SyncLoader,
    },
    mixins: [ permissionsHelper ],
    data() {
      return {
        search: null,
        showResultCounts: false,
        newFaq: {
          questionBody: null,
          answerBody: null,
        },
        selectedFaq: null,
      };
    },
    computed: {
      ...mapGetters([
        'faqs',
        'faqsPageCount',
        'faqsPage',
        'categories',
      ]),
      isLoading() {
        return this.faqs === null;
      },
      searchPresent() {
        return this.search && this.search.length > 0;
      },
      noSearchResults() {
        return this.faqs && this.faqs.length === 0 && this.searchPresent;
      },
      okToSort() {
        return !this.searchPresent;
      },
      faqsLabel() {
        if (this.faqs && (this.faqs.length > 1 || this.faqs.length === 0)) {
          return 'FAQs';
        }
        return 'FAQ';
      },
      categoriesPresent() {
        return this.categories.length > 0;
      },
    },
    methods: {
      ...mapMutations(['setFaqs', 'setFaqsPage']),
      ...mapActions([
        'fetchFaqs',
        'fetchCategories',
      ]),
      onWorkspaceChange() {
        this.fetchFaqs({ searchTerms: null, isBasicAccess: this.isBasicAccess });
        this.$store.commit("setFaq", null);
        if (!this.categories?.length) {
          this.fetchCategories();
        }
      },
      fetchResults: _debounce(
        function () {
          this.setFaqsPage(0);
          this.showResultCounts = false;
          this.fetchFaqs({ searchTerms: this.search, isBasicAccess: this.isBasicAccess }).then(() => {
            if (this.searchPresent) {
              this.showResultCounts = true;
            }
          });
        },
        1000
      ),
      filterByCompany(companyId) {
        this.companyId = companyId;
        this.faqData.compId = companyId;
        this.saveCompanyId();
        this.fetchResults();
      },
      updateOrder(myList) {
        this.setFaqs(myList);
        const newOrder = myList.map(faq => faq.id );
        http
          .post(`/ordered_faqs.json`, { faqs: newOrder })
          .then(res => {
            this.setFaqs(res.data.questions);
          })
          .catch(() => {
            this.emitError(`Sorry, there was an issue updating the FAQ. Please refresh the page and try again.`);
          });
      },
      pageSelected(p) {
        this.setFaqsPage(p - 1);
        this.fetchFaqs({ searchTerms: this.search, isBasicAccess: this.isBasicAccess });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .ghost {
    opacity: 0.5;
    background-color: #E3F2FD;
  }
  .search-input-icon {
    top: 30%;
  }
  .faq-section {
    flex-direction: row;
    flex-wrap: wrap;
  }

</style>
