<template>
  <div class="box w-100" >
    <form
      v-if="faq"
      class="form-group p-sm-2 p-md-4 rounded w-100"
    >
      <div class="form-group">
        <add-category
          :title="'Category'"
          :object="faq"
          :categories="categories"
          @fetch-categories="fetchCategories"
          @update-category="setCategory"
        />
      </div>
      <div class="form-group mb-3">
        <label>Make it Public</label>
        <material-toggle
          class="ml-1"
          :init-active="faq.public"
          @toggle-sample="toggleFAQPublicAccess"
        />
        <p class="small text-muted mb-0 mt-2 pr-0">
          Activating this will make this FAQ publicly accessible.
        </p>
        <div 
          class="background-light-gray p-2 left-border"
          :class="borderClass"
        >
          Access to public articles by unauthenticated users is 
          <span :class="accessStatusClass">
            {{ isPublicAccessEnabled ? 'enabled' : 'disabled' }}
          </span>
          for this workspace in your
          <router-link
            target="_blank"
            :to="{ path: '/settings/help_center/display', query: { highlight: 'allow-faq-page-to-logged-out-users' } }"
            class="text-blue-600 underline ml-1"
          >
            Help Center Settings
          </router-link>.
        </div>
      </div>
      <div class="form-group">
        <label
          for="question"
          class="required"
        >
          Question
        </label>
        <input
          id="question"
          ref="questionInput"
          v-model="faq.questionBody"
          v-validate="'required'"
          :class="{ 'is-invalid': errors.has('question') }"
          type="hidden"
          name="question"
        >
        <trix-vue
          ref="trixEditor"
          v-model="faq.questionBody"
          input-id="question"
          attachable-type="HelpdeskFaq"
          data-tc-answer
          @update-uploading-status="updateUploadingStatus"
          @handle-attachment-add="handleAttachmentAdd"
          @handle-attachment-remove="handleAttachmentRemove"
        />
        <span
          v-if="errors.has('question')"
          class="text-danger smallest"
        >
          {{ errors.first('question') }}
        </span>
      </div>

      <div class="form-group">
        <label
          for="answer"
          class="required"
        >
          Answer
        </label>
        <input
          id="answer"
          ref="answerInput"
          v-model="faq.answerBody"
          v-validate="'required'"
          :class="{ 'is-invalid': errors.has('answer') }"
          type="hidden"
          name="answer"
        >
        <trix-vue
          ref="trixEditor"
          v-model="faq.answerBody"
          input-id="answer"
          attachable-type="HelpdeskFaq"
          data-tc-answer
          @update-uploading-status="updateUploadingStatus"
          @handle-attachment-add="handleAttachmentAdd"
          @handle-attachment-remove="handleAttachmentRemove"
        />
        <span
          v-if="errors.has('answer')"
          class="text-danger smallest"
        >
          {{ errors.first('answer') }}
        </span>
      </div>

      <div class="form-group d-flex justify-content-end">
        <submit-button
          :is-validated="!!canSubmit"
          :is-saving="submit || attachmentUploading"
          btn-content="Submit"
          saving-content="Submiting"
          @submit="submitQuestion"
        />
      </div>
    </form>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from 'vuex';
import permissionsHelper from 'mixins/permissions_helper';
import { watchUnusedAttachments } from 'mixins/trix_vue_helper';
import MaterialToggle from 'components/shared/material_toggle.vue';
import http from 'common/http';
import AddCategory from '../../shared/add_category.vue';
import TrixVue from '../../trix_vue.vue';
import SubmitButton from '../../shared/submit_button.vue';

export default {
  $_veeValidate: {
    validator: "new",
  },
  components: {
    TrixVue,
    AddCategory,
    SubmitButton,
    MaterialToggle,
  },
  mixins: [permissionsHelper, watchUnusedAttachments],
  props: ['value'],
  data() {
    return {
      faq: null,
      submit: false,
      attachmentUploading: false,
      attachmentIds: [],
      isPublicAccessEnabled: null,
    };
  },
  computed: {
    ...mapGetters(['categories']),
    borderClass() {
      return this.isPublicAccessEnabled ? 'border-left-green' : 'border-left-yellow';
    },
    accessStatusClass() {
      const base = 'p-2 rounded ml-1 mr-1';
      return this.isPublicAccessEnabled ? `${base} bg-green-100` : `${base} bg-yellow-100`;
    },
    canSubmit() {
      const { questionBody, answerBody } = this.faq;
      if (questionBody && answerBody) {
        return (this.extractContent(questionBody).trim() && this.extractContent(answerBody).trim());
      }
      return null;
    },
    unCategorizedId(){
      if (this.categories.length > 0){
        return this.categories.find(item => item.name === "Uncategorized").id;
      }
      return null;
    },
  },
  methods: {
    ...mapMutations(['setLoadingStatus']),
    ...mapActions(['fetchCategories']),

    onWorkspaceChange() {
      this.fetchCategories();
      this.setLoadingStatus(false);
      if (this.value) {
        this.faq = { ...this.value };
      }
      this.fetchHelpCenterSettings();
    },
    fetchHelpCenterSettings() {
      http
        .get('/helpdesk_settings.json')
        .then((res) => {
          const settingsArray = res.data.settings?.helpCenterSettings;
          const setting = settingsArray.find(n => n.name === 'Allow unauthenticated users to access the FAQ page');
          this.isPublicAccessEnabled = setting?.enabled;
        })
        .catch(() => {
          this.emitError('There was an issue fetching your helpdesk settings. Please refresh the page and try again.');
        });
    },
    setCategory(categoryId) {
      this.faq.categoryId = categoryId;
    },
    toggleFAQPublicAccess() {
      this.faq.public = !this.faq.public;
    },
    submitQuestion() {
      this.$validator.validateAll().then((result) => {
        if (result) {
          this.submit = true;
          if (!this.faq.categoryId) {
            this.faq.categoryId = this.unCategorizedId;
          }
          if (!this.faq.companyId) {
            this.faq.companyId = this.$currentCompanyId;
          }
          this.$emit("input", { faq: this.faq, attachmentIds: this.attachmentIds });
        } else {
          this.emitError(`Please correct the highlighted errors before submitting.`);
        }
      });
    },
    extractContent(contentToParse) {
      const span = document.createElement("span");
      span.innerHTML = contentToParse;
      return span.textContent || span.innerText;
    },
    updateUploadingStatus(status) {
      this.attachmentUploading = status;
    },
    handleAttachmentAdd(data) {
      this.attachmentIds.push(data.attachment.id);
    },
    handleAttachmentRemove(attachmentId) {
      const idx = this.attachmentIds.indexOf(attachmentId);
      if (idx > -1) {
        this.attachmentIds.splice(idx, 1);
      }
    },
    setCompanyId(value) {
      this.faq.companyId = value;
    },
  },
};
</script>

<style lang="scss" scoped>
  .bg-green-100 {
    background-color: #dff3e7;
  }

  .bg-yellow-100 {
    background-color: #faf9e0;
  }

  .background-light-gray {
    background-color: var(--themed-light);
    display: inline-block;

  }

  .left-border {
    display: inline-block;
    align-items: center;
    border-left: 0.3rem solid $color-caution;
  }  
  
  .border-left-green {
    border-left-color: #7ac899;
  }
  
  .border-left-yellow {
    border-left-color: #fad141;
  }

</style>
