<template>
  <div>
    <div class="box box--with-heading box--natural-height my-5 no-shadow">
      <h6 class="box__heading">
        Scheduled Tasks
      </h6>

      <div class="box__inner p-4 p-lg-5 bg-light">
        <div
          v-if="isLoading"
          class="row mb-n4"
        >
          <div
            v-for="index in 3"
            :key="`skeleton-item-${index}`"
            class="mb-4 col-md-6 col-xl-4"
          >
            <div class="skeleton scheduled-task-item box p-4 text-center">
              <div class="box__inner">
                <div />
              </div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="row mb-n4"
        >
          <div
            v-for="task in scheduledTasks"
            :key="task.id"
            class="mb-4 col-md-6 col-xl-4"
            :data-tc-schedule-task="task.name"
          >
            <div
              class="scheduled-task-item box box--with-hover p-4"
              @click.stop="openScheduledTaskViewModal(task)"
            >
              <div class="box__inner h-100 d-flex flex-column">
                <div class="d-flex align-items-start justify-content-between">
                  <h5
                    ref="taskName"
                    class="truncate mb-0"
                    :data-tc-view-name="task.name"
                  >
                    {{ task.name }}
                  </h5>
                  <div class="pl-1 mt-n3 mr-n3 nowrap">
                    <i
                      class="btn btn-link btn-round text-muted nulodgicon-edit"
                      data-tc-btn="task edit"
                      @click.stop.prevent="openScheduledTaskFormModal(task)"
                    />
                    <i
                      v-if="isWrite"
                      class="btn btn-link btn-round text-muted nulodgicon-trash-b"
                      data-tc-btn="task delete"
                      @click.stop.prevent="openDeleteScheduledTaskModal(task.id)"
                    />
                  </div>
                </div>

                <p
                  ref="taskDescription"
                  class="task-description text-secondary mt-2 mb-2.5 not-as-small"
                  data-tc-view-description
                >
                  {{ task.description }}
                </p>
                <!--
                  The strange use of 2.5 bottom margin (above) next to 2 top padding (below) 
                  is to ensure early cutting off of the description (before the ellipses) leaves it 
                  still readable, but still obviously cut off to show that the description is longer.  
                -->
                <div class="mt-auto">
                  <div class="row mt-n1 pt-1">
                    <div class="col-auto pr-0 mt-1">
                      <user-info
                        v-if="task.assignee"
                        bg-color="none"
                        link-class="p-0 not-as-small"
                        avatar-class="mr-1"
                        :user="task.assignee"
                        :avatar-size="32"
                        :show-contact-info="false"
                      />
                    </div>
                    <div class="col-auto scheduled-tasks__time-data text-right ml-auto mt-1">
                      <div
                        v-if="task.recurring"
                        class="scheduled-tasks__recurring-badge d-inline-block py-1 px-2 true-small font-weight-bold rounded"
                      >
                        <i class="genuicon-repeat align-middle h6" />
                        <span>{{ task.recurrencePatternText }}</span>
                      </div>
                      <div
                        v-else
                        class="scheduled-tasks__one-time-badge d-inline-block py-1 px-2 true-small font-weight-bold rounded"
                      >
                        <i class="genuicon-repeat-once align-middle h6"/>
                        <span>{{ task.recurrencePatternText }}</span>
                      </div>
                      <div class="true-small flatten-line-height mt-2.5 mb-n2">
                        <span class="smallest mr-0.5">Starts:</span>
                        <span class="font-weight-semi-bold">{{ taskStartDate(task) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-4 col-md-6 col-lg-4">
            <div
              class="scheduled-task-item box box--with-hover p-4 text-center"
              @click="openScheduledTaskFormModal(initTasklist)"
            >
              <div class="box__inner">
                <div class="d-flex justify-content-center align-items-center">
                  <div class="text-secondary">
                    <h1 class="mt-n2">&plus;</h1>
                    <h5>Add a Scheduled Task</h5>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <sweet-modal
      ref="deleteScheduledTasksModal"
      v-sweet-esc
      title="Before you delete this Scheduled Task..."
    >
      <template slot="default">
        <h6 data-tc-delete-text>
          Are you sure you want to delete this Scheduled Task?
        </h6>
      </template>
      <div class="d-flex justify-content-end mt-5">
        <submit-button
          class="mr-2"
          btn-content="Cancel"
          :conditional-class="'btn-secondary btn-sm'"
          @submit="cancelDelete"
        />
        <submit-button
          btn-content="Delete"
          :is-saving="disabled"
          :conditional-class="'btn-sm btn-danger'"
          data-tc-btn="delete scheduled task"
          @submit="deleteScheduledTask(scheduledTaskToDelete)"
        />
      </div>
    </sweet-modal>
    <scheduled-task-form
      ref="scheduledTaskFormModal"
      :selected-task="selectedScheduledTask"
      @input="okInput"
      @cancel="resetData"
    />
    <scheduled-task-view
      ref="scheduledTaskViewModal"
      :selected-task="selectedScheduledTask"
      @close="resetData"
    />
  </div>
</template>
<script>
  import vClickOutside from 'v-click-outside';
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from "mixins/permissions_helper";
  import ScheduledTaskData, { defaultRecurrencePattern, defaultInitTasklist } from 'mixins/scheduled_task_data';
  import SubmitButton from '../../shared/submit_button.vue';
  import UserInfo from '../../shared/user_info.vue';
  import ScheduledTaskForm from './scheduled_task_form.vue';
  import ScheduledTaskView from './scheduled_task_view.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      UserInfo,
      ScheduledTaskForm,
      ScheduledTaskView,
      SubmitButton,
      SweetModal,
    },
    mixins: [permissionsHelper, ScheduledTaskData],
    data() {
      return {
        scheduledTaskToDelete: null,
        disabled: false,
        isLoading: true,
        scheduledTasks: null,
        initTasklist: defaultInitTasklist,
        bgColor: 'var(--themed-lighter)',
        selectedScheduledTask: null,
      };
    },
    watch: {
      "$route.params": function watchRouteParams() {
        this.openNewScheduledTaskModal();
      },
    },
    methods: {
      onWorkspaceChange() {
        this.isLoading = true;
        this.openNewScheduledTaskModal();
        this.fetchScheduledTasks();
      },
      taskWithRecurrenceData(task) {
        if (task?.id && task.recurring) {
          task.recurrence.recurrencePattern.daily ||= { recurrenceType: 'revision', revisionCount: 1 };
          task.recurrence.recurrencePattern.weekly ||= { selectedDays: [], revisionCount: 1 };
          task.recurrence.recurrencePattern.monthly ||= { recurrenceType: 'monthDay', monthDay: 1, dayMonthRevision: 1, weekMonthRevision: 1, selectedWeek: 'First', weekDayName: "Monday" };
          task.recurrence.recurrencePattern.yearly ||= { recurrenceType: 'monthDayOption', revisionCount: 1, dayMonthName: "January", weekMonthName: "January", monthDay: 1, weekDay: "First", dayName: "Monday" };
        } else if (task?.id && !task.recurring) {
          task.recurrence ||= defaultRecurrencePattern;
        }
        return task;
      },
      openNewScheduledTaskModal() {
        if (this.$route.query.new) {
          this.openScheduledTaskFormModal(this.initTasklist);
        }
      },
      getCurrentTimePlus30() {
        const addMinutes = (date, minutes) => new Date(date.getTime() + minutes * 60000);

        const formatTime = (date) =>
          `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

        const startDate = addMinutes(new Date(), 30);
        const endDate = addMinutes(startDate, 30);

        return {
          startDateObj: startDate,
          endDateObj: endDate,
          startTime: formatTime(startDate),
          endTime: formatTime(endDate),
        };
      },
      getDefaultDate(baseDate = new Date()) {
        const year = baseDate.getFullYear();
        const month = String(baseDate.getMonth() + 1).padStart(2, '0');
        const day = String(baseDate.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },
      updateScheduledTaskTime(task, newStartTime, newEndTime) {
        task.taskStartedAt.startTime = newStartTime || task.taskStartedAt.startTime;
        task.taskEndedAt.endTime = newEndTime || task.taskEndedAt.endTime;
      },
      openScheduledTaskFormModal(task) {
        let startTime;
        let endTime;
        let startDateObj;
        let endDateObj;

        if (task.id) {
          startDateObj = new Date(task.taskStartedAt.date);
          endDateObj = new Date(task.taskEndedAt.date);
          startTime = task.taskStartedAt.startTime;
          endTime = task.taskEndedAt.endTime;
        } else {
          const timeData = this.getCurrentTimePlus30();
          startDateObj = timeData.startDateObj;
          endDateObj = timeData.endDateObj;
          startTime = timeData.startTime;
          endTime = timeData.endTime;
        }
        const startDate = this.getDefaultDate(startDateObj);
        const endDate = this.getDefaultDate(endDateObj);
        this.updateScheduledTaskTime(task, startTime, endTime);
        task.taskStartedAt = {
          date: startDate,
          startTime,
          timeZone: Vue.prototype.$timezone,
        };

        task.taskEndedAt = {
          date: endDate,
          endTime,
          timeZone: Vue.prototype.$timezone,
        };

        this.selectedScheduledTask = this.taskWithRecurrenceData(task);
        this.$refs.scheduledTaskFormModal.open();
      },
      openScheduledTaskViewModal(task) {
        this.selectedScheduledTask = this.taskWithRecurrenceData(task);
        this.$refs.scheduledTaskViewModal.open();
      },
      openDeleteScheduledTaskModal(id) {
        this.scheduledTaskToDelete = id;
        this.$refs.deleteScheduledTasksModal.open();
      },
      cancelDelete() {
        this.scheduledTaskToDelete = null;
        this.$refs.deleteScheduledTasksModal.close();
      },
      okInput() {
        this.fetchScheduledTasks();
        this.resetData();
      },
      deleteScheduledTask(id) {
        this.disabled = true;
        http
          .delete(`/scheduled_tasks/${id}.json`)
          .then(() => {
            this.disabled = false;
            this.$refs.deleteScheduledTasksModal.close();
            this.fetchScheduledTasks();
            this.emitSuccess('Scheduled Task deleted successfully!');
          })
          .catch(error => {
            this.disabled = false;
            this.$refs.deleteScheduledTasksModal.close();
            this.emitError(`Sorry, there was an error deleting scheduled task. ${error.response.data.message}`);
          });
      },
      resetData() {
        this.selectedScheduledTask = null;
        this.initTasklist = defaultInitTasklist;
      },
      fetchScheduledTasks() {
        const url = '/scheduled_tasks.json';
        http
          .get(url).then(res => {
            this.scheduledTasks = res.data.scheduledTasks;
            this.isLoading = false;
          })
          .catch(error => {
            this.isLoading = false;
            this.emitError(`Sorry, there was an error fetching scheduled tasks. ${error.response.data.message}`);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  $description-rows: 4;
  $description-line-height: 1.3125rem;

  .task-description {
    display: block;
    display: -webkit-box;
    height: $description-line-height * $description-rows;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $description-rows;
  }

  .scheduled-task-item {
    height: 14.875rem; // Based on the height of all the elements, assuming 4 rows of text in the description.
  }
</style>
