<template>
  <div>
    <automated-tasks-banner />

    <div class="mt-4">
      <toggle-view
        ref="listViewTable"
        class="w-100 ml-0 list-wrap"
        table-type="scrollable"
        table-class="table--spaced"
        :min-width="tableMinWidth(tableHeaders.length)"
        :view="viewType"
        :is-loading="loading"
        :data-count="(automatedTasks && automatedTasks.length) || 0"
        :use-flex-classes="false"
        :is-helpdesk="true"
        :use-contained-layout="true"
      >
        <template slot="header">
          <div class="row justify-content-between">
            <div class="d-flex align-items-center col-auto position-relative">
              <h5 class="d-flex font-weight-normal d-inline-block mr-3 mb-0">
                Automated Tasks
                <div
                  v-if="!loading"
                  class="d-flex align-items-center"
                >
                  <span
                    v-if="automatedTasks.length >= 0"
                    class="ml-2 not-as-small text-themed-fair"
                  >
                    ({{ automatedTasks.length }})
                    <i
                      v-tooltip="{
                        content: `Each automated task is evaluted as they appear in the ordered list below.`
                      }"
                      class="nulodgicon-information-circled text-info base-font-size align-text-top ml-1"
                      @click.stop.prevent="openTasksInfoModal"
                    />
                  </span>
                </div>
                <div
                  v-else
                  class="d-flex justify-content-end"
                >
                  <pulse-loader
                    color="#0d6efd"
                    size="0.5rem"
                    :loading="true"
                  />
                </div>
              </h5>
            </div>

            <div class="d-flex col justify-content-between align-items-start">
              <div class="d-flex col-6 col-sm-8 pl-0">
                <div class="d-inline-flex flex-grow-1 search-wrap align-items-center">
                  <input
                    ref="searchInput"
                    type="text"
                    class="form-control not-as-small"
                    placeholder="Search by name, number, trigger or actions"
                    data-tc-ticket-search
                    @input="updateSearch"
                  >
                  <div
                    class="position-absolute right-0 mr-2"
                    @click.prevent="$refs.searchInput.focus()"
                  >
                    <span class="nulodgicon-ios-search-strong search-icon nulodgicon-arrow-left-b" />
                  </div> 
                </div>
              </div>

              <div
                v-if="viewType === 'groupedList' && false"
                class="d-flex align-items-center justify-content-space-between"
              >
                <div class="d-flex justify-content-start align-items-center">
                  <div class="text-themed-light ml-1 not-as-small">Organize by:</div>
                  <kanban-options-select
                    id="kanban_option"
                    ref="kanban"
                    v-model="selectedTaskOrganizationOption"
                    name="kanban"
                    class="kanban-button-select border-0 w-fit-content"
                    style="background-color: transparent;"
                  />
                </div>
              </div>
            </div>

            <div class="col-auto d-flex align-items-center justify-content-end">
              <view-type-toggle
                class="ml-sm-2 ml-md-3 mt-0"
                :module-name="`automated_tasks`"
                :views="filteredViewToggles"
                btn-group-class="border-0 btn-group btn-group-sm shadow-none overflow-hidden"
                @view-type="setViewType"
              />
            </div>
            <span v-if="!isAssetsModule">
              <span
                class="btn btn-primary mr-3"
                role="button"
                @click="openGroupTaskModal"
              >
                <i class="nulodgicon-plus-round" />
                New Group
              </span>
            </span>
          </div>
        </template>

        <template slot="emptyData">
          <div
            v-if="!isLoading && !tasksPresent"
            class="no-tasks-container text-center bg-light"
          >
            <h3 
              class="text-secondary mb-4 pt-4" 
              data-tc-no-tickets
            >
              No tasks present.
            </h3>
            <div>
              <span v-if="searchTerm">
                Try removing your search query
                <span 
                  v-if="isWrite"
                  class="mr-1"
                >
                  or
                </span>
              </span>
              <router-link
                v-if="isWrite"
                :to="{ path: '/automated_tasks?new=true' }"
              >
                <i class="nulodgicon-plus-round mr-n0.5" /> 
                <span :class="{ 'text-uppercase': !searchTerm }">a</span>dd a task now.
              </router-link>
            </div>
          </div>
        </template>

        <draggable
          v-if="automatedTasks && automatedTasks.length > 0"
          slot="grid"
          :value="automatedTasks"
          handle=".handle"
          class="list-grid"
          :class="{'few-items': automatedTasks.length <= 2}"
          animation="150"
          dragover-bubble="true"
          v-bind="dragOptions"
          @change="updatePosition"
        >
          <transition-group
            class="d-contents"
            type="transition"
            name="smooth-move"
            tag="div"
          >
            <div
              v-for="task in automatedTasks"
              :key="task.id"
              class="d-flex mb-4"
            >
              <task-card
                :is-new-or-edit="false"
                :task="task"
                :module="$route.params.module"
                class="h-100"
                @update="updateTask"
                @fetch-tasks="fetchAutomatedTasks"
              />
            </div>
          </transition-group>
        </draggable>

        <template slot="list">
          <thead>
            <table-header-row
              ref="tableHeaderRow"
              :table-header="tableHeaders"
              :list-table-layout-module-style-override="listTableLayoutModuleStyle"
            />
          </thead>
          <draggable
            v-if="automatedTasks && automatedTasks.length > 0"
            :value="automatedTasks"
            handle=".handle"
            tag="tbody"
            animation="150"
            dragover-bubble="true"
            v-bind="dragOptions"
            @change="updatePosition"
          >
            <task-item
              v-for="task in automatedTasks"
              :key="task.id"
              :task="task"
              @update="updateTask"
              @fetch-tasks="fetchAutomatedTasks"
            />
          </draggable>
        </template>

        <template
          v-if="automatedTasks && automatedTasks.length > 0"
          slot="groupedList"
        >
          <div
            v-for="(taskGroup, index) in taskGroups"
            :key="index"
            class="box box--with-heading mb-4 mt-2"
          >
            <task-item-grouped-header
              :task-groups="taskGroups"
              :task-group="taskGroup || {}"
              :is-open="taskGroup.isOpen"
              @open-group-modal="openGroupModal"
              @group-deleted="handleDelete"
              @update="updateTask"
              @click="toggleTaskGroupOpenClosed(index)"
            />

            <div
              v-if="taskGroup.isOpen"
              class="box__inner p-0"
            >
              <task-item-grouped
                v-for="(task, taskIndex) in taskGroup.tasks"
                :key="task.id"
                class="h-100 my-0"
                :class="{'item--with-rounded-bottom': taskIndex === taskGroup.tasks.length - 1}"
                :is-new-or-edit="false"
                :task="task"
                :selected-group="taskGroup"
                :task-groups="taskGroups"
                :task-group="taskGroup.name"
                :module="$route.params.module"
                @update="updateTask"
                @fetch-tasks="fetchAutomatedTasks"
              />

              <div
                v-if="taskGroup.tasks && taskGroup.tasks.length === 0"
                class="p-5 text-center"
              >
                <span>No tasks in this group.
                  <span
                    v-if="isWrite"
                    class="d-inline-flex"
                  >
                    Please move one here from another task group or
                    <span
                      v-tooltip="`Add new task in group`"
                      class="d-inline-flex basic-transition mr-2 group-header__action-button-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle cursor-pointer text-primary"
                      @click="createNewTask(taskGroup)"
                    >
                      <i class="h6 mb-0 nulodgicon-plus-round ml-1 mr-1"/> create a new one
                    </span>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </template>
      </toggle-view>
    </div>
    <task-modal
      ref="taskModal"
      :value="linkedTask"
      :is-edit-task="!isNewTask"
      :selected-template="selectedTemplate"
      :selected-group="selectedTaskGroup"
      :task-groups="taskGroups"
      :open-linked-task="openLinkedTask"
      @update="updateTask"
      @cancel="handleClose"
    />

    <tasks-info-modal ref="tasksInfoModal" />

    <Teleport to="body">
      <sweet-modal
        ref="newTaskGroupModal"
        v-sweet-esc
        modal-theme="wide theme-sticky-footer"
        width="50%"
      >
        <template slot="title">
          <h2>{{ isEditingGroup ? 'Edit Group Name' : 'Create a New Group' }}</h2>
        </template>
        <template slot="default">
          <div class="mb-4">
            <label
              for="groupName"
              class="block font-weight-bold mb-1"
            >
              Group Name
            </label>
            <input
              id="groupName"
              v-model="newGroupName"
              class="form-control"
              type="text"
              placeholder="Enter group name"
            >
          </div>
        </template>
        <template slot="button">
          <div class="d-flex justify-content-end">
            <button
              class="btn btn-secondary mr-2"
              @click="resetGroupName"
            >
              Cancel
            </button>
            <button
              class="btn btn-primary"
              @click="createNewGroup(newGroupName)"
            >
              {{ isEditingGroup ? 'Update Group' : 'Create Group' }}
            </button>
          </div>
        </template>
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
import http from 'common/http';
import _debounce from "lodash/debounce";
import common from 'mixins/automated_tasks/common';
import _cloneDeep from 'lodash/cloneDeep';
import _get from 'lodash/get';
import { SweetModal } from 'sweet-modal-vue';

import { mapMutations, mapActions, mapGetters } from 'vuex';

import Draggable from 'vuedraggable';
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import permissionsHelper from 'mixins/permissions_helper';
import TaskModal from 'components/automated_tasks/new.vue';
import companyModule from 'mixins/company_module';
import tableLayoutStyle from "mixins/table_layout_style";
import ToggleView from '../shared/toggle_view.vue';
import TaskCard from './task_card.vue';
import ViewTypeToggle from '../shared/view_type_toggle.vue';
import TaskItem from './task_item.vue';
import TaskItemGrouped from './task_item_grouped.vue';
import TaskItemGroupedHeader from './task_item_grouped_header.vue';
import TasksInfoModal from './tasks_info_modal.vue';
import KanbanOptionsSelect from '../shared/kanban_options_select.vue';
import TableHeaderRow from "../shared/list_view_table_header.vue";
import AutomatedTasksBanner from './automated_tasks_banner.vue';

export default {
  components: {
    Draggable,
    ViewTypeToggle,
    PulseLoader,
    TaskCard,
    TaskItem,
    TaskItemGrouped,
    TaskItemGroupedHeader,
    ToggleView,
    TaskModal,
    KanbanOptionsSelect,
    TasksInfoModal,
    TableHeaderRow,
    AutomatedTasksBanner,
    SweetModal,
  },
  mixins: [
    common,
    permissionsHelper,
    companyModule,
    tableLayoutStyle,
  ],
  data() {
    return {
      linkedTask: {
        name: '',
        events: [{ nodes: [] }],
        actions: [{}],
      },
      readMore: false,
      viewType: null,
      selected: [],
      isLoading: false,
      selectedTemplate: 0,
      selectedTaskGroup: {},
      selectedTaskOrganizationOption: "Evaluation Order",
      showBanner: true,
      taskGroups: [],
      listToggleViews: [
        { name: 'List', type: 'list', iconClass: 'genuicon-list-no-split'},
        { name: 'Grouped List', type: 'groupedList', iconClass: 'genuicon-stack' },
        { name: 'Grid', type: 'grid', iconClass: 'genuicon-grid' },
      ],
      listTableLayoutModuleStyle: "help_tickets_automated_tasks",
      tableHeaders: [
        { "title": "#"},
        { "title": "Name"},
        { "title": "When this happens..."},
        { "title": "...do this"},
        { "title": "Task Order"},
        { "title": "Times Used"},
        { "title": "Actions"},
      ],
      openLinkedTask: false,
      isNewTask: false,
      newGroupName: '',
      isEditingGroup: false,
      groupToEdit: null,
    };
  },
  computed: {
    ...mapGetters(['automatedTasks', 'moduleFilter', 'searchTerm', 'currentModule']),
    isHelpdesk() {
      return this.currentModule === 'helpdesk';
    },
    loading() {
      return this.automatedTasks == null || this.isLoading;
    },
    selectionCommand() {
      if (navigator.appVersion.indexOf("Mac") !== -1)  {
        return "cmd + click";
      } 
        return "ctrl + click";
      
    },
    tasksPresent() {
      return !this.loading && this.automatedTasks.length > 0;
    },
    dragOptions() {
      return {
        animation: 200,
        disabled: false,
        ghostClass: "ghost",
      };
    },
    modules() {
      return [
        {
          id: 'help_tickets',
          name: 'Help Desk',
        },
      ];
    },
    defaultTasks() {
      return this.automatedTasks.filter(t => t.companyMailer);
    },
    companyTasks() {
      return this.automatedTasks;
    },
    assignmentTasks() {
      return this.automatedTasks.filter(t => t.name === "Notify Assigned-To Changes" );
    },
    attachmentTasks() {
      return this.automatedTasks.filter(t => ["Notify Attachment Added", "Notify Asset Updated"].includes(t.name) );
    },
    filteredViewToggles() {
      const hideGroupsView = this.$router.options.base !== '/help_tickets';
      return this.listToggleViews.filter(view => !(hideGroupsView && view.name === 'Grouped List'));
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  watch: {
    "$route.params": function () {
      this.openTaskModal();
    },
  },
  methods: {
    ...mapMutations(['setAutomatedTasks', 'setModuleFilter', 'setSearchTerm', 'setCurrentModule']),
    ...mapActions(['updatePositions']),

    onWorkspaceChange() {
      this.setModule();
      this.setAutomatedTasks(null);
      this.setSearchTerm('');
      this.fetchAutomatedTasks();
      this.openTaskModal();

      const onCompanyChange = () => {
        /* eslint-disable no-underscore-dangle */
        if (!this._isMounted || this._isBeingDestroyed) {
          document.removeEventListener("company-change", onCompanyChange);
          return;
        }
        /* eslint-enable no-underscore-dangle */
        this.setAutomatedTasks(null);
        this.fetchAutomatedTasks();
      };
      document.addEventListener("company-change", onCompanyChange);
    },
    setModule() {
      if (this.$router.options.base === '/help_tickets') {
        this.setCurrentModule('helpdesk');
      } else {
        this.setCurrentModule('managed_assets');
      }
    },
    createNewTask(taskGroup) {
      this.isNewTask = true;
      this.selectedTaskGroup = taskGroup;
      this.selectedTemplate = taskGroup.taskTemplate || 0;
      this.openLinkedTask = true;
      this.$refs.taskModal.open();
    },
    handleClose() {
      this.openLinkedTask = false;
      if (this.$refs.taskModal.visible) {
        this.$refs.taskModal.close();
      }
    },
    openGroupTaskModal() {
      this.$refs.newTaskGroupModal.open();
    },
    openGroupModal(group) {
      if (group) {
        this.isEditingGroup = true;
        this.groupToEdit = group;
        this.newGroupName = group.name;
      } else {
        this.isEditingGroup = false;
        this.groupToEdit = null;
        this.newGroupName = '';
      }
      this.$refs.newTaskGroupModal.open();
    },
    openTaskModal() {
      // Verify if "template" is really needed to passed as prop
      const { new: isNew, edit, taskId, template, taskGroup } = this.$route.query;
      if (isNew || edit) {
        this.isNewTask = isNew === 'true';
        if (taskId && !this.isNewTask) {
          this.fetchTask(taskId);
        } else {
          this.selectedTemplate = template ? Number(template) : 0;
          this.selectedTaskGroup = taskGroup || {};
          this.$refs.taskModal.open();
        }
      }
    },
    fetchTask(taskId) {
      this.isLoading = true;
      const params = { mod: this.derivedCompanyModule() };
      http
        .get(`/automated_tasks/${taskId}.json`, { params })
        .then(res => {
          this.$set(this, 'linkedTask', res.data);
          this.$refs.taskModal.open();
          this.isLoading = false;
        })
        .catch(error => {
          this.isLoading = false;
          this.emitError(`Sorry, there was an error loading task. (${error.response.data.message})`);
        });
    },
    updateSearch: _debounce(
      function handleUpdateSearch() {
        try {
          const searchText = this.$refs.searchInput.value;
          if (searchText) {
            let searchValue;
            if (searchText.trim() === '#') {
              searchValue = searchText.trim();
            } else {
              searchValue = searchText.replace(/#/g, '').trim();
            }
            this.setSearchTerm(searchValue);
          } else {
            this.setSearchTerm('');
          }
          this.fetchAutomatedTasks();
        } catch (error) {
          this.emitError(`Sorry, there was an error loading automated tasks (${error.message}).`);
        }
      },
      1000
    ),
    updateTask() {
      this.fetchAutomatedTasks();
    },
    setViewType(type) {
      this.viewType = type;
      if (type === 'groupedList') {
        this.fetchAutomatedTasks();
      }
    },
    updatePosition(data) {
      const moved = _get(data, 'moved.element');
      const newIndex = _get(data, 'moved.newIndex');
      const oldIndex = _get(data, 'moved.oldIndex');
      let idx = 0;

      if (!moved) {
        return;
      }
      const myTasks = _cloneDeep(this.automatedTasks);

      myTasks.splice(oldIndex, 1);
      myTasks.splice(newIndex, 0, moved);
      const idMap = myTasks.map((task) => {
        idx += 1;
        return {
          id: task.id,
          order: idx,
        };
      });

      http
        .put('/orderings.json', { tasks: idMap, isAssetsModule: this.isAssetsModule })
        .then((res) => {
          this.setAutomatedTasks(res.data);
          this.emitSuccess("Tasks updated successfully");
        })
        .catch(() => {
          this.emitError("Sorry, an error occurred during update.");
        });
    },
    changePageSize(e) {
      this.setPageSize(e.currentTarget.value);
      this.setPageIndex(0);
    },
    fetchAutomatedTasks() {
      this.isLoading = true;
      this.$store.dispatch('fetchAutomatedTasks')
        .then((res) => {
          this.setAutomatedTasks(res.data);
          this.fetchAutomatedTaskGroups();
          this.isLoading = false;
        })
        .catch(() => {
          this.emitError("Sorry, there was an error fetching the automated tasks.");
          this.isLoading = false;
        });
    },
    toggleTaskGroupOpenClosed(index) {
      this.taskGroups[index].isOpen = !this.taskGroups[index].isOpen;
    },
    fetchAutomatedTaskGroups() {
      if (this.isHelpdesk) {
        const ungroupedGroup = {
          tasks: [],
          name: 'Ungrouped',
          icon: 'genuicon-manager',
          isOpen: false,
          taskTemplate: 0,
          isDeletable: true,
        };

        http
          .get(`/automated_task_groups.json`)
          .then(res => {
            const backendGroups = res.data.map(group => ({
              ...group,
              tasks: [],
              isOpen: false,
            }));

            this.linkedTasks(ungroupedGroup, backendGroups);

          })
          .catch(error => {
            this.emitError(`Sorry, there was an error loading tasks. (${error.response?.data?.message || error.message})`);
            this.taskGroups = [ungroupedGroup];
          });
      } else {
        const assetsTaskGroups = [
          {
            'tasks': this.attachmentTasks || [],
            'name': 'Ungrouped',
            'usedCount': 0,
            'icon': 'genuicon-manager',
            'isOpen': false,
            'taskTemplate': 0,
          },
          {
            'tasks': this.companyTasks || [],
            'name': 'Notify Inactive Agents',
            'usedCount': 1248,
            'icon': 'genuicon-build-email-templates',
            'isOpen': false,
            'taskTemplate': 1,
          },
        ];
        this.taskGroups = assetsTaskGroups;
      }
    },
    linkedTasks(ungroupedGroup, backendGroups) {
      const groupedMap = {};
      this.automatedTasks.forEach(task => {
        const groupId = task.groupedAutomatedTaskId;
        if (groupId) {
          if (!groupedMap[groupId]) groupedMap[groupId] = [];
          groupedMap[groupId].push(task);
        } else {
          ungroupedGroup.tasks.push(task);
        }
      });

      backendGroups.forEach(group => {
        group.tasks = groupedMap[group.id] || [];
      });

      this.taskGroups = [ungroupedGroup, ...backendGroups];
    },
    createNewGroup() {
      if (!this.newGroupName.trim()) {
        this.emitError("Please enter a group name.");
        return;
      }
      if (this.isEditingGroup && this.groupToEdit?.id) {
        this.updateGroupName();
        return;
      }
      const payload = {
        automated_task_group: {
          name: this.newGroupName.trim(),
        },
      };

      http
        .post('/automated_task_groups', payload)
        .then(() => {
          this.fetchAutomatedTasks();
          this.resetGroupName();
          this.emitSuccess('Group task created successfully');
        })
        .catch(() => {
          this.emitError('Sorry, there was an error while creating grouped task');
        });
    },
    updateGroupName() {
      if (!this.groupToEdit?.id) return;

      if (!this.newGroupName.trim()) {
        this.emitError("Please enter a group name.");
        return;
      }

      const payload = {
        automated_task_group: {
          name: this.newGroupName.trim(),
        },
      };

      http
        .put(`/automated_task_groups/${this.groupToEdit.id}`, payload)
        .then(() => {
          this.fetchAutomatedTasks();
          this.resetGroupName();
          this.isEditingGroup = false;
          this.groupToEdit = null;
          this.emitSuccess('Group task updated successfully');
        })
        .catch(() => {
          this.emitError('Sorry, there was an error while updating grouped task');
        });
    },
    resetGroupName() {
      this.newGroupName = '';
      this.$refs.newTaskGroupModal.close();
    },
    handleDelete() {
      this.fetchAutomatedTasks();
    },
    openModal() {
      this.$refs.deleteModal.open();
    },
    openTasksInfoModal() {
      this.$refs.tasksInfoModal.open();
    },
    closeBanner() {
      this.showBanner = false;
    },
  },
};
</script>

<style lang="scss" scoped>
hr {
  border-color: $themed-fair;
  border-top-width: 2px;
}

.ghost {
  background-color: color.adjust($primary, $alpha: -0.9) !important;
  background-image: none !important;
  border: 2px dashed $gray-500;
  border-radius: $border-radius * 2;
  margin-top: -2px; // Offset the border
  opacity: 1;

  > *, &:after, &:before {
    opacity: 0 !important;
    visibility: hidden;
    transition: none;
  }
}

.kanban-button-select :deep(.btn-text),
.kanban-button-select :deep(.nulodgicon-arrow-down-b) {
  color: $themed-light !important;
}
.no-tasks-container {
  height: 50vh;
}
</style>
