<template>
  <div class="row mb-4 form-group">
    <div class="col-6">
      <input
        :value="'firstVendor'"
        required
        class="hidden"
        type="text"
      >
    </div>
    <number-of-days-input
      ref="numberOfDaysInput"
      class="col pr-0"
      :num-of-days="existingNumberOfDays"
      @input="numberOfDays"
    />
  </div>
</template>

<script>
  import NumberOfDaysInput from './number_of_days_input.vue';

  export default {
    components: {
      NumberOfDaysInput,
    },
    props: ['value'],
    data() {
      return {
        integrations: [],
        numOfDays: 0,
      };
    },
    computed: {
      existingNumberOfDays() {
        return this.value ? this.value.numberOfDays : 0;
      },
    },
    mounted() {
      this.numOfDays = this.value?.numberOfDays || 0;
    },
    methods: {
      numberOfDays(days) {
        this.numOfDays = days;
        this.$emit("input", { numberOfDays: this.numOfDays });
      },
      showErrors() {
        return false;
      },
      valid() {
        this.$refs.numberOfDaysInput.showErrors();
        return this.numOfDays > 0 && Number.isInteger(this.numOfDays);
      },
    },
  };
</script>
