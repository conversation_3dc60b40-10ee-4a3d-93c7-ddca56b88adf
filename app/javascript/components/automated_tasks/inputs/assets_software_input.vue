<template>
  <div class="row mb-4 form-group">
    <div class="col-6">
      <label>
        Select which software(s)?
      </label>
      <multi-select
        v-model="assetsSoftwares"
        label="name"
        placeholder="Select a software"
        track-by="name"
        :options="assetsSoftwareOptions"
        :multiple="true"
        @open="fetchAssetsSoftwares"
        @input="handleInput"
      />
      <span
        v-if="errorMessage"
        class="form-text small text-danger"
      >
        {{ errorMessage }}
      </span>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import MultiSelect from 'vue-multiselect';
  import inputMixin from './mixins/input';

  export default {
    components: {
      MultiSelect,
    },
    mixins: [inputMixin],
    props: ['value'],
    data() {
      return {
        assetsSoftwares: [],
        assetsSoftwareOptions: [],
      };
    },
    mounted() {
      this.assetsSoftwares = this.value?.assetSoftwares || [];
    },
    methods: {
      handleInput(option) {
        this.assetsSoftwares = option;
        this.showErrors();
        this.$emit("input", { assetSoftwares: this.assetsSoftwares });
      },
      showErrors() {
        if (!this.assetsSoftwares?.length) {
          this.errorMessage = "Please select at least one software.";
        }
        return !!this.errorMessage;
      },
      valid() {
        return this.assetsSoftwares.length > 0;
      },
      fetchAssetsSoftwares() {
        http
          .get("/asset_automation_tasks/asset_softwares.json")
          .then((res) => {
            this.assetsSoftwareOptions = res.data;
            this.assetsSoftwareOptions = this.assetsSoftwareOptions.map(name => ({ name }));
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
            this.emitError(
              `Sorry, there was an error loading results. Please try again later.`
            );
          });
      },
    },
  };
</script>
