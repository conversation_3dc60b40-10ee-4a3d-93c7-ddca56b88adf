<template>
  <div class="row mb-4 form-group">
    <div class="col-6">
      <label v-if="!isAnyIntegration">
        Select which integration(s)?
      </label>
      <multi-select
        v-if="!isAnyIntegration"
        v-model="integrations"
        label="name"
        placeholder="Select an integration"
        track-by="name"
        :options="integrationOptions"
        :multiple="true"
        @input="handleInput"
      />
      <span
        v-if="errorMessage && !isAnyIntegration"
        class="form-text small text-danger"
      >
        {{ errorMessage }}
      </span>
    </div>
    <number-of-days-input
      ref="numberOfDaysInput"
      class="col pr-0"
      :num-of-days="existingNumberOfDays"
      @input="numberOfDays"
    />
  </div>
</template>

<script>
  import MultiSelect from 'vue-multiselect';
  import inputMixin from './mixins/input';
  import NumberOfDaysInput from './number_of_days_input.vue';
  import onboardingOptions from '../../assets/onboarding_options';

  export default {
    components: {
      MultiSelect,
      NumberOfDaysInput,
    },
    mixins: [onboardingOptions, inputMixin],
    props: ['nodeType', 'value'],
    data() {
      return {
        integrations: [],
        numOfDays: 0,
      };
    },
    computed: {
      integrationOptions() {
        return this.onboardingOptions
          .filter(opt => opt.thirdPartyConnector)
          .map(opt => ({ name: opt.name,  searchableName: opt.searchableName }));
      },
      isAnyIntegration() {
        return this.nodeType.subjectClass === "AnyIntegration";
      },
      existingNumberOfDays() {
        return this.value ? this.value.numberOfDays : 0;
      },
    },
    watch: {
      isAnyIntegration() {
        if (!this.isAnyIntegration) {
          this.$emit("input", { integrations: [], numberOfDays: this.numOfDays });
        }
      },
    },
    mounted() {
      this.integrations = this.value?.integrations || [];
      this.numOfDays = this.value?.numberOfDays || 0;
    },
    methods: {
      handleInput(option) {
        this.integrations = option;
        this.showErrors();
        this.$emit("input", { integrations: this.integrations, numberOfDays: this.numOfDays });
      },
      numberOfDays(days) {
        this.numOfDays = days;
        if (this.isAnyIntegration) {
          this.$emit("input", { integrations: [], numberOfDays: this.numOfDays });
        } else {
          this.$emit("input", { integrations: this.integrations, numberOfDays: this.numOfDays });
        }
      },
      showErrors() {
        if (!this.isAnyIntegration) {
          if (!this.integrations || !this.integrations.length) {
            this.errorMessage = "Please select at least one integration.";
          }
        }
        return !!this.errorMessage;
      },
      valid() {
        this.$refs.numberOfDaysInput.showErrors();
        if (this.isAnyIntegration) {
          return this.numOfDays > 0 && Number.isInteger(this.numOfDays);
        }
        return this.numOfDays > 0 && Number.isInteger(this.numOfDays) && this.integrations.length > 0;
      },
    },
  };
</script>
