<template>
  <div>
    <label>
      Select number of day(s)
    </label>
    <input
      v-model="numberOfDays"
      required
      class="form-control"
      type="number"
      min="0"
      step="1"
      @change="handleInput"
    >
    <span
      v-if="errorMessage"
      class="form-text small text-danger"
    >
      {{ errorMessage }}
    </span>
  </div>
</template>

<script>
  import inputMixin from './mixins/input';

  export default {
    mixins: [inputMixin],
    props: ['numOfDays'],
    data() {
      return {
        numberOfDays: 0,
      };
    },
    mounted() {
      this.numberOfDays = this.numOfDays || 0;
    },
    methods: {
      showErrors() {
        if (this.numberOfDays <= '0') {
          this.errorMessage = 'Please select valid number of days.';
        } else if (Number.isInteger(Number(this.numberOfDays)) === false) {
          this.errorMessage = 'Please enter days without decimal';
        }
        return !!this.errorMessage;
      },
      valid() {
        return true;
      },
      handleInput(input) {
        this.numberOfDays = input.target.value;
        this.showErrors();
        this.$emit('input', Number(this.numberOfDays));
      },
    },
  };
</script>
