<template>
  <div>
    <div class="col-12 row p-0">
      <div class="col-6 mb-3 form-group">
        <label>
          Select the form
        </label>
        <multi-select
          :id="`custom-form-${id}`"
          :value="customForms"
          label="formName"
          :options="formOptions"
          :disabled="disabled"
          placeholder="Select a form"
          track-by="id"
          multiple
          required
          data-tc-form-select
          @input="handleCustomForm"
          @open="fetchCustomForms"
        />
        <input
          :value="customForms"
          required
          class="hidden"
          type="text"
        >
        <span
          v-if="customFormError"
          class="form-text small text-danger"
        >
          {{ customFormError }}
        </span>
      </div>

      <div class="col-12 align-items-center pr-0">
        <div
          class="row col-12 rounded condition-field pl-2 pr-0 py-2"
          :class="{ 'invalid-error':  false }"
        >
          <input
            :id="`form-field-${id}`"
            :value="formField"
            :disabled="disabled"
            required
            class="hidden"
            type="text"
          >
          <multi-select
            :id="`form-field-${id}`"
            :value="formField"
            label="name"
            :options="fieldOptions"
            track-by="name"
            :multiple="false"
            required
            :disabled="disabled"
            :select-label="''"
            :deselect-label="''"
            placeholder="Select Form Field"
            class="multiselect-view multiselect-field"
            data-tc-field-select
            @input="handleFormField"
            @open="fetchFormFields"
          >
            <template
              slot="singleLabel"
              slot-scope="props"
            >
              <div
                v-if="props.option"
                class="align-items-center d-flex"
              >
                <div class="d-inline-block">
                  <i
                    class="text-muted smallest mr-1 ml-n1"
                    :class="isAssets ? 'genuicon-module-assets' : 'genuicon-build-custom-forms'"
                  />
                </div>
                <div class="d-inline-block">
                  {{ objName(props.option.name) }}
                </div>
                <div
                  v-if="props.option"
                  class="pl-2 d-inline text-very-muted small"
                >
                  {{ objName(props.option.fieldAttributeType) }}
                </div>
              </div>
            </template>

            <template
              slot="option"
              slot-scope="props"
            >
              <div class="d-inline align-items-center">
                {{ objName(props.option.name) }}
                <span class="ml-2 text-very-muted small">
                  {{ objName(props.option.fieldAttributeType) }}
                </span>
              </div>
            </template>
          </multi-select>

          <!-- Any / Is / Is Not -->
          <div 
            v-if="formField && !isTicketFieldStateEvent"
            class="p-0 form-group mb-0"
          >
            <div class="col p-0">
              <multi-select
                :id="`match-option-${id}`"
                :value="valueOption"
                label="name"
                class="multiselect-view"
                :class="colorClass(valueOption)"
                :options="valueOptions"
                track-by="name"
                :multiple="false"
                :disabled="disabled"
                :select-label="``"
                :deselect-label="``"
                data-tc-field-select
                @input="handleValueOption"
              >
                <template 
                  slot="singleLabel" 
                  slot-scope="props"
                >
                  <div class="d-flex align-items-center">
                    <span class="option__desc mx-0.5">
                      <span class="option__title">{{ props.option.name }}</span>
                    </span>
                    <i class="nulodgicon-arrow-down-b ml-1"/>
                  </div>
                </template>
                <template 
                  slot="option" 
                  slot-scope="props"
                >
                  <div
                    class="option__desc"
                    :checked="valueOption"
                  >
                    <span class="option__title">{{ props.option.name }}</span>
                  </div>
                </template>
              </multi-select>
            </div>
          </div>

          <div
            v-if="formField && valueOption && valueOption.name !== 'Any'"
            class="col-xl mt-xl-0 col-lg-12 mt-lg-2 row"
          >
            <div
              v-if="isStaffList"
              class="col-auto form-group p-0 mb-0"
            >
              <multi-select
                :id="`staff-list-${id}`"
                :value="selectedMatchType"
                label="name"
                placeholder="Select match type"
                class="multiselect-view"
                :class="colorClass(selectedMatchType)"
                required
                :disabled="disabled"
                :allow-empty="false"
                :options="matchOptions"
                :select-label="``"
                :deselect-label="``"
                @input="handleMatchType"
              />
            </div>
            <div class="col form-group mb-0 trix-toolbar--very-small">
              <text-input
                v-if="partialMatch"
                :id="`text-input-${id}`"
                ref="component"
                :value="formValue"
                :field="formField"
                :required="required"
                :disabled="disabled"
                :module-name="`automatedTasks`"
                :select-label="``"
                :deselect-label="``"
                @input="handleInput"
              />
              <div
                v-else
                :class="{
                  'd-flex align-items-start mt-2.5 h-100': shouldBaseline,
                  'pt-2': shouldHaveTopPadding(component, formValue)
                }"
              >
                <component
                  :is="component"
                  :id="`${component}-${id}`"
                  ref="component"
                  :class="{ 'ml-2 pt-2': shouldBaseline}"
                  :value="formValue"
                  :field="formField"
                  :required="required"
                  :disabled="disabled"
                  :module-name="`automatedTasks`"
                  :select-label="``"
                  :deselect-label="``"
                  @input="handleInput"
                />
              </div>
              <span
                v-if="valueError"
                class="form-text small text-danger"
              >
                {{ valueError }}
              </span>
            </div>
            <slot name="time_interval"/>
          </div>
        </div>
      </div>

      <div class="col-6 mb-3 form-group d-none">
        <label>
          Select the form field to trigger
        </label>
        <input
          :id="`form-field-${id}`"
          :value="formField"
          :disabled="disabled"
          required
          class="hidden"
          type="text"
        >
        <multi-select
          :id="`form-field-${id}`"
          :value="formField"
          label="name"
          :options="fieldOptions"
          track-by="name"
          :multiple="false"
          required
          :disabled="disabled"
          data-tc-field-select
          @input="handleFormField"
          @open="fetchFormFields"
        >
          <template
            slot="singleLabel"
            slot-scope="props"
          >
            <div
              v-if="props.option"
              class="align-items-center d-flex"
            >
              <div class="d-inline-block">
                {{ objName(props.option.name) }}
              </div>
              <div
                v-if="props.option"
                class="pl-2 d-inline text-very-muted small"
              >
                {{ objName(props.option.fieldAttributeType) }}
              </div>
            </div>
          </template>

          <template
            slot="option"
            slot-scope="props"
          >
            <div class="d-inline align-items-center">
              {{ objName(props.option.name) }}
              <span class="ml-2 text-very-muted small">
                {{ objName(props.option.fieldAttributeType) }}
              </span>
            </div>
          </template>
        </multi-select>
        <span
          v-if="formFieldError"
          class="form-text small text-danger"
        >
          {{ formFieldError }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import MultiSelect from 'vue-multiselect';
import strings from 'mixins/string';
import http from 'common/http';
import _cloneDeep from 'lodash/cloneDeep';
import _get from 'lodash/get';
import _difference from 'lodash/difference';
import permissionsHelper from 'mixins/permissions_helper';

import fieldInputHelper from 'mixins/field_input_helper';
import inputMixin from './mixins/input';

import * as formViewerFields from '../../shared/custom_forms/form_viewer_fields/index';

const ANY_FORM_OPTION = {
  id: 'all',
  isActive: true,
  formName: 'Any Form',
};

export default {
  mixins: [inputMixin, strings, permissionsHelper, fieldInputHelper],
  components: {
    MultiSelect,
    ...formViewerFields,
  },
  props: {
    id: {
      type: Number,
    },
    isTicketFieldStateEvent: {
      type: Boolean,
      default: false,
    },
    companyMailer: {
      type: Boolean,
    },
  },
  data() {
    return {
      formOptions: this.initFormOptions(),
      customFormError: null,
      fieldOptions: this.initFieldOptions(),
      formFieldError: null,
      valueError: null,
      currentUrl: '',
      matchOptions: [{ name: 'Exact match', value: false }, { name: 'Partial match', value: true }],
      midSizeInputFields: [ 'people_list',
                            'asset_list',
                            'list',
                            'category',
                            'department',
                            'contract_list',
                            'date',
                            'location_list',
                            'number',
                            'phone',
                            'telecom_list',
                          ],
      valueOptions: [
        { name : 'Any' },
        { name : 'Is' },
        { name : 'Is not' },
      ],
      singularFieldAttributeType: [ 'text',
                                    'text_area',
                                    'rich_text',
                                    'number',
                                    'date',
                                    'phone',
                                    'priority',
                                    'status',
                                    'list',
                                    'category',
                                    'department',
                                    'radio_button',
                                    'email',
                                  ],
    };
  },
  computed: {
    disabled() {
      return this.companyMailer;
    },
    formAttribute() {
      if (!this.formField) {
        return null;
      }
      switch(this.formField.fieldAttributeType) {
        case "asset_list":
          return "assets";
        case "contract_list":
          return "contracts";
        case "people_list":
          return this.partialMatch ? 'partialValue' : 'contributors';
        case "location_list":
          return "locations";
        case "telecom_list":
          return "telecoms";
        case "vendor_list":
          return "vendors";
        case "status":
          return "status";
        case "priority":
          return "priority";
        default:
          return "text";
      }
    },
    required() {
      return !this.anyValue;
    },
    formValue() {
      if (!this.formField || !this.formField.fieldAttributeType) {
        return null;
      }
      switch(this.formField.fieldAttributeType) {
        case "asset_list":
        case "contract_list":
        case "people_list":
        case "location_list":
        case "telecom_list":
        case "vendor_list":
          return this.getFormValue();
        case "status":
        case "priority":
        case "rich_text":
        case "text":
        default:
          return _get(this, `value.${this.formAttribute}`);
      }
    },
    formValuePresent() {
      if (!this.formField || !this.formField.fieldAttributeType) {
        return false;
      }
      switch(this.formField.fieldAttributeType) {
        case "asset_list":
        case "contract_list":
        case "people_list":
        case "location_list":
        case "telecom_list":
        case "vendor_list":
          return this.checkFormValue();
        case "status":
        case "priority":
        case "rich_text":
        case "text":
        default:
          return !!this.formValue;
      }
    },
    component() {
      if (!this.formField) {
        return null;
      }
      const attributeType = this.toTitle(this.formField.fieldAttributeType).replace(/\s/g,'');
      return `${attributeType}Input`;
    },
    isStaffList() {
      return this.formField.fieldAttributeType === 'people_list';
    },
    selectedMatchType() {
      return this.matchOptions.find((opt) => opt.value === this.partialMatch);
    },
    formFieldDisabled() {
      return !this.customForms || this.customForms.length === 0 || this.isCustomFormTask;
    },
    formIds() {
      if (this.formFieldDisabled || !this.customForms) {
        return [];
      }
      return this.customForms.map((f) => f.id);
    },
    negateValue() {
      return _get(this, 'value.negate', false);
    },
    anyValue() {
      return _get(this, 'value.any', false);
    }, 
    customForms() {
      return _get(this, 'value.customForms', [ ANY_FORM_OPTION ]);
    },
    formField() {
      return _get(this, 'value.formField');
    },
    partialMatch() {
      return _get(this, 'value.partialMatch', false);
    },
    isUserfield() {
      return this.midSizeInputFields.includes(this.formField.fieldAttributeType);
    },
    valueOption() {
      if (this.anyValue) {
        return { name: 'Any'};
      }
      if (this.negateValue === false) {
        return { name: 'Is'};
      } else if (this.negateValue === true) {
        return { name: 'Is not'};
      }
      return null;
    },
    isRichTextComponent() {
      return !this.isStaffList && this.component === 'RichTextInput';
    },
  },
  watch: {
    currentUrl(newVal) {
      this.isAssets = newVal.includes('managed_assets');
    },
  },
  methods: {
    onWorkspaceChange() {
      this.fetchFieldOptions();
      this.currentUrl = window.location.href;
    },
    initFieldOptions() {
      const field = _get(this, 'value.formField');
      if (!field) {
        return [];
      }
      return [ field ];
    },
    initFormOptions() {
      return _get(this, 'value.customForms', [ ANY_FORM_OPTION ]);
    },
    getFormValue() {
      if (this.formField.fieldAttributeType === 'people_list' && this.partialMatch) {
        return _get(this, `value.${this.formAttribute}`);  
      }
      return _get(this, `value.${this.formAttribute}`, []);
    },
    checkFormValue() {
      if (this.formField.fieldAttributeType === 'people_list' && this.partialMatch) {
        return !!this.formValue;  
      }
      return this.formValue.length > 0;
    },
    objName(name) {
      const str = name || "";
      return str.replaceAll("_", " ");
    },
    handleCustomForm(value) {
      const added = _difference(value, this.customForms);
      const deleted = _difference(this.customForms, value);
      let customForms;
      if (added.length > 0) {
        if (added[0].id === 'all') {
          customForms = [ added[0] ];
        } else if (this.customForms.length === 1 && this.customForms[0].id === 'all') {
          customForms = added;
        } else {
          customForms = value;
        }
      } else {
        customForms = value;
      }

      let formField = null;
      if (added.length === 0 && deleted.length === 0) {
        formField = this.formField;
      }

      const obj = {
        customForms,
        formField,
        negate: false,
        any: false,
        partialMatch: false,
      };
      obj[this.formAttribute] = null;
      this.$emit('input', obj);
      this.fetchFormFields();
      this.customFormError = null;
      this.formFieldError = null;
    },
    customForm() {
      if (!this.customFormId || !this.customForms) {
        return null;
      }
      return this.customForms.find((form) => form.id === this.customFormId);
    },
    handleFormField(value) {
      this.$emit('input', {
        customForms: this.customForms,
        formField: value,
        value: null,
        negate: this.negateValue,
        any: this.anyValue,
        partialMatch: this.partialMatch,
      });
      this.fetchFieldOptions(value);
      this.customFormError = null;
      this.formFieldError = null;
    },
    fetchFormFields() {
      if (!this.customForms || this.customForms.length === 0) {
        this.fieldOptions = [];
        return;
      }
      const ids = this.customForms.map((f) => f.id);
      const formId = ids.join(",");
      let url = `/custom_forms/${formId}/custom_form_field_options.json`;
      if (formId === 'all') {
        url = `/custom_form_field_options.json`;
      }
      const params = { company_module: 'helpdesk' };
      http
        .get(url, { params })
        .then(res => {
          this.fieldOptions = res.data;
          if (this.isTicketFieldStateEvent) {
            this.fieldOptions = this.fieldOptions.filter(option => this.singularFieldAttributeType.includes(option.fieldAttributeType));
          }
          this.fieldOptions = this.fieldOptions.filter(ff => (ff.fieldAttributeType !== 'attachment' && ff.fieldAttributeType !== 'avatar') );
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error gathering form data. Please refresh the page and try again.`);
        });
    },
    fetchFieldOptions(field) {
      if (this.formIds.length === 0 || !field || field.options) {
        return;
      }
      const formId = this.formIds.join(",");
      let url = `/custom_forms/${formId}/field_options.json`;
      if (formId === 'all') {
        url = `/field_options.json`;
      }
      let params = { };
      if (field) {
        params = {
          name: field.name,
          attr_type: field.fieldAttributeType,
          module: 'helpdesk',
        };
      }
      http
        .get(url, { params })
        .then(res => {
          field.options = res.data;
          field.label = field.name.replace("_", " ");
          this.$emit('input', {
            customForms: this.customForms,
            formField: _cloneDeep(field),
            value: this.formValue,
            negate: this.negateValue,
            any: this.anyValue,
            partialMatch: this.partialMatch,
          });
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error gathering field data. Please refresh the page and try again.`);
        });
    },
    fetchCustomForms() {
      const params = { company_module: 'helpdesk', active: true };
      http
        .get("/custom_form_options.json", { params })
        .then(res => {
          this.formOptions = res.data;
          this.formOptions = this.formOptions.map(({ id, name }) => ({ id, formName: name }));
          this.formOptions.unshift(ANY_FORM_OPTION);
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error gathering form options. Please refresh the page and try again.`);
        });
    },
    showFormErrors() {
      if (!this.customForms || this.customForms.length === 0) {
        this.customFormError = "Please select a form.";
      } else {
        this.customFormError = null;
      }
      return !!this.customFormError;
    },
    showFieldErrors() {
      if (!this.formField) {
        this.formFieldError = "Please select a form field.";
      } else {
        this.formFieldError = null;
      }
      return !!this.formFieldError;
    },
    showValueErrors() {
      if (!this.anyValue && !this.formValuePresent) {
        this.valueError = "Must either have a value or select any value";
      } else {
        this.valueError = null;
      }
    },
    showErrors() {
      return this.showFormErrors() || this.showFieldErrors() || this.showValueErrors();
    },
    handleInput(value) {
      const obj = {
        customForms: this.customForms,
        formField: this.formField,
        negate: this.negateValue,
        any: this.anyValue,
        partialMatch: this.partialMatch,
      };
      obj[this.formAttribute] = value;
      this.valueError = null;
      this.$emit('input', obj);
    },
    handleMatchType(type) {
      const isPartialMatch = this.matchOptions.find((opt) => opt.name === type.name).value;
      const obj = {
        customForms: this.customForms,
        formField: this.formField,
        negate: this.negateValue,
        any: this.anyValue,
        partialMatch: isPartialMatch,
      };
      this.valueError = null;
      this.$emit('input', obj);
    },
    colorClass(option) {
      const positiveOptions = ['Is', 'Exact match'];
      const negativeOptions = ['Is not'];

      if (positiveOptions.includes(option?.name)) {
        return 'multiselect-positive';
      } else if (negativeOptions.includes(option?.name)) {
        return 'multiselect-negative';
      }
      return 'multiselect-neutral';
    },
    /*
    initSelectedCustomForms() {
      if (!this.customForms) {
        let forms = _get(this, 'value.value.customForms');
        if (forms) {
          this.customForms = forms;
        }
        if (!this.customForms) {
          this.customForms = [ ANY_FORM_OPTION ];
        }
      }
    },
    */
    valid() {
      let valueValid = false;
      if (this.formValue instanceof Array) {
        valueValid = this.formValue.reduce((initValue, newValue) => initValue || !!newValue, false);
      } else {
        valueValid = !!this.formValue;
      }
      return !!(this.customForms && this.customForms.length > 0 &&
          this.fieldOptions && this.fieldOptions.length > 0 &&
          (valueValid || this.anyValue));
    },
    handleValueOption(val) {
      let negate = this.negateValue;
      let {anyValue} = this;
      if (val) {
        if (val.name === 'Any') {
          anyValue = !anyValue;
          negate = null;
        } else {
          if (negate == null) {
            negate = val.name !== "Is";
          } else {
            negate = !negate;
          }
          anyValue = false;
        }
      }
      const obj = {
        customForms: this.customForms,
        formField: this.formField,
        negate,
        any: anyValue,
        partialMatch: this.partialMatch,
      };
      if (!anyValue) {
        obj[this.formAttribute] = this.formValue;
      } else {
        obj[this.formAttribute] = null;
      }
      return this.$emit('input', obj);
    },
    checkValue(val) {
      if (val === "Any") {
        return this.anyValue ? "checked" : '';
      } if (val === "Is") {
        return this.negateValue === false ? "checked" : '';
      } if (val === "Is not") {
        return this.negateValue === true ? "checked" : '';
      }
      return null;
    },
  },
};
</script>

<style lang="scss" scoped>
  .condition-toggle {
    justify-content: space-between;
  }

  .nulodgicon-arrow-down-b {
    font-size: 0.7rem;
  }

  :deep(.multiselect__option) {
    display: flex;

    input {
      margin-right: 10px;
    }
  }

  .condition-field {
    background-color: $themed-box-bg;
    border: 1px solid;
    border-color: $themed-light;
    margin-left: 0;
    padding: 0 0.25rem;
  }

  :deep(.multiselect__content-wrapper) {
    min-width: 14rem;
  }

  :deep(.multiselect-view) {
    &.multiselect-positive .multiselect__single {
      background-color: $green-light;
    }

    &.multiselect-neutral .multiselect__single {
      background-color: $themed-muted;
    }

    &.multiselect-negative .multiselect__single {
      background-color: $red-light;
    }

    &.multiselect-field .multiselect__single {
      background: $themed-light;
      color: $themed-secondary;
      margin-left: -.25rem;
      margin-right: .25rem;
      padding-left: .5rem !important;
    }
  }

  .multiselect-view {
    width: fit-content;
    
    :deep(.multiselect__tags) {
      border-color: $themed-box-bg;
      padding-right: 0.5rem;
      width: fit-content;
    }

    :deep(.multiselect__select) {
      display: none;
    }

    :deep(.multiselect__single) {
      background-color: #41b883;
      color: white;
      display: block;
      max-width: 14rem;
      overflow: hidden;
      padding-block: 1px;
      padding-right: 0.5rem;
      padding-left: 0.5rem;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
