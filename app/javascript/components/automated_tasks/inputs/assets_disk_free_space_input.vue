<template>
  <div class="col-12 row mb-1 p-0">
    <div class="col-6">
      <label>
        Disk Space less than:
      </label>
      <input
        v-model="freeSpace"
        required
        class="form-control"
        type="number"
        min="0"
        @change="handleInput"
      >
      <span
        v-if="errorMessage"
        class="form-text small text-danger"
      >
        {{ errorMessage }}
      </span>
    </div>
    <div class="col-6 mt-4">
      <multi-select
        v-model="freeSpaceUnit"
        label="name"
        placeholder="Select free space unit"
        track-by="name"
        :options="freeSpaceUnitOptions"
        :multiple="false"
        :allow-empty="false"
      />
    </div>
  </div>
</template>

<script>
  import MultiSelect from 'vue-multiselect';
  import inputMixin from './mixins/input';

  export default {
    components: {
      MultiSelect,
    },
    mixins: [inputMixin],
    props: ['value'],
    data() {
      return {
        freeSpace: 0,
        freeSpaceUnit: { name: 'GB' },
      };
    },
    computed: {
      freeSpaceUnitOptions() {
        return [{name: 'GB'}, {name: '%'}];
      },
      isAnyIntegration() {
        return this.nodeType.subjectClass === "AnyIntegration";
      },
    },
    watch: {
      freeSpaceUnit() {
        this.$emit('input', { freeSpace: this.freeSpace, freeSpaceUnit: this.freeSpaceUnit });
      },
    },
    mounted() {
      this.freeSpace = this.value?.freeSpace || 0;
      this.freeSpaceUnit = this.value?.freeSpaceUnit || {name: 'GB'};
    },
    methods: {
      handleInput(input) {
        this.freeSpace = input.target.value;
        this.showErrors();
        this.$emit('input', { freeSpace: this.freeSpace, freeSpaceUnit: this.freeSpaceUnit });
      },
      showErrors() {
        if (this.freeSpace <= '0') {
          this.errorMessage = 'Please enter a valid number.';
        } else if (this.freeSpaceUnit.name === '%' && this.freeSpace > 100) {
          this.errorMessage = 'Please enter value less than 100.';
        }
        return !!this.errorMessage;
      },
      valid() {
        this.showErrors();
        if (this.freeSpaceUnit.name === '%') {
          return this.freeSpace > 0 && this.freeSpace < 100;
        }
        return this.freeSpace > 0;
      },
    },
  };
</script>
