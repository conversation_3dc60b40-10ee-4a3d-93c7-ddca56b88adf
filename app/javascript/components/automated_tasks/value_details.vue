<template>
  <div>
    <component
      :is="component"
      :id="id"
      ref="component"
      :class="{ 'col-12 p-0' : !isFormFieldIncluded }"
      :value="value"
      :required="true"
      :is-custom-form-task="isCustomFormTask"
      :is-ticket-field-state-event="isTicketFieldStateEvent"
      :node-type="nodeType"
      :company-mailer="companyMailer"
      @input="handleInput"
    />
  </div>
</template>

<script>
import _get from 'lodash/get';
import common from 'mixins/automated_tasks/common';

import TextInput from './inputs/text_input.vue';
import PriorityInput from './inputs/priority_input.vue';
import DurationInput from './inputs/duration_input.vue';
import ContributorInput from './inputs/contributor_input.vue';
import VendorInput from './inputs/vendor_input.vue';
import AssetInput from './inputs/asset_input.vue';
import ContractInput from './inputs/contract_input.vue';
import FormValueInput from './inputs/form_value_input.vue';
import FormFieldInput from './inputs/form_field_input.vue';
import TelecomServiceInput from './inputs/telecom_service_input.vue';
import DayOfTheWeekInput from './inputs/day_of_the_week_input.vue';
import DateOfTheMonthInput from './inputs/date_of_the_month_input.vue';
import DateOfTheYear from './inputs/date_of_the_year_input.vue';
import DateOfTheYearQuarter from './inputs/date_of_the_year_quarter_input.vue';
import DateOfTheFiscalYear from './inputs/date_of_the_fiscal_year_input.vue';
import DateOfTheYearBiAnnual from './inputs/date_of_the_year_bi_annual_input.vue';
import SourceInput from './inputs/source_input.vue';
import TimeStateInput from './inputs/time_state_input.vue';
import BusinessHourInput from './inputs/business_hour_input.vue';
import PeopleListInput from './inputs/people_list_input.vue';
import AssetsIntegrationInput from './inputs/assets_integration_input.vue';
import AssetsSoftwareInput from './inputs/assets_software_input.vue';
import NumberOfDaysInput from './inputs/number_of_days_input.vue';
import AssetsDiskFreeSpaceInput from './inputs/assets_disk_free_space_input.vue';
import AgentSyncInput from './inputs/agent_sync_input.vue';

export default {
  components: {
    TextInput,
    PriorityInput,
    DurationInput,
    ContributorInput,
    VendorInput,
    AssetInput,
    ContractInput,
    FormValueInput,
    FormFieldInput,
    TelecomServiceInput,
    DayOfTheWeekInput,
    DateOfTheMonthInput,
    DateOfTheYear,
    DateOfTheYearQuarter,
    DateOfTheFiscalYear,
    DateOfTheYearBiAnnual,
    SourceInput,
    TimeStateInput,
    BusinessHourInput,
    PeopleListInput,
    AssetsIntegrationInput,
    NumberOfDaysInput,
    AssetsSoftwareInput,
    AssetsDiskFreeSpaceInput,
    AgentSyncInput,
  },
  mixins: [common],
  props: ['value', 'nodeType', 'isCustomFormTask', 'isTicketFieldStateEvent', 'id', 'companyMailer'],
  data() {
    return {
      node: { ...this.value },
    };
  },
  computed: {
    keyName() {
      const name = _get(this, "nodeType.name");
      if (name && name.toLowerCase().trim() === "any integration") {
        return "any integration";
      } else if (name && name.toLowerCase().trim() === "any asset") {
        return "any asset";
      } else if (name && name.toLowerCase().trim() === "any agent") {
        return "any agent";
      }
      const matches = name.match(/[\{\[]an?\s?(.+)[\}\]]/) || name.match(/[\{\[]a?\s?(.+)[\}\]]/);
      if (matches && matches.length > 1) {
        return matches[1];
      }
      return null;
    },
    component() {
      switch(this.keyName) {
        case 'text':
          return 'TextInput';
        case 'priority':
          return 'PriorityInput';
        case 'duration':
          return 'DurationInput';
        case 'status':
          return 'StatusInput';
        case 'user or group':
          return 'ContributorInput';
        case 'vendor':
          return 'VendorInput';
        case 'asset':
          return 'AssetInput';
        case 'form value':
          return 'FormValueInput';
        case 'form field':
         return this.isTicketFieldStateEvent ? 'TimeStateInput' : 'FormFieldInput';
        case 'contract':
          return 'ContractInput';
        case 'telecom service':
          return 'TelecomServiceInput';
        case 'day of the week':
          return 'DayOfTheWeekInput';
        case 'date of the month':
          return 'DateOfTheMonthInput';
        case 'date of the year':
          return 'DateOfTheYear';
        case 'date of each year quarter':
          return 'DateOfTheYearQuarter';
        case 'date of fiscal year':
          return 'DateOfTheFiscalYear';
        case 'date of each year bi-annually':
          return 'DateOfTheYearBiAnnual';
        case 'description':
          return 'TextInput';
        case 'with source':
          return 'SourceInput';
        case 'during':
          return 'BusinessHourInput';
        case 'people list fields':
          return 'PeopleListInput';
        case 'any integration':
          return 'AssetsIntegrationInput';
        case 'specific integration':
          return 'AssetsIntegrationInput';
        case 'specific software':
          return 'AssetsSoftwareInput';
        case 'any asset':
          return 'AssetsDiskFreeSpaceInput';
        case 'any agent':
          return 'AgentSyncInput';
        default:
          return null;
      }
    },
    isFormFieldIncluded() {
      const fieldValues = ['form field', 'with source'];
      if (this.isTicketFieldStateEvent) {
        fieldValues.push('remains in the same state');
      }
      return fieldValues.includes(this.keyName);
    },
  },
  methods: {
    valid() {
      if (this.$refs.component) {
        return this.$refs.component.valid();
      }
      return true;
    },
    showErrors() {
      if (this.$refs.component) {
        this.$refs.component.showErrors();
      }
    },
    handleInput(node) {
      this.$emit('input', node);
    },
  },
};
</script>

