<template>
  <div class="d-inline-flex col-12 justify-content-between">
    <h5 class="mb-0 text-secondary font-weight-normal align-self-center">
      <b>{{ label }}</b>
      <span
        v-if="task.id"
        class="text-muted small"
        data-tc="task id"
      >
        #{{ task.serialNumber }}
      </span>
    </h5>
    <div
      v-if="task.companyMailer"
      v-tooltip="'Default tasks can not be altered.  Please edit as a copy of this task instead.'"
      class="badge bg-lighter has-tooltip ml-2 px-3 py-2"
    >
      <i class="nulodgicon-locked" />
      Default Task
    </div>
    <div class="float-right">
      <button
        v-if="task.id"
        v-tooltip.top="'Edit as a copy'"
        class="btn btn-lighter btn-flat rounded-circle mr-2 px-2 py-1"
        @click="emitClone"
      >
        <i class="genuicon-duplicate-contract mt-auto text-muted" />
      </button>
      <!-- hiding the button for now because we are not giving delete functionality for any task element -->
      <!-- <button
        v-if="task && !task.companyMailer"
        v-tooltip.top="'Delete task'"
        class="btn btn-lighter btn-flat rounded-circle mr-2 px-2 py-1"
        @click="emitDelete"
      >
        <i class="nulodgicon-trash-b mt-auto text-muted" />
      </button> -->
      <material-toggle
        v-tooltip="{ content: activateDeactivate }"
        :init-active="!disabled"
        @toggle-sample="updateDisable"
      />
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex';
import MaterialToggle from '../shared/material_toggle.vue';

export default {
  components: {
    MaterialToggle,
  },
  props: ['task'],
  computed: {
    // Originally this was being passed in as a prop, but it it seems silly to pass 
    // this as a prop two levels just to change a word.
    label() {
      if (!this.task.id) {
        return "New Task";
      }
      return "Edit Task";
    },
    disabled() {
      return !!this.task.disabledAt;
    },
    forceDisabled() {
      return this.task.forceDisabled;
    },
    activateDeactivate() {
      if (this.disabled) {
        return "Activate this task";
      }
      return "Deactivate this task";
    },
  },
  methods: {
    ...mapMutations(['setAutomatedTasks']),
    updateDisable() {
      if (this.forceDisabled) {
        this.emitError("Please update task action(s) to enable this task");
      } else {
        this.$store.dispatch("updateDisable", this.task)
          .then(() => {
            this.$emit('fetch-task');
          });
      }
    },
    emitClone() {
      this.$emit('clone');
    },
    emitDelete() {
      this.$emit('delete');
    },
  },
};
</script>

