<template>
  <div class="w-100">
    <div class="col-12 row mb-1 p-0">
      <div class="col-6">
        <multi-select
          ref="selectTriggerSelect"
          v-model="eventType"
          label="name"
          class="mb-3"
          track-by="id"
          :multiple="false"
          placeholder="Select trigger"
          :select-label="``"
          :deselect-label="``"
          :options="eventTypes"
          :taggable="false"
          :disabled="disabled || !!task.customFormId"
          data-tc-event-select
          @open="fetchEventTypes"
          @input="setEventType"
        >
          <template 
            slot="singleLabel" 
            slot-scope="props"
          >
            {{ formattedOption(props.option.name) }}
          </template>
          <template 
            slot="option" 
            slot-scope="props"
          >
            {{ formattedOption(props.option.name) }}
          </template>
        </multi-select>
        <span
          v-if="eventTypeError"
          class="form-text small text-danger"
        >
          {{ eventTypeError }}
        </span>
      </div>
      <div class="col-6 pr-0">
        <multi-select
          v-if="showSubjectSelect"
          v-model="subjectType"
          label="name"
          class="mb-3"
          track-by="id"
          :multiple="false"
          placeholder="Select trigger detail"
          :select-label="``"
          :deselect-label="``"
          :options="subjectTypes"
          :taggable="false"
          data-tc-event-detail
          :disabled="disableSubjectDropdown || !!task.customFormId"
          @open="fetchSubjectTypes"
          @input="setSubjectType"
        >
          <template 
            slot="singleLabel" 
            slot-scope="props"
          >
            {{ formattedOption(props.option.name) }}
          </template>
          <template 
            slot="option" 
            slot-scope="props"
          >
            {{ formattedOption(props.option.name) }}
          </template>
        </multi-select>
        <span
          v-if="subjectTypeError && eventType"
          class="form-text small text-danger"
        >
          {{ subjectTypeError }}
        </span>
      </div>
    </div>
    <div 
      v-if="showSubjectValue"
      class="form-group"
    >
      <value-details
        :id="id"
        ref="component"
        :value="subjectValue"
        :disabled="disabled"
        :node-type="subjectType"
        :company-mailer="!!task.companyMailer"
        :is-custom-form-task="isCustomFormTask"
        :is-ticket-field-state-event="isTicketFieldStateEvent"
        @input="handleInput"
      />
    </div>
  </div>
</template>

<script>
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import http from 'common/http';
import MultiSelect from 'vue-multiselect';
import permissionsHelper from 'mixins/permissions_helper';

import ValueDetails from '../value_details.vue';

export default {
  components: {
    MultiSelect,
    ValueDetails,
  },
  mixins: [permissionsHelper],
  props: ['value', 'id', 'allowDelete', 'openSelectWhenMounted', 'task'],
  data() {
    return {
      event: _cloneDeep(this.value.event),
      eventType: null,
      eventTypeError: null,
      subjectType: null,
      subjectTypeError: null,
      eventTypes: [],
      subjectTypes: [],
      subjectValue: null,
      conditionalSwitch: 0,
      conditionalNodes: [],
      initFlag: true,
    };
  },
  computed: {
    disabled() {
      if (this.task.companyMailer){
        return !(this.value.companyMailer || this.value.customFormId);
      }
      return false;
    },
    disableSubjectDropdown() {
      return this.disabled || this.isTicketFieldStateEvent;
    },
    showSubjectSelect() {
      return !!this.eventType && !!this.eventType.name;
    },
    isTicketFieldStateEvent() {
      return this.eventType.name === '{a ticket} remains in the same state' 
        || this.eventType.name === "{an agent} didn't resync"
        || this.eventType.name === "{a software} wasn't detected"
        || this.eventType.name === "{an asset's} disk space is low";
    },
    showSubjectValue() {
      return !!this.subjectType && (!this.subjectType.subjectClass.startsWith("Any") || this.isAnyAgent || this.isAnyIntegration || this.isAnyAsset);
    },
    isAnyAgent() {
      return this.subjectType.subjectClass.toLowerCase().includes('agent');
    },
    isAnyIntegration() {
      return this.subjectType.subjectClass.toLowerCase().includes('integration');
    },
    isAnyAsset() {
      return this.subjectType.subjectClass.toLowerCase().includes('asset');
    },
    isCustomFormTask() {
      if (this.value.customFormId) {
        return true;
      }
      return false;
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  watch: {
    value() {
      this.eventType = null;
      this.subjectType = null;
      this.subjectValue = null; 
      this.initValues();
    },
  },
  methods: {
    onWorkspaceChange() {
      this.initValues();

      if (this.openSelectWhenMounted) {
        this.$refs.selectTriggerSelect.activate();
      }
    },
    valid() {
      return !this.eventTypeError && !this.subjectTypeError && (!this.$refs.component || !!this.$refs.component.valid());
    },
    showErrors() {
      this.eventTypeError = null;
      this.subjectTypeError = null;
      if (!this.eventType) {
        this.eventTypeError = "Missing trigger";
      }
      if (!this.subjectType) {
        this.subjectTypeError = "Missing trigger detail";
      }
      if (Array.isArray(this.$refs.component)) {
        for (let idx = 0; idx < this.conditionsLength; idx += 1) {
          this.$refs.component[idx].showErrors();  
        }
      } else if (this.$refs.component) {
        this.$refs.component.showErrors();
      }
    },
    setEventType() {
      this.subjectType = null;
      this.subjectValue = null;
      this.emitValue();
    },
    setSubjectType(value) {
      if (value) {
        const subjectType = this.subjectTypes.find(t => t.id === value.id);
        if (_get(this, "subjectType.id") !== subjectType.id) {
          this.subjectValue = null;
        }
        this.subjectType = subjectType;
        if (this.conditionsLength === 0 && this.isTicket) {
          this.addCondition();
        } else {
          this.conditions = [];
        }
        this.emitValue();
      }
    },
    setOption(option) {
      const typeId = _get(this, 'eventType.id');
      if (typeId !== option.id) {
        this.event = option;
      }
    },
    emitValue() {
      this.eventTypeError = null;
      this.subjectTypeError = null;
      this.$emit('input', {
        eventType: this.eventType,
        subjectType: this.subjectType,
        subjectValue: this.subjectValue,
        index: this.id,
      });
    },
    handleInput(value) {
      this.subjectValue = { ...value };
      this.emitValue();
    },
    initValues() {
      this.eventType = _cloneDeep(_get(this, 'value.nodeType'));
      if (this.eventType && this.eventType.name) {
        if (!this.task?.id && this.initFlag && this.isTicketFieldStateEvent) {
          this.initFlag = false;
          this.fetchSubjectTypes();
        } else {
          this.subjectType = _cloneDeep(_get(this, 'value.nodes[0].nodeType'));
        }
        if (this.subjectType) {
          this.subjectValue = _cloneDeep(_get(this, 'value.nodes[0].value'));
        } else if (this.isTicketFieldStateEvent) {
          this.fetchSubjectTypes();
        }
      }
    },
    formattedOption(name) {
      return name ? name.replace( /[[\]{}\\/]/g, '' ) : "";
    },
    fetchEventTypes() {
      const url = this.isAssetsModule ? '/asset_task_event_types.json' : '/event_types.json';
      http
        .get(url)
        .then(res => {
          this.eventTypes = res.data;
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error fetching events. ${error.response.data.message}`);
        });
    },
    fetchSubjectTypes() {
      // So, we're flattened out the hierarchy, so the type is always going to be event.
      const url = this.isAssetsModule ? '/asset_task_event_subject_types.json' : "/event_subject_types.json";
      http
        .get(url, { params: { id: this.eventType.id, type: 'Event' } })
        .then(res => {
          this.subjectTypes = res.data;
          if (this.isTicketFieldStateEvent) {
            [this.subjectType] = this.subjectTypes;
          }
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error fetching events. ${error.response.data.message}`);
        });
    },
    emitRemove() {
      this.$emit('remove', this.id);
    },
  },
};

</script>

<style lang="scss" scoped>
  .task-step-trigger {
    background: $themed-task-trigger-two;
  }
</style>
