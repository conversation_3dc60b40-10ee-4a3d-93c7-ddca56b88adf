<template>
  <div>
    <div
      v-for="(action, idx) in actions"
      :key="`action-${idx}`"
      class="my-0 box box--with-heading box--flat mb-3"
      :class="{
        'selected': selectedAction === idx,
        'mb-3': idx < (actions.length - 1)
      }"
    >
      <div
        class="box__heading base-font-size p-2 d-flex align-items-center clickable"
        :class="{
          'selected pl-3 ml-n2': selectedAction === idx,
          'bg-themed-lighter text-secondary rounded': selectedAction !== idx}
        "
        @click="$emit('select-action', idx)"
      >
        <span
          class="onboarding-rounded-circle align-middle text-center d-inline-block small font-weight-semi-bold mr-2"
          :class="{'ml-n2 bg-themed-task-action text-themed-base': selectedAction === idx, 'bg-themed-branded-light text-themed-base': selectedAction !== idx}"
        >
          {{ idx + 1}}
        </span>

        <span class="d-inline-flex align-items-center">
          <span>
            <span
              v-if="actionDescription(idx)"
              class="small action-description py-1 px-1"
            > 
              {{ actionDescription(idx) }}
              <span v-if="isChannelNameRequired(idx)">
                to channel <b>"{{ channelName(idx) }}"</b>
              </span> 
            </span>
            <span v-else>
              <span class="small action-description py-1 px-1"> do this </span>
            </span>
          </span>
        </span>

        <span class="position-absolute right-0 mr-3">
          <span
            class="mr-3"
            :class="{'required': actionErrors.includes(idx)}"
          >
            <i
              v-if="action && action.nodeType"
              class="ml-1"
              :class="[iconFromTriggerEventClass(action), {'text-danger': actionErrors.includes(idx)}]"
            />
          </span>

          <span
            v-if="value && !value.companyMailer"
            href="#"
            :disabled="actions.length <= 1"
            @click.stop.prevent="removeAction(idx)"
          >
            <i
              v-tooltip="actions.length > 1 ? 'Remove Action' : 'Cannot remove only action'"
              class="nulodgicon-trash-b mr-3"
            />
          </span>

          <i :class="{'nulodgicon-chevron-up': selectedAction === idx, 'nulodgicon-chevron-down': selectedAction !== idx }"/>
        </span>
      </div>

      <div
        v-show="selectedAction === idx"
        class="box__inner p-3"
      >
        <div class="mb-1 row col-12">
          <div class="col-6">
            <multi-select
              :ref="`selectActionSelect${idx}`"
              v-model="action.nodeType"
              label="name"
              class="mb-3"
              track-by="id"
              :multiple="false"
              placeholder="Select an action"
              :options="actionTypes"
              :taggable="false"
              :disabled="disabled"
              data-tc-action-select
              @open="fetchActions"
              @input="setOption(idx)"
              @remove="removeOption"
            >
              <template slot="noOptions">
                <p class="text-center mb-1 not-as-small">No actions available yet,</p>
                <p class="text-center mb-0 not-as-small">make sure the triggers have been selected.</p>
              </template>
            </multi-select>
            <div
              v-if="actionTypeErrors && actionTypeErrors[idx]"
              class="float-left small text-danger"
            >
              {{ actionTypeErrors[idx] }}
            </div>
          </div>
        </div>
        <component
          :is="component(idx)"
          v-if="component(idx)"
          :id="idx"
          ref="component"
          :value="task(idx)"
          @input="handleInput"
        />
      </div>
    </div>

    <div
      v-if="!disabled && isMsp"
      class="mt-4"
    >
      <div class="row mx-3 mt-4 align-items-center justify-content-around">
        <div class="col-6 border-line"/>
        <div class="border-label mr-3 small bg-light">AND</div>
        <div class="col-6 border-line"/>
      </div>

      <div class="row justify-content-between m-0 mt-4 pl-3 pr-2">
        <div
          class="form-control mb-2 text-muted text-left col"
          :class="{'clickable': !newActionDisabled, 'bg-light': newActionDisabled}"
          @click.prevent="addAction"
        >
          <span
            class="task-action text-dark align-middle py-1 px-2 rounded"
            :class="{'bg-light': newActionDisabled}"
          >
            Add a new action
          </span>
        </div>
        <div class="col-auto ml-3">
          <a
            class="btn btn-primary btn-sm text-white rounded-circle mt-1"
            :class="{'btn-flat': newActionDisabled}"
            href="#"
            role="button"
            :disabled="disabled || newActionDisabled"
            data-tc-add-action
            @click.prevent="addAction"
          >
            <i class="nulodgicon-plus-round"/>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MultiSelect from 'vue-multiselect';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _compact from 'lodash/compact';
import _cloneDeep from 'lodash/cloneDeep';
import http from 'common/http';

import permissionsHelper from 'mixins/permissions_helper';
import common from 'mixins/automated_tasks/common';
import AddAlert from './actions/help_tickets/add_alert.vue';
import AddComment from './actions/help_tickets/add_comment.vue';
import AddTicket from './actions/help_tickets/add_ticket.vue';
import AddTimeEntry from './actions/help_tickets/add_time_entry.vue';
import SendEmail from './actions/help_tickets/send_email.vue';
import SendSms from './actions/help_tickets/send_sms.vue';
import SetFormField from './actions/help_tickets/set_form_field.vue';
import HaltExecution from './actions/help_tickets/halt_execution.vue';
import SendSlackMessage from './actions/help_tickets/send_slack_message.vue';
import SendMsTeamsMessage from './actions/help_tickets/send_ms_teams_message.vue';
import SendNotification from './actions/help_tickets/send_desktop_notification.vue';
import SendMobileNotification from './actions/help_tickets/send_mobile_notification.vue';

export default {
  components: {
    AddAlert,
    AddComment,
    AddTicket,
    AddTimeEntry,
    HaltExecution,
    MultiSelect,
    SendEmail,
    SendMsTeamsMessage,
    SendSlackMessage,
    SendSms,
    SetFormField,
    SendNotification,
    SendMobileNotification,
  },
  mixins: [permissionsHelper, common],
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    selectedAction: {
      type: Number,
      default: 0,
    },
    isMsp: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      actionTypes: [],
      actionType: _cloneDeep(_get(this, "value.action.nodeType")),
      actionErrors: [],
      actionTypeErrors: [],
      actionValue: _cloneDeep(_get(this, "value.action.value")),
      actionNodes: [],
      lastActionIsValid: true,
    };
  },
  computed: {
    companyMailer() {
      return _get(this, 'value.companyMailer');
    },
    event() {
      return _get(this, "value.events");
    },
    disabled() {
      return !!_get(this, "value.companyMailer") || _get(this, "value.customFormId");
    },
    actions: {
      get() {
        return this.actionNodes;
      },
      set(val) {
        if (val) {
          this.actionNodes = val;
        }
      },
    },
    allowDelete() {
      return !this.disabled && this.actions.length > 1;
    },
    newActionDisabled() {
      return false;
      // return !this.lastActionIsValid;
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  methods: {
    onWorkspaceChange() {
      if (this.value.actions && this.value.actions.length > 0) {
        this.actions = this.value.actions;
      } else {
        this.addAction();
      }

      this.setLastActionValidity();
    },
    initializeData() {
      if (this.value.actions && this.value.actions.length > 0) {
        this.actions = this.value.actions;
      } else {
        this.addAction();
      }
    },
    model() {
      const modelValues = _map(this.value.events, 'nodeType.model');
      return _compact(modelValues);
    },
    valid() {
      this.actionErrors = [];

      for (let idx = 0; idx < this.actions.length; idx += 1) {
        // if has selected action check for form validity else push error onto errors array
        let result = true;
        if (this.actions[idx].nodeType) {
          // if array of components then find component that matches id
          if (Array.isArray(this.$refs.component)) {
            const component = this.$refs.component.find(c => c.id === idx);
            result = component ? component.valid() : true;  // halt component was casuing issue requiring conditional assignment
          } else {
            result = this.$refs.component?.valid();
          }

          if (!result) {
            this.actionErrors.push(idx);
          }
        } else {
          this.actionErrors.push(idx);
        }
      }

      return this.actionErrors;
    },
    showErrors() {
      for (let idx = 0; idx < this.actions.length; idx += 1) {
        if (this.actions[idx].nodeType) {
          this.actionTypeErrors.splice(idx, 0, null);
        } else {
          this.actionTypeErrors.splice(idx, 0, "Must select an action.");
        }
      }
      if (Array.isArray(this.$refs.component)) {
        for (let idx = 0; idx < this.$refs.component.length; idx += 1) {
          this.$refs.component[idx].showErrors();    
        }
      } else if (this.$refs.component) {
        this.$refs.component.showErrors();
      }
    },
    setOption(id) {
      this.handleInput({ index: id, node: { value: this.actions[id].value } });
      this.actionTypeErrors[id] = null;
      this.setLastActionValidity();
    },
    removeOption() {
      this.setLastActionValidity();
    },
    setLastActionValidity() {
      this.$nextTick(() => {
        this.lastActionIsValid = this.isLastActionIsValid();
      });
    },
    isLastActionIsValid() {
      const lastAction = this.actions ? this.actions[this.actions.length - 1] : null;
      return lastAction ? !!lastAction.nodeType : false;
    },
    handleInput(node) {
      const idx = node.index;
      this.actions.splice(idx, 1, { nodeType: this.actions[idx].nodeType, value: node.value });
      this.$emit('input', this.actions);
    },
    fetchActions(checkFilter) {
      const url = this.isAssetsModule ? '/asset_task_action_types.json' : '/action_types.json';
      http
        .get(url, { params: { model: this.model() } })
        .then(res => {
          this.actionTypes = res.data;
          if (checkFilter) {
            this.filterCommonActions();
          }
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error fetching actions. ${error.response.data.message}`);
        });
    },
    addAction() {
      if (this.newActionDisabled) {
        return;
      }

      this.actions.push({});
      this.setLastActionValidity();
      this.$nextTick(() => {
        // const actionId = this.actions.length - 1;
        // if (actionId > 0) {
        //   const lastActionSelectString = `selectActionSelect${actionId}`;
        //   this.$refs[lastActionSelectString][0].activate();
        // }
        // this.$emit('select-action', actionId);
      });
    },
    removeAction(idx) {
      if (this.actions.length > 1){
        this.actions.splice(idx, 1);
        this.actionTypeErrors.splice(idx, 1);
        this.setLastActionValidity();
      }
    },
    task(idx) {
      const task = _cloneDeep(this.value);
      task.actions[idx] = {
        nodeType: this.actions[idx].nodeType,
        value: this.actions[idx].value,
      };
      return task;
    },
    component(idx) {
      return this.actions[idx].nodeType ? this.actions[idx].nodeType.actionClass : null;
    },
    filterCommonActions() {
      for (let idx = 0; idx < this.actions.length; idx += 1) {
        if (this.actions[idx].nodeType) {
          const index = this.actionTypes.findIndex((type) => type.actionClass === this.actions[idx].nodeType.actionClass);
          if (index < 0) {
            this.actions.splice(idx, 1, {});
          }
        }
      }
      this.$emit('input', this.actions);
    },
    actionDescription(id) {
      return this.actionName(id) ? `${this.removeBraces(this.actionName(id))}` : '';
    },
    actionName(id) {
      return this.actionTypeMethod(id) ? this.actionTypeMethod(id).name : '';
    },
    actionTypeMethod(id) {
      return this.actions[id] ? this.actions[id].nodeType : null;
    },
  },
};
</script>

<style lang="scss" scoped>
  .task-action {
    background: $themed-task-action;
  }
</style>

