<template>
  <div class="col-12 mb-2">
    <div class="mb-3 form-group">
      <div>
        <add-recipient
          v-if="targetOptions"
          ref="alert"
          :id="`alert`"
          class="row"
          :target-options="targetOptions"
          :value="targets"
          :disabled="disabled"
          @change="handleTarget"
        />
        <span
          v-if="targetError"
          class="form-text small text-danger"
        >
          {{ targetError }}
        </span>
      </div>
      <div
        v-if="optionIncluded('specified')"
        class="mt-4"
      >
        <div class="mb-4 form-group">
          <label>
            Select the recipient users or groups
          </label>
          <input
            :id="`assign-agent--contributors-${id}`"
            type="text"
            required
            class="hidden"
            :value="contributors"
          >
          <mass-assign
            :id="`assign-agent--contributors-${id}`"
            :value="contributors"
            class="mass-assign"
            :disabled="disabled"
            @input="handleContributors"
          />
          <span
            v-if="contributorsError"
            class="form-text small text-danger"
          >
            {{ contributorsError }}
          </span>
        </div>
      </div>
    </div>

    <div class="form-group mb-0">
      <label>Add the following alert</label>
      <input
        :id="`message-${id}`"
        :value="message"
        required
        class="hidden"
        type="text"
      >
      <trix-vue
        ref="trixEditor"
        :key="compKey"
        :input-id="`message-${id}`"
        :value="message"
        :allow-attachments="false"
        :disabled="disabled"
        @update-uploading-status="updateUploadingStatus"
        @input="handleMessage"
      />
      <span
        v-if="messageError"
        class="form-text small text-danger"
      >
        {{ messageError }}
      </span>
    </div>
    <div v-if="model != 'ExecutionDate'">
      <interpolated-note 
        class="mb-3" 
        :text="message"
        @select="setInterpolatedNote"
      />
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import _compact from 'lodash/compact';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import TrixVue from 'components/trix_vue.vue';
import MassAssign from 'components/shared/company_user_assign/mass_assign.vue';
import permissionsHelper from 'mixins/permissions_helper';
import { mapActions } from 'vuex';
import InterpolatedNote from '../interpolated_note.vue';
import AddRecipient from './add_recipients.vue';
import common from "../common";

const TARGET_OPTIONS = [
  {
    value: "assigned",
    name: "Agent Assigned",
  },
  {
    value: "admins",
    name: "All Admins",
  },
  {
    value: "agents",
    name: "All Agents",
  },
  {
    value: "creator",
    name: "Teammate who created the ticket",
  },
  {
    value: 'related',
    name: "People related to the ticket",
  },
  {
    value: 'specified',
    name: 'Specified People or Groups',
  },
]

export default {
  components: {
    InterpolatedNote,
    MassAssign,
    TrixVue,
    AddRecipient,
  },
  mixins: [ common, permissionsHelper ],
  data() {
    return {
      target: _get(this, `value.actions[${this.id}].value.target`),
      targetError: null,
      message: _get(this, `value.actions[${this.id}].value.message`),
      messageError: null,
      contributors: _get(this, `value.actions[${this.id}].value.contributors`),
      contributorsError: null,
      attachmentUploading: false,
      attachmentIds: [],
      compKey: true,
    }
  },
  computed: {
    targetOptions() {
      if (this.event.findIndex((ev) => ev.nodeType && ev.nodeType.eventClass == "DateOccurred") >= 0) {
        const dateEventTargets = ['agents', 'specified'];
        return TARGET_OPTIONS.filter((opt) => dateEventTargets.includes(opt.value))
      }
      if (this.isAssetsModule) {
        const assetTargets = ['admins', 'specified'];
        return TARGET_OPTIONS.filter((opt) => assetTargets.includes(opt.value));
      }
      return TARGET_OPTIONS;
    },
    model() {
      return _get(this, `value.actions[${this.id}].nodeType.model`);
    },
    targets() {
      return _get(this, 'target', '');
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  methods: {
    ...mapActions('GlobalStore', ['fetchStringInterpolationKeys']),

    onWorkspaceChange() {
      this.fetchStringInterpolationKeys(this.model);
      if (this.$refs.alert) {
        this.$refs.alert.populateTargets();
      }
    },
    setInterpolatedNote(text) {
      this.handleMessage(text);
      this.compKey = !this.compKey;
    },
    valid() {
      if (!this.message || !this.target) {
        return false;
      }
      if (this.optionIncluded('specified') && (!!this.contributors && this.contributors.length === 0)) {
        return false;
      }
      return true;
    },
    targetIconCss(option) {
      let css = 'genuicon-circle-line';
      if (this.optionIncluded(option.value)) {
        css = 'genuicon-check-mark-circle-two';
      }
      return css;
    },
    targetCss(option) {
      if (this.optionIncluded(option.value)) {
        return 'font-weight-bold';
      }
      return null;
    },
    showErrors() {
      this.showMessageErrors();
      this.showTargetErrors();
      this.showContributorsErrors();
    },
    showMessageErrors() {
      if (this.message) {
        this.messageError = null;
      } else {
        this.messageError = "Please fill out this field";
      }
    },
    showTargetErrors() {
      if (this.target) {
        this.targetError = null
      } else {
        this.targetError = "Please select a recipient for the alert";
      }
    },
    showContributorsErrors() {
      if (!this.optionIncluded('specified')) {
        this.contributorsError = null;
      } else  if (this.contributors && this.contributors.length > 0) {
        this.contributorsError = null;
      } else {
        this.contributorsError = "Please select a contributor";
      }
    },
    handleInput() {
      const value = {
        target: this.target,
        message: this.message,
        contributors: this.contributors,
      };
      this.$emit('input', { value: value, index: this.id });
    },
    handleMessage(message) {
      this.message = message;
      this.showMessageErrors();
      this.handleInput();
    },
    handleContributors(contributors) {
      this.contributors = contributors;
      this.showContributorsErrors();
      this.handleInput();
    },
    updateUploadingStatus(status) {
      this.attachmentUploading = status;
    },
    handleTarget(target) {
      this.target = target;
      if (!this.target) {
        this.targetError = "Must select a recipient for the alert."
      } else {
        if (!this.optionIncluded('specified')) {
          this.contributors = [];
        }
        this.targetError = null;
      }
      this.handleInput();
    },
    optionIncluded(option) {
      return this.targets && this.targets.includes(option);
    },
  }
}
</script>
