<template>
  <div class="position-relative">
    <div
      v-if="showDetails && stringKeys.length > 0"
      class="text-left mb-4"
    >
      <div class="w-100 form-text text-info small">
        String replacements allow values to be inserted into text.  For example, if you have a ticket created,
        the value "{ticket subject}" would be replaced with the actual ticket subject.
      </div>
      <div class="w-100 form-text text-info small">
        Below are the valid string replacements:
      </div>
      <div
        v-if="stringInterpolationKeys && stringInterpolationKeys.length > 0"
        class="row"
      >
        <div
          v-for="key in stringKeys"
          :key="key"
          v-tooltip.top="`${toSnakecase(key)}`"
          class="col-4 small truncate"
        >
          <a 
            class="keyword" 
            @click="interpolateValue(key)"
          > 
            {{ formattedText(key) }} 
          </a>
        </div>
      </div>
    </div>
    <div 
      v-if="!isAssetsModule" 
      class="d-flex justify-content-end"
    >
      <a
        href="#"
        style="bottom: 0; right: 0px;"
        class="position-absolute small"
        @click.stop.prevent="toggleView"
      >
        {{ showVerb }} Text Replacements
      </a>
    </div>
  </div>
</template>

<script>
import _get from 'lodash/get';
import string from 'mixins/string';
import interpolatedNote from 'mixins/interpolated_note';
import { mapGetters } from 'vuex';

export default {
  mixins: [string, interpolatedNote],
  props: {
    text: {
      type: String,
      default: ""
    },
    currentFocused: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      showDetails: false,
    }
  },
  computed: {
    ...mapGetters('GlobalStore', ['stringInterpolationKeys']),

    showVerb() {
      if (this.showDetails) {
        return "Hide";
      } else {
        return "Show";
      }
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
    stringKeys() {
      return _get(this, 'stringInterpolationKeys', []);
    }
  },
  methods: {
    toggleView() {
      this.showDetails = !this.showDetails;
    },
    interpolateValue(key) {
      key = this.formattedText(key);
      key = this.currentFocused === "subject" && key === "{ticket_number}" ? "[#{ticket_number}]" : key;
      return this.setInterpolatedValue(key);
    }
  },
}
</script>

<style lang="scss" scoped>
  a.keyword:hover {
    color: $themed-link;
  }
</style>
