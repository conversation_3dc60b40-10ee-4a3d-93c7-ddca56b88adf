<template>
  <div
    class="w-100"
    :class="{
      'force-disabled': forceDisabled || isMSTeamsRecipient,
      'hovering': hover
    }"
    @mouseenter="hover = true"
    @mouseleave="hover = false"
  >
    <a
      v-if="task"
      class="box box--flat align-content-start border-bottom-0 border-left-0 border-right-0 rounded-0"
      :class="{'': isWrite, 'disable-cursor': !isWrite}"
      @click.stop.prevent="openEditTaskModal"
    >

      <div class="box__inner">
        
        <!-- <span
          v-else-if="selectedItemsLookUp['updated_at']"
          v-tooltip="`Updated at ${updatedAt}`"
          class="box__source left_box text-muted"
        >
          {{ updatedAt }}
        </span> -->


        <div class="row">
          <div class="d-inline-flex text-right col-auto justify-content-end align-items-center mr-2">
            <material-toggle
              v-tooltip="'Activate this task'"
              transformed
              :init-active="!disabled"
              @toggle-sample="updateDisable"
            />
          </div>
          <div
            class="col-auto align-content-center mx-3"
            :class="{'position-relative': disabled}"
          >
            <div
              v-if="disabled"
              class="box--disabled-overlay rounded"
            />
             
            <div class="icons-container">
              <div
                class="events-group position-relative"
                :class="{ 'is-hovering': isHoveringEventIcons}"
                @mouseenter="isHoveringEventIcons = true"
                @mouseleave="isHoveringEventIcons = false"
              >
                <div
                  v-for="(event, eventIndex) in task.events"
                  :key="`event-${eventIndex}`"
                  class="icon-wrapper event-icon text-center rounded bg-blue-subtle p-1 rounded"
                  :class="{ 'is-first': eventIndex === 0, 'is-hidden': eventIndex > 0 }"
                  :style="getIconPosition(eventIndex, task.events.length, 'event')"
                >
                  <i
                    v-if="event"
                    class="mx-1 text-themed-secondary"
                    :class="[iconFromTriggerEventClass(event)]"
                  />
                </div>
                <span
                  v-if="task.events.length > 1 && !isHoveringEventIcons"
                  class="counter-badge events-counter d-inline-block cursor-pointer rounded-pill btn btn-xs px-2 ml-0 mr-3 bg-themed-lighter text-themed-base border border-blue-subtle true-small"
                >
                  +{{ task.events.length -1 }}
                </span>
              </div>

              <div
                class="actions-group position-relative"
                :class="{ 'is-hovering': isHoveringActionIcons}"
                @mouseenter="isHoveringActionIcons = true"
                @mouseleave="isHoveringActionIcons = false"
              >
                <div
                  v-for="(action, actionIndex) in task.actions"
                  :key="`action-${actionIndex}`"
                  class="icon-wrapper actions-icon text-center rounded bg-purple-subtle p-1 rounded"
                  :class="{ 'is-first': actionIndex === 0, 'is-hidden': actionIndex > 0 }"
                  :style="getIconPosition(actionIndex, task.actions.length, 'action', task.events.length)"
                >
                  <i
                    v-if="action"
                    class="mx-1 text-themed-secondary"
                    :class="[iconFromTriggerEventClass(action)]"
                  />
                </div>
                <span
                  v-if="task.actions.length > 1 && !isHoveringActionIcons"
                  class="counter-badge actions-counter d-inline-block cursor-pointer rounded-pill btn btn-xs px-2 ml-0 mr-3 bg-themed-lighter text-themed-base border border-purple-subtle true-small"
                >
                  +{{ task.actions.length - 1}}
                </span>
              </div>
            </div>
          </div>

          <div
            class="col"
            :class="{'position-relative': disabled}"
          >
            <div
              v-if="disabled"
              class="box--disabled-overlay rounded"
            />
            <h6 class="mt-0 asset-name mb-2">
              <span>
                {{ taskName }}
              </span>
            </h6>
            <span
              v-if="task.events"
              class="text-dark"
            >
              <span class="d-inline-flex not-as-small">WHEN</span>
              <event-clause
                :node="task.events[0]"
                :link="false"
              />
              <span
                v-if="task.events.length > 1"
                v-tooltip="`${task.events.length - 1} more trigger(s)`"
                class="d-inline-block bg-primary cursor-pointer rounded-pill btn btn-xs px-2 ml-0 mr-3  bg-blue-subtle text-themed-link"
              >
                +{{ task.events.length - 1 }} {{ pluralize('trigger', task.events.length - 1) }}
              </span>
            </span>
            <!-- ACTION -->
            <span
              class="text-dark mb-0 ml-n1"
            >
              <span class="d-inline-flex not-as-small">THEN</span>
              <p class="mb-0 phrase_wrapper pl-0 small">{{ actionDescription }}</p>
              <span class="smallest text-muted mb-0 d-inline-flex">
                <span
                  v-if="task.actions.length > 1"
                  v-tooltip="`${task.actions.length - 1} more action(s)`"
                  class="d-inline-block bg-primary cursor-pointer rounded-pill btn btn-xs px-2 ml-0 mr-3 bg-blue-subtle text-themed-link"
                >
                  +{{ task.actions.length - 1 }} {{ pluralize('action', task.actions.length - 1) }}
                </span>
              </span>
              <span v-if="isChannelNameRequired(0)">
                to channel <b>"{{ channelName(0) }}"</b>
              </span>
            </span>
          </div>

          <!-- Secondary Info Section -->
          <div
            class="col-auto align-content-center"
            :class="{'position-relative': disabled}"
          >
            <div
              v-if="disabled"
              class="box--disabled-overlay rounded"
            />
            <i
              v-if="isDefaultTask"
              v-tooltip="`This is a default task and can be partially customized`"
              class="nulodgicon-locked text-muted h5"
            />
          </div>
          <div class="col-auto align-content-center">
            <i
              v-if="isCustomFormTask"
              v-tooltip="`This can only be modified from it's custom form`"
              class="genuicon-build-custom-forms text-muted text-muted h5"
            />
          </div>
          
          <div
            class="task-box d-flex col-auto justify-content-between p-2 rounded align-items-center align-self-center my-1.5 mr-3"
            :class="{
              'bg-lighter': !hover,
              'bg-light': hover,
              'position-relative': disabled
            }"
          >
            <div
              v-if="disabled"
              class="box--disabled-overlay rounded"
            />
            <div class="d-inline-block mx-2">
              <span
                class="text-muted small align-self-center"
              >
                <span class="not-as-small font-weight-light">#</span>
                {{ taskNumber }}
              </span>
            </div>
            <span class="d-flex text-muted small">•</span>
            <div class="d-inline-block mx-2">
              <span class="text-muted small align-self-center">
                <span class="not-as-small font-weight-light">Order: </span>
                {{ taskOrder }}
              </span>
            </div>
            <span class="d-flex text-muted small">•</span>
            <div class="d-inline-block mx-2">
              <span
                class="text-muted small align-self-center"
              >
                <span class="not-as-small font-weight-light">Used: </span>
                <span style="usage-value">{{ taskUsed || 0 }}</span>
              </span>
            </div>
          </div>

          <!-- Action Button Section -->
          <div
            v-if="isWrite"
            class="d-inline-flex line-item-actions-wrap text-right col-auto justify-content-end align-items-center"
          >
            <span 
              class="line-item-actions nowrap"
              :class="{ 'visible-actions': hover }"
            >
              <a
                v-tooltip="`Change task group`"
                href="#"
                class="mr-2 px-2 rounded d-flex align-items-center justify-content-center bg-lighter"
                @click.stop.prevent="editTaskHandler"
              >
                <div class="dropdown d-inline-block">
                  <a
                    href="#"
                    class="text-secondary form-btn--responsive d-flex align-items-baseline mt-n0.5"
                    @click.stop.prevent="toggleGroupDropdown"
                  >
                    <i class="genuicon-stack text-muted mt-1" />
                    <i class="clickable nulodgicon-arrow-down-b text-muted ml-2" />
                  </a>

                  <basic-dropdown
                    class="dropdown-menu not-as-small dropdown-filter d-block"
                    :show-dropdown="showGroupOptions"
                    @on-blur="onGroupBlur"
                  >
                    <span
                      v-for="(option, index) in filteredTaskGroups"
                      :key="index"
                    >
                      <a
                        class="dropdown-item clickable truncate text-capitalize"
                        :class="{ 'font-weight-bold text-secondary bg-light' : option === filteredTaskGroups }"
                        href="#"
                        @mouseover="hoveredGroupIndex = index"
                        @mouseleave="hoveredGroupIndex = null"
                        @click.stop.prevent="selectGroupOption(option)"
                      >
                        {{ option.name }}
                      </a>
                    </span>
                  </basic-dropdown>
                </div>
              </a>
              <a
                v-if="!isDefaultTask"
                v-tooltip="`Remove task`"
                href="#"
                class="mr-1 task-action-icon d-flex align-items-center justify-content-center bg-lighter"
                @click.stop.prevent="openModal"
              >
                <i class="nulodgicon-trash-b text-muted mt-1" />
              </a>
              <a
                v-if="!isDefaultTask"
                v-tooltip="`Edit task`"
                href="#"
                class="mr-2 task-action-icon d-flex align-items-center justify-content-center bg-lighter"
                @click.stop.prevent="editTaskHandler"
              >
                <i
                  class="nulodgicon-edit text-muted mt-1"
                />
              </a>
              <a
                v-else
                v-tooltip="`View task`"
                href="#"
                class="mr-2 task-action-icon d-flex align-items-center justify-content-center bg-lighter"
                @click.stop.prevent="editTask"
              >
                <i class="nulodgicon-eye text-muted mt-1" />
              </a>
            </span>
            <span
              class="line-item-actions-overlay"
              :class="{ 'hidden-actions-overlay': hover }"
            >
              <i class="nulodgicon-dot-3 h6 mb-0 text-muted" />
            </span>
          </div>
        </div>
      </div>
      
      <div
        v-if="forceDisabled || isMSTeamsRecipient"
        v-tooltip="tooltipMessage"
        class="disabled-badge text-secondary"
      >
        Outdated
      </div>

      <div
        v-tooltip="{
          content: 'Drag task to change evaluation order',
          placement: 'top-start',
          delay: { show: 300 }
        }"
        class="handle drag-overlay"
      >
        <span class="handle-dots genuicon-draggable"/>
      </div>
    </a>
    <delete-modal
      ref="deleteModal"
      :value="task"
    />
    <edit-task-modal
      v-if="isModalOpen"
      ref="editTaskModal"
      :value="task"
      :selected-group="selectedGroup"
      :task-groups="taskGroups"
      @fetch-task="fetchTask"
      @close-model="closeEditModal"
    />
  </div>
</template>

<script>
import http from 'common/http';
import common from 'mixins/automated_tasks/common';
import recipients from 'mixins/automated_tasks/recipients';
import _get from 'lodash/get';
import permissionsHelper from 'mixins/permissions_helper';
import pluralize from 'pluralize/pluralize';
import BasicDropdown from 'components/shared/basic_dropdown.vue';
import eventClause from './event_clause.vue';
import DeleteModal from './delete_modal.vue';
import EditTaskModal from './edit.vue';
import MaterialToggle from '../shared/material_toggle.vue';
import string from '../../mixins/string';

export default {
  components: {
    BasicDropdown,
    DeleteModal,
    eventClause,
    MaterialToggle,
    EditTaskModal,
  },
  mixins: [
    common,
    recipients,
    permissionsHelper,
    string,
  ],
  props: ['task', 'selected', 'module', 'taskGroup', 'taskGroups', 'selectedGroup'],
  data() {
    return {
      isModalOpen: false,
      disabledAt: _get(this, 'task.disabledAt', null),
      hover: false,
      forceDisabled: _get(this, 'task.forceDisabled', false),
      showGroupOptions: false,
      hoveredGroupIndex: false,
      isHoveringEventIcons: false,
      isHoveringActionIcons: false,
    };
  },
  computed: {
    actionDescription() {
      return `${this.removeBraces(this.actionName)}`;
    },
    tooltipMessage() {
      let tooltip = '';
      if (this.forceDisabled) {
        tooltip += 'A user linked to one or more task actions no longer exists. Please update these actions to enable this automated task.';
      }
      if (this.isMSTeamsRecipient) {
        tooltip += ' Due to the Microsoft Teams limitations, email updates to Teams channels may not work properly.';
      }
      return tooltip;
    },
    isDefaultTask() {
      return !!this.task.companyMailer;
    },
    isCustomFormTask() {
      return !!this.task.customFormId;
    },
    name() {
      return this.task.name;
    },
    taskCss() {
      if (this.disabled) {
        return "text-muted";
      } 
        return null;
      
    },
    disabled() {
      return !!this.disabledAt;
    },
    actionType() {
      return _get(this, 'task.actions[0].nodeType');
    },
    actionName() {
      return _get(this, 'actionType.name');
    },
    taskNumber() {
      return _get(this, 'task.serialNumber');
    },
    taskOrder() {
      return _get(this, 'task.order');
    },
    taskUsed() {
      return _get(this, 'task.triggerCount');
    },
    isNameLengthGood() {
      return this.name && this.name.length < 35;
    },
    isLengthGoodForHover() {
      return this.name && this.name.length < 30;
    },
    taskName() {
      if (this.isNameLengthGood) {
        return this.name;
      }
      return this.truncate(this.name, 36);
    },
    toolTipText() {
      if (this.isNameLengthGood) {
        return '';
      }
      return this.name;
    },
    isHover() {
      if (this.isLengthGoodForHover) {
        this.turnOffGoodHover();
        return this.name;
      }
      return this.truncate(this.name, 31);
    },
    hoverName() {
      if (this.isLengthGoodForHover) {
        return '';
      }
      return this.name;
    },
    filteredTaskGroups() {
      return this.taskGroups.filter(group => group.name?.toLowerCase() !== 'ungrouped');
    },
  },
  methods: {
    cloneTask() {
      http
        .post(`/cloned_tasks.json`, { id: this.task.id } )
        .then(res => {
          this.emitSuccess("Task successfully cloned");
          this.$emit('fetch-tasks', res.data.id);
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error cloning this task.`);
        });
    },
    openEditTaskModal() {
      if (this.isWrite) {
        this.isModalOpen = true;
        this.$nextTick(() => {
          this.$refs.editTaskModal.open();
        });
      }
    },
    editTaskHandler() {
      if (this.isWrite) {
        if (this.task.customFormId) {
          const routeData = this.$router.resolve({path: `/help_tickets/settings/custom_forms/${this.task.customFormId}/edit`});
          return window.open(routeData.href, '_blank');
        }
        this.openEditTaskModal();
      }
      return null;
    },
    updateDisable() {
      if (this.forceDisabled) {
        this.emitError("Please update task action(s) to enable this task");
      } else {
        this.$store.dispatch("updateDisable", this.task).then(res => {
          this.$emit('update', res.data);
        });
      }
    },
    openModal() {
      this.$refs.deleteModal.open();
    },
    turnOffGoodHover() {
      this.isGoodHover = false;
    },
    fetchTask() {
      this.$emit('fetch-tasks');
    },
    closeEditModal(){
      this.isModalOpen = false;
    },
    pluralize(str, count) {
      return pluralize(str, count);
    },
    toggleGroupDropdown() {
      this.showGroupOptions = !this.showGroupOptions;
    },
    onGroupBlur() {
      this.showGroupOptions = false;
    },
    selectGroupOption(option) {
      const task = {
        ...this.task,
        task_group: option,
      };
      http
        .put(`/automated_tasks/${this.task.id}.json`, { task } )
        .then(() => {
          this.toggleGroupDropdown();
          this.$emit('update');
          this.emitSuccess("Task updated successfully");
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error saving task. ${error.response.data.message}`);
        });
    },

    getIconPosition(index, total, type) {
      const positionLookup = {
        0: { x: -10, y: -10 },
        1: { x: -28, y: -6 },
        2: { x: -28, y: -24 },
        3: { x: -28, y: 12 },
        4: { x: -44, y: -16 },
        5: { x: -44, y: 2 },
      };
      
      const defaultPosition = { x: 0, y: 0 };
      const basePosition = positionLookup[index] || defaultPosition;
      
      let xOffset;
      let yOffset;
      
      if (type === 'event') {
        xOffset = basePosition.x;
        yOffset = basePosition.y;
      } else {
        xOffset = -basePosition.x;
        yOffset = -basePosition.y;
      }
      
      const scale = index === 0 ? 0.9 : 0.65;
      
      return {
        position: 'absolute',
        left: `${xOffset}px`,
        top: `${yOffset}px`,
        zIndex: `${100 - index}`,
        transform: `scale(${scale})`,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.box--with-hover {
  padding: 1.25rem;

  .bottom-section {
    padding-left: inherit;
    transition: padding-left 0.18s linear; // have to manually set 0.18s transition since .bottom-section is absolute positioned
  }

  &:hover {
    padding-left: 2rem !important;

    .bottom-section {
      padding-left: 2rem !important;
    }

    .drag-overlay {
      opacity: 1;
      transition: 0.5s;
      visibility: visible;
    }
  }
}

.box {
  padding: 0.875rem 1.125rem;

  .hovering & {
    background-color: $themed-lighter;
  }

  .item--with-rounded-bottom & {
    border-bottom-left-radius: $border-radius !important;
    border-bottom-right-radius: $border-radius !important;
  }
}

a.task-action-icon {
  i {
    font-size: 0.925rem;
    color: $themed-fair;
  }
  &:hover {
    i.nulodgicon-edit {
      color: $themed-link;
    }
    i.nulodgicon-trash-b {
      color: $danger;
    }
    i.nulodgicon-eye {
      color: $themed-link;
    }
  }
}
.task-action-icon {
  height: 1.5rem;
  width: 1.5rem;
}

.handle-dots {
  font-size: 1.125rem;
  position: absolute;
  top: 36%;
}

.drag-overlay {
  background-color: rgba(33, 37, 41, 0.1);
  border-radius: 0.25rem 0 0 0.25rem;
  color: $themed-base;
  content: "";
  cursor: move;
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  visibility: hidden;
  width: 1.25rem;
  z-index: 2;

  &:hover {
    background-color: rgba(33, 37, 41, 0.3);
  }
}

@media only screen and (max-width: 900px) {
  .task-col {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.disable-cursor {
  cursor: auto;
}

.bottom-section {
  align-content: center;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  bottom: 0;
  left: unset;
  position: absolute;
  right: 0;
  top: 0;
  width: 40%;
}

.blinker {
  animation: background-color 1.4s ease-out;
}

@keyframes background-color {
  from {  background-color: #F5F5DC; }
  to { background-color: white; }
}

.action-description {
  background: $themed-task-action;
  padding: 0.125rem 0.3rem;
  border-radius: 0.25rem;
}

.force-disabled {
  border: 0.32rem solid $color-caution;
  border-width: 0.19rem;
  border-radius: 0.7rem;
}

.disabled-badge {
  background-color: $color-caution;
  border-radius: 0 0 0 0.5rem;
  top: 0;
  right: 0;
  padding: 0 0.5rem;
  position: absolute;
  font-size: 0.75rem;
}

:deep(.phrase_wrapper){
  display: inline-flex;
}

:deep(.phrase_1),
:deep(.phrase_2) {
  background: none;
  display: flex;
  margin-bottom: 0.1875rem;
  padding-left: 0;
}

:deep(.action-description) {
  background: none;
  display: flex;
  margin-bottom: 0.1875rem;
  padding-left: 0;
}

.line-item-actions-wrap {
  width: 10rem;
}

.line-item-actions {
  display: inline-flex;
  min-width: 7.5rem;
  opacity: 0;
  transition: $transition-base;
}

.visible-actions {
  opacity: 1;
  width: auto;
}

.line-item-actions-overlay {
  left: 50%;
  opacity: 1;
  position: absolute;
  top: 50%;
  transform: translate(-50%,-50%);
  transition: $transition-base;
  visibility: visible;
}

.hidden-actions-overlay {
  opacity: 0;
  visibility: hidden;
}

.box--disabled-overlay {
  top: 0;
  z-index: 1000;
}

$icon-size: 0.85;
$offset: 0.9rem;
$animation-curve: cubic-bezier(0.34, 1.56, 0.64, 1);

.icons-container {
  position: relative;
  min-height: 2.75rem;
  min-width: 2.75rem;
  overflow: visible;
}

.icon-wrapper {
  position: absolute;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
  border-radius: 0.25rem;
  transition: all 0.4s $animation-curve;
  opacity: 1;

  &.is-hidden {
    opacity: 0;
    transform: translate(0, 0) scale(0.5);
    pointer-events: none;
  }

  &.is-first {
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
}

.events-group.is-hovering .event-icon.is-hidden,
.actions-group.is-hovering .actions-icon.is-hidden {
  opacity: 1;
  pointer-events: auto;
}

@mixin position-icon($x, $y, $delay) {
  transform: translate($x, $y) scale($icon-size);
  transition-delay: $delay;
}

.events-group.is-hovering {
  .event-icon {
    &:nth-child(2) { @include position-icon(-$offset, -$offset, 0.05s); }
    &:nth-child(3) { @include position-icon($offset, -$offset, 0.075s); }
    &:nth-child(4) { @include position-icon(-$offset, $offset, 0.10s); }
    &:nth-child(5) { @include position-icon($offset, $offset, 0.125s); }
  }
}

// Actions positioning is a mirrored pattern
.actions-group.is-hovering {
  .actions-icon {
    &:nth-child(2) { @include position-icon(-$offset, $offset, 0.05s); }
    &:nth-child(3) { @include position-icon($offset, $offset, 0.075s); }
    &:nth-child(4) { @include position-icon(-$offset, -$offset, 0.10s); }
    &:nth-child(5) { @include position-icon($offset, -$offset, 0.125s); }
  }
}

.counter-badge {
  cursor: pointer;
  opacity: 1.0;
  position: absolute;
  transform: scale(0.7);
  z-index: 101;
  
  &.events-counter {
    top: -1.375rem;
    left: -1.75rem;
  }

  &.actions-counter {
    top: 1.75rem;
    left: 1.75rem;
  }
}

.task-box {
  height: 2.375rem;
}

.usage-value {
  display: inline-block;
  min-width: 2rem;
  text-align: center;
}

</style>
