<template>
  <div
    v-if="actions"
    class="readable-length not-as-big"
  >
    <div class="mb-3">
      <label class="mb-0">Pick which action you wish to do:</label>
    </div>
    <div class="form-group">
      <input
        :value="actionName"
        required
        class="hidden"
        type="text"
      >

      <div v-if="!actions">
        <h4>
          Loading
          <span class="ml-3 d-inline-block">
            <pulse-loader
              :loading="true"
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </h4>
      </div>
      <div v-else>
        <div
          v-for="action in actions"
          :key="action.id"
          class="event-option p-2 clickable"
          @click="handleAction(action)"
        >
          <span
            :class="objectCss(action)"
            data-tc-action
          >
            <i
              :class="objectIconCss(action)"
              class="mr-2 align-sub"
            />
            {{ removeBraces(action.name) }}
          </span>
        </div>
      </div>
      <span
        v-if="selectError"
        class="form-text small text-danger"
      >
        {{ selectError }}
      </span>
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import string from 'mixins/string';
import common from 'mixins/automated_tasks/common.js';
import _cloneDeep from 'lodash/cloneDeep';
import _get from 'lodash/get';
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import permissionsHelper from 'mixins/permissions_helper';

export default {
  name: 'ActionSelect',
  components: {
    PulseLoader,
  },
  mixins: [string, common, permissionsHelper],
  props: ['value'],
  data() {
    return {
      actions: null,
      node: _cloneDeep(this.value),
      selectError: null,
    };
  },
  computed: {
    actionName() {
      return _get(this, 'node.nodeType.name');
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  methods: {
    onWorkspaceChange() {
      this.fetchActions();
    },
    showErrors() {
      if (this.actionName) {
        this.selectError = "";
      } else {
        this.selectError = "Please select an action";
      }
    },
    objectIconCss(details) {
      return (this.node.nodeType.name === details.name) ? 'genuicon-check-mark-circle-two' : 'genuicon-circle-line';
    },
    objectCss(details) {
      if (this.node.nodeType.name === details.name) {
        return 'font-weight-bold';
      }
      return null;
    },
    handleAction(action) {
      this.node.nodeType = {... action };
      this.node.nodeType.class = "Action";
      this.showErrors();
      this.$emit("input", this.node);
    },
    fetchActions() {
      this.actions = null;
      const url = this.isAssetsModule ? '/asset_task_action_types.json' : '/action_types.json';
      http
        .get(url, { params: { model: this.node.nodeType.model } })
        .then(res => {
          this.actions = res.data;
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error fetching actions. ${error.response.data.message}`);
        });
    },
  }
}
</script>

<style lang="scss" scoped>
.event-option {
  &:hover {
    background-color: $themed-lighter;
  }
}
</style>

