<template>
  <div class="w-100">
    <div
      class="box__heading row w-100 justify-content-between clickable"
      :class="{'border-bottom': isOpen, 'rounded': !isOpen, 'text-themed-dark bg-themed-lighter': isLightThemed }"
      @mouseenter="hover = true"
      @mouseleave="hover = false"
      @click="handleClick"
    >
      <div class="col-auto border-r-300 pr-5 task-group-info align-content-center">
        <div class="row">
          <div class="col-auto px-0">
            <span class="position-relative d-inline-block group-header__icon-wrap align-middle text-center p-2 rounded">
              <i 
                class="h5 text-themed-fair"
                :class="taskGroup.icon ? taskGroup.icon : 'genuicon-automated-tasks'"
              />
            </span>
          </div>
          <div class="col align-content-center">
            <h6 class="font-weight-bold d-inline-block mb-1">
              {{ taskGroup.name && taskGroup.name }} Tasks
            </h6>
          </div>
        </div>
      </div>
      <div class="col-auto text-center">
        <div class="text-muted more-readable-letter-spacing mb-2 small">
          Default Tasks
        </div>
        <div class="h4 font-weight-normal mb-0">
          {{ taskGroup.tasks && taskGroup.tasks.filter(t => t.companyMailer).length }}
        </div>
      </div>
      <div class="col-auto text-center">
        <div class="text-muted more-readable-letter-spacing mb-2 small">
          Custom Tasks
        </div>
        <div class="h4 font-weight-normal mb-0">
          {{ taskGroup.tasks && taskGroup.tasks.filter(t => !t.companyMailer).length }}
        </div>
      </div>
      <div class="col-auto text-center">
        <div class="text-muted more-readable-letter-spacing mb-2 small">
          Tasks Used
        </div>
        <div class="h4 font-weight-normal mb-0">
          {{ totalTriggerCount }}
        </div>
      </div>
      <div class="col-auto align-content-center mr-n2">
        <span
          v-show="isWrite"
          v-tooltip="!ungroupedTask ? 'Remove task group' : null"
          class="d-inline-flex basic-transition mr-2 group-header__action-button-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle"
          :class="[
            removeTaskToGroupHover ? 'bg-red-light text-white' : '',
            !hover ? 'text-very-muted' : '',
            ungroupedTask ? 'invisible-no-pointer' : ''
          ]"
          @mouseenter="removeTaskToGroupHover = true"
          @mouseleave="removeTaskToGroupHover = false"
        >
          <i
            class="h5 mb-0 nulodgicon-trash-b "
            @click.stop="openDeleteGroupTaskModal"
          />
        </span>
        <span
          v-if="isWrite"
          v-tooltip="`Edit task group`"
          class="d-inline-flex basic-transition mr-2 group-header__action-button-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle"
          :class="{ 'bg-primary text-white': editTaskGroupHover, 'text-very-muted': !hover}"
          @mouseenter="editTaskGroupHover = true"
          @mouseleave="editTaskGroupHover = false"
        >
          <i 
            class="h5 mb-0 nulodgicon-edit"
            @click.stop.prevent="$emit('open-group-modal', taskGroup)"
          />
        </span>
        <span 
          v-else
          class="d-inline-flex basic-transition mr-2 group-header__action-button-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle"
        />
        <span
          v-if="isWrite"
          v-tooltip="`Add new task in group`"
          class="d-inline-flex basic-transition mr-2 group-header__action-button-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle"
          :class="{ 'bg-primary text-white': addTaskToGroupHover, 'text-very-muted': !hover}"
          @mouseenter="addTaskToGroupHover = true"
          @mouseleave="addTaskToGroupHover = false"
        >
          <i 
            class="h5 mb-0 nulodgicon-plus-round "
            @click.stop="createNewTask(taskGroup)"
          />
        </span>
        <div
          class="d-inline-flex basic-transition group-header__action-button-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle"
          :class="{ 'bg-themed-light-hover-bg text-themed-secondary' : hover, 'text-very-muted': !hover}"
        >
          <i
            class="h5 mb-0"
            :class="isOpen ? 'nulodgicon-chevron-down' : 'nulodgicon-chevron-up'" 
          />
        </div>
      </div>
    </div>
    <task-modal
      ref="taskModal"
      :value="linkedTask"
      :is-edit-task="!isNewTask"
      :selected-template="selectedTemplate"
      :selected-group="selectedTaskGroup"
      :task-groups="taskGroups"
      :open-linked-task="openLinkedTask"
      @update="updateTask"
      @cancel="handleClose"
    />

    <Teleport to="body">
      <sweet-modal
        ref="deleteGroupTaskModal"
        v-sweet-esc
        modal-theme="wide theme-sticky-footer"
        width="50%"
        title="Before you delete this Automated Task Group"
      >
        <template slot="default">
          <div class="mb-2">
            <h6>Are you sure you want to delete this automated task group? The associated automated tasks will be moved to the “Ungrouped Tasks” group.</h6>
          </div>
        </template>
        <template slot="button">
          <div class="d-flex justify-content-end">
            <submit-button
              class="mr-2"
              btn-content="Cancel"
              :conditional-class="'btn-secondary btn-sm'"
              @submit="$refs.deleteGroupTaskModal.close()"
            />
            <submit-button
              btn-content="Delete"
              :conditional-class="'btn-sm btn-danger'"
              @submit="deleteGroup"
            />
          </div>
        </template>
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
import http from 'common/http';
import permissionsHelper from 'mixins/permissions_helper';
import TaskModal from 'components/automated_tasks/new.vue';
import { SweetModal } from 'sweet-modal-vue';
import SubmitButton from '../shared/submit_button.vue';

export default {
  components: {
    TaskModal,
    SweetModal,
    SubmitButton,
  },
  mixins: [
    permissionsHelper,
  ],
  props: {
    taskGroup: {
      type: Object,
      default: () => {},
      required: true,
    },
    isOpen: {
      type: Boolean,
      required: false,
      default: false,
    },
    isLightThemed: {
      type: Boolean,
      required: false,
      default: true,
    },
    taskGroups: {
      type: Array,
      default: () => [],
      required: false,
    },
  },
  data() {
    return {
      // isGroupOpen: false,
      hover: false,
      addTaskToGroupHover: false,
      removeTaskToGroupHover: false,
      editTaskGroupHover: false,
      isNewTask: true,
      selectedTemplate: null,
      linkedTask: {},
      openLinkedTask: false,
      selectedTaskGroup: {},
    };
  },
  computed: {
    totalTriggerCount() {
      const tasks = this.taskGroup?.tasks || [];
      return tasks.reduce((sum, task) => sum + (task.triggerCount || 0), 0);
    },
    ungroupedTask() {
      return this.taskGroup.name === "Ungrouped" && this.taskGroup.isDeletable;
    },
  },
  methods: {
    handleClick() {
      this.$emit('click');
    },
    createNewTask(taskGroup) {
      this.isNewTask = true;
      this.selectedTaskGroup = taskGroup || {};
      this.selectedTemplate = taskGroup.taskTemplate || 0;
      this.openLinkedTask = true;
      this.$refs.taskModal.open();
    },
    handleClose() {
      this.openLinkedTask = false;
      if (this.$refs.taskModal.visible) {
        this.$refs.taskModal.close();
      }
    },
    openDeleteGroupTaskModal() {
      this.$refs.deleteGroupTaskModal.open();
    },
    deleteGroup() {
      if (!this.taskGroup?.id) return;

      http
        .delete(`/automated_task_groups/${this.taskGroup.id}`)
        .then(() => {
          this.$refs.deleteGroupTaskModal.close();
          this.$emit('group-deleted');
          this.emitSuccess(`Task group deleted successfully`);
        })
        .catch(() => {
          this.emitError('Sorry, there was an error while deleting the task group.');
        });
    },
    updateTask() {
      this.$emit('update');
    },
  },
};
</script>

<style lang="scss" scoped>
.task-group-info {
  width: 20rem;
}

.group-header__icon-wrap {
  align-content: center;
  background-color: lighten($dark-drawer-alt, 5%);
  height: 2.625rem;
  width: 2.625rem;
}

.group-header__action-button-wrap {
  height: 41px;
  width: 41px;
}

.invisible-no-pointer {
  opacity: 0 !important;
  pointer-events: none !important;
}
</style>
