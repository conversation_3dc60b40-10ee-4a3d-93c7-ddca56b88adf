<script>
import common from 'mixins/automated_tasks/common';
import string from 'mixins/string';
import _get from 'lodash/get';

import pluralize from 'pluralize/pluralize';
import SmartListView from "./values/smart_list.vue";
import AssetView from "./values/asset.vue";
import ContractView from "./values/contract.vue";
import ContributorView from "./values/contributor.vue";
import DurationView from "./values/duration.vue";
import PriorityView from "./values/priority.vue";
import TextView from "./values/text.vue";
import VendorView from "./values/vendor.vue";
import FormFieldView from "./values/form_field.vue";
import FormValueView from "./values/form_value.vue";
import TelecomServiceView from "./values/telecom_service.vue";

export default {
  name: 'EventClause',
  components: {
    AssetView,
    ContractView,
    SmartListView,
    ContributorView,
    DurationView,
    PriorityView,
    TextView,
    VendorView,
    Form<PERSON>ieldView,
    FormValueView,
    TelecomServiceView,
  },
  mixins: [ common, string ],
  props: {
    node: {
      type: Object,
      default: () => {},
    },
    link: {
      type: Boolean,
      default: true,
    },
    displayInline: {
      type: Boolean,
      default: false,
    },
    bgTransparent: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    createEventElement(createElement) {
      const elements = [];
      const elementsAtTheEnd = [];
      const {name} = this.node.nodeType;
      if (!name) { return []; }
      let currentIdx = 0;
      let phrase = "";
      let startIdx;
      let endIdx;

      startIdx = name.substring(currentIdx).search(/[{[/]/);
      while (startIdx > -1) {
        endIdx = name.substring(startIdx + 1).search(/[}\]/]/) + startIdx + 1;
        if (currentIdx < startIdx) {
          phrase += name.substring(currentIdx, startIdx);
        }
        if (name[startIdx] === '/') {
          startIdx = endIdx + 1;
        } else if (name[startIdx] === '[' || name[startIdx] === '{') {
          // We first have to determine if the child node replaces the subject or gets appended at the end
          const child = this.node.nodes[0];
          const childName = _get(child, 'nodeType.name');
          if (childName && childName.startsWith("any")) {
            // Replace the subject
            if (phrase.length > 0) {
              const element = this.createLabel(createElement, phrase);
              elements.push(element);
              phrase = "";
            }
            const element = this.createNode(createElement, child);
            elements.push(element);
          } else {
            // Otherwise, append it to the end
            if (this.node.value) {
              phrase += this.valuePhrase(this.node);
            } else {
              phrase += name.substring(startIdx + 1, endIdx);
            }

            if (child && child.nodeType.id) {
              const element = this.createNode(createElement, child);
              elementsAtTheEnd.push(element);
            }
          }
        }
        currentIdx = endIdx + 1;
        startIdx = name.substring(currentIdx).search(/[{[/]/);
        if (startIdx > -1) { startIdx += currentIdx; }
      }
      if (currentIdx < name.length) {
        phrase += name.substring(currentIdx, name.length);
        phrase += this.additionalPhrase(this.node, name);
      }
      if (phrase.length > 0) {
        const element = this.createLabel(createElement, phrase);
        elements.push(element);
      }

      for (let i = 0; i < elementsAtTheEnd.length; i+=1) {
        const element = elementsAtTheEnd[i];
        elements.push(element);
      }
      return elements;
    },

    createSubjectElement(createElement) {
      const {name} = this.node.nodeType;
      if (!name) { return []; }
      let currentIdx = 0;
      let phrase = "";
      const elementsAtTheEnd = [];
      const childrenElements = [];
      let startIdx = name.substring(currentIdx).search(/[{[/]/);
      let endIdx;

      if (startIdx < 0) {
        phrase = name;
      } else {
        while (startIdx > -1) {
          endIdx = name.substring(startIdx + 1).search(/[}\]/]/) + startIdx + 1;
          if (name[startIdx] === "/") {
            // Purposefully doing nothing
            startIdx = endIdx + 1;
          } else if (name[startIdx] === '[' || name[startIdx] === '{') {
            // We first have to determine if the child node replaces the subject or gets appended at the end
            const child = _get(this, "node.nodes[0]");
            const childName = _get(child, 'nodeType.name');
            if (childName && childName.startsWith("any")) {
              // Replace the subject
              phrase += childName + name.substring(endIdx + 1);
            } else {
              if (currentIdx < startIdx) {
                phrase += name.substring(currentIdx, startIdx);
              }
              // Otherwise, append it to the end
              if (this.node.value) {
                const valuePhrase = this.valuePhrase(this.node);
                if (valuePhrase) {
                  phrase += valuePhrase;
                } else {
                  const valueNode = this.createValueNode(createElement, this.node);
                  if (valueNode) {
                    childrenElements.push(valueNode);
                  }
                }
              } else {
                phrase += name.substring(startIdx).replace(/[^a-zA-Z ]/g, "");
              }

              if (_get(child, "nodeType.id")) {
                const element = this.createNode(createElement, child);
                elementsAtTheEnd.push(element);
              }
            }
          }
          currentIdx = endIdx + 1;
          startIdx = name.substring(currentIdx).search(/[{[/]/);
          if (startIdx > -1) { startIdx += currentIdx; }
        }
      }

      if (phrase.length > 0) {
        const element = this.createLink(createElement, phrase, childrenElements);
        elementsAtTheEnd.unshift(element);
      }
      return elementsAtTheEnd;
    },
    createViewNode(createElement, node, name) {
      let ids = null;
      let attrs = null;
      switch(name) {
        case "user":
        case "contributor":
        case "user or group":
          ids = _get(node, 'value.contributors');
          attrs = {
            attrs: {
              'url': '/contributor_options.json',
              'label': 'staff',
              'class': 'font-weight-bold',
            },
            props: {
              value: ids,
            },
          };
          return createElement(`smart-list-view`, attrs);
        case "contract":
          ids = _get(node, 'value.contracts');
          attrs = {
            attrs: {
              'url': '/contract_options.json',
              'label': 'contracts',
              'class': 'font-weight-bold',
            },
            props: {
              value: ids,
            },
          };
          return createElement(`smart-list-view`, attrs);
        case "location":
          ids = _get(node, 'value.locations');
          attrs = {
            attrs: {
              'url': '/location_options.json',
              'label': 'locations',
              'class': 'font-weight-bold',
            },
            props: {
              value: ids,
            },
          };
          return createElement(`smart-list-view`, attrs);
        case "asset":
          ids = _get(node, 'value.assets');
          attrs = {
            attrs: {
              'url': '/managed_asset_options.json',
              'label': 'assets',
              'class': 'font-weight-bold',
            },
            props: {
              value: ids,
            },
          };
          return createElement(`smart-list-view`, attrs);
        case "telecom service":
          ids = _get(node, 'value.telecomServices');
          attrs = {
            attrs: {
              'url': '/telecom_service_options.json',
              'label': 'telecom services',
              'class': 'font-weight-bold',
            },
            props: {
              value: ids,
            },
          };
          return createElement(`smart-list-view`, attrs);
        case "vendor":
          ids = _get(node, 'value.vendors');
          attrs = {
            attrs: {
              'url': '/vendor_options.json',
              'label': 'vendors',
              'class': 'font-weight-bold',
            },
            props: {
              value: ids,
            },
          };
          return createElement(`smart-list-view`, attrs);
        case "form value":
          {
          const valueName = _get(node, 'value');
          if (valueName) {
            const keyName = Object.keys(valueName)[0];
            if (keyName) {
              return this.createViewNode(createElement, node, pluralize(keyName, 1));
            }
          }
        }
          return null;
        default:
          return null;
      }
    },
    createValueNode(createElement, node, nodeTypeName = node.nodeType.name) {
      const name = this.subjectName(nodeTypeName);
      return this.createViewNode(createElement, node, name);
    },
    createNodeElements(createElement) {
      if (this.node.nodeType) {
        if (this.node.nodeType.type === 'Event') {
          return this.createEventElement(createElement);
        }
        return this.createSubjectElement(createElement);
      }
      return '';
    },
    createValue(createElement, node) {
      let subject = this.subjectName(node.nodeType.name);
      if (subject === 'user or group') {
        subject = 'contributor';
      }
      subject = subject.replaceAll(" ", "-");
      const attrs = {
        attrs: {
          'data-tc-event-clause': '',
          'class': 'small',
        },
        props: {
          node,
        },
      };
      if (this.link) {
        attrs.attrs.class = 'clause-link';

        attrs.on = {
          input: this.handleInput,
        };
      }
      return createElement(`${subject}-view`, attrs);
    },
    createNode(createElement, node) {
      const attrs = {
        props: {
          node,
          link: this.link,
        },
      };
      if (this.link) {
        attrs.on = {
          input: this.handleInput,
        };
      }
      return createElement('event-clause', attrs);
    },
    handleInput(node) {
      this.$emit('input', node);
    },
    handleClick(event) {
      // Stop event propagation
      event.stopPropagation();

      // Prevent the default keyup handler for this element
      event.preventDefault();

      this.$emit("input", this.node);
    },
    handleClickWithValue(event) {
      // Stop event propagation
      event.stopPropagation();

      // Prevent the default keyup handler for this element
      event.preventDefault();

      this.$emit("input", this.node, true);
    },
    createLabel(createElement, phrase) {
      const attrs = {
        attrs: {
          'data-tc-event-trigger': '',
          'class': `small phrase_1 text-dark ${this.bgTransparent ? 'bg-transparent' : ''}`,
        },
      };
      return createElement('span', attrs, [ phrase ]);
    },
    createLink(createElement, phrase, childrenElements = []) {
      const attrs = {
        attrs: {
          'data-tc-event-clause': '',
          'class' : `small phrase_2 ${this.bgTransparent ? 'bg-transparent' : ''}`,
        },
      };
      if (this.link) {
        attrs.attrs.class = 'clause-link';
        attrs.on = {
          click: this.handleClick,
        };
      }
      let childElements = [ this.truncate(phrase, 45) ];
      childElements = childElements.concat(childrenElements);
      return createElement('span', attrs, childElements);
    },
  },
  render(createElement) {
    if (this.node) {
      const elements = this.createNodeElements(createElement);
      let attrs = null;
      attrs = {
          attrs: {
          'class': `mb-0 phrase_wrapper`,
        },
      };
      return createElement("p", attrs, elements);
    }
    return createElement("div");
    
  },
};
</script>

<style lang="scss" scoped>
.clause-link {
  background-color: $themed-light;
  border-radius: $border-radius;
  margin: 0 0.25rem;
  padding: 0.5rem;

  &:hover {
    background-color: $themed-secondary;
    color: #FFF;
    cursor: pointer;
  }
}
</style>
