<template>
  <div>
    <Teleport to="body">
      <sweet-modal
        v-if="isExistingTask "
        ref="editTaskFormModal"
        v-sweet-esc
        modal-theme="dark-header theme-right theme-sticky-footer"
        blocking
        width="85%"
        @close="handleClose"
      >
        <template slot="title">
          <div class="d-flex align-items-center h-100">
            <h4
              v-if="!editingTaskName"
              class="mb-0"
            >
              {{ truncate(templateModalTitle, 50) }}
            </h4>
            <div
              v-else
              class="input-group w-50"
            >
              <input
                v-if="task"
                id="name"
                v-model="task.name"
                v-validate="'required|max:50'"
                type="text"
                class="form-control"
                name="name"
                placeholder="e.g., Notify on custom status"
                @keydown.enter="handleTaskNameAction('save')"
                @keydown.esc="handleTaskNameAction('discard')"
              >
            </div>
            <span class="sweet-box-actions position-relative mt-1 t-0 right-0 ml-4 not-as-small">
              <div
                v-if="!editingTaskName"
                v-tooltip="'Edit Task Name'"
                class="sweet-action-close"
                @click="handleTaskNameAction('start')"
              >
                <i class="nulodgicon-edit" />
              </div>
              <div
                v-else
                v-tooltip="'Save Task Name'"
                class="sweet-action-close"
                @click="handleTaskNameAction('save')"
              >
                <i class="genuicon-checkmark" />
              </div>
              <div
                v-if="editingTaskName"
                v-tooltip="'Discard Changes'"
                class="sweet-action-close ml-2"
                @click="handleTaskNameAction('discard')"
              >
                <i class="genuicon-ios-close" />
              </div>
            </span>
          </div>
        </template>

        <template slot="box-action">
          <div
            v-tooltip="'Clone this task'"
            class="sweet-action-close mx-2 clone-task-icon"
            @click.stop.prevent="handleClone"
          >
            <i
              class="genuicon-duplicate-contract base-font-size"
            />
          </div>

          <div
            v-if="task && !task.companyMailer"
            v-tooltip="'Delete task'"
            class="sweet-action-close mx-2 task-icon"
            @click.stop.prevent="openDeleteModal"
          >
            <i
              class="nulodgicon-trash-b not-as-big"
            />
          </div>
          <material-toggle
            v-if="task"
            v-tooltip="{ content: activateDeactivate }"
            :init-active="!disabled"
            class="mx-2 mt-2 d-inline-flex"
            @toggle-sample="updateDisable"
          />
        </template>

        <template slot="default">
          <task-form
            v-if="task"
            ref="taskForm"
            :value="task"
            :task-group="selectedGroup"
            :task-groups="taskGroups"
            class="w-100"
            @input="saveTask"
            @cancel="handleCancel"
          />
        </template>
      </sweet-modal>
    </Teleport>
    <Teleport to="body">
      <delete-modal
        ref="deleteModal"
        :value="task"
      />
    </Teleport>
  </div>
</template>

<script>
import http from 'common/http';
import { SweetModal } from 'sweet-modal-vue';
import { mapMutations, mapActions } from 'vuex';
import _cloneDeep from 'lodash/cloneDeep';
import common from 'mixins/automated_tasks/common';
import permissionsHelper from "mixins/permissions_helper";
import taskNameMixin from 'mixins/automated_tasks/task_name';
import companyModule from 'mixins/company_module';
import MaterialToggle from 'components/shared/material_toggle.vue';
import TaskForm from './form_modal.vue';
import DeleteModal from './delete_modal.vue';

export default {
  components: {
    TaskForm,
    SweetModal,
    DeleteModal,
    MaterialToggle,
  },
  mixins: [common, companyModule, permissionsHelper, taskNameMixin],
  props: ['value', 'taskGroups', 'selectedGroup'],
  data() {
    return {
      task: _cloneDeep(this.value),
      tempTaskName: null,
      editingName: null,
      hovering: false,
      isModalOpen: false,
    };
  },
  computed: {
    templateModalTitle() {
      return this.task?.name ? this.task.name : "Add the Task Name";
    },
    isExistingTask() {
      return this.value?.id;
    },
    activateDeactivate() {
      return this.disabled ? "Activate this task" : "Deactivate this task";
    },
    forceDisabled() {
      return this.task.forceDisabled;
    },
    disabled() {
      return !!this.task.disabledAt;
    },
  },
  watch: {
    value(newTask) {
      this.task = { ...newTask };
    },
  },
  methods: {
    ...mapMutations(['setAutomatedTasks']),
    ...mapActions(['fetchAutomatedTasks']),

    // updateNameStatus(value) {
    //   this.isNamePresent = value;
    // },

    handleClone() {
      http
        .post(`/cloned_tasks.json`, { id: this.task.id } )
        .then(() => {
          this.emitSuccess("Task successfully cloned");
          this.loadTasks();
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error cloning that task.`);
        });
    },

    updateDisable() {
      if (this.forceDisabled) {
        this.emitError("Please update task action(s) to enable this task");
      } else {
        this.$store.dispatch("updateDisable", this.task)
          .then(() => {
            this.loadTasks();
          });
      }
    },

    loadTasks() {
      this.fetchAutomatedTasks()
        .then(res => {
          this.setAutomatedTasks(res.data);
        })
        .catch(() => {
          this.emitError("Sorry, but there was an error fetching the automated tasks.");
        });
    },

    saveTask(task) {
      // the task which we recieve in the param is not updated when we update the name,
      // as we recieve it from task-form emit. Hence, we need to update the task with latest task name here explicitly

      http
        .put(`/automated_tasks/${task.id}.json`, { task } )
        .then(() => {
          this.emitSuccess("Task saved successfully");
          this.$emit('fetch-task');
          this.$emit('close-model');
          this.$refs.editTaskFormModal.close();
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error saving task. ${error.response.data.message}`);
        });
    },

    handleClose() {
      if (this.isModalOpen) {
        this.isModalOpen = false;
        this.$emit('fetch-task');
        this.$emit('close-model');
        this.$refs.editTaskFormModal.close();
      }
    },

    handleCancel() {
      this.$emit('close-model');
      this.$emit('fetch-task');
      this.$refs.editTaskFormModal.close();
    },

    close() {
      if (this.$refs.editTaskFormModal.visible) {
        this.$emit('fetch-task');
        this.$emit('close-model');
        this.$refs.editTaskFormModal.close();
      }
    },

    open() {
      if (!this.isModalOpen) {
        this.isModalOpen = true;
        if (this.task) {
          this.redirectOnWorkspaceChange(this.task.workspaceId, '/automated_tasks');
        }
        this.$nextTick(() => {
          this.$refs.editTaskFormModal.open();
        });
      }
    },

    openDeleteModal() {
      this.$nextTick(() => {
        this.$refs.deleteModal.open();
      });
    },

    handleDeleteTask() {
      this.$refs.editTaskFormModal.close();
      this.$refs.deleteModal.close();
    },
  },
};
</script>

<style lang="scss" scoped>
  .alert-icon {
    top: 0;
    right: 0;
    border-radius: 0 4px 0 4px;
  }
  .clone-task-icon {
    position: relative;
    top: 0.10rem;
  }
  .task-icon {
    position: relative;
    top: 0.07rem;
    padding-right: 0;
  }

  :deep(label) {
    margin-bottom: .25rem;
  }
  :deep(.sweet-modal) {
    .sweet-box-actions {
      display: flex;
    }
  }
</style>

