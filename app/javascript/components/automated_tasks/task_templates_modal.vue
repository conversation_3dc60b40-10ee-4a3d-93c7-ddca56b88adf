<template>
  <Teleport to="body">
    <sweet-modal
      ref="newTaskSetupModal"
      v-sweet-esc
      width="75%"
      blocking
      title="Select a New Task Template"
      @close="closeTaskSetupModal"
    >
      <template slot="default">
        <div class="text-left mt-0">
          <div>
            <div class="form-group">
              <label>Name this Task</label>
              <div class="input-group">
                <input
                  v-if="task"
                  id="name"
                  v-model="task.name"
                  v-validate="'required|max:50'"
                  type="text"
                  class="form-control"
                  name="name"
                  placeholder="e.g., Notify on custom status"
                >
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div
            v-for="(taskTemplate, index) in taskTemplates"
            :key="index"
            class="col-6 col-lg-4 mb-4 col-xxl-wide-theme-3"
          >
            <div
              class="box box--flat align-items-start p-3 clickable"
              :class="{'bg-blue-subtle border border-biscay-blue selected_temp': index === selectedTaskTemplate}"
              @click="handleTemplateSelection(index)"
            >
              <div class="box__inner">
                <div class="row">
                  <div class="col-auto">
                    <i
                      class="icon-box"
                      :class="getIconClass(taskTemplate.name)"
                    />
                  </div>
                  <div class="col pl-0">
                    <h6 class="my-0">
                      {{ taskTemplate.name }}
                    </h6>
                    <p class="mt-1 small text-muted mb-0">
                      {{ taskTemplate.description }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div
        slot="button"
        class="clearfix text-right"
      >
        <submit-button
          class="ml-2"
          display-icon="checkmark"
          :btn-content="`Create Task`"
          :btn-classes="'px-4 form-create-btn'"
          @submit="createOrCloseTask"
        />
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';
  import SubmitButton from 'components/shared/submit_button.vue';

  export default {
    components: {
      SweetModal,
      SubmitButton,
    },
    props: {
      taskTemplates: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        task: {
          events: {
            nodes: [],
          },
          actions: { },
        },
        selectedTaskTemplate: 0,
      };
    },
    mounted() {
      this.$nextTick(() => {
        const modalEl = this.$refs.newTaskSetupModal?.$el;
        const closeBtn = modalEl?.querySelector('.sweet-action-close');

        if (closeBtn) {
          closeBtn.addEventListener('click', this.cancelTemplateSelection);
        }
      });
      this.handleTemplateSelection();
    },
    beforeUnmount() {
      const modalEl = this.$refs.newTaskSetupModal?.$el;
      const closeBtn =  modalEl?.querySelector('.sweet-action-close');

      if (closeBtn) {
        closeBtn.removeEventListener('click', this.cancelTemplateSelection);
      }
    },
    methods: {
      open() {
        this.$refs.newTaskSetupModal.open();
      },
      close() {
        this.selectedTaskTemplate = 0;
        this.$refs.newTaskSetupModal.close();
      },
      closeTaskSetupModal() {
        if (this.$route.query.new) {
          this.$router.push('/automated_tasks');
        }
      },
      createOrCloseTask() {
        this.$emit('submit-template-selection');
      },
      handleTemplateSelection(index=0) {
        const selectedTemplate = this.taskTemplates[index];
        this.selectedTaskTemplate = index;
        this.task = { ...selectedTemplate };
        this.task.template = selectedTemplate;
        this.$emit('selected-template', this.task);
      },
      cancelTemplateSelection() {
        this.resetData();
        this.handleTemplateSelection();
      },
      resetData() {
        this.task = {
          name: null,
          template: null,
          events: [{ nodes: [] }],
          actions: [{}],
        };
        this.selectedTaskTemplate = 0;
      },
      getIconClass(templateName) {
        const iconMap = {
          'Blank Task': "nulodgicon-plus-round text-secondary",
          'Notify Ticket Created': "genuicon-ticket",
          'Notify on Open': "genuicon-ticket",
          'Notify on Priority': "genuicon-flag",
          'Notify on New Comment': "genuicon-comment-o",
          'Notify on Close': "genuicon-build-email-templates",
          'Notify Assigned-To Changes': "genuicon-build-custom-forms",
          'Notify on Status': "genuicon-status",
          'Notify Attachment Added': "nulodgicon-file-o",
        };

        return iconMap[templateName];
      },
    },
  };
</script>

<style lang="scss" scoped>
  .nulodgicon-plus-round:before {
    color: $themed-fair;
  }

  .selected_temp {
    body[data-theme="dark"] & .box__inner h6 {
      color: #fff !important;
    }

    body[data-theme="dark"] & .box__inner p {
      color: #242323 !important;
    }

    .box__inner h6 {
      color: #333 !important;
    }

    .box__inner p {
      color: #242323 !important;
    }
  }
</style>
