<template>
  <div class="h-100">
    <div class="mb-2 mt-1 subpage-menu w-fit-content">
      <div
        v-for="(page, index) in pages"
        :key="index"
        class="cursor-pointer subpage-menu__item"
        :class="{
          'router-link-exact-active': isActiveClass(index),
          'required': isFormErrorNavButton(index),
        }"
        @click.stop="goToPage(index)"
      >
        {{ page.label }}
      </div>
    </div>

    <div
      v-if="currentPage || currentPageIndex > -1"
      class="py-4"
    >
      <div
        v-show="currentPageIndex === 0"
        class="position-relative"
      >
        <div
          class="d-flex align-items-center justify-content-between mb-0 position-absolute right-0 pt-1"
          style="margin-top: -4rem"
        >
          <button
            v-if="isNotDefaultTask"
            v-tooltip="'Add new trigger'"
            class="btn btn-primary btn-sm mt-1 text-white add-btn"
            :disabled="!!task.customFormId"
            @click.stop.prevent="addNewTriggerClick"
          >
            <i class="nulodgicon-plus-round mb-0 clickable text-white mr-1" />
            Add New Trigger
          </button>
        </div>
        
        <div class="row">
          <div class="col-12 col-md-8">
            <div class="row w-100">
              <div class="col-12">
                <div class="row">
                  <div class="col-12 pb-5">
                    <form class="w-100">
                      <div
                        v-for="(event, idx) in events"
                        :key="`${event.id}-${idx}`"
                        class="my-0 box box--with-heading box--flat mb-3"
                        :class="{
                          'selected': selectedTrigger === idx,
                          'mb-3': idx < (events.length - 1)
                        }"
                      >
                        <div
                          class="box__heading base-font-size p-2 d-flex align-items-center clickable"
                          :class="{
                            'selected pl-3 ml-n2': selectedTrigger === idx,
                            'bg-themed-lighter text-secondary rounded': selectedTrigger !== idx}
                          "
                          @click="selectTrigger(idx)"
                        >
                          <span
                            class="onboarding-rounded-circle align-middle text-center d-inline-block small font-weight-semi-bold mr-2"
                            :class="{'ml-n2 bg-blue-light text-white': selectedTrigger === idx, 'bg-themed-branded-light text-themed-base': selectedTrigger !== idx}"
                          >
                            {{ idx + 1}}
                          </span>

                          <span class="d-inline-flex align-items-center">
                            <event-clause
                              v-if="event.nodes && event.nodes.length > 0"
                              class="mb-0 d-inline-flex"
                              :node="event"
                              :link="false"
                            />
                            <span 
                              v-else
                              class="small phrase_2--empty d-flex align-items-center"
                            >
                              If this happens
                            </span>
                          </span>

                          <span class="position-absolute right-0 mr-3">
                            <span
                              class="mr-3"
                              :class="{'required': eventsFormErrors.includes(idx)}"
                            >
                              <i
                                v-if="event && event.nodeType"
                                class="ml-1"
                                :class="[iconFromTriggerEventClass(event), {'text-danger': eventsFormErrors.includes(idx)}]"
                              />
                            </span>

                            <span
                              v-if="isNotDefaultTask"
                              href="#"
                              :disabled="events.length <= 1"
                              @click.stop.prevent="removeEvent(idx)"
                            >
                              <i
                                v-tooltip="events.length > 1 ? 'Remove Trigger' : 'Cannot remove only trigger'"
                                class="nulodgicon-trash-b mr-3"
                              />
                            </span>

                            <i :class="{'nulodgicon-chevron-up': selectedTrigger === idx, 'nulodgicon-chevron-down': selectedTrigger !== idx }"/>
                          </span>
                        </div>

                        <div
                          v-show="selectedTrigger === idx"
                          class="w-100"
                        >
                          <event-form
                            :id="idx"
                            ref="eventForm"
                            class="box__inner p-3"
                            :allow-delete="events.length > 1"
                            :value="event"
                            :task="task"
                            :disabled="!!task.companyMailer"
                            :data-tc-task-disable="!!task.companyMailer"
                            @input="handleEventInput"
                            @remove="removeEvent"
                          />
                        </div>
                      </div>

                      <div
                        v-if="!task.companyMailer"
                        class="mt-4"
                      >
                        <div
                          v-if="false"
                          class="row mx-3 mt-4 align-items-center justify-content-around"
                        >
                          <div class="col-6 border-line"/>
                          <div class="border-label mr-3 small bg-light">OR</div>
                          <div class="col-6 border-line"/>
                        </div>

                        <div
                          v-if="false"
                          class="row justify-content-between m-0 mt-4 pl-3 pr-2"
                        >
                          <div
                            class="form-control clickable mb-2 text-muted text-left col"
                            :class="{'clickable': !newTriggerDisabled, 'bg-light': newTriggerDisabled}"
                            @click.prevent="addEvent"
                          >
                            <span
                              class="task-step-trigger text-dark align-middle py-1 px-2 rounded"
                              :class="{'bg-light': newTriggerDisabled}"
                            >
                              Add a new trigger
                            </span>
                          </div>
                          <div class="col-auto ml-3">
                            <a
                              class="btn btn-primary btn-sm text-white rounded-circle mt-1"
                              :class="{'btn-flat': newTriggerDisabled}"
                              href="#"
                              role="button"
                              :disabled="!!task.companyMailer || newTriggerDisabled"
                              :data-tc-trigger-disable="!!task.companyMailer || newTriggerDisabled"
                              data-tc-add-condition
                              @click.prevent="addEvent"
                            >
                              <i class="nulodgicon-plus-round"/>
                            </a>
                          </div>
                        </div>
                      </div>

                      <!-- <hr class="bg-fair">

                      <action-form
                        v-if="task"
                        ref="actionForm"
                        :value="task"
                        :event-changed="isEventChanged"
                        @input="handleActionInput"
                      /> -->
                    </form>
                    <sweet-modal
                      ref="surveyAlert"
                      v-sweet-esc
                      title="Before we proceed..."
                    >
                      <template slot="default">
                        <h6 class="mb-3 text-center">
                          Please know that the survey url will only be sent if the ticket's survey is enabled.
                        </h6>
                      </template>
                      <button
                        slot="button"
                        class="btn btn-link text-secondary"
                        @click.stop="closeSurveyAlertModal"
                      >
                        Go Back
                      </button>
                      <button
                        slot="button"
                        class="btn btn-primary"
                        @click.stop.prevent="moveToSaveTask()"
                      >
                        Save Task
                      </button>
                    </sweet-modal>

                  </div>
                </div>
              </div>
              <!-- <div 
                class="bg-lighter text-center border-top border-light row justify-content-end"
                :class="isMsp ? 'sticky-btn' : 'sticky-btn-holder'"
              >
                <div class="col-2 d-flex justify-content-betweeen mr-6">
                  <button
                    class="btn btn-link text-secondary"
                    data-tc-back
                    @click.prevent="handleBack"
                  >
                    Cancel
                  </button>
                  <submit-button
                    v-if="modifyOnce"
                    btn-content="Assign to Company"
                    :is-validated="false"
                    :btn-classes="'px-4 form-create-btn ml-3'"
                    @submit="assignToCompany"
                  />
                  <submit-button
                    v-else
                    :is-validated="false"
                    :btn-content="`Save`"
                    :btn-classes="'px-4 form-create-btn ml-3'"
                    @submit="handleAction"
                  />
                </div>
                <div class="col-3 pt-2 d-flex justify-content ml-7">
                  <a
                    v-if="task.companyMailer"
                    class="text-primary"
                    @click="resetTask"
                  >
                    Reset
                  </a>
                </div>
              </div>
              -->
              
              <delete-modal
                ref="deleteModal"
                :value="task"
              /> 

              <warning-modal
                v-if="task && task.id && showWarningBox"
                ref="warningModal"
                @update:showWarningBox="showWarningBox = $event"
                @back-to="goToAutomatedTaskIndex"
              />
              <Teleport to="body">
                <copy-destination-modal
                  v-if="isMsp && modifyOnce"
                  ref="copyModal"
                  :selected-items="selected"
                  @copy-completed="$emit('cancel')"
                />
              </Teleport>
            </div>
          </div>
          <div class="col-4 d-sm-none d-md-block">
            <task-minimap
              :task="task"
              :selected-trigger="selectedTrigger"
              :selected-action="selectedAction"
              :selected-page="currentPageIndex"
              :events-form-errors="eventsFormErrors"
              :action-form-errors="actionFormErrors"
              @select-trigger="selectTrigger"
              @select-action="selectAction"
            />
          </div>
        </div>
      </div>

      <div
        v-show="currentPageIndex === 1"
        class="position-relative"
      >
        <div
          class="d-flex align-items-center justify-content-between mb-0 position-absolute right-0 pt-1"
          style="margin-top: -4rem"
        >
          <button
            v-if="isNotDefaultTask"
            v-tooltip="'Add new action'"
            class="btn btn-primary btn-sm mt-1 text-white add-btn"
            :disabled="!!task.customFormId"
            @click.stop.prevent="addNewActionClick"
          >
            <i class="nulodgicon-plus-round mb-0 clickable text-white mr-1" />
            Add New Action
          </button>
        </div>

        <div class="row">
          <div class="col-12 col-md-8">
            <div class="row w-100">
              <div class="col-12">
                <div class="row">
                  <div class="col-12 pb-5">
                    <action-form
                      v-if="task"
                      ref="actionForm"
                      :value="task"
                      :event-changed="isEventChanged"
                      :selected-action="selectedAction"
                      @input="handleActionInput"
                      @select-action="selectAction"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-4 d-sm-none d-md-block">
            <task-minimap
              :task="task"
              :selected-trigger="selectedTrigger"
              :selected-action="selectedAction"
              :selected-page="currentPageIndex"
              :events-form-errors="eventsFormErrors"
              :action-form-errors="actionFormErrors"
              @select-trigger="selectTrigger"
              @select-action="selectAction"
            />
            <!-- <task-summary
              class=""
              :task="task"
            /> -->

            <div class="mt-3">
              <div
                v-if="!isAssets"
                class="d-flex p-2 pt-3 mb-2 outdated-alert"
              >
                <i class="genuicon-info-circled align-middle"/>
                <span class="small ml-2">
                  Task notifications will be sent exclusively to the agents that have access to the current workspace.
                </span>
              </div>
              <div
                v-if="task.forceDisabled"
                class="d-flex p-2 pt-3 mb-2 outdated-alert"
              >
                <i class="genuicon-info-circled align-middle"/>
                <span class="small ml-2">
                  A user linked to one or more task actions no longer exists. Please update these actions to enable this automated task.
                </span>
              </div>
              <div
                v-if="isMSTeamsRecipient"
                class="d-flex p-2 pt-3 mb-2 outdated-alert"
              >
                <i class="genuicon-info-circled align-middle"/>
                <span class="small ml-2">
                  Due to the Microsoft Teams limitations, email updates to Teams channels may not work properly.
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="currentPageIndex === 2"
        class="position-relative"
      >
        <div class="row">
          <div class="col-12 col-md-8">
            <div class="row w-100">
              <div class="col-12">
                <div
                  v-if="!isAssets"
                  class="row"
                >
                  <div class="col-12 pb-5">
                    <div class="my-0 box box--with-heading box--flat mb-3">
                      <div class="box__heading base-font-size py-2 d-flex align-items-center">
                        Task Group
                      </div>
                      <div class="box__inner p-3">
                        <div
                          class="form-group col-6"
                        >
                          <label for="article_tags">Assign Task Group</label>
                          <multi-select
                            id="task_group"
                            placeholder="Select task group"
                            tag-placeholder=""
                            :allow-empty="true"
                            :multiple="false"
                            :options="filteredTaskGroups"
                            :taggable="true"
                            :value="selectedTaskGroup"
                            label="name"
                            track-by="id"
                            @select="selectTaskGroup"
                            @remove="removeTaskGroup"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12 pb-5">
                    <div class="my-0 box box--with-heading box--flat mb-3">
                      <div class="box__heading base-font-size py-2 d-flex align-items-center">
                        Task History
                      </div>
                      <div
                        v-if="!isAssets"
                        class="box__inner p-3"
                      >
                        <module-event-log 
                          module="helpdesk"
                          :task-id="task.id"
                        />
                      </div>
                      <div
                        v-if="isAssets"
                        class="box__inner p-3"
                      >
                        <module-event-log 
                          module="asset"
                          :task-id="task.id"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div 
            v-if="!isAssets"
            class="col-4 d-sm-none d-md-block"
          >
            <div class="col-12 pb-5">
              <div class="my-0 box box--with-heading box--flat mb-3">
                <div class="box__heading base-font-size py-2 d-flex align-items-center">
                  Task Metrics
                </div>
                <div class="box__inner box__inner-task-metrics p-3 d-flex">
                  <div class="col text-center">
                    <div class="text-muted more-readable-letter-spacing mb-2 small">
                      Task Order
                    </div>
                    <div class="h4 font-weight-normal mb-0">
                      {{ isNewTask ? '-' : task.order || '-' }}
                    </div>
                  </div>
                  <div class="col text-center">
                    <div class="text-muted more-readable-letter-spacing mb-2 small">
                      Task Used
                    </div>
                    <div class="h4 font-weight-normal mb-0">
                      {{ task.triggerCount || 0 }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="sweet-modal__sticky-footer text-right pr-5 modal-footer">
      <div class="d-flex justify-content-between align-items-center">
        <span>
          <a
            v-if="currentPageIndex > 0"
            class="mt-2 mr-1 text-secondary ml-2"
            role="button"
            href="#"
            @click.prevent.stop="goToPreviousPage"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span>Back to <strong>{{ previousPageName }}</strong></span>
          </a>
        </span>
        <div>
          <button
            slot="button"
            class="btn btn-outline bg-white font-weight-semi-bold mr-2 px-4 text-black"
            @click.stop="handleCancel"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-primary font-weight-semi-bold px-4"
            @click.prevent="mainCTAClick"
          >
            {{ buttonValue() }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import strings from 'mixins/string';
import common from 'mixins/automated_tasks/common';
import recipients from 'mixins/automated_tasks/recipients';

import { SweetModal } from 'sweet-modal-vue';
import MultiSelect from 'vue-multiselect';
import permissionsHelper from 'mixins/permissions_helper';
import companyModule from 'mixins/company_module';
import CopyDestinationModal from 'components/related_companies/copy_destination_modal.vue';
import ModuleEventLog from "components/shared/module_event_logs.vue";
import ActionForm from './forms/action.vue';
import EventForm from './forms/event.vue';
import TaskMinimap from './task_minimap.vue';
import EventClause from './event_clause.vue';
import DeleteModal from './delete_modal.vue';
import arrayComparisonHandler from '../../mixins/array_comparison_handler';
import warningModal from '../shared/custom_forms/modals/warning_modal.vue';

export default {
  components: {
    ActionForm,
    EventForm,
    TaskMinimap,
    EventClause,
    SweetModal,
    MultiSelect,
    DeleteModal,
    warningModal,
    CopyDestinationModal,
    ModuleEventLog,
  },
  mixins: [strings, common, recipients, permissionsHelper, arrayComparisonHandler, companyModule],
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    module: {
      type: String,
      default: '',
    },
    modifyOnce: {
      type: Boolean,
      default: false,
    },
    taskGroups: {
      type: Array,
      required: false,
      default: () => [],
    },
    isNewTask: {
      type: Boolean,
      default: false,
    },
    taskGroup: {
      type: Object,
      default: () => {},
      required: false,
    },
  },
  data() {
    return {
      task: _cloneDeep(this.value),
      idx: 0,
      showValue: false,
      component: this.value.id ? 'ActionForm' : 'EventForm',
      componentStates: ['EventForm', 'ActionForm'],

      // Selected is the option the user selected
      selectedNode: null,
      events: [],
      tempName: _get(this, 'value.name'),
      eventChanged: false,
      eventAdded: false,
      newTriggerWasAdded: false,
      lastTriggerIsValid: true,
      isSaving: false,
      eventsFormValid: true,
      actionFormValid: true,
      eventsFormErrors: [],
      actionFormErrors: [],
      copiedTask: _cloneDeep(this.value),
      selected: {
        automatedTasks: [],
      },
      pages: [
        { name: 'triggers', label: 'Triggers', hasSeen: true },
        { name: 'actions', label: 'Actions', hasSeen: false },
        { name: 'details', label: 'Details', hasSeen: false },
      ],
      currentPage: null,
      currentPageIndex: 0,
      selectedTrigger: 0,
      selectedAction: 0,
      currentUrl: '',
      groupTasks: [],
      selectedTaskGroup: null,
    };
  },
  computed: {
    isNotDefaultTask() {
      return this.task && !this.task.companyMailer;
    },
    isEventChanged() {
      return this.eventChanged;
    },
    isMsp() {
      return document.location.pathname.startsWith("/related_companies");
    },
    actionVerb() {
      if (this.component === 'ActionForm') {
        return "Save";
      }
      return "Action";
    },
    id() {
      return this.$route.params.id;
    },
    nextDisabled() {
      return (this.component === 'ActionForm' && this.value.companyMailer || this.component === 'ActionForm' && this.value.customFormId);
    },
    newTriggerDisabled() {
      return false;
      // return !this.lastTriggerIsValid;
    },
    previousPageName() {
      return this.pages[this.currentPageIndex - 1].label;
    },
    isExistingSurvey() {
      return this.task?.id;
    },
    isFinisingNewTaskSetup() {
      return !this.task?.id && this.task?.name;
    },
    filteredTaskGroups() {
      return this.taskGroups.filter(group => group.name?.toLowerCase() !== 'ungrouped');
    },
  },
  watch: {
    currentUrl(newVal) {
      this.isAssets = newVal.includes('managed_assets');
    },
    value(newVal) {
      this.task = _cloneDeep(newVal);
    },
  },
  mounted() {
    if (this.task.id) {
      this.events = this.task.events || [];
    }
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.beforeWindowUnload);
  },
  methods: {
    onWorkspaceChange() {
      window.addEventListener('beforeunload', this.beforeWindowUnload);
      if (!this.task.id && !this.isContextualSuggestion()) {
        this.addEvent();
      } else {
        this.events = this.task.events;
        this.selectedTaskGroup = this.task?.selectedTaskGroup;
      }

      this.setLastTriggerValidity();
      this.currentUrl = window.location.href;
    },
    initializeData() {
      if (this.task.id) {
        this.currentPage = 0;
        this.currentPageIndex = 0;
        this.events = this.task.events || [];
        this.copiedTask = _cloneDeep(this.value);
        this.$refs.actionForm.initializeData();
      }
      const groupSelected = this.taskGroups.find(group => group.id === this.taskGroup.id);
      this.selectedTaskGroup = this.task.selectedTaskGroup || groupSelected;
    },
    fetchTask() {
      this.$emit('fetch-task');
    },
    isEventsFormValid() {
      this.eventsFormValid = true;
      this.eventsFormErrors = [];

      if (this.$refs.eventForm) {
        for (let idx = 0; idx < this.$refs.eventForm.length; idx += 1) {
          const result = this.$refs.eventForm[idx].valid();
          if (!result) {
            this.eventsFormValid = false;
            this.eventsFormErrors.push(idx);
          }
        }        
      }
      return this.eventsFormValid;
    },
    isActionFormValid() {
      this.actionFormValid = true;

      this.actionFormErrors = this.$refs.actionForm && this.$refs.actionForm.valid();
      this.actionFormValid = this.actionFormErrors.length === 0;
      return this.actionFormValid;
    },
    valid() {
      const isEventsValid = this.isEventsFormValid();
      const isActionsValid = this.isActionFormValid();

      return isEventsValid && isActionsValid;
    },
    handleClone() {
      this.$emit("clone");
    },

    handleBack() {
      if (this.isMsp) {
        this.$emit('cancel');
      } else {
        this.$router.push(`/automated_tasks`);
      }
    },
    handleCancel() {
      this.$emit('cancel');
    },
    assignToCompany() {
      this.isSaving = true;
      this.showErrors();
      if (!this.valid()) {
        this.isSaving = false;
        return;
      }
      if (this.checkSurveyUrl()) {
        this.$refs.surveyAlert.open();
      } else {
        this.selected.automatedTasks = [this.task];
        this.$refs.copyModal.open();
      }
    },

    handleAction() {
      this.isSaving = true;
      this.showErrors();
      if (!this.valid()) {
        this.isSaving = false;
        this.emitError('Please correct the highlighted errors before saving.');
        return;
      }
      if (this.checkSurveyUrl()) {
        this.$refs.surveyAlert.open();
      } else if (this.isAssets) {
        this.$emit('input', { ...this.task });
      } else {
        this.$emit('input', { ...this.task, task_group: this.selectedTaskGroup });
      }
    },

    showNode(node) {
      if (!this.task.companyMailer) {
        this.showValue = false;
        this.selectedNode = node;
      } else {
        this.emitError("Sorry, navigation is disabled for default tasks");
      }
    },

    showErrors() {
      // Ok, this should never happen, but better safe than sorry
      if (this.$refs.eventForm) {
        for (let idx = 0; idx < this.$refs.eventForm.length; idx += 1) {
          this.$refs.eventForm[idx].showErrors();
        }        
      }
      if (this.$refs.actionForm) {
        this.$refs.actionForm.showErrors();
      }
    },
    handleEventInput(value) {
      const task = _cloneDeep(this.task);
      task.event = {
        nodeType: value.eventType,
        nodes: [
          {
            nodeType: value.subjectType,
            value: value.subjectValue,
          },
        ],
      };
      if (value.name) {
        task.name = value.name;
      }
      this.checkTaskEventChanged(value);
      this.events.splice(value.index, 1, task.event);
      this.task.events.splice(value.index, 1, task.event);
      if (this.eventChanged || this.eventAdded) {
        this.filterActions();
      }
      this.setLastTriggerValidity();
    },
    handleActionInput(value) {
      if (!(value.nodeType && value.nodeType.actionClass === "HaltExecution")) {
        const task = _cloneDeep(this.task);
        task.actions = value;
        this.task = task;
      } else if (!Array.isArray(this.task.actions)) {
        const task = _cloneDeep(this.task);
        task.actions = [value];
        this.task = task;
      }
    },
    moveToSaveTask() {
      if (this.$refs.surveyAlert) {
        this.closeSurveyAlertModal();
      }
      this.$emit('input', this.task);
    },
    closeSurveyAlertModal() {
      this.$refs.surveyAlert.close();
    },
    checkSurveyUrl() {
      if (Array.isArray(this.task.actions)) {
        for (let idx = 0; idx < this.task.actions.length; idx += 1) {
          if (this.isEmailAction(idx)) {
            return true;
          }
        }
      }
      return false;
    },
    addEvent() {
      if (this.newTriggerDisabled || this.task.customFormId) {
        return;
      }
      this.events.push({ nodes: [] });
      this.task.events = this.events;
      this.selectedTrigger = this.events.length - 1;

      this.newTriggerWasAdded = true;
      this.setLastTriggerValidity();
    },
    removeEvent(idx) {
      if (this.events.length > 1) {
        this.events.splice(idx, 1);
        const task = _cloneDeep(this.task);
        task.events = this.events;
        this.task = task;

        this.filterActions();
        this.setLastTriggerValidity();
      }
    },
    handleNameInput() {
      const task = _cloneDeep(this.task);
      task.name = this.tempName;
      this.task = task;
    },
    handleDelete() {
      this.$refs.deleteModal.open();
    },
    isEmailAction(idx) {
      return (this.task.actions[idx].nodeType &&
              this.task.actions[idx].nodeType.actionClass === 'SendEmail' &&
              this.task.actions[idx].value.body.includes('{survey_url}'));
    },
    checkTaskEventChanged(value) {
      this.eventChanged = false;
      this.eventAdded = false;
      const event = this.task.events[value.index];
      if (event.nodeType && event.nodeType.name && value.eventType && value.eventType.name !== event.nodeType.name) {
        this.eventChanged = true;
      } else if (!event.nodes.length) {
        this.eventAdded = true;
      }
    },
    setLastTriggerValidity() {
      this.$nextTick(() => {
        this.lastTriggerIsValid = this.isLastTriggerIsValid();
      });
    },
    isLastTriggerIsValid() {
      const lastTrigger = this.events ? this.events[this.events.length - 1] : null;
      return lastTrigger ? !!lastTrigger.nodeType : false;
    },
    filterActions() {
      this.$refs.actionForm.fetchActions(true);
    },
    isContextualSuggestion() {
      return this.$route.name === 'automated-task-new' && Object.keys(this.$route.query).length > 0;
    },
    beforeWindowUnload(e) {
      if (this.isManualTask()) {
        e.preventDefault();
        Object.defineProperty(e, 'returnValue', { writable: true, value: '' });
      }
    },
    goToAutomatedTaskIndex(flag) {
      this.showWarningBox = false;
      if (flag && this.isManualTask()) {
        this.showWarningBox = true;
        return this.showWarningBox;
      }
      return this.$router.push('/automated_tasks');
    },
    isManualTask() {
      return this.task.id && !this.task.companyMailer && this.isFormValuesChanged(this.task, this.copiedTask);
    },
    resetTask() {
      if (this.task.id) {
        const params = { mod: this.derivedCompanyModule() };
        http
          .get(`/automated_tasks/${this.task.id}/reset_task.json`, { params })
          .then(res => {
            this.resetAll(res.data);
          })
          .catch(error => {
            this.$router.push(`/automated_tasks`);
            this.emitError(`Sorry, there was an error loading task. (${error.response.data.message})`);
          });
      }
    },
    resetAll(task) {
      this.task.actions[0].value.body = task.body;
      this.task.actions[0].value.subject = task.subject;
      this.task.actions[0].value.target = task.target;
    },
    mainCTAClick() {
      let totalPages = 0;
      totalPages =  2;
      if (this.currentPageIndex !== totalPages) {
        this.showErrors();
        this.isActionFormValid();
        if (this.isEventsFormValid()) {
          this.goToNextTab();
        } else {
          this.emitError('Please correct the highlighted errors before continuing.');
        }
      } else if (this.currentPageIndex === totalPages) {
        this.handleAction();
      }
    },
    goToNextTab() {
      this.currentPageIndex += 1;
      this.pages[this.currentPageIndex].hasSeen = true;
    },
    buttonValue() {
      if (this.isExistingTask) {
        return 'Update Task';
      }
      return this.currentPageIndex === (this.pages.length - 1) ? 'Save and Close' : 'Continue';
    },
    isActiveClass(index) {
      return this.currentPageIndex === index;
    },
    isFormErrorNavButton(index) {
      return (index === 0 && !this.eventsFormValid) || (index === 1 && !this.actionFormValid);
    },
    goToPage(index) {
      this.currentPage = { ...this.pages[index] };
      this.currentPageIndex = index;
      this.pages[this.currentPageIndex].hasSeen = true;
    },
    goToPreviousPage() {
      this.currentPageIndex -= 1;
      this.pages[this.currentPageIndex].hasSeen = true;
      this.currentPage = { ...this.pages[this.currentPageIndex] };
    },
    addNewTriggerClick() {
      this.addEvent();
    },
    addNewActionClick() {
      this.$refs.actionForm.addAction();
    },
    selectTrigger(index) {
      if (index === this.selectedTrigger) {
        if (this.currentPageIndex === 0) {
          this.selectedTrigger = -1;
        }
      } else {
        this.selectedTrigger = index;
      }

      if (this.currentPageIndex === 1) {
        this.goToPage(0);
      }
    },
    selectAction(index) {
      if (index === this.selectedAction) {
        if (this.currentPageIndex === 1) {
          this.selectedAction = -1;
        }
      } else {
        this.selectedAction = index;
      }

      if (this.currentPageIndex === 0) {
        this.goToPage(1);
      }
    },
    updatedTaskName(newName) {
      this.task.name = newName;
    },
    selectTaskGroup(group) {
      this.selectedTaskGroup = group;
    },
    removeTaskGroup() {
      this.selectedTaskGroup = null;
    },
  },
};
</script>

<style lang="scss" scoped>
input:invalid, textarea:invalid {
  border: 1px solid red;
}

.task-step-trigger {
  background: $themed-task-trigger-one;
}

:deep(label) {
  margin-bottom: .25rem;
}

.outdated-alert {
  border-left: 4px solid $color-caution;
  background: $themed-light;
  border-radius: 0 0.25rem 0.25rem 0;
}

.genuicon-info-circled {
  color: $color-caution;
}

.sticky-btn {
  bottom: 0;
  left: 0;
  padding: 1.5rem 0;
  position: sticky;
  width: 100%;
  z-index: 11;
}

:deep(.phrase_2) {
  background: none;
  display: flex;
  margin-bottom: 0.1875rem;
}

.box__inner-task-metrics {
  min-height: 7.5rem;
}
</style>
