import _get from 'lodash/get';
import _difference from 'lodash/difference';
import _merge from 'lodash/merge';
import http from 'common/http';
import string from 'mixins/string';

const DAY_NAMES = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];

export default {
  mixins: [string],
  data() {
    return {
      dataMap: { },
    };
  },
  methods: {
    findLastNode(node) {
      if (node.nodes && node.nodes.length > 0) {
        return this.findLastNode(node.nodes[node.nodes.length - 1]);
      }
      return node;
    },
    findNode(node, head) {
      const headNodeTypeId = _get(head, 'nodeType.id');
      if (headNodeTypeId && node.nodeType.id === headNodeTypeId &&
          node.nodeType.name === head.nodeType.name) {
        return head;
      }
      for (let idx = 0; idx < head.nodes.length; idx + 1) {
        const found = this.findNode(node, head.nodes[idx]);
        if (found) {
          return found;
        }
      }
      return null;
    },
    removeBraces(obj) {
      return obj.replace(/[\{\}\[\]\/]/g, '');
    },
    /*
    populateParents(node) {
      for (let idx = 0; idx < node.nodes.length; idx++) {
        node.nodes[idx].parent = node;
        this.populateParents(node.nodes[idx]);
      }
    },
    */
    moduleIconSrc(module) {
      if (module === "help_tickets") {
        return "https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-helpdesk.svg";
      }
      return `https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-${module}.svg`;
    },

    cleanNode(node) {
      if (!node) {
        return node;
      }
      node.parent = null;
      for (let idx = 0; idx < node.nodes.length; idx + 1) {
        node.nodes[idx] = this.cleanNode(node.nodes[idx]);
      }
      return node;
    },

    nodeTemplate(node) {
      const type = _get(node, 'nodeType.type', null);
      if (type === 'Object') {
        return 'ObjectDetails';
      } else if (type === 'Type') {
        return 'SimpleDetails';
      } else if (type === 'Action') {
        return 'ActionSelect';
      } else if (type === 'Event') {
        return 'EventSelect';
      }
      return null;
    },
    iconCss(detail) {
      return detail.icon;
    },
    getSource(source) {
      const modifiedSources = {
        'manually_added': 'Manually added',
        'slack' : 'Slack',
        'ms_teams': 'Microsoft Teams',
        'splitted': 'Splitting',
        'auto_generated': 'Automated task',
        'email': 'Email',
        'mobile_app': 'Mobile app',
        'desktop_app': 'Desktop app',
      };
      return source ? modifiedSources[source] : '';
    },

    valuePhrase(node, nodeTypeName = node.nodeType.name) {
      let name = this.subjectName(nodeTypeName);
      switch(name) {
        case "text":
          return `"${node.value.text}"`;
          break;
        case "category":
          // code block
          break;
        case "service category":
          // code block
          break;
        case "city":
          return `${node.value.city}`;
          break;
        case "zip code":
          // code block
          break;
        case "state":
          // code block
          break;
        case "cost":
          // code block
          break;
        case "duration":
          return `duration ${node.value.duration}`;
          break;
        case "priority":
          return `"${node.value.priority}" priority`;
          break;
        case "form value":
          return this.formValue(node);
          break;
        case 'with source':
          return `with source ${this.getSource(node.value.source)}`;
        case "form field":
          let formField = _get(node, 'value.formField');
          if (!formField) {
            return "a form field";
          }
          let fieldPhrase = `field "${formField.name}"`;
          if (node.value.customForms && node.value.customForms.length > 0) {
            let myList = node.value.customForms.map((f) => f.name || f.formName || null);
            if (myList.length === 1 && node.value.customForms[0].id === 'all') {
              fieldPhrase = `${fieldPhrase} from any form`;
            } else if (myList.length > 0) {
              let label = myList.length > 1 ? 'forms' : 'form';
              var last = myList.pop();
              last = myList.length ? ` and ${last}` : last;
              fieldPhrase = `${fieldPhrase} from ${label} ${myList.join(", ")} ${last}`;
            }
          }
          return fieldPhrase;
          break;
        case 'remains in the same state':
          return `with "${_get(node, 'value.formField.label', null)}" state remains unchanged ` +
                 `for ${_get(node, 'value.timeInterval', 0)} hours`;
        case 'during':
          return `during "${this.toTitle(this.node.value.hours).toLowerCase()}"`;
        case "status":
          // code block
          return `status "${node.value.status}"`;
          break;
        case "user":
        case "user or group":
          const val = this.node.value;
          if ([val.mention_type, val.mentionType].includes('any')) return 'any user or group';
          return null;
          break;
        case "contract":
          return null;
          break;
        case "location":
          return null;
          break;
        case "asset":
          return null;
          break;
        case "telecom service":
          return null;
          break;
        case "vendor":
          return null;
          break;
        case "date of the month":
          const monthDates = node.value.dates;
          if (monthDates && monthDates.length > 0) {
            const dates = monthDates.map((d) => this.getOrdinal(d));
            return `${this.listPhrase(dates, "")} of the month`;
          }
          break;
        case "date of the year":
          if (node.value.yearDates) {
            const yearDates = node.value.yearDates.map((d) => {
              const date = new Date(d);
              const yearDay = date.getDate();
              const yearMonth = date.toLocaleDateString('en-US', { month: 'long' });
              return `${this.getOrdinal(yearDay)} ${yearMonth}`;
            });
            return `${this.listPhrase(yearDates, "")} of the year`;
          }
          break;
        case "date of each year quarter":
          if (node.value.yearQuarterDates) {
            const yearQuarterDates = node.value.yearQuarterDates.map((d) => {
              const date = new Date(d);
              const yearDay = date.getDate();
              const yearMonth = date.toLocaleDateString('en-US', { month: 'long' });
              return `${this.getOrdinal(yearDay)} ${yearMonth}`;
            });
            return `${this.listPhrase(yearQuarterDates, "")} of the year`;
          }
          break;
        case "date of each year bi-annually":
          if (node.value.yearBiAnnualDates) {
            const yearBiAnnualDates = node.value.yearBiAnnualDates.map((d) => {
              const date = new Date(d);
              const yearDay = date.getDate();
              const yearMonth = date.toLocaleDateString('en-US', { month: 'long' });
              return `${this.getOrdinal(yearDay)} ${yearMonth}`;
            });
            return `${this.listPhrase(yearBiAnnualDates, "")} of the year`;
          }
          break;
        case "day of the week":
          let days = node.value.days;
          if (days && days.length > 0) {
            let names = days.map((d) => DAY_NAMES[d] )
            return `${this.listPhrase(names)}`;
          }
          break;
        case "people list fields":
          const fields = node.value.peopleList;
          if (fields.length > 0) {
            const names = fields.map((field) => field.label);
            return `${this.listPhrase(names)}`;
          }
          break;
        case "specific integration":
          if (node.value.integrations.length > 1) {
            return `integration "${node.value.integrations[0].name}" + ${node.value.integrations.length - 1}`;
          } 
          return `integration "${node.value.integrations[0]?.name}"`;
          break;
        case "specific software":
          if (node.value.assetSoftwares.length > 1) {
            return `software "${node.value.assetSoftwares[0].name}" + ${node.value.assetSoftwares.length - 1}`;
          }
          return `software "${node.value.assetSoftwares[0]?.name}"`;
          break;
        default:
          if (node.value) {
            if (node.value.text != null) {
              return `"${node.value.text}"`;
            }
            return `"${node.value.value}"`;
          }
          return name;
      }
    },
    getOrdinal(n) {
      let s=["th","st","nd","rd"], v=n%100;
      return n+(s[(v-20)%10]||s[v]||s[0]);
    },
    fetchEntities(url, mapKey, ids) {
      const params = {
        "includes[]": ids,
      };
      http
        .get(url, { params: params })
        .then(res => {
          this.dataMap[mapKey] = res.data;
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error gathering field data. Please refresh the page and try again.`);
        });
    },
    getEntityNames(url, mapKey, ids) {
      if (!this.dataMap[mapKey]) {
        this.dataMap[mapKey] = [];
      }
      let diff = _difference(ids, this.dataMap[mapKey].map((e) => e.name));
      if (diff.length) {
        this.fetchEntities(`${url}.json`, mapKey, ids);
        return null;
      }
      return this.dataMap['vendors'].select((e) => ids.includes(e.id)).map((e) => e.name);
    },
    formValue(node) {
      const valueAttrs = Object.keys(node.value);
      if (!valueAttrs || valueAttrs.length === 0) {
        return '';
      } 
      const name = valueAttrs[0];
      switch(name) {
        case "vendors":
          return this.getEntityNames("/vendor_options", "vendors", node.value.vendors);
          break;
        case "locations":
          return this.getEntityNames("/location_options", "locations", node.value.locations);
          return null;
          break;
        case "managed_assets":
          return this.getEntityNames("/managed_asset_options", "managed_assets", node.value.assets);
          return null;
          break;
        case "telecom_services":
          return this.getEntityNames("/telecom_service_options", "telecom_services", node.value.telecoms);
          return null;
          break;
        case "contracts":
          return this.getEntityNames("/contract_options", "contracts", node.value.contracts);
          return null;
          break;
        case "contributors":
          return this.getEntityNames("/contributor_options", "contributors", node.value.contributors);
          return null;
          break;
        case "priority":
          return `"${node.value.priority}" priority`;
          break;
        case "status":
          // code block
          return `status "${node.value.status}"`;
          break;
        default:
          let text = _get(node, `value.${name}`);
          return `"${text}"`;
      }
    },
    listPhrase(value, delimiter = '"') {
      let phrase = "";
      if (value.length === 1) {
        return `${delimiter}${value[0]}${delimiter}`;
      } else if (value.length === 2) {
        return `${delimiter}${value[0]}${delimiter} or ${delimiter}${value[1]}${delimiter}`;
      } else {
        const starts = value.slice(0, value.length -1);
        for (let i = 0; i < starts.length; i++) {
          phrase += `${delimiter}${starts[i]}${delimiter}, `;
        }
        phrase += `or ${delimiter}${value[value.length - 1]}${delimiter}`;
      }
      return phrase;
    },
    eventPhrase(obj) {
      let startIdx = 0;
      let idx = 0;
      let nodes = [];
      let searchString = obj.nodeType.name;
      const closingChars = { "{": "}", "[": "]"};
      let phrase = obj.nodeType.name;

      startIdx = searchString.search(/[\{\[]/);

      // If this is a value node, then just return the value.
      if (startIdx < 0) {
        if (obj.nodeType.type === 'Value' && obj.value) {
          return this.valuePhrase(obj);
        } else if (obj.nodeType.type === 'Type' && obj.nodes.length === 0 && !obj.value) {
          // This is an odd case, but this is meant to short circuit some of the any string
          // type nodes
          return null;
        }
      }

      while (startIdx > -1) {
        const closingChar = closingChars[searchString[startIdx]];
        const closeIdx = searchString.indexOf(closingChar);
        const replaceString = searchString.substring(startIdx, closeIdx + 1);
        const node = obj.nodes[idx];

        let newPhrase = searchString.substring(startIdx + 1, closeIdx)
        if (node && node.nodeType) {
          if (node.nodeType.name) {
            newPhrase = this.eventPhrase(node);
            if (node.nodeType.type === 'Type') {
              if (newPhrase) {
                const subject = `${this.typeName(node.parent.nodeType.name)} which`;
                const slashName = `/${this.slashName(newPhrase)}/`;
                newPhrase = newPhrase.replace(slashName, subject);
              } else {
                return this.removeBraces(obj.nodeType.name);
              }
            }
          } else if (node.value) {
            newPhrase = this.valuePhrase(node);
          }
        }
        phrase = phrase.replace(replaceString, newPhrase);
        searchString = searchString.substring(closeIdx + 1);
        startIdx = searchString.search(/[\{\[]/);
        idx += 1;
      }

      return phrase;
    },
    stripArticle(str) {
      if (!str) {
        return str;
      } else if (str.startsWith("a ")) {
        str = str.substring(2);
      } else if (str.startsWith("an ")) {
        str = str.substring(3);
      }
      return str;
    },
    slashName(name) {
      const startIdx = name.indexOf('/');
      const closeIdx = name.indexOf('/', startIdx + 1);
      return this.stripArticle(name.substring(startIdx + 1, closeIdx));
    },
    subjectName(name) {
      if (!name) {
        return null;
      }
      return this.objectName(name) || this.typeName(name);
    },
    objectName(name) {
      const startIdx = name.indexOf('{');
      const closingChar = '}';
      const closeIdx = name.indexOf(closingChar);
      return this.stripArticle(name.substring(startIdx + 1, closeIdx));
    },
    typeName(name) {
      const startIdx = name.indexOf('[');
      const closingChar = ']';
      const closeIdx = name.indexOf(closingChar);
      return this.stripArticle(name.substring(startIdx + 1, closeIdx));
    },
    readableName(module) {
      if (module === "help_tickets") {
        return "Help Desk";
      } else if (module === "vendors") {
        return "SaaS/Vendor";
      }
      return module.charAt(0).toUpperCase() + module.slice(1);
    },
    replaceNode(selected) {
      let parent = selected.parent;
      let node = parent.nodes.find(node => node.nodeType.key === selected.nodeType.key);
    },
    eventClassStyles(event) {
      switch(event) {
        case 'CommentAdded':
        case 'AttachmentAdded':
        case 'HelpTicketAttachmentAdded':
          return "#ffc107";
        case 'TicketUpdated':
          return "#0d6efd";
        case 'TicketAdded':
          return "#6610f2";
        case 'TaskAdded':
          return "#41b883";
        case 'TimeEntryAdded':
          return "#E14144";
        default:
          return "#fd7e14";
      }
    },
    actionClassStyles(actionType) {
      const actionClass = _get(actionType, 'actionClass');
      switch(actionClass) {
        case 'SendEmail':
          return "#fd7e14";
        case 'AssignPriority':
          return "#41b883";
        case 'SetStatus':
          return "#ffc107";
        case 'AddComment':
          return "#ffc107"; // keep this same color as HelpTicketComment bgColor
        case 'AssignAgent':
          return "#0d6efd"; // keep this same color as HelpTicketAssignment bgColor
        case 'AddAlert':
          return "#E14144";
        default:
          return "#fd7e14";
      }
    },
    isChannelNameRequired(id) {
      return ['SendMsTeamsMessage', 'SendSlackMessage'].includes(this.actionClass(id));
    },
    channelName(id) {
      let name = '';
      if (this.actionClass(id) === 'SendMsTeamsMessage') {
        name = _get(this, `task.actions[${id}].value.msTeamsConfig.channelName`);
      } else if (this.actionClass(id) === 'SendSlackMessage') {
        name = _get(this, `task.actions[${id}].value.slackConfig.channelName`);
      }
      return name ? name : 'to this channel';
    },
    actionClass(id) {
      return _get(this, `task.actions[${id}].nodeType.actionClass`);
    },
    actionEventClass(item) {
      return item.nodeType?.actionClass || item.nodeType?.eventClass;
    },
    iconFromTriggerEventClass(item) {
      const classToIconMap = {
        'TicketAdded' : 'genuicon-ticket',
        'TicketUpdated' : 'genuicon-ticket',
        'TicketRemainsInSameState': 'genuicon-ticket',
        'CommentAdded': 'genuicon-comment-o',
        'TaskAdded': 'genuicon-calendar-o',
        'TaskUpdated': 'genuicon-calendar-o',
        'TimeEntryAdded': 'genuicon-android-time',
        'AttachmentAdded' : 'nulodgicon-file-o',
        'DateOccurred': 'genuicon-calendar-o',
        'SendEmail': 'genuicon-build-email-templates',
        'AddComment': 'genuicon-comment-o',
        'AddAlert': 'genuicon-alerts',
        'SendSms': 'genuicon-mobile',
        'SendMobileNotification': 'genuicon-mobile',
        'SetFormField': 'genuicon-build-custom-forms',
        'HaltExecution': 'nulodgicon-alert-circled',
        'SendSlackMessage': 'genuicon-comment-o',
        'SendMsTeamsMessage': 'genuicon-comment-o',
        'SendNotification': 'genuicon-comment-o',
        'AssetUpdated': 'genuicon-module-assets',
      };
      const actionEventClass = this.actionEventClass(item);
      const mappedIcon = classToIconMap[actionEventClass] ? classToIconMap[actionEventClass] : 'genuicon-ticket';

      return mappedIcon;
    },
    additionalPhrase(node, nodeTypeName) {
      if (nodeTypeName === "{an integration} failed continuously" || nodeTypeName === "{an agent} didn't resync") {
        return ` for ${node.nodes[0].value.numberOfDays} days`;
      } else if (nodeTypeName === "{an asset's} disk space is low") {
        return ` (less than ${node.nodes[0].value.freeSpace} ${node.nodes[0].value.freeSpaceUnit.name})`;
      }
      return "";
    },
  },
};
