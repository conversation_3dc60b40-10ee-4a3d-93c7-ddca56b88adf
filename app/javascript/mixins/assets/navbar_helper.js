import { mapMutations, mapGetters } from 'vuex';
import permissionsHelper from "mixins/permissions_helper";
import mspHelper from 'mixins/msp_helper';
import helpCenterLinks from 'mixins/help_center_links';

export default {
  mixins: [permissionsHelper, mspHelper, helpCenterLinks],
  data() {
    return {
      placeholder: [
        {
          label: 'Main',
          isMainSection: true,
          items: [75, 50],
        },
        {
          label: 'Additional',
          isMainSection: true,
          items: [75, 75, 50],
        },
        {
          label: 'Data',
          items: [25, 50],
        },
        {
          label: 'Settings',
          items: [75, 50],
        },
      ],
    };
  },
  computed: {
    ...mapGetters([
      'discoveredAssetsCount',
      'appsLocationSummary',
      'hasUnseenDiscoveryTool',
    ]),
    outdatedApplicationsCount() {
      const { agentCount, probeCount } = this.appsLocationSummary;
      return agentCount + probeCount;
    },
    isImportPage() {
      return this.$route.path === '/import_assets';
    },
    routePath() {
      return this.$route.path;
    },
    routeName() {
      return this.$route.name;
    },
    navLinks() {
      return [
        {
          label: 'Main',
          blockPermissions: this.isWrite || this.isRead,
          mspBlock: true,
          isMainSection: true,
          items: [
            {
              label: 'Dashboard',
              name: 'dashboard',
              link: '/',
              linkPermissions: this.isWrite || this.isRead,
              mspItem: true,
            },
            {
              label: 'Assets',
              name: 'assets',
              link: '/assets',
              linkPermissions: this.isWrite || this.isRead,
              mspItem: true,
            },
          ],
        },
        {
          label: 'Additional',
          blockPermissions: true,
          mspBlock: false,
          isMainSection: true,
          items: [
            {
              label: 'Discovery Tools',
              name: 'discovery_tools',
              linkPermissions: this.isWrite,
              showOptionalText:
                this.hasUnseenDiscoveryTool && this.outdatedApplicationsCount,
              optionalText: this.outdatedApplicationsCount,
              link: '/discovery_tools/connectors',
              callback: this.onDiscoveryToolsTabClick,
            },
            {
              label: 'Discovered Assets',
              name: 'discovered_assets',
              linkPermissions: this.isWrite,
              optionalText: this.discoveredAssetsCount,
              link: '/discovered_assets/ready_for_import',
            },
            {
              label: 'Reporting',
              name: 'reporting',
              linkPermissions: true,
              link: '/insights',
            },
            ...(this.displayRiskCenterSection ? [{
              label: 'Risk Center',
              name: 'risk_center',
              linkPermissions: true,
              // optionalText: "12",
              link: '/risk_center',
            }] : []),
            {
              label: 'Automation',
              name: 'automated_tasks',
              linkPermissions: true,
              link: '/automated_tasks',
            },
          ],
        },
        {
          label: 'Data',
          mspBlock: false,
          blockPermissions: this.isWrite || this.isReadAny,
          items: [
            {
              label: 'Export Data',
              name: 'export',
              icon: 'nulodgicon-cloud-download',
              linkPermissions: true,
              callback: this.downloadAssetData,
            },
            {
              label: 'Import Data',
              name: 'import',
              icon: 'nulodgicon-cloud-upload',
              linkPermissions: this.isWrite,
              callback: this.navigateToImportAssetsData,
            },
          ],
        },
        {
          label: 'Settings',
          items: [],
        },
      ];
    },
    routesLookUp() {
      return {
        dashboard: this.routeName === 'dashboard',
        assets: this.routePath === '/assets',
        discovery_tools: this.routePath.startsWith('/discovery_tools'),
        discovered_assets: this.routePath.startsWith('/discovered_assets'),
        reporting: ['insights', '/analytics'].includes(this.routePath),
        import: this.routePath === '/import_assets',
      };
    },
    commonActions() {
      return [
        {
          label: 'Common Actions',
          name: 'common_actions',
          links: [
            {
              label: 'Module Permissions',
              name: 'permissions',
              icon: 'genuicon-permissions',
              linkPermissions: this.isCompanyWrite,
              callback: this.goToAssetsPermissions,
            },
            {
              label: 'Genuity Help Center',
              name: 'help_center',
              icon: 'genuicon-info-circled',
              url: this.helpCenterLinks.assets,
              linkType: 'external',
              linkPermissions: true,
            },
          ],
        },
      ];
    },
    settingsMenuOptions() {
      return [
        {
          label: 'General',
          name: 'general',
          links: [
            {
              label: 'QR Codes',
              name: 'qr_code',
              link: '/settings/qr_code',
            },
            {
              label: 'Managed Assets Views',
              name: 'assets_views',
              links: [
                {
                  label: 'Table Data',
                  name: 'table_data',
                  link: '/settings/table_data',
                  parent: 'assets_views',
                },
                {
                  label: 'Card Data',
                  name: 'card_data',
                  link: '/settings/card_data',
                  parent: 'assets_views',
                },
              ],
            },
          ],
        },
        {
          label: 'Discovered Assets',
          name: 'discovered_assets',
          links: [
            {
              label: 'Discovered Assets Views',
              name: 'discovered_assets_views',
              links: [
                {
                  label: 'Table Data',
                  name: 'discovered_asset_table_data',
                  link: '/settings/discovered_asset_table_data',
                  parent: 'discovered_assets_views',
                },
              ],
            },
          ],
        },
        {
          label: 'Asset Data',
          name: 'asset_data',
          links: [
            {
              label: 'Asset Types',
              name: 'asset_types',
              link: '/settings/asset_types',
            },
            {
              label: 'Tag Management',
              name: 'tags',
              link: '/settings/tags',
            },
            {
              label: 'Usage Statuses',
              name: 'statuses',
              link: '/settings/statuses',
            },
            {
              label: 'Lifecycle Management',
              name: 'lifecycle_management',
              link: '/settings/lifecycle_management',
              links: [
                {
                  label: 'Automatic',
                  name: 'automatic',
                  callback: () => this.goToSection('automatic'),
                },
                {
                  label: 'Custom',
                  name: 'custom',
                  callback: () => this.goToSection('custom'),
                },
              ],
            },
          ],
        },
      ];
    },
  },
  updated() {
    this.$store.dispatch('checkIfUnseenDiscoveryLocations');
  },
  methods: {
    ...mapMutations(['setLastSeenDiscoveryToolTab']),
    onDiscoveryToolsTabClick() {
      this.setLastSeenDiscoveryToolTab(new Date());
    },
    goToSection(section) {
      document
        .getElementById(section)
        ?.scrollIntoView({ block: 'start', behavior: 'smooth' });
    },
  },
};
