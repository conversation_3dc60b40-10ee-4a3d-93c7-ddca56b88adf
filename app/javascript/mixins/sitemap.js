export default {
  data() {
    return {
      siteMap: {
        'HelpTickets': [
          {
            match: /\/help_tickets/,
            path: '/help_tickets/dashboard',
            name: 'Help Desk',
            title:'Help Desk Ticketing',
          },
          {
            match: /\/help_tickets\/$/,
            path: '/help_tickets/',
            name: 'Help Tickets',
            title:'Help Desk Ticketing',
          },
          {
            match: /\/help_tickets\/(\d+)/,
            path: '/help_tickets/:id',
            name: 'Ticket Detail',
            title:'Help Desk Ticketing',
          },
          {
            match: /\/help_tickets\/ticket_activities/,
            path: '/help_tickets/ticket_activities',
            name: 'Logs - Ticket Events',
            title:'Help Desk Logs',
          },
          {
            match: /\/help_tickets\/incoming/,
            path: '/help_tickets/incoming',
            name: 'Incoming - Ticket Emails',
            title:'Help Desk Incoming',
          },
          {
            match: /\/help_tickets\/blocked_mails/,
            path: '/help_tickets/blocked_mails',
            name: 'Incoming - Blocked Emails & Domains',
            title:'Help Desk Incoming',
          },
          {
            match: /\/help_tickets\/blocked_keywords/,
            path: '/help_tickets/blocked_keywords',
            name: 'Incoming - Blocked Emails Keywords',
            title:'Help Desk Incoming',
          },
          {
            match: /\/help_tickets\/articles/,
            path: '/help_tickets/articles',
            name: 'Resources - Knowledge Base',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/responses/,
            path: '/help_tickets/responses',
            name: 'Resources - Canned Responses',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/task_checklists/,
            path: '/help_tickets/task_checklists',
            name: 'Resources - Task Checklists',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/documents/,
            path: '/help_tickets/documents',
            name: 'Resources - Documents',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/scheduled_tasks/,
            path: '/help_tickets/scheduled_tasks',
            name: 'Task Scheduler',
            title: 'Task Scheduler',
          },
          {
            match: /\/help_tickets\/faqs/,
            path: '/help_tickets/faqs',
            name: 'Resources - FAQs',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/faqs\/(\d+)$/,
            path: '/help_tickets/faqs/:id',
            name: 'FAQ Detail',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/faqs\/(\d+)\/edit/,
            path: '/help_tickets/faqs/:id/edit',
            name: 'Edit',
            title:'Help Desk Resources',
          },
          {
            match: /\/help_tickets\/settings\/general/,
            path: '/help_tickets/settings/general',
            name: 'Settings - General',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/custom_forms/,
            path: '/help_tickets/settings/custom_forms',
            name: 'Settings - Custom Forms',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/custom_forms\/new/,
            path: '/help_tickets/settings/custom_forms/new',
            name: 'New Custom Forms',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/custom_forms\/(\d+)/,
            path: '/help_tickets/settings/custom_forms/:id/edit',
            name: 'Edit',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/email_notifications/,
            path: '/help_tickets/settings/email_notifications',
            name: 'Settings - Email Notifications',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/valid_email_extensions/,
            path: '/help_tickets/settings/valid_email_extensions',
            name: 'Settings - Email - Allowed File Types',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/connectors/,
            path: '/help_tickets/settings/connectors',
            name: 'Settings - Connectors',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/event_log/,
            path: '/help_tickets/settings/event_log',
            name: 'Logs - Help Desk Module',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/connectors\/slack/,
            path: '/help_tickets/settings/connectors/slack',
            name: 'Slack',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/workspaces/,
            path: '/help_tickets/workspaces',
            name: 'Workspaces',
            title:'Help Desk Workspaces',
          },
          {
            match: /\/help_tickets\/people/,
            path: '/help_tickets/people',
            name: 'People',
            title:'People and Tickets',
          },
          {
            match: /\/help_tickets\/workspaces\/(\d+)/,
            path: '/help_tickets/settings/workspaces/:id',
            name: 'Workspace Detail',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/sla\/policies/,
            path: '/help_tickets/settings/sla/policies',
            name: 'Settings - Sla Policy',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/automated_tasks/,
            path: '/help_tickets/automated_tasks',
            name: 'AutomatedTasks',
            title:'Help Desk Automated Tasks',
          },
          {
            match: /\/help_tickets\/reports\/overview/,
            path: '/help_tickets/reports/overview',
            name: 'Reporting - Overview',
            title:'Help Desk Reporting',
          },
          {
            match: /\/help_tickets\/reports\/analytics/,
            path: '/help_tickets/reports/analytics',
            name: 'Reporting - Analytics',
            title:'Help Desk Reporting',
          },
          {
            match: /\/help_tickets\/reports\/saved/,
            path: '/help_tickets/reports/saved',
            name: 'Reporting - Saved Reports',
            title:'Help Desk Reporting',
          },
          {
            match: /\/help_tickets\/reports\/scheduled/,
            path: '/help_tickets/reports/scheduled',
            name: 'Reporting - Scheduled Reports',
            title:'Help Desk Reporting',
          },
          {
            match: /\/help_tickets\/settings\/help_center/,
            path: '/help_tickets/settings/help_center',
            name: 'Settings - Help Center',
            title:'Help Desk Settings',
          },
          {
            match: /\/help_tickets\/settings\/help_center\/display/,
            path: '/help_tickets/settings/help_center/display',
            name: 'Display',
            title:'Help Desk Settings',
          },
        ],
        'Assets': [
          {
            match: /\/managed_assets/,
            path: '/managed_assets',
            name: 'Assets',
            title:'Asset Management',
          },
          {
            match: /\/managed_assets\/assets/,
            path: '/managed_assets/assets',
            name: 'All',
            title:'Asset Management',
          },
          {
            match: /\/managed_assets\/people_assets\/assigned/,
            path: '/managed_assets/people_assets/assigned',
            name: 'People - Assigned',
            title:'Asset Management',
          },
          {
            match: /\/managed_assets\/people_assets\/unassigned/,
            path: '/managed_assets/people_assets/unassigned',
            name: 'People - Unassigned',
            title:'Asset Management',
          },
          {
            match: /\/managed_assets\/people_assets\/archived/,
            path: '/managed_assets/people_assets/archived',
            name: 'People - Archived',
            title:'Asset Management',
          },
          {
            match: /\/managed_assets\/discovery_tools\/connectors/,
            path: '/managed_assets/discovery_tools/connectors',
            name: 'Discovery Tools - Connectors',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovery_tools\/probes/,
            path: '/managed_assets/discovery_tools/probes',
            name: 'Discovery Tools - Probes',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovery_tools\/logs/,
            path: '/managed_assets/discovery_tools/logs',
            name: 'Discovery Tools - Logs',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovery_tools\/agents/,
            path: '/managed_assets/discovery_tools/agents',
            name: 'Discovery Tools - Agents',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovered_assets\/ready_for_import/,
            path: '/managed_assets/discovered_assets/ready_for_import',
            name: 'Discovery Assets - Ready for Import',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovered_assets\/incomplete/,
            path: '/managed_assets/discovered_assets/incomplete',
            name: 'Discovery Assets - Incomplete',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovered_assets\/unrecognized/,
            path: '/managed_assets/discovered_assets/unrecognized',
            name: 'Discovery Assets - Unrecognized',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovered_assets\/ignored/,
            path: '/managed_assets/discovered_assets/ignored',
            name: 'Discovery Assets - Ignored',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovered_assets\/imported/,
            path: '/managed_assets/discovered_assets/imported',
            name: 'Discovery Assets - Imported',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/discovered_assets\/logs/,
            path: '/managed_assets/discovered_assets/logs',
            name: 'Discovery Assets - Logs',
            title:'Asset Discovery',
          },
          {
            match: /\/managed_assets\/analytics/,
            path: '/managed_assets/analytics',
            name: 'Analytics',
            title:'Asset Reporting',
          },
          {
            match: /\/managed_assets\/insights/,
            path: '/managed_assets/insights',
            name: 'Insights',
            title:'Asset Reporting',
          },
          {
            match: /\/managed_assets\/risk_center/,
            path: '/managed_assets/risk_center',
            name: 'Risk Center',
            title: 'Asset Risk Center',
          },
          {
            match: /\/managed_assets\/automated_tasks/,
            path: '/managed_assets/risk_center',
            name: 'Automation',
            title: 'Asset Automation',
          },
          {
            match: /\/managed_assets\/settings\/asset_types/,
            path: '/managed_assets/settings/asset_types',
            name: 'Settings - Asset Types',
            title:'Asset Settings',
          },
          {
            match: /\/managed_assets\/settings\/table_data/,
            path: '/managed_assets/settings/table_data',
            name: 'Settings - Table Data',
            title:'Asset Settings',
          },
          {
            match: /\/managed_assets\/settings\/card_data/,
            path: '/managed_assets/settings/card_data',
            name: 'Settings - Card Data',
            title:'Asset Settings',
          },
          {
            match: /\/managed_assets\/settings\/qr_code/,
            path: '/managed_assets/settings/qr_code',
            name: 'Settings - Qr Code',
            title:'Asset Settings',
          },
          {
            match: /\/managed_assets\/settings\/statuses/,
            path: '/managed_assets/settings/statuses',
            name: 'Settings - Statuses',
            title:'Asset Settings',
          },
          {
            match: /\/managed_assets\/settings\/tags/,
            path: '/managed_assets/settings/tags',
            name: 'Settings - Tags',
            title:'Asset Settings',
          },
          {
            match: /\/managed_assets\/settings\/lifecycle_management/,
            path: '/managed_assets/settings/lifecycle_management',
            name: 'Settings - Lifecycle Management',
            title:'Asset Settings',
          },
          {
            match: /\/discovered_assets\/settings\/discovered_asset_table_data/,
            path: '/discovered_asset_table_data',
            name: 'Settings - Table Data',
            title:'Discovered Asset Settings',
          },
        ],
      },
    };
  },
};
