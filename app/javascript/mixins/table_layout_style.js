/**
 * Table Layout Style customizations for different module tables
 * 
 *  1) Define the customizations for a new style in allTableHeaderSettings below 
 *  2) Import tableLayoutStyle from 'mixins/table_layout_style.js';
 *  3) Assign a component data attribute `listTableLayoutModuleStyle: "managed_assets"` to the desired style.
 *     This applies the proper settings when calling helper methods like `tableMinWidth()` or `allHeaderClasses()`
 *  4) Use the helper methods from this mixin to set your table classes and other display related data.
 *  
 *  Avoid duplicating any of the classname helper methods below in other components.
 */

export default {
  data() {
    return {
      listTableLayoutModuleStyle: "default",
      allTableHeaderSettings: {
        "default": {
          longTitleCharacterThreshold: 12,
          averageColWidth: 10,
          shortColumns: [],
          mediumColumns: [],
          longColumns: [],
          veryLongColumns: [],
          stickyColumnNames: [],
          nonSortableColumns: [],
          isSortable: true,
          isResizable: true,
          isDraggable: false,
          nonDraggableColumnNames: [],
          headerTitleDisplayMap: {},
        },
        "peopleInHd":{
          isSortable: true,
          isResizable: false,
          isDraggable: false,
          longColumns: ['People', "Open Ticket", "Last 30 Days", "Last 60 Days", "Last 90 Days", "Full History"],
        },
        "help_tickets": {
          shortColumns: ['Ticket Number'],
          mediumColumns: ['Comment Count', 'Attachments Count', 'Priority'],
          longColumns: ['Assigned To', 'Created By'],
          veryLongColumns: ['peopleList', 'Subject'],
          stickyColumnNames: ['Ticket Number'],
          isDraggable: true,
          nonDraggableColumnNames: ['Ticket Number'],
          headerTitleDisplayMap: {
            "Ticket Number": "#",
            "Comment Count": "Comments",
            "Attachments Count": "Attachments",
          },
        },
        "help_tickets_reports": {
          shortColumns: [],
          mediumColumns: ['Ticket Number', 'Comment Count', 'Attachments Count', 'Priority'],
          longColumns: ['Assigned To', 'Created By'],
          veryLongColumns: ['peopleList', 'Subject'],
          stickyColumnNames: ['Ticket Number'],
          isDraggable: false,
          isResizable: false,
          headerTitleDisplayMap: {
            "Ticket Number": "Ticket #",
            "Comment Count": "Comments",
            "Attachments Count": "Attachments",
          },
        },
        "help_tickets_automated_tasks": {
          shortColumns: ['#'],
          mediumColumns: ['Name'],
          longColumns: ['When this happens...', 'Actions'],
          veryLongColumns: [],
          stickyColumnNames: [],
          isDraggable: false,
          isResizable: false,
          isSortable: false,
          headerTitleDisplayMap: {
            "Actions": " ",
          },
        },
        "workspaces": {
          averageColWidth: 8,
          longColumns: ['Name'],
          isSortable: false,
          isResizable: false,
        },
        "managed_assets": {
          averageColWidth: 9,
          mediumColumns: ['Type', 'Source'],
          longColumns: ['Machine Serial Number', 'Hardware Detail type', 'Mac Addresses', 'Ip Address', 'Operating System', 'Acquisition Date', 'Warranty'],
          stickyColumnNames: ['Type', 'Asset Name'],
          nonDraggableColumnNames: ['Type', 'Asset Name'],
          nonSortableColumns: ['Source', 'Tags', 'Cost'],
          isDraggable: true,
        },
        "people_assets": {
          averageColWidth: 9,
          shortColumns: ['Location', 'Source'],
          mediumColumns: ['Tags'],
          longColumns: ['Used By', 'Managed By'],
          veryLongColumns: ['Device'],
          stickyColumnNames: ['Device'],
          nonDraggableColumnNames: ['Device'],
          nonSortableColumns: ['Source', 'Tags'],
          isResizable: false,
        },
        "managed_assets_insights": {
          averageColWidth: 9,
          shortColumns: ['Type'],
          mediumColumns: ['Source'],
          longColumns: ['Machine Serial Number', 'Hardware Detail type', 'Mac Addresses', 'Ip Address', 'Operating System', 'Acquisition Date', 'Warranty'],
          isSortable: false,
          isResizable: false,
        },
        "contracts": {
          averageColWidth: 8,
          longColumns: ['Vendor Name', 'Next Notification Date'],
          veryLongColumns: ['Name'],
          stickyColumnNames: ['Name'],
        },
        "telecom_services": {
          averageColWidth: 11,
          mediumColumns: ['Monthly Cost'],
          longColumns: ['Provider', 'Service Name'],
          stickyColumnNames: ['Provider'],
        },
        "telecom_dashboard": {
          averageColWidth: 10,
          mediumColumns: ['Monthly Cost', 'Total Services'],
          longColumns: ['Name'],
          stickyColumnNames: ['Name'],
        },
        "phone_ip_addresses": {
          averageColWidth: 8,
          shortColumns: ['Notes'],
          isSortable: false,
          isResizable: false,
        },
        "child_company_permissions": {
          averageColWidth: 8,
          longColumns: ['Person/Group', 'Permissions'],
          shortColumns: ['Revoke'],
          isSortable: false,
          isResizable: false,
        },
        "vendors": {
          averageColWidth: 8,
          longColumns: ['Vendor Name'],
          stickyColumnNames: ['Vendor Name'],
        },
        "recent_builds": {
          averageColWidth: 10,
          longColumns: ['Applied Companies'],
          mediumColumns: ['Build By', 'Build & Version'],
          isSortable: false,
          isResizable: false,
        },
        "survey_responses": {
          averageColWidth: 10,
          shortColumns: ['Status', 'Score', 'Comments', 'Actions'],
          mediumColumns: ['Name', 'Last Action Date', 'Respondent', 'Linked Ticket'],
          nonSortableColumns: ['Actions', 'Linked Ticket'],
          isSortable: true,
          isResizable: false,
        },
      },
    };
  },
  computed: {
    tableHeaderSettings() {
      const computedHeaderSettings = { ...this.allTableHeaderSettings.default, ...this.allTableHeaderSettings[this.listTableLayoutModuleStyle]};
      return computedHeaderSettings;
    },
  },
  methods: {
    allHeaderClasses(header) {
      return {
        'header-min-width--short': this.shortTitleMinWidth(header),
        'header-min-width--medium': this.mediumTitleMinWidth(header),
        'header-min-width--long': this.longTitleMinWidth(header),
        'header-min-width--very-long': this.veryLongTitleMinWidth(header),
        'header-data--sticky': this.isSticky(header),
        'draggable' : this.isDraggable(header),
        'resizable' : this.isResizable(),
      };
    },
    shortTitleMinWidth(title) {
      return this.tableHeaderSettings.shortColumns.includes(title);
    },
    mediumTitleMinWidth(title) {
      return this.tableHeaderSettings.mediumColumns.includes(title);
    },
    longTitleMinWidth(title) {
      return this.tableHeaderSettings.longColumns.includes(title) ||
      title.length > this.tableHeaderSettings.longTitleCharacterThreshold &&
      !this.shortTitleMinWidth(title) &&
      !this.mediumTitleMinWidth(title);
    },
    veryLongTitleMinWidth(title) {
      return this.tableHeaderSettings.veryLongColumns.includes(title);
    },
    isSticky(header) {
      if (this.$route.name !== 'insights') {
        return this.tableHeaderSettings.stickyColumnNames.includes(header);
      }
      return false;
    },
    isSortable(title) {
      return this.tableHeaderSettings.isSortable && !this.tableHeaderSettings.nonSortableColumns.includes(title);
    },
    isDraggable(title) {
      return this.tableHeaderSettings.isDraggable && !this.tableHeaderSettings.nonDraggableColumnNames.includes(title);
    },
    firstDraggableIndex() {
      return this.tableHeaderSettings.nonDraggableColumnNames.length;
    },
    isResizable() {
      return this.tableHeaderSettings.isResizable;
    },
    headerTitleDisplay(title) {
      if (this.tableHeaderSettings.headerTitleDisplayMap) {
        return this.tableHeaderSettings.headerTitleDisplayMap[title] ?
          this.tableHeaderSettings.headerTitleDisplayMap[title] :
          title;
      }
      return null;
    },
    tableMinWidth(numColumns = 5) {
      const minWidth = numColumns * this.tableHeaderSettings.averageColWidth;
      return `${minWidth}rem`;
    },
  },
};
