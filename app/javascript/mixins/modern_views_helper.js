export default {
  methods: {
    applyDefaultVerticalNav() {
      const hasNavTheme = document.cookie.split('; ').find(row => row.startsWith('nav-theme='));

      if (!hasNavTheme && this.shouldApplyModernFeatures()) {
        this.setCookie('nav-theme', 'vertical', true);
      }
    },
    setCookie(name, val, useRootDomain) {
      const shouldSetRootDomainWithSubdomains = useRootDomain && this.$rootDomain !== "localhost";
      const rootDomain = shouldSetRootDomainWithSubdomains ? `; domain=.${this.$rootDomain}` : '';

      const expirationDate = new Date();
      expirationDate.setFullYear(expirationDate.getFullYear() + 1);

      const toSet = `${name}=${val}${rootDomain}; expires=${expirationDate.toUTCString()}; path=/`;
      document.cookie = toSet;
    },
    shouldApplyModernFeatures() {
      const userCreationDate = moment(this.$currentUserCreatedAT).startOf('day');
      // Cutoff date to default new companies to modern view and vertical nav
      const cutoffDate = moment('2025-07-02').startOf('day');
      const isSampleCompany = window.$company.subdomain === 'sample';

      return userCreationDate.isAfter(cutoffDate) || isSampleCompany;
    },
  },
};
