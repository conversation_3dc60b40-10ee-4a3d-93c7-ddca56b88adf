ActiveAdmin.register AiUsageCost, as: "Companies AI Usage Cost" do
  menu parent: 'Companies', priority: 8, label: "AI Usage Cost"
  breadcrumb do
    ['admin', 'companies']
  end

  actions :index, :show
  config.batch_actions = false

  filter :company, as: :select, collection: Company.order("name ASC").map { |comp| [comp.name, comp.id] }
  filter :ai_type, as: :select, collection: ['summarizer', 'edit-text']
  filter :model_type, as: :select, collection: ['gpt-4.1', 'gpt-4.1-mini', 'gpt-4.1-nano']
  filter :month, as: :select, collection: AiUsageCost.distinct.pluck(:month).sort

  index do
    selectable_column
    id_column
    column :company
    column :ai_type
    column :model_type
    column :month
    column("Total Cost") { |agg| number_to_currency(total_cost(agg.usage_by_day)) }
    actions
  end

  show do
    attributes_table do
      row :company
      row :ai_type
      row :model_type
      row :month
      row("Total Cost") { number_to_currency(total_cost(resource.usage_by_day)) }
    end

    panel "Daily Breakdown" do
      usage_data = resource.usage_by_day
      month_dates = usage_data.keys
      table_for month_dates do
        column "Date" do |date| date end
        column "Prompt Tokens" do |date|
          usage_data[date]["prompt_token"]
        end
        column "Completion Tokens" do |date| usage_data[date]["completion_token"] end
        column "Cache Tokens" do |date| usage_data[date]["cache_token"] end
        column "Cost" do |date| number_to_currency(usage_data[date]["cost"]) end
      end
    end
  end

  controller do
    def total_cost(usage_by_day)
      usage_by_day&.values&.sum { |v| v["cost"].to_f } || 0.0
    end
    helper_method :total_cost
  end
end
