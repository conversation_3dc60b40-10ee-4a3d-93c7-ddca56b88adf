/*
 * Global Vue Options:
 * Accessible throughout the Vue app w/ `this.$relevantVariable`
 * Can be accessed outside of Vue files if needed w/ `Vue.prototype.$relevantVariable`
 */

/* All Sessions */
javascript:
  Vue.prototype.$mixpanelProjectKey = "#{Rails.application.credentials.mixpanel[:key]}";
  Vue.prototype.$sessionId = "#{request.session_options[:id]}";
  Vue.prototype.$sampleCompanyId = parseInt("#{Company.find_by_cache(is_sample_company: true)&.id || 0}");
  Vue.prototype.$timezone = "#{(current_user&.timezone unless current_user&.timezone == "UTC") || current_company&.timezone || "America/Chicago"}";
  Vue.prototype.$runCaptcha = #{Rails.application.credentials.run_captcha};
  Vue.prototype.$recaptchaSiteKey = "#{Rails.application.credentials.recaptcha[:site_key]}";
  Vue.prototype.$nodeEnv = "#{Rails.application.credentials.node_env}";
  Vue.prototype.$cognitoBaseUri = "#{Rails.application.credentials.aws[:cognito][:base_uri]}";
  Vue.prototype.$cognitoRedirectUri = "#{Rails.application.credentials.aws[:cognito][:redirect_uri]}";
  Vue.prototype.$cognitoClientId = "#{Rails.application.credentials.aws[:cognito][:client_id]}";
  Vue.prototype.$rootDomain = "#{Rails.application.credentials.root_domain}";
  Vue.prototype.$baseModule = "#{yield :base_module}";
  Vue.prototype.$siteTheme = "#{cookies['site_theme']}";

- if current_company.blank?
  / Authenticated, no company selected
  javascript:
    Vue.prototype.$allowSampleCompany = false;
    Vue.prototype.$isSampleCompany = false;
    Vue.prototype.$isDefaultCompany = false;
- else
  /* Authenticated, company selected */
  javascript:
    Vue.prototype.$currentCompanyGuid = "#{current_company.guid}";
    Vue.prototype.$currentCompanyId = "#{current_company.id}";
    Vue.prototype.$currentCompanySubdomain = "#{current_company.subdomain}";
    Vue.prototype.$currentCompanyCreatedAt = "#{current_company.created_at}";
    Vue.prototype.$isSampleCompany = #{!!current_company.is_sample_company};
    Vue.prototype.$companyName = "#{{current_company.name}}";
    Vue.prototype.$companyLogoUrl = "#{current_company.logo_or_default_logo.html_safe}";
    Vue.prototype.$companyChatSupport = "#{current_company.chat_support_enabled}";
    Vue.prototype.$isLegacyCompany = (#{current_company.is_legacy_company && !current_company.show_new_plans});
    Vue.prototype.$isReseller = #{current_company.is_reseller_company};
    Vue.prototype.$canSeeManagementView = #{can_see_management_view};
    Vue.prototype.$showBannerRegardingPlanUpgrade = #{show_banner_regarding_plan_upgrade};
    Vue.prototype.$isChildCompany = #{current_company.reseller_company.present?};
    Vue.prototype.$hotGlueFlowId = "#{Rails.application.credentials.hotglue[:flow_id]}";
    Vue.prototype.$canAccessHelpCenter = (#{can_access_help_center});
    window.$defaultCompany = { id: #{current_company.id}, name: "#{current_company.name}" };

- if current_user.blank?
  /* Unauthenticated sessions */
  javascript:
    Vue.prototype.$allowSampleCompany = false;
    Vue.prototype.$isSampleCompany = false;
    Vue.prototype.$isDefaultCompany = false;

- else
  /* authenticated sessions */
  javascript:
    Vue.prototype.$currentUserFirstName = "#{current_user.first_name}";
    Vue.prototype.$currentUserLastName = "#{current_user.last_name}";
    Vue.prototype.$currentUserEmail = "#{current_user.email.downcase}";
    Vue.prototype.$currentUserEmailHashed = "#{Digest::SHA1.hexdigest(current_user.email.downcase)}";
    Vue.prototype.$currentUserGuid = "#{current_user.guid}";
    Vue.prototype.$currentUserId = "#{current_user.id}";
    Vue.prototype.$superAdminUser = #{!!current_user.super_admin};
    Vue.prototype.$currentUserCreatedAT = "#{current_user.created_at}";
    Vue.prototype.$hasUnconfirmedEmail = #{!current_user.has_confirmed_email};
    Vue.prototype.$hasSeenModuleWalkthrough = #{{current_user.has_seen_module_walkthroughs.to_json}};

  - if current_user.admin?(current_company) || current_company&.subdomain == "sample"
    /* Admin user, company selected */
    - if current_user.super_admin?
      javascript:
        Vue.prototype.$defaultCompanyId = #{current_user.companies.not_sample.first&.id || current_company.id};
    - else
      javascript:
        Vue.prototype.$defaultCompanyId = #{current_user.company_users.access_granted.includes(:company).order(:created_at).select { |cu| cu.company.is_sample_company == false }.first&.company&.id || current_company.id};
    javascript:
      Vue.prototype.$sampleCompanyId = parseInt("#{Company.find_by_cache(is_sample_company: true)&.id || 0}");
      Vue.prototype.$allowSampleCompany = !!Vue.prototype.$sampleCompanyId; /* Only set to true if the sample company id is set and company present */
      Vue.prototype.$hasSeenSampleOnboarding = #{!!current_user.has_seen_sample_onboarding};
      Vue.prototype.$isDefaultCompany = Vue.prototype.$currentCompanyId == Vue.prototype.$defaultCompanyId;
    
  - else
    /* Non-admin user */
    javascript:
      /* Non Admin users don't have access to the sample company, */
      /* So we want to ignore any onboarding as it relies on sample company access */
      Vue.prototype.$sampleCompanyId = null;
      Vue.prototype.$allowSampleCompany = false;
      Vue.prototype.$isSampleCompany = false;
      // TODO: SHOULD IS_DEFAULT_COMPANY BE FALSE?
      Vue.prototype.$hasSeenSampleOnboarding = true;

- if current_company_user.present?
  javascript:
    Vue.prototype.$currentCompanyUserId = #{current_company_user.id};
    Vue.prototype.$currentCompanyUserGuid = "#{current_company_user.guid}";
    Vue.prototype.$currentContributorId = #{current_company_user.contributor_id || "null"};
    Vue.prototype.$currentContributorLocationIds = #{current_company_user.location_ids};
    Vue.prototype.$currentContributorName = "#{current_company_user.name}";
    Vue.prototype.$currentCompanyAdminUser = #{!!current_company_user&.is_admin?};
    Vue.prototype.$freeTrialEndedAndUnsubscribed = (#{!current_company.allow_access? && !current_user.super_admin?});
    Vue.prototype.$hasOnlyInsolventSubscription = (#{has_only_insolvent_subscription});
    Vue.prototype.$hasInsolventSubscription = (#{has_insolvent_subscription});
    Vue.prototype.$hasFreeTrial = (#{has_free_trial});
    Vue.prototype.$showFreeModules = (#{current_company.show_free_modules});
    Vue.prototype.$activeLegacyCompany = (#{is_active_legacy_company?})


/* Custom Global Methods */
javascript:
  Vue.mixin({
    methods: {
      showDefaultCompany(params) {
        if (this.$defaultCompanyId && !this.$isDefaultCompany) {
          const l = window.location;
          let path = l.pathname;
          if (params) {
            path += encodeURIComponent(`?${params}`);
          }
          const windowLocation = `${l.protocol}//${l.host}/user_accesses/new?company_id=${this.$defaultCompanyId}&redirect_route=${path}`;
          this.trackSampleToggle(windowLocation);
          window.location = windowLocation;
        }
      },
      showSampleCompany(params) {
        if (this.$sampleCompanyId && !this.$isSampleCompany) {
          const l = window.location;
          const windowLocation = `${l.protocol}//${l.host}/user_accesses/new?company_id=${this.$sampleCompanyId}`;
          this.trackSampleToggle(windowLocation);
          window.location = windowLocation;
        }
      },
      hideSampleCompany(params) {
        if (this.$defaultCompanyId && this.$isSampleCompany) {
          const l = window.location;
          const windowLocation = `${l.protocol}//${l.host}/user_accesses/new?company_id=${this.$defaultCompanyId}`
          this.trackSampleToggle(windowLocation);
          window.location = windowLocation;
        }
      },
      toggleSampleCompany(params) {
        if (this.$defaultCompanyId && this.$sampleCompanyId) {
          if (this.$isSampleCompany) {
            this.hideSampleCompany(params);
          } else {
            this.showSampleCompany(params);
          }
        }
      },
      trackSampleToggle(windowLocation) {
        mixpanel.track('Toggle Sample Company', {
          'module': 'sample_company',
          'window_location': windowLocation
        });
      },
    },
  });

