/**
 * COLORS
 *
 * Colors with a name, like `blue` or `maastricht blue`,
 * should be referenced by their color name. 
 *
 * Colors with a generic or themed role, like `caution` or `telecom`
 * should have the word `color` prepended to the variable.
 * e.g. `$color-caution`, or `$color-telecom`.
 *
 * Unless there's a good reason for doing so, we should
 * avoid adding any new color variables to the site.
 * If you *really* need a different shade or a certain color,
 * consider using a sass mixin to accomplish it. E.g.
 * `background-color: shift-color($success, -20%)`
 */


//
// Color Variables
//

$base-colors: (
  'white':    rgb(255, 255, 255), // #ffffff
  'gray-50':  rgb(251, 251, 252), // #fbfbfc
  'gray-75':  rgb(249, 251, 253), // #f9fbfd
  'gray-100': rgb(248, 249, 250), // #f8f9fa
  'gray-200': rgb(235, 238, 241), // #ebeef1
  'gray-300': rgb(222, 226, 230), // #dee2e6
  'gray-400': rgb(206, 212, 218), // #ced4da
  'gray-500': rgb(158, 169, 178), // #9ea9b2
  'gray-600': rgb(108, 117, 125), // #6c757d
  'gray-700': rgb(73, 80, 87),    // #495057
  'gray-800': rgb(52, 58, 64),    // #343a40
  'gray-900': rgb(33, 37, 41),    // #212529
  'black':    rgb(18, 20, 23),    // #121417

  'blue':     rgb(13, 110, 253),  // #0d6efd
  'indigo':   rgb(102, 16, 242),  // #6610f2
  'purple':   rgb(111, 66, 193),  // #6f42c1
  'pink':     rgb(214, 51, 132),  // #d63384
  'red':      rgb(220, 53, 69),   // #dc3545
  'orange':   rgb(253, 126, 20),  // #fd7e14
  'yellow':   rgb(255, 193, 7),   // #ffc107
  'green':    rgb(25, 135, 84),   // #198754
  'teal':     rgb(32, 201, 151),  // #20c997
  'cyan':     rgb(13, 202, 240),  // #0dcaf0


  'maastricht-blue': rgb(4, 30, 66),     // #041e42
  'dark-blue':       rgb(28, 45, 69),    // #1C2D45
  'biscay-blue':     rgb(23, 53, 96),    // #173560
  'blue-oblivion':   rgb(40, 70, 143),   // #28468f
  'azure-blue':      rgb(38, 168, 255),  // #26a8ff
  'de-france-blue':  rgb(48, 138, 217),  // #308ad9
  'iceberg-blue':    rgb(230, 238, 245), // #e6eef5
  'titan-white':     rgb(249, 251, 253), // #f9fbfd;

  // High contrast dark theme
  'dark-high-25':  rgb(223, 234, 242), // #DFEAF2
  'dark-high-50':  rgb(211, 222, 231), // #D3DEE7
  'dark-high-75':  rgb(160, 172, 185), // #A0ACB9
  'dark-high-100': rgb(137, 144, 156), // #89909C
  'dark-high-200': rgb(108, 117, 125), // #6C757D (placeholder text)
  'dark-high-300': rgb(86, 97, 116),   // #566174 (filters)
  'dark-high-400': rgb(68, 78, 96),    // #444E60
  'dark-high-500': rgb(48, 67, 92),    // #30435C (boxes)
  'dark-high-600': rgb(39, 56, 79),    // #27384F (light)
  'dark-high-700': rgb(28, 45, 69),    // #1C2D45 
  'dark-high-800': rgb(2, 13, 28),     // #020D1C (theme-bg)
  'dark-high-900': rgb(3, 17, 37),     // #031125 (left module menu)
  // End high contrast dark theme

  // Blue gray lower contrast theme
  'blue-gray-25':  rgb(223, 234, 242), // #DFEAF2
  'blue-gray-50':  rgb(211, 222, 231), // #D3DEE7
  'blue-gray-75':  rgb(160, 172, 185), // #A0ACB9
  'blue-gray-100': rgb(137, 144, 156), // #89909C
  'blue-gray-200': rgb(108, 117, 125), // #6C757D (placeholder text)
  'blue-gray-300': rgb(86, 97, 116),   // #566174 (filters)
  'blue-gray-400': rgb(68, 78, 96),    // #444E60
  'blue-gray-500': rgb(52, 66, 88),    // #344258 (boxes)
  'blue-gray-600': rgb(39, 56, 79),    // #27384F (search bg) 
  'blue-gray-700': rgb(28, 45, 69),    // #1C2D45 (theme-bg)
  'blue-gray-800': rgb(10, 29, 64),    // #0A1D40 
  'blue-gray-900': rgb(3, 17, 37),     // #031125 (left module menu)
  // End blue gray lower contrast theme
);

/* 
 * Genuity Branded Colors 
 *
 * These colors are used rarely outside of the marketing platform,
 * but are found across the site to convey a sense of our brand 
 */
$maastricht-blue: map-get($base-colors, 'maastricht-blue'); // Genuity typeface
$biscay-blue:     map-get($base-colors, 'biscay-blue');     // Genuity typeface, alt
$blue-oblivion:   map-get($base-colors, 'blue-oblivion');   // Flame background
$de-france-blue:  map-get($base-colors, 'de-france-blue');  // Outer flame
$azure-blue:      map-get($base-colors, 'azure-blue');      // Inner flame
$iceberg-blue:    map-get($base-colors, 'iceberg-blue');    // Light typeface
$titan-white:     map-get($base-colors, 'titan-white');    // Light typeface alt

/* 
 * Module 'Branded' colors 
 *
 * Like the the genuity brand colors, these are used infrequently,
 * but are found in key places in each module to help user passively
 * identify which module they are working in.
 */
$color-asset:       #9575CD;
$color-contract:    #00B0FF;
$color-vendor:      #2db273;
$color-telecom:     #27b4a6;
$color-helpdesk:    #EF5350;
$color-network:     #f07059;
$color-assets:      $color-asset;
$color-contracts:   $color-contract;
$color-vendors:     $color-vendor;
$color-telecoms:    $color-telecom;
$color-networks:    $color-network;
$color-home:        $azure-blue;
$color-marketplace: $color-home;

$color-assets-light:    mix(white, $color-asset, 85%);
$color-contracts-light: mix(white, $color-contract, 85%);
$color-vendors-light:   mix(white, $color-vendor, 85%);
$color-telecom-light:   mix(white, $color-telecom, 85%);
$color-helpdesk-light:  mix(white, $color-helpdesk, 85%);
$color-network-light:   mix(white, $color-network, 85%);

$color-assets-dark:    mix(black, $color-asset, 35%);
$color-contracts-dark: mix(black, $color-contract, 35%);
$color-vendors-dark:   mix(black, $color-vendor, 35%);
$color-telecom-dark:   mix(black, $color-telecom, 35%);
$color-helpdesk-dark:  mix(black, $color-helpdesk, 35%);
$color-network-dark:   mix(black, $color-network, 35%);


/* 
 * Base Colors 
 *
 * This is the basic 'color wheel' of the site.
 * Each color has a standard and light option.
 * Some of these are simple repeats of the bootstrap defaults,
 * but have been repeated here for reference.
 */
$purple: $purple;
$indigo: $indigo;
$blue:   $blue;
$cyan:   #17a2b8;
$teal:   #23c68c;
$green:  #198754;
$yellow: #ffb648;
$orange: $orange;
$red:    #e14144;
$pink:   $pink;

/* 'Light' Base Colors */
$purple-light: tint-color($purple, 40%);
$indigo-light: tint-color($indigo, 40%);
$blue-light:   tint-color($blue, 40%);
$cyan-light:   tint-color($cyan, 40%);
$teal-light:   tint-color($teal, 40%);
$green-light:  tint-color($green, 40%);
$yellow-light: tint-color($yellow, 40%);
$orange-light: tint-color($orange, 40%);
$red-light:    tint-color($red, 40%);
$pink-light:   tint-color($pink, 40%);

/* 'Dark' Base Colors */
$purple-dark: shade-color($purple, 20%);
$indigo-dark: shade-color($indigo, 20%);
$blue-dark:   shade-color($blue, 20%);
$cyan-dark:   shade-color($cyan, 20%);
$teal-dark:   shade-color($teal, 20%);
$green-dark:  shade-color($green, 20%);
$yellow-dark: shade-color($yellow, 20%);
$orange-dark: shade-color($orange, 20%);
$red-dark:    shade-color($red, 20%);
$pink-dark:   shade-color($pink, 20%);

/* 'Subtle' Base Colors (mostly used as subtle backgrounds in pills and alerts) */
$purple-subtle: tint-color($purple, 85%);
$indigo-subtle: tint-color($indigo, 85%);
$blue-subtle:   tint-color($blue, 85%);
$cyan-subtle:   tint-color($cyan, 85%);
$teal-subtle:   tint-color($teal, 85%);
$green-subtle:  tint-color($green, 85%);
$yellow-subtle: tint-color($yellow, 85%);
$orange-subtle: tint-color($orange, 85%);
$red-subtle:    tint-color($red, 85%);
$pink-subtle:   tint-color($pink, 85%);

/* Additional Basic Colors */
// Colors with known or intuitive names
// Add to these sparingly
$red-orange:    #f44336;
$royal-blue:    #004bc3;
$faded-blue:    #cce5ff;
$blue-gray:     #bec2d8;
$purple-gray:   #f2f3f8;
$true-black:    #000000;
$active-metric: #5b6168;
$black:         map-get($base-colors, 'black');

/*
 * Pastels
 *
 * These are a unique set of colors that build off the base color wheel
 * and are meant to be used exclusively in graphical content,
 * like charts, graphs, or visualizations, and occasionally for highlights.
 * I.e. These colors are not meant for buttons, text, or notifications.
 *
 * We use a simple numbering system to move clockwise through the pastel 'wheel'.
 * So blue-100 is slightly more indigo, and blue-200 more cyan.
 */

$pastel-purple-100: #614b9b;
$pastel-purple-200: #8871c3;
$pastel-indigo-100: #878ecf;
$pastel-indigo-200: #6477ba;
$pastel-blue-100:   #51709f;
$pastel-blue-200:   #467d9b;
$pastel-cyan-100:   #5097be;
$pastel-cyan-200:   #70b4ca;
$pastel-teal-100:   #8abdc0;
$pastel-teal-200:   #70aea3;
$pastel-green-100:  #6ebd8e;
$pastel-green-200:  #a2ca7f;
$pastel-yellow-100: #d5d770;
$pastel-yellow-200: #dbbf5c;
$pastel-orange-100: #dc9f45;
$pastel-orange-200: #c97324;
$pastel-red-100:    #bf4c27;
$pastel-red-200:    #b74458;
$pastel-pink-100:   #b56098;
$pastel-pink-200:   #8B569A;

$pastels: (
  'purple-100': $pastel-purple-100,
  'purple-200': $pastel-purple-200,
  'indigo-100': $pastel-indigo-100,
  'indigo-200': $pastel-indigo-200,
  'blue-100':   $pastel-blue-100,
  'blue-200':   $pastel-blue-200,
  'cyan-100':   $pastel-cyan-100,
  'cyan-200':   $pastel-cyan-200,
  'teal-100':   $pastel-teal-100,
  'teal-200':   $pastel-teal-200,
  'green-100':  $pastel-green-100,
  'green-200':  $pastel-green-200,
  'yellow-100': $pastel-yellow-100,
  'yellow-200': $pastel-yellow-200,
  'orange-100': $pastel-orange-100,
  'orange-200': $pastel-orange-200,
  'red-100':    $pastel-red-100,
  'red-200':    $pastel-red-200,
  'pink-100':   $pastel-pink-100,
  'pink-200':   $pastel-pink-200,
);

/* Non-module-specific 'branded' colors */
$color-help:         $teal;
$color-gamification: $royal-blue;
$color-staff:        #7E57C2;
$color-selected:     $faded-blue;
$color-app-icon:     #297ae6;

/* 
 * Basic Roles/Themes
 *
 * Many of these are semantic additions to bootstrap defaults,
 * since we prefer to prepend `color-` to variables that
 * aren't intrinsically color-specific.
 * With some rare exceptions most interactive elements play a role
 * that matches with one of these predefined themes.
 */
$color-primary:    $blue; 
$color-secondary:  $gray-600;
$color-success:    $green;
$color-info:       $cyan;
$color-warning:    $orange;
$color-danger:     $red;
$color-dark:       $gray-800;
$color-fair:       $gray-400;
$color-light:      $gray-200;
$color-lighter:    $gray-100;
$color-very-light: $gray-50;
$color-accent:     $red-orange;
$color-alternate:  $maastricht-blue;
$color-body:       $black;
$color-caution:    $yellow;
$color-error:      $red;
$color-safe:       $teal;

/* 'Light' Roles/Themes */
// Not every role needs a light version, but add to here as needed.
// If you do add a light alternative, be sure to add to the 'theme' map as well.
$color-primary-light: $blue-light;
$color-success-light: $green-light;
$color-info-light:    $cyan-light;
$color-warning-light: $orange-light;
$color-danger-light:  $red-light;
$color-caution-light: $yellow-light;
$color-error-light:   $red-light;
$color-safe-light:    $teal-light;

/* 'Dark' Roles/Themes */
// Not every role needs a dark version, but add to here as needed.
// If you do add a dark alternative, be sure to add to the 'theme' map as well.
$color-primary-dark: $blue-dark;
$color-danger-dark:  $red-dark;
$color-error-dark:   $red-dark;
$color-info-dark:    $cyan-dark;

/* Specific Roles/Themes */

// Expiration
// $color-safe:  $color-safe;    // Already defined above, use when a green "active" state doesn't make sense
$color-task-trigger-one:       #FEE9E9;
$color-task-trigger-one--dark: #f63f3f;
$color-task-trigger-two:       #DDF2F6;
$color-task-trigger-two--dark: #519fc0;
$color-task-action:            #e6e2ff;
$color-task-action--dark:      #8a77ff;

$color-active:   $color-success; // $green
$color-expiring: $color-warning; // $orange
$color-expired:  $color-danger;  // $red

// Priority, e.g. ticket priority
$color-low-priority:    $color-caution; // $yellow
$color-medium-priority: $color-warning; // $orange
$color-high-priority:   $color-danger;  // $red

// Status, e.g. ticket status
$color-status-open:    $green;
$color-status-in-prog: $cyan;
$color-status-closed:  $gray-500;

// Telecom types
$color-telecom-voice:        $color-telecom;
$color-telecom-data:         $de-france-blue;
$color-telecom-consolidated: map-get($pastels, 'purple-200');

// Main Navigation Drawer
// `dark-` and `light-` replace `color-` prefix
$dark-drawer:         $maastricht-blue;
$dark-drawer-alt:     $biscay-blue;
$light-drawer:        $iceberg-blue;
$light-drawer-alt:    $titan-white;
$color-secondary-nav: $iceberg-blue;


// Semantic color variables added for color themeing
// Will be used to create the variables from _css-variables.scss
// and to generate utility classes in a bootstrap safe manner from _theming-overrides.scss
$themed-colors-map: (
  // general
  "themed-main-bg": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'dark-high-800')},
    "dark-low": #{map-get($base-colors, 'blue-gray-700')},
    "theme-description": "Main background color for the app"
  ),
  "themed-scroll-gradient": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'dark-high-800')},
    "dark-low": #{map-get($base-colors, 'blue-gray-500')},
  ),
  "themed-footer-bg": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'dark-high-800')},
    "dark-low": #{map-get($base-colors, 'blue-gray-700')},
    "theme-description": "Light: white Dark: dark-high-800 "
  ),
  // typography
  "themed-base": (
    "light": #{map-get($base-colors, 'gray-900')},
    "dark": #{map-get($base-colors, 'gray-200')},
    "dark-low": #{map-get($base-colors, 'gray-100')},
    "theme-description": "Main text color: (previously gray-900)"
  ),
  "themed-secondary": (
    "light": #{map-get($base-colors, 'gray-700')},
    "dark": #{map-get($base-colors, 'gray-400')},
    "dark-low": #{map-get($base-colors, 'gray-400')},
    "theme-description": "Secondary text color: (previously gray-700)"
  ),
  "themed-muted": (
    "light": #{map-get($base-colors, 'gray-600')},
    "dark": #{map-get($base-colors, 'gray-500')},
    "dark-low": #{map-get($base-colors, 'gray-500')},
    "theme-description": "Muted text color: (previously gray-600)"
  ),
  "themed-very-muted": (
    "light": #{map-get($base-colors, 'gray-500')},
    "dark": #{map-get($base-colors, 'gray-600')},
    "dark-low": #{map-get($base-colors, 'gray-600')},
    "theme-description": "Very muted text color: (previously gray-500)"
  ),
  "themed-dark": (
    "light": #{map-get($base-colors, 'gray-800')},
    "dark": #{map-get($base-colors, 'gray-300')},
    "dark-low": #{map-get($base-colors, 'gray-300')},
    "theme-description": "High contrast/impact text color: (previously gray-800)"
  ),
  "themed-fair": (
    "light": #{map-get($base-colors, 'gray-400')},
    "dark": #{map-get($base-colors, 'dark-high-200')},
    "dark-low": #{map-get($base-colors, 'dark-low-200')},
    "theme-description": "Low contrast/impact text color: (previously gray-400)"
  ),
  "themed-very-fair": (
    "light": #{map-get($base-colors, 'gray-300')},
    "dark": #{map-get($base-colors, 'gray-500')},
    "dark-low": #{map-get($base-colors, 'gray-500')},
    "theme-description": "Very low contrast/impact text color: (previously gray-300)"
  ),
  "themed-light": (
    "light": #{map-get($base-colors, 'gray-200')},
    "dark": #{map-get($base-colors, 'dark-high-600')},
    "dark-low": #{map-get($base-colors, 'gray-700')},
    "theme-description": "Low contrast/impact text color and some disabled backgrounds: (previously gray-200)"
  ),
  "themed-moderate-light": (
    "light": #{map-get($base-colors, 'gray-400')},
    "dark": #{map-get($base-colors, 'dark-high-700')},
    "dark-low": #{map-get($base-colors, 'gray-800')},
    "theme-description": "Moderate contrast/impact color"
  ),
  "themed-moderate-lighter": (
    "light": #{map-get($base-colors, 'gray-300')},
    "dark": #{map-get($base-colors, 'dark-high-700')},
    "dark-low": #{map-get($base-colors, 'gray-800')},
    "theme-description": "Even more moderate contrast/impact color"
  ),
  "themed-lighter": (
    "light": #{map-get($base-colors, 'gray-100')},
    "dark": #{map-get($base-colors, 'dark-high-700')},
    "dark-low": #{map-get($base-colors, 'gray-800')},
    "theme-description": "Low contrast/impact text color: (previously gray-100)"
  ),
  "themed-very-light": (
    "light": #{map-get($base-colors, 'gray-50')},
    "dark": #{map-get($base-colors, 'dark-high-700')},
    "dark-low": #{map-get($base-colors, 'gray-800')},
    "theme-description": "Low contrast/impact text color (previously gray-50)"
  ),
  "themed-branded-light": (
    "light": #{map-get($base-colors, 'iceberg-blue')},
    "dark": #{map-get($base-colors, 'biscay-blue')},
    "dark-low": #{map-get($base-colors, 'biscay-blue')},
    "theme-description": "Low contrast/impact text color (previously iceberg-blue)"
  ),
  "themed-branded-lighter": (
    "light": #{map-get($base-colors, 'titan-white')},
    "dark": #{map-get($base-colors, 'biscay-blue')},
    "dark-low": #{map-get($base-colors, 'biscay-blue')},
    "theme-description": "Slightly-higher-but-still-low-contrast/impact text color (previously titan-white). Often for accent backgrounds."
  ),
  "themed-heading": (
    "light": #{map-get($base-colors, 'gray-900')},
    "dark": #{map-get($base-colors, 'gray-100')},
    "dark-low": #{map-get($base-colors, 'gray-100')},
    "theme-description": "Semantic variable for heading for easier customization"
  ),
  "themed-vertical-nav": (
    "light": $purple-gray,
    "dark": #{map-get($base-colors, 'dark-high-700')},
    "dark-low": #{map-get($base-colors, 'gray-800')},
    "theme-description": "Background color for vertical navigation"
  ),
  "themed-vertical-nav-hover-link": (
    "light": mix(black, $purple-gray, 7.5%),
    "dark": mix(white, map-get($base-colors, 'dark-high-700'), 20%),
    "dark-low": mix(white, map-get($base-colors, 'dark-high-700'), 20%),
    "theme-description": "Background color for active or hovered vertical nav elements"
  ),
  "themed-placeholder": (
    "light": #{map-get($base-colors, 'gray-600')},
    "dark": #{map-get($base-colors, 'gray-300')},
    "dark-low": #{map-get($base-colors, 'gray-300')},
    "theme-description": "Semantic variable for input placeholders"
  ),
  "themed-secondary-hover": (
    "light": #545b62,
    "dark": #{map-get($base-colors, 'gray-400')},
    "dark-low": #{map-get($base-colors, 'gray-400')},
  ),
  "themed-dark-hover": (
    "light": #1d2124,
    "dark": #{map-get($base-colors, 'gray-400')},
    "dark-low": #{map-get($base-colors, 'gray-400')},
  ),
  "themed-light-hover-bg": (
    "light": #d3d9df,
    "dark": #{map-get($base-colors, 'dark-high-500')},
    "dark-low": #{map-get($base-colors, 'gray-600')},
  ),
  "themed-link": (
    "light": $blue,
    "dark": adjust-color($blue, $lightness: 10%),
    "dark-low": adjust-color($blue, $lightness: 10%),
    "theme-description": "Themed link color for increased darkmode contrast",
  ),
  "themed-link-hover": (
    "light": #024dbc,
    "dark": adjust-color(#024dbc, $lightness: 20%),
    "dark-low": adjust-color(#024dbc, $lightness: 20%),
  ),
  // header
  "themed-header-bg": (
    "light": #{map-get($base-colors, 'gray-100')},
    "dark": #{map-get($base-colors, 'maastricht-blue')},
    "dark-low": #{map-get($base-colors, 'maastricht-blue')},
  ),
  "themed-bare-header-bg": (
    "light": #{map-get($base-colors, 'gray-200')},
    "dark": #{map-get($base-colors, 'white')},
    "dark-low": #{map-get($base-colors, 'white')},
  ),
  "themed-header-link": (
    "light": #{map-get($base-colors, 'gray-900')},
    "dark": #{map-get($base-colors, 'gray-200')},
    "dark-low": #{map-get($base-colors, 'gray-200')},
  ),
  // left module nav colors
  "themed-dark-drawer-bg": (
    "light": #{map-get($base-colors, 'maastricht-blue')},
    "dark": #{map-get($base-colors, 'dark-high-900')},
    "dark-low": #{map-get($base-colors, 'blue-gray-900')},
  ),
  "themed-dark-drawer-alt-bg": (
    "light": #{map-get($base-colors, 'biscay-blue')},
    "dark": #{map-get($base-colors, 'dark-high-800')},
    "dark-low": #{map-get($base-colors, 'blue-gray-800')},
  ),
  // modal headers and footers
  "themed-modal-sticky-footer": (
    "light": $iceberg-blue,
    "dark": $biscay-blue,
    "dark-low": #{map-get($base-colors, 'blue-gray-900')},
  ),
  // boxes
  "themed-box-bg": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'dark-high-500')},
    "dark-low": #{map-get($base-colors, 'blue-gray-500')},
  ),
  "themed-box-hover-opacity": (
    "light": 0.0,
    "dark": 0.075,
    "dark-low": 0.075,
  ),
  // overlays, tooltips, shades and filters
  "themed-overlay-bg-rgb-values": (
    "light": '11, 32, 60',
    "dark": '52, 58, 64',
    "dark-low": '52, 58, 64',
  ),
  "themed-overlay--bg-card-rgb-values": (
    "light": '255, 255, 255',
    "dark": '0, 0, 0',
    "dark-low": '0, 0, 0',
  ),
  "themed-tooltip-bg-rgb-values": (
    "light": '52, 58, 64',
    "dark": '255, 255, 255',
    "dark-low": '255, 255, 255',
  ),
  "themed-white-black": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'black')},
    "dark-low": #{map-get($base-colors, 'black')},
  ),
  "themed-black-white": (
    "light": #{map-get($base-colors, 'gray-900')},
    "dark": #{map-get($base-colors, 'white')},
    "dark-low": #{map-get($base-colors, 'white')},
  ),
  "themed-base-rgb-values": (
    "light": '0, 20, 64',
    "dark": '223, 234, 242',
    "dark-low": '223, 234, 242',
  ),
  "themed-dropdown-hover-bg-rgb-values": (
    "light": '0, 0, 0',
    "dark": '223, 234, 242',
    "dark-low": '223, 234, 242',
  ),
  "themed-light-icon-filter": (
    "light": 'none',
    "dark": #{brightness(0) saturate(100%) invert(100%) sepia(87%) saturate(782%) hue-rotate(159deg) brightness(113%) contrast(92%)},
    "dark-low": #{brightness(0) saturate(100%) invert(100%) sepia(87%) saturate(782%) hue-rotate(159deg) brightness(113%) contrast(92%)}
  ),
  "themed-badge-icon-filter": (
    "light": 'brightness(0.5)',
    "dark": #{brightness(0) saturate(100%) invert(100%) sepia(87%) saturate(782%) hue-rotate(159deg) brightness(113%) contrast(92%)},
    "dark-low": #{brightness(0) saturate(100%) invert(100%) sepia(87%) saturate(782%) hue-rotate(159deg) brightness(113%) contrast(92%)}
  ),
  "themed-edit-dash-gradient": (
    "light": #{linear-gradient(140deg, #{map-get($base-colors, 'biscay-blue')} 0%, #{map-get($base-colors, 'de-france-blue')} 100%)},
    "dark": #{linear-gradient(140deg, #{map-get($base-colors, 'de-france-blue')} 0%, #{map-get($base-colors, 'biscay-blue')} 100%)},
    "dark-low": #{linear-gradient(140deg, #{map-get($base-colors, 'de-france-blue')} 0%, #{map-get($base-colors, 'biscay-blue')} 100%)}
  ),
  // badges
  "themed-badge-bg": (
    "light": #{map-get($base-colors, 'gray-100')},
    "dark": #{map-get($base-colors, 'dark-high-900')},
    "dark-low": #{map-get($base-colors, 'blue-gray-800')},
  ),
  "themed-icon-badge-bg": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'gray-200')},
    "dark-low": #{map-get($base-colors, 'blue-gray-600')},
  ),
  "themed-badge-border": (
    "light": #{map-get($base-colors, 'gray-200')},
    "dark": #{map-get($base-colors, 'dark-high-700')},
    "dark-low": #{map-get($base-colors, 'blue-gray-700')},
  ),
  // buttons
  "themed-button-hover": (
    "light": #{map-get($base-colors, 'gray-100')},
    "dark": #{map-get($base-colors, 'dark-high-800')},
    "dark-low": #{map-get($base-colors, 'blue-gray-800')},
  ),
  // forms
  "themed-form-control-bg": (
    "light": #{map-get($base-colors, 'white')},
    "dark": #{map-get($base-colors, 'dark-high-800')},
    "dark-low": #{map-get($base-colors, 'blue-gray-800')},
  ),
  // tasks
  "themed-task-trigger-one": (
    "light": $color-task-trigger-one,
    "dark": $color-task-trigger-one--dark,
    "dark-low": $color-task-trigger-one--dark,  
  ),
  "themed-task-trigger-two": (
    "light": $color-task-trigger-two,
    "dark": $color-task-trigger-two--dark,
    "dark-low": $color-task-trigger-two--dark,  
  ),
  "themed-task-action": (
    "light": $color-task-action,
    "dark": $color-task-action--dark,
    "dark-low": $color-task-action--dark,  
  ),
  "themed-box--tips-bg": (
    "light": #{map-get($base-colors, 'gray-75')},
    "dark": #{map-get($base-colors, 'dark-high-500')},
    "dark-low": #{map-get($base-colors, 'blue-gray-500')},
  ),
  "themed-purple-dark": (
    "light": $purple-dark,
    "dark": $purple-subtle,
    "dark-low": $purple-subtle,
  ),
  "themed-indigo-dark": (
    "light": $indigo-dark,
    "dark": $indigo-subtle,
    "dark-low": $indigo-subtle,
  ),
  "themed-blue-dark": (
    "light": $blue-dark,
    "dark": $blue-subtle,
    "dark-low": $blue-subtle,
  ),
  "themed-cyan-dark": (
    "light": $cyan-dark,
    "dark": $cyan-subtle,
    "dark-low": $cyan-subtle,
  ),
  "themed-teal-dark": (
    "light": $teal-dark,
    "dark": $teal-subtle,
    "dark-low": $teal-subtle,
  ),
  "themed-green-dark": (
    "light": $green-dark,
    "dark": $green-subtle,
    "dark-low": $green-subtle,
  ),
  "themed-yellow-dark": (
    "light": $yellow-dark,
    "dark": $yellow-subtle,
    "dark-low": $yellow-subtle,
  ),
  "themed-orange-dark": (
    "light": $orange-dark,
    "dark": $orange-subtle,
    "dark-low": $orange-subtle,
  ),
  "themed-red-dark": (
    "light": $red-dark,
    "dark": $red-subtle,
    "dark-low": $red-subtle,
  ),
  "themed-pink-dark": (
    "light": $pink-dark,
    "dark": $pink-subtle,
    "dark-low": $pink-subtle,
  ),
  "themed-purple-subtle": (
    "light": $purple-subtle,
    "dark": $purple-dark,
    "dark-low": $purple-dark,
  ),
  "themed-indigo-subtle": (
    "light": $indigo-subtle,
    "dark": $indigo-dark,
    "dark-low": $indigo-dark,
  ),
  "themed-blue-subtle": (
    "light": $blue-subtle,
    "dark": $blue-dark,
    "dark-low": $blue-dark,
  ),
  "themed-cyan-subtle": (
    "light": $cyan-subtle,
    "dark": $cyan-dark,
    "dark-low": $cyan-dark,
  ),
  "themed-teal-subtle": (
    "light": $teal-subtle,
    "dark": $teal-dark,
    "dark-low": $teal-dark,
  ),
  "themed-green-subtle": (
    "light": $green-subtle,
    "dark": $green-dark,
    "dark-low": $green-dark,
  ),
  "themed-yellow-subtle": (
    "light": $yellow-subtle,
    "dark": $yellow-dark,
    "dark-low": $yellow-dark,
  ),
  "themed-orange-subtle": (
    "light": $orange-subtle,
    "dark": $orange-dark,
    "dark-low": $orange-dark,
  ),
  "themed-red-subtle": (
    "light": $red-subtle,
    "dark": $red-dark,
    "dark-low": $red-dark,
  ),
  "themed-pink-subtle": (
    "light": $pink-subtle,
    "dark": $pink-dark,
    "dark-low": $pink-dark,
  ),
  // Asset statuses
  "themed-asset-not-reporting": (
    "light": #994b09,
    "dark": $orange-subtle,
    "dark-low": $orange-subtle,
  ),
  "themed-asset-check": (
    "light": #9b6c24,
    "dark": $yellow-subtle,
    "dark-low": $yellow-subtle,
  ),
  "themed-asset-syncing": (
    "light": #1a8494,
    "dark": $blue-subtle,
    "dark-low": $blue-subtle,
  ),
  "themed-new-row": (
    "light": $blue-subtle,
    "dark": #0e2951,
    "dark-low": #0e2951,
  ),
  "themed-check-row": (
    "light": $yellow-subtle,
    "dark": #5e441c,
    "dark-low": #5e441c,
  ),
  "themed-not-reporting-row": (
    "light": $orange-subtle,
    "dark": #433429,
    "dark-low": #433429,
  ),
  "themed-new-shadow": (
    "light": #9bb3d2,
    "dark": #{map-get($base-colors, 'dark-high-500')},
    "dark-low": #{map-get($base-colors, 'dark-high-500')},
  ),
  "themed-check-shadow": (
    "light": #c1b58d,
    "dark": #{map-get($base-colors, 'dark-high-500')},
    "dark-low": #{map-get($base-colors, 'dark-high-500')},
  ),
  "themed-not-shadow": (
    "light": #d2b49b,
    "dark": #{map-get($base-colors, 'dark-high-500')},
    "dark-low": #{map-get($base-colors, 'dark-high-500')},
  )
);

// General typography and colors
$themed-white-black: var(--themed-white-black);
$themed-black-white: var(--themed-black-white);

// Semantic colors: general
$themed-main-bg: var(--themed-main-bg);
$themed-footer-bg: var(--themed-footer-bg); 

// Semantic colors: header colors
$themed-header-bg: var(--themed-header-bg); 
$themed-bare-header-bg: var(--themed-bare-header-bg); 
$themed-header-link: var(--themed-header-link);

// Semantic colors: left module nav colors
$themed-dark-drawer-bg: var(--themed-dark-drawer-bg);
$themed-dark-drawer-alt-bg: var(--themed-dark-drawer-alt-bg);

// Semantic colors: Modal headers and footers
$themed-modal-sticky-footer: var(--themed-modal-sticky-footer);

// Semantic colors: module navigation

// Semantic colors: typography
$themed-base: var(--themed-base);
$themed-secondary: var(--themed-secondary);
$themed-muted: var(--themed-muted);
$themed-very-muted: var(--themed-very-muted);
$themed-dark: var(--themed-dark);
$themed-fair: var(--themed-fair);
$themed-very-fair: var(--themed-very-fair);
$themed-light: var(--themed-light);
$themed-lighter: var(--themed-lighter);
$themed-very-light: var(--themed-very-light);
$themed-moderate-light: var(--themed-moderate-light);
$themed-moderate-lighter: var(--themed-moderate-lighter);
$themed-branded-light: var(--themed-branded-light);
$themed-branded-lighter: var(--themed-branded-lighter);
$themed-heading: var(--themed-heading);
$themed-placeholder: var(--themed-placeholder);
$themed-link: var(--themed-link);

// Semantic colors: boxes
$themed-box-bg: var(--themed-box-bg);
$themed-box--tips-bg: var(--themed-box--tips-bg);

// Semantic colors: badges
$themed-badge-bg: var(--themed-badge-bg);
$themed-badge-border: var(--themed-badge-border);

// Semantic colors:forms
$themed-form-control-bg: var(--themed-form-control-bg);

// Semantic colors: tasks
$themed-task-trigger-one: var(--themed-task-trigger-one);
$themed-task-trigger-two: var(--themed-task-trigger-two);
$themed-task-action: var(--themed-task-action);

// Semantic colors: scroll gradient
$themed-scroll-gradient: var(--themed-scroll-gradient);

// Themed CSS Variable Overrides of useful ulitity classes and manually override currently implemented sass variables.
$themed-overrides-map: (
  "secondary": "themed-secondary",
  "dark": "themed-dark",
  "muted": "themed-muted",
  "very-muted": "themed-very-muted",
  "fair": "themed-fair",
  "very-fair": "themed-very-fair",
  "light": "themed-light",
  "lighter": "themed-lighter",
  "very-light": "themed-very-light",
);

$secondary: $themed-secondary;
$dark: $themed-dark;
$muted: $themed-muted;
$very-muted: $themed-very-muted;
$fair: $themed-fair;
$very-fair: $themed-very-fair;
$light: $themed-light;
$lighter: $themed-lighter;
$very-light: $themed-very-light;


// Bootstrap Overrides
$warning:            $orange;
$body-color:         $color-body;
$table-border-color: $themed-very-fair;
  

//
// Color System
//

$colors: (
  // Reiterating Bootstrap $colors variables because those aren't defined yet
  "purple":    $purple,
  "indigo":    $indigo,
  "blue":      $blue,
  "cyan":      $cyan,
  "teal":      $teal,
  "green":     $green,
  "yellow":    $yellow,
  "orange":    $orange,
  "red":       $red,
  "pink":      $pink,
  "white":     $white,
  "gray":      $gray-600,
  "gray-dark": $gray-800,

  // 'Light' color
  "purple-light": $purple-light,
  "indigo-light": $indigo-light,
  "blue-light":   $blue-light,
  "cyan-light":   $cyan-light,
  "teal-light":   $teal-light,
  "green-light":  $green-light,
  "orange-light": $orange-light,
  "yellow-light": $yellow-light,
  "red-light":    $red-light,
  "pink-light":   $pink-light,

  // 'Dark' color
  "purple-dark": $purple-dark,
  "indigo-dark": $indigo-dark,
  "blue-dark":   $blue-dark,
  "cyan-dark":   $cyan-dark,
  "teal-dark":   $teal-dark,
  "green-dark":  $green-dark,
  "orange-dark": $orange-dark,
  "yellow-dark": $yellow-dark,
  "red-dark":    $red-dark,
  "pink-dark":   $pink-dark,

  // New colors not also defined in Bootstrap
  "azure-blue":      $azure-blue,
  "biscay-blue":     $biscay-blue,
  "black":           $black,
  "blue-gray":       $blue-gray,
  "blue-oblivion":   $blue-oblivion,
  "de-france-blue":  $de-france-blue,
  "iceberg-blue":    $iceberg-blue,
  "maastricht-blue": $maastricht-blue,
  "red-orange":      $red-orange,
  "royal-blue":      $royal-blue,
  "titan-white":     $titan-white,
  "true-black":      $true-black,
  'faded-blue':      $faded-blue,

  // 'Subtle' colors
  "purple-subtle": $purple-subtle,
  "indigo-subtle": $indigo-subtle,
  "blue-subtle":   $blue-subtle,
  "cyan-subtle":   $cyan-subtle,
  "teal-subtle":   $teal-subtle,
  "green-subtle":  $green-subtle,
  "orange-subtle": $orange-subtle,
  "yellow-subtle": $yellow-subtle,
  "red-subtle":    $red-subtle,
  "pink-subtle":   $pink-subtle,
);

$modules: 'home', 'assets', 'contracts', 'vendors', 'telecom', 'helpdesk', 'network';
$module-colors: (
  "asset":       $color-asset,
  "assets":      $color-assets,
  "contract":    $color-contract,
  "contracts":   $color-contracts,
  "helpdesk":    $color-helpdesk,
  "home":        $color-home,
  "marketplace": $color-marketplace,
  "network":     $color-network,
  "networks":    $color-networks,
  "telecom":     $color-telecom,
  "telecoms":    $color-telecoms,
  "vendor":      $color-vendor,
  "vendors":     $color-vendors,
);
$module-colors-light: (
  "assets":    $color-assets-light,
  "contracts": $color-contracts-light,
  "helpdesk":  $color-helpdesk-light,
  "network":   $color-network-light,
  "telecom":   $color-telecom-light,
  "vendors":   $color-vendors-light
);
$module-colors-dark: (
  "assets":    $color-assets-dark,
  "contracts": $color-contracts-dark,
  "helpdesk":  $color-helpdesk-dark,
  "network":   $color-network-dark,
  "telecom":   $color-telecom-dark,
  "vendors":   $color-vendors-dark
);

// Gets merged in with existing bootstrap $theme-colors map.
// Many of these are repeats from bootstrap defaults,
// but are included here to reference the `color-` naming scheme.
$colors-and-modules: map-merge($colors, $module-colors);
$theme-colors: map-merge(
  (
    "accent":               $color-accent,
    "active":               $color-active,
    "alternate":            $color-alternate,
    "body":                 $color-body,
    "caution":              $color-caution,
    "danger":               $color-danger,
    "dark":                 $color-dark,
    "error":                $color-error,
    "expired":              $color-expired,
    "expiring":             $color-expiring,
    "fair":                 $color-fair,
    "gamification":         $color-gamification,
    "help":                 $color-help,
    "high-priority":        $color-high-priority,
    "info":                 $color-info,
    "light":                $color-light,
    "lighter":              $color-lighter,
    "very-light":           $color-very-light,
    "low-priority":         $color-low-priority,
    "medium-priority":      $color-medium-priority,
    "primary":              $color-primary,
    "safe":                 $color-safe,
    "secondary":            $color-secondary,
    "secondary-nav":        $color-secondary-nav,
    "selected":             $color-selected,
    "staff":                $color-staff,
    "status-closed":        $color-status-closed,
    "status-in-prog":       $color-status-in-prog,
    "status-open":          $color-status-open,
    "success":              $color-success,
    "telecom-consolidated": $color-telecom-consolidated,
    "telecom-data":         $color-telecom-data,
    "telecom-voice":        $color-telecom-voice,
    "warning":              $color-warning,

    // Light alternatives
    "primary-light": $blue-light,
    "success-light": $green-light,
    "info-light":    $cyan-light,
    "warning-light": $orange-light,
    "danger-light":  $red-light,
    "caution-light": $yellow-light,
    "error-light":   $red-light,
    "safe-light":    $teal-light,

    // Dark alternatives
    "primary-dark": $blue-dark,
    "danger-dark":  $red-dark,
    "error-dark":   $red-dark,
    "info-dark":    $cyan-dark,
  ),
  $colors-and-modules
);


/**
 * HEADER & NAVIGATION
 */

 $header-height: 3rem;
 $drawer-width: 16rem;
 $drawer-collapse-width: 4.5rem;
 $drawer-link-collapse-width: ($drawer-collapse-width - 2.175rem);
 $container-padding: 2.25rem;
 $container-padding-sm: 1.25rem;
 $container-padding-lg: 2.25rem;
 $container-padding-xl: 2.25rem;
 $bottom-bar-height: 0.5rem;
 
 $nav-transition-functions: (
   "base": cubic-bezier(0.4, 0.0, 0.2, 1),
   "in": cubic-bezier(0.0, 0.0, 0.2, 1),
   "out": cubic-bezier(0.4, 0.0, 1, 1)
 );
 
 $nav-transition-durations: (
   "instant": 50ms,
   "linger": 150ms,
   "base": 200ms,
   "in": 300ms,
   "out": 250ms,
   "slow": 400ms
 );
 
 $nav-transition-base: all map-get($nav-transition-durations, base) map-get($nav-transition-functions, base);
 $nav-transition-in: all map-get($nav-transition-durations, in) map-get($nav-transition-functions, in);
 $nav-transition-out: all map-get($nav-transition-durations, out) map-get($nav-transition-functions, out);


/**
 * LAYOUT
 */

$small: 479px;
$medium: 767px;
$large: 1023px;
$max: max-width;
$min: min-width;

$grid-breakpoints: (
  xs: 0,
  sm: 320px,
  sm-md: 544px,
  md: 768px,
  md-lg: 992px,
  lg: 1152px,
  xl: 1400px, // 1312px container + left-hand nav
  xxl: 2468px,
);

$vertical-nav-width: 224px;

@function parseInt($n) {
  @return calc($n / ($n * 0 + 1rem));
}
$non-container-space-base: calc(parseInt($drawer-collapse-width) * 16px) + calc(2 * parseInt($container-padding) * 16px);
$non-container-space-sm: calc(2 * parseInt($container-padding-sm) * 16px);
$container-breakpoints: (
  xs: 0,
  sm: map-get($grid-breakpoints, 'sm') - $non-container-space-sm,
  md: map-get($grid-breakpoints, 'md') - $non-container-space-base,
  md-lg: map-get($grid-breakpoints, 'md-lg') - $non-container-space-base,
  lg: map-get($grid-breakpoints, 'lg') - $non-container-space-base,
  xl: map-get($grid-breakpoints, 'xl') - $non-container-space-base,
  xxl: map-get($grid-breakpoints, 'xxl') - $non-container-space-base,
);

// We want our layout to be fully responsive down to 1024px
// (i.e., stretches across the full width of the screen
// and adjusts pixel by pixel according to viewport),
// so all container sizes share ~ the same max width,
// ±6 since compilation expects ascending sizes,
// and we want it to stay divisible by 2
$container-max-widths: (
  sm: 1674px,
  md: 1676px,
  lg: 1678px,
  xl: 1680px,  // 108rem
  xxl: 4224px, // 264rem
);

$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 2.25,
  6: $spacer * 3.5,
  7: $spacer * 5
);
$advanced-spacers: (
  "0.5": $spacer * .125,
  "2.5": $spacer * 0.75,
);
$all-spacers: map-merge($spacers, $advanced-spacers);

// These are some of the default spacing we use across modules pages. The varaints are for basic pages and pages that utilize the box--display-container (or similar) class.
$content-top-margin: map-get($spacers, 4);
$content-bottom-margin: map-get($spacers, 5) + $bottom-bar-height;
$contained-content-bottom-margin: map-get($spacers, 4) + $bottom-bar-height;
$content-min-margin: $header-height + $content-top-margin + $content-bottom-margin;
$contained-content-min-margin: $header-height + $content-top-margin + $contained-content-bottom-margin;
$module-content-default-margin: map-get($spacers, 4);
$max-content-height: calc(100vh - #{$content-min-margin});
$max-contained-content-height: calc(100vh - #{$contained-content-min-margin});
$module-header-height: 7.25rem;
$module-header-height--vertical-nav: 4.875rem;


/**
 * SHADOWS
 */

$shadows: (
  1: (0 1px 1px 0 rgba(0,0,0,0.14), 0 2px 1px -1px rgba(0,0,0,0.12), 0 1px 3px 0 rgba(0,0,0,0.20)),          // Base
  2: (0 2px 2px 0 rgba(0,0,0,0.14), 0 3px 1px -2px rgba(0,0,0,0.12), 0 1px 5px 0 rgba(0,0,0,0.20)),          // Button
  3: (0 4px 5px 0 rgba(0,0,0,0.14), 0 1px 10px 0 rgba(0,0,0,0.12), 0 2px 4px -1px rgba(0,0,0,0.20)),         // Lifted
  4: (0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.20)),    // Hover
  5: (0 16px 24px 2px rgba(0,0,0,0.14), 0 6px 30px 5px rgba(0,0,0,0.12), 0 8px 10px -5px rgba(0,0,0,0.20)),  // Above
  6: (0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.20)), // Top
);

$shadow-variants: (
  "hover-overlap": (-5px 10px 10px -10px rgba(0,0,0,0.14), -3px 3px 14px -14px rgba(0,0,0,0.12), -10px 0 5px -5px rgba(0,0,0,0.20)),
  "small-hover": (0 2px 4px 0 rgba(0,0,0,0.14), 0 1px 4px 0 rgba(0,0,0,0.12), 0 2px 4px -1px rgba(0,0,0,0.20)),
  "small-hover-sticky-column": (0px 4px 4px -2px rgba(0,0,0,0.14), 0px 3px 4px -2px rgba(0,0,0,0.12), -1px 4px 4px -2px rgba(0,0,0,0.20)),
);

$semantic-shadows: (
  'base'         : map-get($shadows, 1),  // Cards, switch toggles, search bars (1dp)
  'button'       : map-get($shadows, 2),  // Buttons, badges (2dp)
  'lifted'       : map-get($shadows, 3),  // Outer navigation - top and side (4dp)
  'hover'        : map-get($shadows, 4),  // Hovers, dropdowns, tooltips (8dp)
  'above'        : map-get($shadows, 5),  // Open side navigation, flash alerts (16dp)
  'top'          : map-get($shadows, 6),  // Dialog/modal (24dp)
  'hover--small' : map-get($shadow-variants, "small-hover"),  // Smaller hover shadow
  'hover--sticky-column' : map-get($shadow-variants, "small-hover-sticky-column"), // Smaller hover shadow with overlap
);
$shadows: map-merge($shadows, $semantic-shadows);

$shadow-base: map-get($shadows, 'base');
$shadow-button: map-get($shadows, 'button');
$shadow-lifted: map-get($shadows, 'lifted');
$shadow-hover: map-get($shadows, 'hover');
$shadow-above: map-get($shadows, 'above');
$shadow-top: map-get($shadows, 'top');
$shadow-hover--small: map-get($shadow-variants, "small-hover");
$shadow-hover--sticky-column: map-get($shadow-variants, "small-hover-sticky-column");
$shadow-container-box-inner-heading: (
  0px 0.3px 0.3px hsla(0deg, 0%, 0%, 0.14), 
  0px 1.2px 1.3px -0.8px hsla(0deg, 0%, 0%, 0.14), 
  0px 3px 3.4px -1.7px hsla(0deg, 0%, 0%, 0.14), 
  0px 7.4px 8.3px -2.5px hsla(0deg, 0%, 0%, 0.14)
);

/**
 * OVERLAYS
 */

$overlays: (
  1: (rgba(var(--themed-overlay-bg-rgb-values), 0.6)),
  2: (rgba($maastricht-blue, .65)),
  3: (rgba(var(--themed-overlay--bg-card-rgb-values), .6)),
  4: (rgba(var(--themed-overlay-bg-rgb-values), 0.25)),
);

$semantic-overlays: (
  'base': map-get($overlays, 1), // Basic cards, table previews
  'dark': map-get($overlays, 2), // Insights cards
  'light': map-get($overlays, 3), // Some subtle cards
  'very-light': map-get($overlays, 4), // Very translucent walkthrough overlay
);
$overlays: map-merge($overlays, $semantic-overlays);

$overlay-base: map-get($overlays, 'base');
$overlay-dark: map-get($overlays, 'dark');
$overlay-light: map-get($overlays, 'light');
$overlay-very-light: map-get($overlays, 'very-light');

/**
 * TYPOGRAPHY
 */

$font-size-base-pixels: 16px;
$font-size-base-percentage: ($font-size-base-pixels / 16px) * 100%;
$font-size-base: ($font-size-base-percentage / 100%) * 1rem;
$font-family-sans-serif: 'Lato', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

$heading-multiplyers: (
  "h1": 2.5,
  "h2": 2,
  "h3": 1.75,
  "h4": 1.5,
  "h5": 1.25,
  "h6": 1,
);

$h1-font-size: $font-size-base * map-get($heading-multiplyers, "h1"); 
$h2-font-size: $font-size-base * map-get($heading-multiplyers, "h2");
$h3-font-size: $font-size-base * map-get($heading-multiplyers, "h3");
$h4-font-size: $font-size-base * map-get($heading-multiplyers, "h4");
$h5-font-size: $font-size-base * map-get($heading-multiplyers, "h5");
$h6-font-size: $font-size-base * map-get($heading-multiplyers, "h6");


/**
 * COMPONENTS
 */

$border-radius: 0.5rem;
$border-radius-lg: 1rem;
$border-radius-sm: 0.25rem;
$input-border-radius-lg: $border-radius;
$pagination-padding-y-sm: 0.125rem;
$simplebar-size: 18px;

/**
 * CUSTOM TRANSITIONS
 */

$transition-smooth: all .4s ease !default;

/**
 * WORLD WAR Z(-INDEXES)
 */

$zIndex: (
  'way-behind': -20,
  'behind': -10,
  'base': 0,
  'above': 10,
  'main-content': 10,
  'dropdown': 20,
  'tooltip': 122,
  'drawer-closed': 40,
  'header-sample-toggle': 41,
  'header': 50,
  'drawer-overlay': 60,
  'drawer-open': 70,
  'company-drawer': 80,
  'drawer-toggle': 90,
  'drawer-more': 91,
  'walkthrough-item': 110,
  'modal': 120,
  'notification': 130,
);
