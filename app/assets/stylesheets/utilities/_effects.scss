@mixin clickable {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.clickable {
  @include clickable;
}

.clickable--with-hover {
  @include clickable;
  transition: $transition-base;
  transition-property: background-color;

  &:hover {
    background-color: $themed-light;
  }
}

.unclickable {
  cursor: default;
  pointer-events: none;
}

.pointer-events-all {
  pointer-events: all;
}

.rotated {
  transform: rotate(180deg);
}
