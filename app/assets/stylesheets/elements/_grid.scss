// CSS Grid (true grid)
.grid-wrapper {
  align-items: stretch;
  column-gap: 2.25rem;
  display: grid;
  grid-auto-rows: minmax(16rem, auto);
  grid-template-columns: 1fr 1fr;
  row-gap: 2.25em;

  @include content-breakpoint-down('lg') {
    grid-template-columns: 100% !important;

    .grid-item {
      grid-column: 1 !important;
      grid-row: auto !important;
    }
  }
}

.grid-wrapper--5-7 {
  grid-template-columns: 5fr 7fr;
}

.rowspan-1 { 
  grid-row: span 1;
}

.rowspan-2 {
  grid-row: span 2;
}

.rowspan-3 {
  grid-row: span 3;
}

.colspan-2 {
  grid-column: span 2;
}


// You can't mix units in Sass, so we'll try to preemptively do as much of the calc as possible
$main-offset-width: ($drawer-collapse-width + ($container-padding-lg * 2));
$xl-grid-item: 32rem;
$xxl-grid-item: 42rem;
$default-grid-item: 23;
$padding-box: 1.25;
$one-third-percent: 33.33%;
$min-cols: 3;
$gapless-xl-width: $main-offset-width + ($min-cols * $xl-grid-item);
$gapless-xxl-width: $main-offset-width + ($min-cols * $xxl-grid-item);
$gap: 30px;
$gap-width: ($min-cols - 1) * $gap;

.list-wrap .list-grid {
  display: grid;
  gap: 0 #{$gap};
  grid-template-columns: repeat(auto-fit, minmax(#{$default-grid-item}rem, 1fr));

  @media only screen and ($max: map-get($grid-breakpoints, 'lg')) {
    grid-template-columns: repeat(auto-fit, minmax(#{$default-grid-item}rem, 1fr)) !important;
  }

  &.few-items {
    grid-template-columns: repeat(auto-fit, minmax(#{$default-grid-item}rem, calc(#{$one-third-percent} - #{$padding-box}rem)));

    .content-wide-theme & {
      @media (min-width: calc(#{$gapless-xl-width} + #{$gap-width})) {
        grid-template-columns: repeat(auto-fit, minmax(#{$xl-grid-item}, #{$xl-grid-item}));
      }

      @media (min-width: calc(#{$gapless-xxl-width} + #{$gap-width})) {
        grid-template-columns: repeat(auto-fit, minmax(#{$xxl-grid-item}, #{$xxl-grid-item}));
      }
    }
  }
}

.list-wrap :deep(.simplebar-content-wrapper) {
  height: 100%!important;
}
