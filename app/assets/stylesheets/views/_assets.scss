$assettype_desktop: #B02B2C;
$assettype_laptop: #D15600;
$assettype_server: #C79810;
$assettype_phone: #6BBA70;
$assettype_phone_system: #3F4C6B;
$assettype_router: #356AA0;
$assettype_switch: #FF7400;
$assettype_firewall: #4096EE;
$assettype_mobile: #FFFF88;
$assettype_tablet: #FF0084;
$assettype_printer: #FF0084;

.assettype--desktop { color: $assettype_desktop; }
.assettype--laptop { color: $assettype_laptop; }
.assettype--server { color: $assettype_server; }
.assettype--phone { color: $assettype_phone; }
.assettype--phone_system { color: $assettype_phone_system; }
.assettype--router { color: $assettype_router; }
.assettype--switch { color: $assettype_switch; }
.assettype--firewall { color: $assettype_firewall; }
.assettype--mobile { color: $assettype_mobile; }
.assettype--tablet { color: $assettype_tablet; }
.assettype--printer { color: $assettype_printer; }

.asset-search {
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  height: 3.4rem;
  padding: 1rem 2rem;
  padding-left: 0;
  border-radius: 0px;

  &:focus {
    box-shadow:none !important; // overwriting default browser styling to remove shadow on focus
  }
}

.no-click {
  pointer-events: none;
  opacity: 0.3;
  cursor: crosshair;
}

.asset-item {
  cursor: pointer;
  position: relative;
  transition: $transition-base;
  transition-property: opacity;

  &:hover {
    background-color: $themed-lighter;
  }
}

.asset-item__image {
  height: 1.5rem;
  width: 1.5rem;
}

.asset-item__ticket-warning {
  display: inline-block;
  color: orange;
  background: inherit;
  font-size: 1.375rem;
  line-height: 1.375rem;
  vertical-align: text-top;
}

.warranty-progress {
  background: linear-gradient(90deg, $green, $green 48%, $orange 83%, $red);
  border-radius: 0.25rem;
  height: 0.5rem;
  margin: 0 auto;
  margin-bottom: 3rem;
  width: 90%;
  position: relative;
}

.warranty-progress__today-marker {
  background: $themed-secondary;
  box-shadow: $shadow-base;
  height: 1rem;
  position: absolute;
  top: 50%;
  transform: translate(-50%,-50%);
  transition: left 0.2s ease-in-out;
  width: 1rem;
}

.warranty-progress__today-text {
  font-weight: 500;
  left: 50%;
  min-width: 5rem;
  position: absolute;
  top: 100%;
  transform: translate(-50%, 0.25rem);
  transition: left 0.2s ease-in-out;
}

.asset-attribute:nth-child(even){
  background-color: $themed-lighter;
}
.asset-attribute:nth-child(odd){
  background-color: $themed-very-fair;
}

.asset-data-download {
  height: 8rem;
  width: 8rem;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.asset-data-download__img {
  height: 4rem;
}

.sweet-modal .sweet-title > h2 {
  line-height: 64px;
}

.donut-chart-info-assets {
  padding-left: 1.5rem;

  &:before {
    border-top: 1px solid $gray-500;
    content: "";
    left: -3rem;
    position: absolute;
    top: 3.75rem;
    width: 5rem;
  }
}
.box-hover{
  &:hover{
    color: #fff;
  }
}
.dz-border {
  border: 2px dashed #e7e7e7;
  transition: border 0.2s ease-in-out;

  &.dz-drag-hover {
    border-color: $primary;
  }
}

#preview-template {
  margin: 0 auto 1rem;

  .dz-details {
    border: 0;
  }

  .input-group-addon{
    color: $red;
    font-size: 1.2rem;
    border: 0;
    background: transparent;
    cursor: pointer;
  }
}

$curve: cubic-bezier(0.650, 0.000, 0.450, 1.000);

.checkmark {
  animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
  box-shadow: inset 0px 0px 0px $green;
  border-radius: 50%;
  display: inline-block;
  height: 80px;
  stroke: $green;
  stroke-miterlimit: 10;
  stroke-width: 3;
  width: 80px;
}

.checkmark__circle {
  animation: stroke .6s $curve forwards;
  fill: $white;
  stroke: $green;
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-miterlimit: 10;
  stroke-width: 1;
}

.checkmark__check {
  animation: stroke .3s $curve .8s forwards;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  transform-origin: 50% 50%;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%, 100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 30px $green;
  }
}

.bootstrap-table {
  max-height: 50vh;
  overflow-y: scroll;
  th.small {
    letter-spacing: 0.7px;
  }

  tbody tr td {
    font-size: 14px;
  }

  td.checkbox{
    input {
      vertical-align: middle;
    }
  }
}

.search-img-wrap {
  border-radius: 2px 2px 0 0;
  display: block;
  height: 0;
  overflow: hidden;
  padding-bottom: 46.75%;
  position: relative;
  width: 100%;
}

.search-bg-img {
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.search-img {
  left: 0;
  position: absolute;
  transition: all 0.4s ease-in-out;
  top: 0;
  width: 100%;
  border-bottom: 1px solid $themed-light;

  .is-next & {
    transform: translateX(100%);
  }

  .is-prev & {
    transform: translateX(-100%);
  }
}

.search-slides {
  height: 30rem;
  width: 50rem;
  overflow: hidden;
  position: relative;
}

.search-slide {
  left: 0;
  position: absolute;
  top: 0;
  transform: translateX(100%);
  transition: all 0.2s ease-in-out;
  width: 100%;

  &.is-prev {
    transform: translateX(-100%);
  }

  &.is-next {
    transform: translateX(100%);
  }

  &.is-active {
    transform: translateX(0);
  }
}

.search-slide-text {
  opacity: 0;
  transition: all 0.2s 0.2s ease-in-out;

  .is-active & {
    opacity: 1;
  }
}

.search-dots {
  bottom: -1.5rem;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}

.search-dot {
  background-color: #aaa;
  border-radius: 50%;
  cursor: pointer;
  float: left;
  height: 7px;
  margin-right: 5px;
  transition: background 0.1s ease-in-out;
  width: 7px;
}

.search-active-dot {
  background-color: blue;
  border-radius: 50%;
  height: 7px;
  position: absolute;
  top: 0;
  transition: all 0.1s ease-in-out;
  width: 7px;

  @for $i from 1 through 5 {
    &[data-active-slide="#{$i}"] {
      transform: translateX(#{($i - 1) * 12}px);
    }
  }
}

.search-btn {
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  cursor: pointer;
  display: block;
  height: 2rem;
  line-height: 2rem;
  position: absolute;
  right: -3rem;
  text-align: center;
  top: 50%;
  transition: all 0.2s ease-in-out;
  transform: translate(100%,-50%);
  width: 2rem;

  &:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.25);
    text-decoration: none;
  }
}

.search-btn--prev {
  left: -1rem;
  transform: translate(-100%,-50%);
}

.search-btn--next > i:before {
  left: 0.1em;
}

.tooltips {
 position: relative;
 display: inline;
}

.tooltips span {
  position: absolute;
  min-width: 117px;
  color: #FFFFFF;
  line-height: 16px;
  text-align: center;
  visibility: visible;
  border-radius: 6px;
  opacity: 0.8;
  left: 100%;
  top: 25%;
  margin-top: -15px;
  margin-left: 15px;
  z-index: 999;
  padding: 6px;
  background-color: $themed-dark;
  border: 1px solid $themed-dark;
  font-size: 12px;
  font-weight: 500;
}

.tooltips span:after {
 content: '';
 position: absolute;
 top: 50%;
 right: 100%;
 margin-top: -8px;
 width: 0; height: 0;
 border-right: 8px solid $themed-dark;
 border-top: 8px solid transparent;
 border-bottom: 8px solid transparent;
}
.assets-holder {
  .btn-toolbar {
    .btn-group {
      padding-left: 18px;
    }
  }
}

.type-select--assets {
  filter: brightness(0) grayscale(1) opacity(0.2);

  &.impact-low {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/impact-low.svg");
  }

  &.impact-medium {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/impact-medium.svg");
  }

  &.impact-high {
    background-image: url("https://nulodgic-static-assets.s3.amazonaws.com/images/impact-high.svg");
  }

  &:hover {
    filter: opacity(.65);
  }
}

.dark-theme {
  .type-select--assets {
    filter: brightness(0) grayscale(1) opacity(1) contrast(0.2);
  }
}

.discovered-assets-table tr {
  transition: $transition-base;
  &:hover {
    background-color: $themed-lighter;
  }
}

.action-icon {
  border-radius: 50%;
  line-height: 30px;
  height: 30px;
  width: 30px;
  text-align: center;
  display: inline-block;
  i {
    color: white;
  }
}

.history-row:after {
  position: absolute;
  border: 1px solid $themed-very-fair;
  width: 0;
  height: calc(100% - 4rem);
  display: block;
  content: '';
  z-index: 0;
  margin-left: 15px;
  bottom: 25px;
}

.history-row-item {
  z-index: map-get($zIndex, 'above');
}

.network-status {
  width: 13px;
  height: 13px;
  display: inline-block;
  border-radius: 100%;
  border: 1px solid #fff;
  &.danger {
    background: #bd0b19;
    border: 1px solid #bd0b19;
  }
  &.success {
    background: #4fb273;
    border: 1px solid #4fb273;
  }
  &.warning {
    background: #f5a20b;
    border: 1px solid #f5a20b;
  }
  &.disabled {
    background: #7d7a7a;
    border: 1px solid #7d7a7a;
  }
  &.in-active {
    background: lightgrey;
    border: 1px solid lightgrey;
  }
}

.save-asset-btn {
  // takes drawer wrapper into account to better
  // center the button under the form
  margin-right: calc(#{$drawer-collapse-width} / 2);
}

.badge--merged {
  background-color: gray;
  color: $white;
  padding: 4px 10px;
  border-radius: 5px;
}

.asset-status-icon {
  border-radius: 50%;
  height: 1rem;
  line-height: 1rem;
  font-size: 0.75rem;
  text-align: center;
  top: -1px;
  position: relative;
  width: 1rem;

  &:before {
    line-height: 1rem;
    top: 0; // This overrides the top position default to nulodgicons
  }
}
@media (min-width: 769px) {
  .mobile-two-col,
  .mobile-icon-col,
  .mobile-content-col {
    display: contents !important;
  }
}
@media (max-width: 768px) {
  .mobile-scrollable {
    min-height: 200px;
    max-height: 50vh;
    overflow-y: scroll !important;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }
}

.associated-scrollable {
  max-height: 13.75rem;
  -webkit-overflow-scrolling: touch;
}

.sweet-buttons.sweet-custom-footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 768px) {
  .qr-modal .sweet-modal {
    max-width: 90vw !important;
    min-width: 0 !important;
    max-height: 90vh !important;
    height: auto !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    border-radius: 1rem !important;
    box-sizing: border-box !important;
    overflow: auto !important;
    display: block !important;
  }
  .qr-modal .sweet-modal-content {
    padding: 1rem 1rem 0.5rem 1rem !important;
    margin-bottom: 0 !important;
    padding-bottom: 5.5rem !important;
  }
  .qr-modal .sweet-buttons {
    margin-top: auto !important;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0.75rem !important;
    position: static !important;
    background: #fff !important;
  }
  .qr-modal .sweet-buttons .btn {
    min-width: 5.625rem;
    font-size: 0.92rem;
    padding: 0.35rem 0.75rem;
    margin-left: 0.25rem !important;
  }
  .qr-modal .sweet-buttons .btn.ml-2 {
    margin-left: 0.25rem !important;
  }
  .table-responsive-mobile {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    display: block;
  }
  .table-responsive-mobile table {
    min-width: 37.5rem;
    width: max-content;
    table-layout: auto;
  }
  .table-responsive-mobile th,
  .table-responsive-mobile td {
    white-space: nowrap;
  }
  .lifecycle-progress__warning-line {
    position: static !important;
    border-left: none !important;
    height: auto !important;
    top: auto !important;
    left: auto !important;
    width: 100% !important;
    transition: none !important;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
  }
  .lifecycle-progress__warning-text {
    position: static !important;
    left: auto !important;
    top: auto !important;
    margin-top: 1rem;
    transform: none !important;
    width: 100% !important;
    text-align: center !important;
    transition: none !important;
  }
}
