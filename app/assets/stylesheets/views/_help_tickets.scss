.help-ticket {
  overflow: hidden;
  margin-bottom: 1.5rem;
  padding: 0;
  position: relative;
  transition: $transition-base;

  &:after {
    content: "";
    position: absolute;
    bottom: 0;
    background-color: white;
    height: 0.5rem;
  }
}

.no-click {
  pointer-events: none;
  opacity: 0.3;
  cursor: crosshair;
}

.ticket__delete {
  color: $themed-muted;
  display: inline-block;
  margin-top: 0.5rem;

  &:hover {
    color: $red;
  }
}

.help-ticket__mini-view {
  background-color: $themed-lighter;
  transition: $transition-base;

  .is-expanded &,
  &:hover {
    background-color: $themed-lighter;
  }
}

.priority-medium__border-left {
  border-left: solid 6px $orange;
}

.priority-low__border-left {
  border-left: solid 6px $yellow;
}

.priority-high__border-left {
  border-left: solid 6px $red;
}

.status-closed__border-left {
  border-left: solid 6px $themed-very-fair;
}

.btn-circle {
  text-align: center;
  padding: 6px 0;
  font-size: 12px;
  border: 5px solid $white;
  color: inherit;
}

.open-state {
  color: $themed-muted;
}

.open-state input.btn-circle {
  background-color: $color-status-open;
}

.in-progress-state {
  color: $themed-muted;
}

.in-progress-state input.btn-circle {
  background-color: $cyan;
}

.closed-state {
  color: $themed-muted;
}

.closed-state input.btn-circle {
  background-color: $themed-muted;
}

.bar {
  height: 10px;
  display: block;
  float: left;
}

.active .btn-circle {
  border-color: inherit;
}

.priority {
  width: 80px;
}

.high-priority__circle {
  color: $red;
  background-color: $red;
}

.medium-priority__circle {
  background-color: $orange;
  color: $orange;
}

.low-priority__circle {
  background-color: $yellow;
  color: $yellow;
}

.open-status__circle {
  color: $color-status-open;
  background-color: $color-status-open;
}

.in-progress-status__circle {
  background-color: $cyan;
  color: $cyan;
}

.closed-status__circle {
  background-color: $themed-muted;
  color: $themed-muted;
}

.assigned-to {
  height: 2.6em;
}

.status-btn {
  color: $themed-lighter;
}

.status-btn-open {
  color: $color-status-open;
}

.status-btn-in-progress em {
  color: $cyan;
}

.status-btn-complete {
  color: $themed-fair;
}

.help-ticket__comment-header {
  border-bottom: 2px solid $gray-500;
  color: $gray-500;

  &:hover {
    color: $cyan;
    border-bottom: 2px solid $cyan;
  }

  &.comment-header__selected {
    border-bottom: 2px solid $themed-secondary;
    color: $themed-secondary;
  }
}

.open-status {
  background-color: $green;
}

.in-progress-status {
  background-color: $cyan;
}

.closed-status {
  background-color: $themed-very-fair;
}

.help-ticket-attachment__link {
  color: $themed-muted;
  &:hover {
    color: $themed-dark;
  }
  width: 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.help-ticket-attachment-delete {
  font-size: 1.25em;
  color: $themed-fair !important;
  &:hover {
    color: $red !important;
  }
}

.help-ticket__new-file {
  font-size: 1.125rem;
  color: $themed-muted !important;
}

.mx-30 {
  max-width: 30px;
}

.action-icon {
  border-radius: 50%;
  line-height: 28px;
  height: 30px;
  width: 30px;
  text-align: center;
  display: inline-block;
  z-index: 1;
  
  i {
    bottom: -3px;
    color: white;
    font-size: 16px;
    line-height: 32px;
    right: 7px;
  }

  &.action-icon--lg {
    i {
      font-size: 20px;
      line-height: 35px;
      right: 5px;
    }
  }
}
.trix-wrapper  pre{
    display: inline-block;
    width: 100%;
    vertical-align: top;
    font-family: monospace;
    font-size: 0.9em;
    margin: 0;
    padding: 0.5em;
    white-space: pre;
    background-color: #eee;
    overflow-x: auto;
    color: #d01f3e;
}

.mass-update-table-scroll {
  overflow: auto;
  max-height: 31.25rem;
}

.avatar--plus-one {
  background-color: $themed-very-fair;
  border-radius: 50%;
  color: $themed-very-muted;
  display: inline-block;
  font: 600 10px / 28px Lato;
  height: 28px;
  margin-left: -14px;
  margin-top: 0;
  padding-left: 12px;
  position: relative;
  vertical-align: middle;
  width: 28px;
}

.avatar--created-by--empty {
  background-color: $themed-very-fair !important;
  color: $themed-very-muted !important;
  font-size: 1.25rem !important;

  &.small {
    font-size: 1rem !important;
  }
}

.avatar-holder {
  margin-right: 0.5rem;
  justify-content: flex-start;
}

.scheduled-tasks__time-data {
  max-width: calc(100% - 48px);
}

.scheduled-tasks__time-data--large {
  max-width: calc(100% - 68px);
}

.scheduled-tasks__one-time-badge {
  background-color: tint-color($green, 85%);
  color: $green;
}

.scheduled-tasks__recurring-badge {
  background-color: tint-color($blue, 85%);
  color: $blue;
}

.light-icon-filter {
  filter: var(--themed-light-icon-filter);
}
