class AssetAutomationTasksController < ModulesController
  include MultiCompany::GeneralScoping
  include ReadReplicaDb
  include ManagedAssetRiskCenterHelper

  before_action :need_font_awesome
  before_action :check_task, only: [:show, :destroy, :edit, :update]
  before_action :authorize_write, only: [:edit, :new]

  def index
    respond_to do |format|
      format.json { render json: tasks_json }
    end
  end

  def show
    respond_to do |format|
      format.json { render json: task_json }
    end
  end

  def destroy
    respond_to do |format|
      # set_log_params(resource)
      if resource.destroy
        format.json { render json: task_json }
      else
        format.json { render json: { message: resource.errors.full_messages.to_sentence } }
      end
    end
  end

  def create
    contributor = scoped_company_user.contributor
    existing_orders = scoped_company.assets_automated_tasks.pluck(:order) || []
    new_order = existing_orders.any? ? existing_orders.max + 1 : 1
    existing_serial_numbers = scoped_company.assets_automated_tasks.pluck(:serial_number)
    new_serial = existing_serial_numbers.any? ? existing_serial_numbers.max + 1 : 1
    task = Assets::AutomatedTask.new(
      contributor: contributor,
      name: task_name,
      company_id: scoped_company&.id,
      order: new_order,
      serial_number: new_serial
    )
    set_log_params(task)
    creator = AssetAutomatedTasks::Create.new(task)
    task = creator.create(task_params)
    respond_to do |format|
      format.json { render json: task }
    end
  end

  def update
    task = Assets::AutomatedTask.find_by(id: params[:id])
    if task
      set_log_params(task)
      updater = AssetAutomatedTasks::Update.new(task)
      task = updater.update(task_params)
      respond_to do |format|
        if task[:error_message]
          format.json { render json: { message: task[:error_message] }, status: :unprocessable_entity }
        else
          format.json { render json: task }
        end
      end
    else
      respond_to do |format|
        format.json { render json: { message: "Unable to find task" }, status: :not_found }
      end
    end
  end

  def asset_softwares
    assets_softwares = set_read_replica_db do
      get_base_assets(scoped_company)
        .joins(:asset_softwares)
        .where(asset_softwares: { archived_at: nil })
        .distinct
        .pluck('asset_softwares.name')
    end
    respond_to do |format|
      format.json { render json: assets_softwares }
    end
  end

  private

  def reset_for_assigned_or_status_changed(task, company_mailer)
    event_detail_value = task.task_events.first.event_details.first.value
    custom_form = CustomForm.find(JSON.parse(event_detail_value)['custom_forms'].first['id'])
    generator_class = get_generator_class(company_mailer)
    generator_class.new(company_mailer, custom_form).value_as_json
  end

  def reset_for_other_events(task, company_mailer)
    generator_class = get_generator_class(company_mailer)
    generator_class.new(task.company_mailer).value_as_json
  end

  def need_font_awesome
    @font_awesome_needed = true
  end

  def task_params
    @task_params ||= params[:task].as_json
  end

  def task_name
    params["task"]["name"]
  end

  def load_resources
    resource_list = Assets::AutomatedTask.joins(task_events: [:event_type, { event_details: :event_subject_type }])
                                                  .joins(task_actions: :action_type)
                                                  .includes(task_events: [:event_type, { event_details: [:event_subject_type] }],
                                                            task_actions: [:action_type])
                                                  .order(:order)
    resource_list = resource_list.where(company_id: scoped_company&.id)
    resource_list = resource_list.where('automated_tasks_event_types.module' => params[:mod]) if params[:mod]
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      resource_list = resource_list.where('assets_automated_tasks.name ILIKE :search OR 
                                            assets_automated_tasks.serial_number::text ILIKE :search OR 
                                            regexp_replace(assets_event_types.name, \'[{}\\[\\]/]\', \'\', \'g\') ILIKE :search OR 
                                            regexp_replace(assets_event_subject_types.name, \'[{}\\[\\]/]\', \'\', \'g\') ILIKE :search OR 
                                            regexp_replace(assets_action_types.name, \'[{}\\[\\]/]\', \'\', \'g\') ILIKE :search', search: 
                                            search_term)  
    end
    resource_list.distinct
  end

  def resources
    @resources ||= load_resources
  end

  def tasks_json
    resources.map do |task|
      task_json(task)
    end
  end

  def resource
    @resource ||= Assets::AutomatedTask.find_by(id: params[:id])
  end

  def task_json(task = resource)
    AssetAutomatedTasks::JsonOutput.new(scoped_company).json(task)
  end

  def check_task
    if resource.nil?
      render json: { message: "Task was not found" }, status: :not_found
    end
  end

  def set_log_params(task)
    task.set_params(nil, scoped_company_user.id, params)
  end
end
