class CompanyUsersController < ModulesController
  include PermissionsHelper
  include ApplicationHelper
  include HandleCompanyCache<PERSON><PERSON>s
  set_privilege_name("CompanyUser")

  before_action :authorize_write, only: [:create, :update, :destroy, :new]
  before_action :set_resource, only: [:show, :edit, :update, :destroy, :invite_staff, :update_user_office_status, :user_dependent_automated_task, :move_teammate, :reset_authenticator_app, :update_user_active_status]
  before_action :assign_attributes, only: [:update]
  before_action :authorize_company_user, except: [:index, :show, :update_password]
  skip_before_action :authorize_read, :if => :profile_page
  skip_before_action :authorize_write, :if => :profile_page

  def index
    @company_users = nil
    @company_users = scoped_company.company_users.not_sample_company_user.includes(:user, :custom_form_values, custom_form: :custom_form_fields)
    sort_by = company_user_filtering_params[:sort_by]
    @company_users = @company_users.order("users.#{sort_by}") if sort_by.present?

    if company_user_filtering_params[:location_id].present?
      @company_users = @company_users.where(location_id: company_user_filtering_params[:location_id])
    end

    if !company_user_filtering_params[:all]
      if company_user_filtering_params[:archived]
        @company_users = @company_users.where.not(archived_at: nil)
      else
        @company_users = @company_users.where(archived_at: nil)
      end
    end

    company_domains = scoped_company.company_domains.where(is_registered: true).pluck(:domain_name)

    if company_user_filtering_params[:domain] == "company_domain"
      @company_users = @company_users.joins(:user).where("substring(users.email from '@(.+?)\\..+$') ILIKE ANY (ARRAY[:domains])", domains: company_domains)
    elsif company_user_filtering_params[:domain] == "external_domain"
      @company_users = @company_users.joins(:user).where.not("substring(users.email from '@(.+?)\\..+$') ILIKE ANY (ARRAY[:domains])", domains: company_domains)
    end

    if company_user_filtering_params[:state] == "invited"
      @company_users = @company_users.where.not(id: @company_users.uninvited.pluck(:id))
    elsif company_user_filtering_params[:state] == "uninvited"
      @company_users = @company_users.uninvited
    end

    if company_user_filtering_params[:search_terms].present?
      search = company_user_filtering_params[:search_terms]
      user_ids = scoped_company.users.where("""
        lower(CONCAT(users.first_name, ' ',users.last_name)) ILIKE ? OR users.email ILIKE ?
      """, "%#{search}%", "%#{search}%" ).pluck(:id)
      @company_users = @company_users.where(user_id: user_ids)
    end
    if company_user_filtering_params[:location].present?
      location = scoped_company.locations.find company_user_filtering_params[:location]
      @company_users = @company_users.includes(:custom_form_values)
                        .joins(:custom_form_fields)
                        .where(custom_form_fields: { field_attribute_type: 'location_list' }, 
                               custom_form_values: { value_int: location.id }) if location.present?
    end

    if company_user_filtering_params[:form_id].present?
      @company_users = @company_users.where(custom_form_id: company_user_filtering_params[:form_id])
    end

    @user_count = @company_users.count

    if company_user_filtering_params[:page].present?
      @company_users = @company_users.paginate(
        page: company_user_filtering_params[:page].to_i,
        per_page: company_user_filtering_params[:per_page].to_i || 25
      )
    end

    discovered_users_counts = scoped_company.discovered_users.where(status: "ready_for_import").count
    users_lookup = User.where(id: @company_users.map(&:user_id)).index_by(&:id)
    company_users_avatars
    departments = company_users_departments
    titles = company_users_titles
    user_sources = DiscoveredUser.where(company_id: scoped_company.id, email: @company_users.pluck(:email))
                                 .includes(:discovered_user_sources)
                                 .index_by(&:email)

    user_data = []
    @company_users.each do |company_user|
      avatar_url = company_user_avatar(company_user.id)
      user = users_lookup[company_user.user_id]
      user_source = user_sources[user.email]&.discovered_user_sources&.pluck(:source)&.first
      data = {
        id: company_user.id,
        contributor_id: company_user.contributor_id,
        invite_token: company_user.invite_token,
        granted_access_at: company_user.granted_access_at,
        last_logged_in_at: company_user.last_logged_in_at,
        archived_at: company_user.archived_at,
        self_onboarding: company_user.self_onboarding,
        department: departments[company_user.id]&.first&.value_str,
        title: titles[company_user.id]&.first&.value_str,
        mfa_enabled: company_user.mfa_enabled,
        locked: company_user.locked,
        mfa_verified: company_user.mfa_verified,
        source: user_source,
        custom_form: {
          form_name: form_name_if_not_teammates(company_user),
          color: company_user.custom_form&.color,
          company_name: get_form_company_name(company_user),
          form_id: company_user.custom_form_id
        },
        user: {
          id: user&.id,
          full_name: user&.full_name || "Missing name",
          first_name: user&.first_name,
          last_name: user&.last_name,
          email: user&.email,
          sso_user: user&.sso_user,
          sso_user_source: user&.sso_user_source
        }
      }

      if avatar_url.present?
        data['avatar_thumb_url'] = avatar_url
      end
      user_data << data
    end

    render json: { company_users: user_data, company_user_count: @user_count, discovered_users_count: discovered_users_counts }
  end

  def update_password
    service = CognitoService.new(current_user)
    token = service.refresh_token
    response = service.update_password params[:old_password], params[:new_password]

    if response
      current_user.invalidate_all_sessions!
      render json: {}
    else
      render json: { message: "There was an error updating password" }, status: :unprocessable_entity
    end
  end

  def uninvited_users
    @company_users = scoped_company.company_users
                                    .where(granted_access_at: nil)
                                    .includes(:user)
                                    .order('users.first_name ASC, users.last_name ASC')
                                    .to_json(include: {user: {only: [:first_name, :last_name]}})
    respond_to do |format|
      format.json { render :json => @company_users }
    end
  end

  def show
    if is_cache_enabled? && validate_cache_key_requirements?
      cache_key = "company_user_#{@company_user.id}_#{params['fetch_permission']}_#{scoped_company.id}_#{params[:action]}_#{params[:controller]}"
      data = Rails.cache.fetch(cache_key, expires_in: 8.hours) do
        track_cached_keys(cache_key, 'show_company_user')
        company_user_data
      end
    else
      data = company_user_data
    end
    respond_to do |format|
      format.html {  }
      format.json { render json: data }
    end
  end

  def create
    @company_user = CompanyUser.new(company_user_params)
    @company_user.company = scoped_company
    if scoped_company_user
      @company_user.created_by = scoped_company_user # used for reward points
    end
    @user = User.find_by_cache(email: user_params[:email]&.downcase)
    if @user.present?
      @company_user.user = @user
      existing_company_user = CompanyUser.find_by_cache(user_id: @user.id, company_id: scoped_company.id)
      if existing_company_user
        @company_user = existing_company_user
        @company_user.convert_ticket_emails
      end
    else
      @user = User.new(user_params)
      @user.password = SecureRandom.hex(32)
      @user.reset_password_token = SecureRandom.hex(32)
      @user.confirmation_token = SecureRandom.hex(32)
      @user.generate_authentication_token!
      @company_user.user = @user
    end

    if @company_user.save
      save_related_items if params[:company_user][:universal_links].present?
      everyone_group = scoped_company.groups.find_by(name: 'Everyone')
      everyone_group.group_members.create(contributor_id: @company_user.contributor_id)
      render json: @company_user
    else
      render json: { message: @company_user.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def destroy
    @company_user&.custom_form_values.map { |value| value.skip_event_trigger = true }
    if current_user.super_admin?
      user = @company_user.user
      @company_user.activity_owner_id = scoped_company_user.id
      if @company_user&.destroy
        remove_from_sla
        disable_dependant_automated_tasks
        render json: {}, status: :ok
      else
        render json: { message: @company_user.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    else
      if scoped_company_user.id == @company_user.id
        render json: { message: "You cannot remove yourself." }, status: :not_acceptable
      elsif scoped_company.admin_company_users.active.count == 1 && scoped_company.admin_company_users.active.first.id == @company_user.id
        render json: { message: "You cannot remove the last admin." }, status: :not_acceptable
      elsif @company_user&.destroy
        user = @company_user.user
        remove_from_sla
        disable_dependant_automated_tasks
        render json: {}, status: :ok
      else
        render json: { message: @company_user&.errors&.full_messages&.to_sentence || "Unknown error" }, status: :unprocessable_entity
      end
    end
  end

  def update
    @company_user.created_by = scoped_company_user # used for reward points
    return_message = @is_cognito_email ? "Email already exists in our system." : ""
    if !@is_cognito_email && @company_user.valid? && @user.valid?
      save_user_and_company_user
      unless user_params[:password].present?
        save_related_items unless params[:company_user][:universal_links].nil?
      end
      is_current_user = @company_user.user_id == current_user.id
      render json: @company_user.as_json.merge({is_current_user: is_current_user})
    else
      render json: { message: "#{return_message} #{@company_user.errors.full_messages.to_sentence} #{@user.errors.full_messages.to_sentence}" }, status: :precondition_failed
    end
  end

  def update_user_office_status
    if @company_user.present?
      @company_user.activity_owner_id = scoped_company_user.id
      @company_user.update(out_of_office: !@company_user.out_of_office)
    end
  end

  def user_dependent_automated_task
    helpdesk_dependent_tasks, assets_dependant_tasks = AutomatedTasks::DependentAutomatedTasksService.new(workspace_ids, @company_user&.contributor_id, @company_users, is_archive_modal, false, false, false, scoped_company.id).call
    tasks = {
      dependant_tasks: helpdesk_dependent_tasks,
      assets_dependant_tasks: assets_dependant_tasks
    }
    render json: { tasks: tasks }, status: :ok
  end

  def move_teammate
    response = CompanyModule::MoveTeammate.new(params, @company_user, scoped_company, scoped_company_user).call
    render json: response, status: :ok
  rescue Exception => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def reset_authenticator_app
    if !(scoped_company_user.is_admin? || scoped_company_user.super_admin?)
      render json: { message: 'Sorry, you are not authorize to perform this action.' }, status: :unauthorized
    elsif @company_user.update_columns(mfa_secret_key: nil)
      render json: { message: "Teammate's authenticator app successfully reset." }, status: :ok
    else
      render json: { message: 'There was an error resetting authenticator app.' }, status: :unprocessable_entity
    end
  end

  def fetch_company_user_vendors
    return render json: {} unless params[:company_user_ids].present?
    
    company_user_ids = params[:company_user_ids]

    company_users = CompanyUser.includes(:primary_vendors, :secondary_vendors)
                               .where(id: company_user_ids)
    vendors_data = company_users.index_by(&:name).transform_values do |company_user|
      (company_user.primary_vendors + company_user.secondary_vendors).uniq.presence
    end.compact

    render json: vendors_data
  end

  def update_user_active_status
    if @company_user.present?
      @company_user.update(active_status: !@company_user.active_status)
    end
  end

  private
  def company_user_data
    if params['fetch_permission'] == 'false'
      json_hash = @company_user.attributes
      json_hash[:is_agent] = @company_user.helpdesk_agent?
      json_hash[:user] = @company_user.user.as_json
    else
      json_hash = @company_user.as_json(include: :apps)

      if !@company_user.super_admin?
        json_hash[:permissions] = user_permissions 
        unless @company_user.privilege_network_monitoring.nil?
          json_hash[:permissions] = json_hash[:permissions].map do |obj|
            if obj[:privilege_name] == 'Monitoring'
              { :privilege_name => "Monitoring", :permission_type => @company_user.privilege_network_monitoring }
            else
              obj
            end
          end
        end
      end

      if @company_user&.linkable
        json_hash[:linkable_id] = @company_user.linkable&.id
        json_hash[:universal_links] = @company_user.linkable.source_linkable_links.map { |linkable_link| { id: linkable_link.id, target: linkable_link.target } }
      end

      if @company_user&.contributor
        json_hash[:groups] = Group.includes(:group_members)
                                  .where(group_members: { contributor_id: @company_user.contributor_id })
                                  .map do |group|
                                    group_json = group.as_json.merge(member_count: group.company_users.count)
                                    if group.workspace_id.nil? || has_subscription?("it_help_desk")
                                      group_json
                                    end
                                  end.compact
      end
    end
    json_hash = json_hash.merge({is_workspace_agent: @company_user.is_workspace_agent?(scoped_workspace)}) if is_help_desk_module?
    json_hash
  end

  def validate_cache_key_requirements?
    if @company_user.blank?
      raise 'Company user is not present for cache key.'
    elsif scoped_company.blank?
      raise 'Company is not present for cache key.'
    elsif params[:action].blank? || params[:controller].blank?
      raise 'Action/Controller information is missing in parameters for cache key.'
    end

    true
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
    false
  end

  def matching_company_users(contributors_ids)
    @company_users.select { |company_user| contributors_ids.include?(company_user.contributor_id) }
  end

  def workspace_ids
    @workspace_ids ||= scoped_company.workspaces.pluck(:id)
  end

  def is_archive_modal
    @is_archive_modal ||= params[:is_archive_modal]
  end

  def disable_dependant_automated_tasks
    AutomatedTasks::DisableDependantTasksWorker.perform_async(workspace_ids, @company_user&.contributor_id, @company_users, is_archive_modal, false, scoped_company.id)
  end

  def save_related_items
    links_array = params[:company_user][:universal_links].map { |ul| ul['target'] }
    LinkableService.new(@company_user).save_related_items(links_array)
  end

  def set_resource
    if (params[:id] == 'current')
      @company_user = scoped_company_user
    elsif params[:id] =~ /\-/
      @company_user = scoped_company.company_users.find_by_cache(guid: params[:id])
    elsif params[:is_archive_modal]
      company_user_ids = params[:id].split(',').map(&:to_i)
      @company_users = scoped_company.company_users.where(id: company_user_ids)
    else
      @company_user = scoped_company.company_users.find(params[:id])
    end
    raise ActiveRecord::RecordNotFound, "Missing user" unless @company_user || @company_users
  rescue ActiveRecord::RecordNotFound => e
    respond_to do |format|
      format.json { render json: { message: "Teammate was not found." }, status: :not_found }
      format.html { render 'shared/not_found', status: :not_found }
    end
  end

  def form_name_if_not_teammates(company_user)
    company_user.custom_form&.form_name unless company_user.custom_form&.form_name == 'Teammates'
  end

  def get_form_company_name(company_user)
    company_name_field = company_user.custom_form&.custom_form_fields&.find { |field| 
    field.name == 'company_name' }
    if company_name_field.present?
      company_user.custom_form_values.find { |value| value.custom_form_field_id == company_name_field.id }&.value_str
    end
  end

  def company_user_filtering_params
    params.permit(:search_terms, :page, :per_page, :format, :archived, :location, :all, :domain, :state, :sort_by, :form_id)
  end

  def company_user_params
    params.require(:company_user).permit(
      :id,
      :location_id,
      :helpdesk_agent,
      :mobile_phone,
      :work_phone,
      :work_phone_extension,
      :location_id,
      :work_phone_country_code,
      :work_phone_country_code_number,
      :mobile_phone_country_code,
      :mobile_phone_country_code_number,
      :mfa_enabled,
      :mfa_verified,
      :mfa_verified_at,
      :mfa_attempts_remaining,
      :locked
    )
  end

  def user_params
    params.require(:company_user).require(:user).permit(
      :full_name,
      :first_name,
      :last_name,
      :email,
      :address,
      :avatar,
      :avatar_url,
      :avatar_content_type,
      :avatar_filename,
      :avatar_image_data,
      :timezone,
      :password
    )
  end

  def authorize_company_user
    render('layouts/access_denied', layout: false) unless @company_user.try(:id) == scoped_company_user.try(:id) || expanded_privileges.find_by(name: 'CompanyUser', permission_type: 'write').present? || current_user.super_admin?
  end

  def assign_attributes
    @service = CognitoService.new
    @user = @company_user.user
    @original_email = @company_user.email
    @company_user.assign_attributes(company_user_params)
    @user.assign_attributes(user_params)
    @is_cognito_email = false
    if @user.will_save_change_to_email?
      if User.find_by_cache(email: @user['email'].downcase).present?
        return render json: [ "This email is already taken." ], status: :conflict
      end
      @user['temp_email'] = user_params['email'].downcase
      @user['email'] = @original_email
      @user['has_confirmed_email'] = false
      @email_changed = true
    end
    if !scoped_company.mfa_enabled && @company_user.will_save_change_to_mobile_phone?
      @company_user.required_device_verification = true
    end
  end

  def save_user_and_company_user
    is_cognito_user = @service.find_user_by_email(@original_email)
    @service.update_user_attributes(@original_email, @original_email, user_params['first_name'], user_params['last_name'], @company_user.company.guid, @company_user.company.name) if is_cognito_user
    @user.save!
    @user.create_email_update_log(scoped_company, current_user, 'CompanyUsersController') if @email_changed
    @company_user.save!
    bypass_sign_in @company_user.user if (user_params[:password].present? and @company_user.user.id == current_user.id) # to keep user logged in
    Devise::Mailer.email_changed(@user, @company_user.company).deliver if @email_changed && !scoped_company.is_sample_company?
  end

  def user_permissions
    @user_permissions ||= begin
      user_privileges = Permissions::Mapper.new(@company_user).call.permissions
      filtered_privileges = filter_permissions(user_privileges)

      filtered_privileges.keys.map do |name|
        {
          name: name
        }.merge(filtered_privileges[name])
      end
    end
  end

  def profile_page
    request.referer&.include?("/profile")
  end

  def remove_from_sla
    Sla::Email.where(target: [@company_user.contributor_id]).each do |email|
      targets = email.target - [@company_user.contributor_id]
      email.update_columns(target: targets)
    end
  end

  def company_users_avatars
    @company_users_avatars ||= begin
      @company_user_ids = @company_users.pluck(:id)
      cfvs = CustomFormValue.includes(custom_form_attachment: [
                                        :library_document, 
                                        { attachment_attachment: :blob }
                                      ])
                            .joins(:custom_form_field)
                            .where(module_id: @company_user_ids,
                                   custom_form_fields: { field_attribute_type: "avatar" },
                                   module_type: "CompanyUser")
                            .order(:order_position)
                            .group_by(&:module_id).values.map(&:first)
    end
  end

  def company_users_departments
    cfvs = CustomFormValue.joins(:custom_form_field)
                          .where(module_id: @company_user_ids,
                                 custom_form_fields: { name: "department" },
                                 module_type: "CompanyUser")
                          .group_by(&:module_id)
  end

  def company_users_titles
    cfvs = CustomFormValue.joins(:custom_form_field)
                          .where(module_id: @company_user_ids,
                                 custom_form_fields: { name: "title" },
                                 module_type: "CompanyUser")
                          .group_by(&:module_id)
  end

  def company_user_avatar(user_id)
    cfv = company_users_avatars.find { |c| c.module_id == user_id }&.as_json
    cfv[:attachment][:url] if cfv && cfv[:attachment]
  end
end
