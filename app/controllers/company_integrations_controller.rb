class CompanyIntegrationsController < AuthenticatedController
  include DiscoveryToolsLogs
  def index
    integrations_array = []

    company_integrations = scoped_company.company_integrations
    company_integrations.find_each do |company_integration|
      integration_object = {}
      integration_object[:id] = company_integration.id
      integration_object[:config_id] = company_integration.integrable.id
      integration_object[:name] = company_integration.integration.name
      integration_object[:sync_status] = company_integration.sync_status
      integration_object[:status] = company_integration.status
      integration_object[:last_synced_at] = company_integration.last_synced_at
      integration_object[:updated_at] = company_integration.updated_at
      integration_object[:error_message] = company_integration.error_message
      integration_object[:user_error_message] = company_integration.user_error_message
      integration_object[:active] = company_integration.active
      integration_object[:alert_info] = company_integration.alert_info

      integration_config = INTEGRATION_CONFIGS[company_integration.integrable_type]
      integrable = company_integration.integrable
      if integration_config
        integration_config.each do |key, value|
          if value.is_a?(Symbol)
            integration_object[key] = integrable.send(value)
          elsif value.is_a?(Proc)
            integration_object[key] = value.call(integrable)
          end

          mask_sensitive_fields(integration_object, key)
        end
      end

      integrations_array << integration_object
    end
    render json: { integrations: integrations_array }, status: :ok
  end

  def mask_sensitive_fields(integration_object, key)
    mask_fields = [:token, :password, :access_key, :client_id, :client_secret]
    if mask_fields.include?(key)
      integration_object[key] = mask_value(integration_object[key])
    end
  end

  def display_google_workspace_integration
    service_option = ServiceOption.find_by(service_name: "discovery_tools/google_workspace_integration")
    render json: { show_google_workspace_integration: !service_option.status }, status: :ok
  end

  def dismiss_delete_alert
    value_to_save = params[:delete].present? ? "deleted" : params[:count]
    company_integration = CompanyIntegration.find_by(id: params[:id])    
    alert_info = company_integration.alert_info || {}

    if alert_info.key?(params[:alert_key])
      alert_info[params[:alert_key]] = value_to_save
      company_integration.update(alert_info: alert_info)

      render json: { message: "Alert dismissed successfully" }, status: :ok
    else
      render json: { error: "Invalid alert key" }, status: :unprocessable_entity
    end
  end

  def dismissed_alerts
    intgs = current_company.company_integrations
              .joins(:integration)
              .where(
                "alert_info ->> 'new_asset' != 'false' OR
                alert_info ->> 'out_of_sync' != 'false' OR
                alert_info ->> 'not_reporting' != 'false' OR
                alert_info ->> 'failed' != 'false'"
              )
              .select('company_integrations.alert_info, integrations.name AS source_name', 'id')
        
    intgs = intgs.map do |intg|
      {
        alert_info: intg.alert_info,
        source: intg.source_name,
        id: intg.id
      }
    end

    render json: { dismissed_alerts: intgs }
  end

  COMMON_CONFIGS = {
    user_name: :name,
    token: :token
  }
  COMMON_AWS_CONFIG = {
    access_key: :access_key
  }

  INTEGRATION_CONFIGS = {
    "Integrations::Okta::Config" => COMMON_CONFIGS,
    "Integrations::Aws::Config" => COMMON_AWS_CONFIG,
    "Integrations::AwsAssets::Config" => COMMON_AWS_CONFIG,
    "Integrations::Meraki::Config" => {
      user_name: :name,
      token: :token,
      import_type: :import_type
    },
    "Integrations::JamfPro::Config" => {
      user_name: :username,
      password: :password
    },
    "Integrations::Mosyle::Config" => {
      user_name: :username,
      token: :access_token
    },
    "Integrations::Bill::Config" => {
      user_name: :user_name,
      password: :password
    },
    "Integrations::Ubiquiti::Config" => {
      user_name: ->(integrable) { integrable.ubiquiti_controllers.first.username },
      password: ->(integrable) { integrable.ubiquiti_controllers.first.password },
      import_type: :import_type
    },
    "Integrations::Microsoft::Config" => {
      ad_enabled: :ad_enabled
    },
    "Integrations::Kaseya::Config" => {
      kaseya_authenticated: ->(integrable) { integrable.access_token.present? },
      integrator_username: :integrator_username,
      import_type: :import_type
    },
    "Integrations::Kandji::Config" => {
      api_url: :api_url,
      token: :api_token
    },
    "Integrations::Expensify::Config" => {
      user_name: :user_id,
      password: :user_secret
    },
    "Integrations::OneLogin::Config" => {
      client_id: :client_id,
      token: :client_secret
    },
    "Integrations::Sophos::Config" => {
      client_id: :client_id,
      client_secret: :client_secret,
      import_type: :import_type
    }
  }
end
