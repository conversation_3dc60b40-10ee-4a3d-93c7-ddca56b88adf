module AutomatedTasks
  class OrderingsController < AuthenticatedController
    def update
      id_lookup = params[:tasks].each_with_object({}) do |task_params, hash|
        hash[task_params[:id].to_i] = task_params[:order]
      end

      if params[:is_assets_module] === true
        Assets::AutomatedTask.where(id: id_lookup.keys).update_all(
                                            ['"order" = CASE id ' +
                                              id_lookup.map { |id, order| "WHEN #{id} THEN #{order}" }.join(' ') +
                                            ' END']
                                          )
        output = AssetAutomatedTasks::JsonOutput.new(scoped_company)
        json = scoped_company.assets_automated_tasks.order(:order).map{ |task| output.json(task) }
      else
        AutomatedTasks::AutomatedTask.where(id: id_lookup.keys).update_all(
                                              ['"order" = CASE id ' +
                                                id_lookup.map { |id, order| "WHEN #{id} THEN #{order}" }.join(' ') +
                                              ' END']
                                            )
        output = AutomatedTasks::JsonOutput.new(scoped_company)
        json = scoped_workspace.automated_tasks.order(:order).map{ |task| output.json(task) }
      end

      render json: json
    end
  end
end
