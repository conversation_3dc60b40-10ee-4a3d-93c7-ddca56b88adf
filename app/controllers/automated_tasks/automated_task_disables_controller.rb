class AutomatedTasks::AutomatedTaskDisables<PERSON>ontroller < AuthenticatedController
  def update
    if !resource.force_disabled
      resource.disabled_at = nil
      set_log_params('enable')
      if resource.save
        render json: resource
      else
        render json: { error: 'Unable to enable that task.  Please try later.' }, status: :unprocessable_entity
      end
    else
      render json: { error: 'Please update the task actions to enable the task.' }, status: :bad_request
    end
  end

  def destroy
    if resource
      resource.disabled_at = Time.zone.now
      set_log_params('disable')
      if resource.save
        render json: resource
      else
        render json: { error: 'Unable to disable that task.  Please try later.' }, status: :unprocessable_entity
      end
    else
      render json: { message: 'Unable to find task.' }, status: :not_found
    end
  end

  private
  def resource
    if params[:mod] === 'managed_assets'
      @resource ||= Assets::AutomatedTask.find_by(id: params[:id])
    else
      @resource ||= AutomatedTasks::AutomatedTask.find_by(id: params[:id])
    end
  end

  def set_log_params(activity_type)
    resource.set_params(nil, scoped_company_user.id, params, activity_type)
  end
end
