class CurrentActionableAlertsController < AuthenticatedController
  def index
    page = params[:page]&.to_i || 1
    per_page = params[:per_page]&.to_i || 25

    if params[:module] === 'assets'
      actionable_alert = Assets::CurrentActionableAlerts.new(scoped_company_user)
    else
      actionable_alert = HelpTickets::CurrentActionableAlerts.new(scoped_workspace, scoped_company_user)
    end

    paginated_alerts = actionable_alert.paginated_resources(page, per_page)

    render json: {
      alerts: paginated_alerts,
      total_count: actionable_alert.total_count,
      page_count: (actionable_alert.total_count / per_page.to_f).ceil
    }
  end
end
