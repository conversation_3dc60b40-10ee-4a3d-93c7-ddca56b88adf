class Assets::AssetTaskActionTypesController < ApplicationController
  def index
    render json: actions
  end

  def details
    scope = if params[:module]
              Assets::ActionType.where(module: params[:module])
            elsif params[:model]
              Assets::ActionType.where(model: Array(params[:model]).uniq)
            else
              []
            end
  
    scope.map do |a|
      {
        id: a.id,
        name: a.name.gsub(/[\[\]]/, ''),
        actionClass: a.action_class,
        model: a.model,
      }
    end
  end

  def actions
    if params['model'] && params['model'].uniq.count > 1
      common_action_threshold = params['model'].uniq.count - 1
      common_actions = details.group_by { |action| action[:actionClass] }.values.map do |value|
        value.first if value.count > common_action_threshold
      end.compact
      return common_actions
    end
    return details
  end
end
