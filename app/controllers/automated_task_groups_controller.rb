class AutomatedTaskGroupsController < AuthenticatedController
  before_action :set_grouped_task, only: [:update, :destroy]

  def index
    respond_to do |format|
      format.json { render json: grouped_tasks.as_json }
    end
  end

  def create
    @grouped_task = scoped_workspace.automated_task_groups.new(grouped_task_params)
    @grouped_task.company_id = current_company.id

    if @grouped_task.save
      render json: @grouped_task, status: :ok
    else
      render json: { errors: @grouped_task.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @grouped_task.update(grouped_task_params)
      render json: @grouped_task
    else
      render json: { errors: @grouped_task.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def destroy
    @grouped_task.destroy
    render json: {}, status: :ok
  end

  private

  def grouped_tasks
    @grouped_tasks ||= scoped_workspace.automated_task_groups.order(:name)
  end

  def set_grouped_task
    @grouped_task = scoped_workspace.automated_task_groups.find(params[:id])
  end

  def grouped_task_params
    params.require(:automated_task_group).permit(:name)
  end
end
