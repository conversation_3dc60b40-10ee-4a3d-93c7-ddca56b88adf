class AutomatedTasksController < ModulesController
  include MultiCompany::GeneralScoping

  before_action :need_font_awesome
  before_action :check_task, only: [:show, :destroy, :edit, :update]
  before_action :authorize_write, only: [:edit, :new]

  def index
    respond_to do |format|
      format.json { render json: tasks_json }
    end
  end

  def show
    respond_to do |format|
      format.json { render json: task_json }
    end
  end

  def destroy
    respond_to do |format|
      set_log_params(resource)
      if resource.destroy
        format.json { render json: task_json }
      else
        format.json { render json: { message: resource.errors.full_messages.to_sentence } }
      end
    end
  end

  def create
    contributor = scoped_company_user.contributor
    existing_orders = scoped_workspace.automated_tasks.pluck(:order) || []
    new_order = existing_orders.any? ? existing_orders.max + 1 : 1
    existing_serial_numbers = scoped_workspace.automated_tasks.pluck(:serial_number)
    new_serial = existing_serial_numbers.any? ? existing_serial_numbers.max + 1 : 1
    task = AutomatedTasks::AutomatedTask.new(
      contributor: contributor,
      name: task_name,
      workspace_id: scoped_workspace&.id,
      order: new_order,
      serial_number: new_serial,
      automated_task_group_id: selected_task_group_id
    )
    set_log_params(task)
    creator = AutomatedTasks::Create.new(task)
    task = creator.create(task_params)
    respond_to do |format|
      format.json { render json: task }
    end
  end

  def update
    task = scoped_workspace.automated_tasks.find_by(id: params[:id])
    if task
      set_log_params(task)
      updater = AutomatedTasks::Update.new(task)
      task = updater.update(task_params)
      task.update_columns(automated_task_group_id: selected_task_group_id)
      respond_to do |format|
        if task[:error_message]
          format.json { render json: { message: task[:error_message] }, status: :unprocessable_entity }
        else
          format.json { render json: task }
        end
      end
    else
      respond_to do |format|
        format.json { render json: { message: "Unable to find task" }, status: :not_found }
      end
    end
  end

  def reset_task
    task = scoped_workspace.automated_tasks.find_by(id: params[:id])
    company_mailer = task.company_mailer&.event

    reset = case company_mailer
            when "assigned_to_anyone", "assigned_to_me", "priority_changed", "status_changed"
              reset_for_assigned_or_status_changed(task, company_mailer)
            else
              reset_for_other_events(task, company_mailer)
            end
    render json: reset
  end

  def update_at_assign_value
    task = scoped_workspace.automated_tasks.find_by(id: params[:id])
    if task.present?
      task.update_columns(assign_values: !task.assign_values)
    end
  end

  private

  def reset_for_assigned_or_status_changed(task, company_mailer)
    event_detail_value = task.task_events.first.event_details.first.value
    custom_form = CustomForm.find(JSON.parse(event_detail_value)['custom_forms'].first['id'])
    generator_class = get_generator_class(company_mailer)
    generator_class.new(company_mailer, custom_form).value_as_json
  end

  def reset_for_other_events(task, company_mailer)
    generator_class = get_generator_class(company_mailer)
    generator_class.new(task.company_mailer).value_as_json
  end

  def get_generator_class(company_mailer)
    case company_mailer
    when "assigned_to_anyone"
      AutomatedTasks::Generators::AssignedToAnyone
    when "assigned_to_me"
      AutomatedTasks::Generators::AssignedToMe
    when "priority_changed"
      AutomatedTasks::Generators::PriorityChanged
    when "status_changed"
      AutomatedTasks::Generators::StatusChanged
    when "note_added"
      AutomatedTasks::Generators::CommentAdded
    when "attachment_added"
      AutomatedTasks::Generators::AttachmentAdded
    when "ticket_created"
      AutomatedTasks::Generators::TicketCreated
    end
  end

  def need_font_awesome
    @font_awesome_needed = true
  end

  def task_params
    @task_params ||= params[:task].as_json
  end

  def task_name
    params["task"]["name"]
  end

  def load_resources
    resource_list = AutomatedTasks::AutomatedTask.joins(task_events: [:event_type, { event_details: :event_subject_type }])
                                                 .joins(task_actions: :action_type)
                                                 .includes(:company_mailer,
                                                           :workspace,
                                                           :custom_form,
                                                           task_events: [:event_type, { event_details: [:event_subject_type, :children] }],
                                                           task_actions: [:action_type])
                                                 .order(:order)
    resource_list = resource_list.where(workspace_id: scoped_workspace&.id)
    resource_list = resource_list.where('automated_tasks_event_types.module' => params[:mod]) if params[:mod]
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      resource_list = resource_list.where('automated_tasks_automated_tasks.name ILIKE :search OR 
                                           automated_tasks_automated_tasks.serial_number::text ILIKE :search OR 
                                           regexp_replace(automated_tasks_event_types.name, \'[{}\\[\\]/]\', \'\', \'g\') ILIKE :search OR 
                                           regexp_replace(automated_tasks_event_subject_types.name, \'[{}\\[\\]/]\', \'\', \'g\') ILIKE :search OR 
                                           regexp_replace(automated_tasks_action_types.name, \'[{}\\[\\]/]\', \'\', \'g\') ILIKE :search', search: search_term)  
    end
    resource_list.distinct
  end

  def resources
    @resources ||= load_resources
  end

  def tasks_json
    resources.map do |task|
      task_json(task)
    end
  end

  def resource
    @resource ||= AutomatedTasks::AutomatedTask.find_by(id: params[:id])
  end

  def task_json(task = resource)
    AutomatedTasks::JsonOutput.new(scoped_company).json(task)
  end

  def check_task
    if resource.nil?
      render json: { message: "Task was not found" }, status: :not_found
    elsif scoped_workspace&.id != resource.workspace_id
      render json: { message: resource.errors.full_messages.to_sentence }
    end
  end

  def privilege_name
    "HelpTicket"
  end

  def set_log_params(task)
    task.set_params(nil, scoped_company_user.id, params)
  end

  def selected_task_group_id
    params.dig(:task, :task_group, :id)
  end
end
