class Integrations::Sophos::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper
  include ReadReplicaDb

  before_action :ensure_config, only: [:destroy, :deactivate]

  def create
    token_detail = sophos_service.token(auth_params)
    if token_detail['access_token'].present?
      @sophos_config = Integrations::Sophos::Config.find_or_initialize_by(company_id: @current_company.id)
      @sophos_config.assign_attributes(
        client_id: auth_params['client_id'],
        client_secret: auth_params['client_secret'],
        import_type: auth_params['import_type'],
        access_token: token_detail['access_token'],
        expires_in: token_detail['expires_in'],
        company_user_id: current_company_user&.id
      )

      if @sophos_config.save
        create_or_update_sophos_tenant_record
        Integrations::Sophos::SyncDataWorker.perform_async(@sophos_config.id, true, false, current_company_user&.id)
        render json: { message: 'Sophos configuration successfully saved.' }, status: :ok
      else
        render json: { message: @sophos_config.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    else
      render json: { message: 'Invalid credentials.' }, status: :not_found
    end
  end

  def update_import_type
    sophos_config = set_read_replica_db do
      Integrations::Sophos::Config.find_by_id(params[:id])
    end

    if sophos_config
      sophos_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  def destroy
    company_integration = @sophos_config.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: { message: 'Sorry, there was an error deleting the integration. Please try again.' }, status: :unprocessable_entity
    end
  end

  def deactivate
    if @sophos_config.company_integration
      @sophos_config.company_integration.update(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: 'Integration deactivated successfully' }, status: :ok
    else
      render json: { message: 'Sorry, there was an error deactivating the integration. Please try again.' }, status: :unprocessable_entity
    end
  end

  private

  def auth_params
    params.require(:sophos_config).permit(:client_id, :client_secret, :import_type)
  end

  def sophos_service
    @sophos_service ||= Integrations::Sophos::FetchData.new(@current_company.id)
  end

  def ensure_config
    @sophos_config ||= Integrations::Sophos::Config.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { message: 'Config was not found.' }, status: :not_found }
    end
  end

  def create_or_update_sophos_tenant_record
    sophos_service = Integrations::Sophos::FetchData.new(@current_company.id, @sophos_config)
    tenant_info = sophos_service.fetch_tenant_info(@sophos_config.access_token)

    if @sophos_config.sophos_tenant.present?
      @sophos_config.sophos_tenant.update(
        tenant_id: tenant_info['id'],
        data_region_url: tenant_info.dig('apiHosts', 'dataRegion')
      )
    else
      @sophos_config.create_sophos_tenant(
        tenant_id: tenant_info['id'],
        data_region_url: tenant_info.dig('apiHosts', 'dataRegion')
      )
    end
  end
end
