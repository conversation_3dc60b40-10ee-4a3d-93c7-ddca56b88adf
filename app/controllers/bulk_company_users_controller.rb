class BulkCompanyUsersController < AuthenticatedController
  before_action :set_resources
  attr_accessor :notes

  def bulk_archive
    exclude_current_company_user
    exclude_last_admin
    if @ids.present?
      company_users = scoped_company.company_users.where(id: @ids)
      company_users.where(id: @ids).each do |cu|
        cu.activity_owner_id = scoped_company_user.id
        cu.skip_assignment_callback = true
        cu.update(invite_token: nil, granted_access_at: nil, archived_at: DateTime.now, invitation_due_at: nil)
      end
      remove_associated_assets(company_users)
      render json: { message: notes }, status: :ok
    else
      render json: { message: notes }, status: :unprocessable_entity
    end
  end

  def remove_associated_assets(company_users)
    contributor_ids = company_users.map(&:contributor_id).compact
    return if contributor_ids.empty?

    used_by_records = AssignmentInformation.where(used_by_contributor_id: contributor_ids)
    managed_by_records = AssignmentInformation.where(managed_by_contributor_id: contributor_ids)

    used_by_records.update_all(used_by_contributor_id: nil) if used_by_records.exists?
    managed_by_records.update_all(managed_by_contributor_id: nil) if managed_by_records.exists?
  end

  def bulk_delete
    exclude_current_company_user
    exclude_last_admin
    if @ids.present?
      company_users = scoped_company.company_users.where(id: @ids)
      company_users.each do |user|
        user.custom_form_values.map { |value| value.skip_event_trigger = true }
      end
      disable_dependant_automated_tasks(company_users)
      company_users.destroy_all
      render json: { message: notes }, status: :ok
    else
      render json: { message: notes }, status: :unprocessable_entity
    end
  end

  def disable_dependant_automated_tasks(company_users)
    contributor_ids = company_users.pluck(:contributor_id)
    AutomatedTasks::DisableDependantTasksWorker.perform_async(workspace_ids, contributor_ids, [], false, is_bulk_delete = true, scoped_company.id)
  end

  def workspace_ids
    @workspace_ids ||= scoped_company.workspaces.pluck(:id)
  end

  def bulk_unarchive
    if @ids.present?
      scoped_company.company_users.where(id: @ids).where.not(archived_at: nil).each do |company_user|
        company_user.activity_owner_id = scoped_company_user.id
        company_user.update(archived_at: nil)
      end
      render json: { message: notes }, status: :ok and return
    else
      render json: { message: notes }, status: :unprocessable_entity and return
    end
  end

  def bulk_assign_location
    if @ids.present?
      scoped_company.company_users.where(id: @ids).each do |company_user|
        custom_form = company_user.custom_form
        field = find_location_field(custom_form)

        if field.present?
          form_value = CustomFormValue.find_or_initialize_by(
            module_type: 'CompanyUser',
            module_id: company_user.id,
            value_int: params[:location_id],
            custom_form: custom_form,
            company: scoped_company,
            custom_form_field: field
          )

          unless form_value.persisted?
            form_value.save!
            activity_params = {
              owner_id: scoped_company_user.id,
              activity_type: CompanyUserActivity.activity_types["added"],
              data: { field_type: "location_list", current_value: params[:location_id], activity_label: "Location", previous_value: ""}
            }
            company_user.activities.create(activity_params)
          end
        end
      end
      render json: { message: notes }, status: :ok and return
    else
      render json: { message: notes }, status: :unprocessable_entity and return
    end
  end

  def bulk_assign_group
    if @ids.present? && @group.present?
      scoped_company.company_users.where(id: @ids).each do |company_user|
        group_member = @group.group_members.find_or_initialize_by(contributor_id: company_user.contributor_id)

        unless group_member.persisted?
          group_member.save!
          activity_params = {
            owner_id: scoped_company_user.id,
            activity_type: CompanyUserActivity.activity_types["added"],
            data: { field_type: "group", current_value: params[:group_id], activity_label: "Group", previous_value: ""}
          }
          company_user.activities.create(activity_params)
        end
      end
      render json: { message: "Group has been assigned to company users successfully." }, status: :ok
    else
      render json: { message: "An unknown error has occured while assigning group to company users." }, status: :unprocessable_entity
    end
  end

  def find_location_field(custom_form)
    field = custom_form.custom_form_fields.find_by(name: 'location')
    return field if field.present?
    custom_form.custom_form_fields.find_by(field_attribute_type: "location_list")
  end

  private

  def set_resources
    @notes = ""
    @ids = params[:company_users_ids]
    if params[:group_id].present?
      @group = Group.find(params[:group_id])
    end
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: "Group was not found." }, status: :not_found
  end
  
  def exclude_current_company_user
    if @ids.include? scoped_company_user.id
      @ids.delete(scoped_company_user.id)
      @notes = "You cannot remove yourself."
    end
  end

  def exclude_last_admin
    admins = scoped_company.admin_company_users.active
    if admins.count == 1 && @ids.include?(admins.first&.id)
      @ids.delete(admins.first.id)
      @notes = "You cannot remove the last admin."
    end
  end
end
