class EventLogsController < AuthenticatedController
  include MultiCompany::GeneralScoping

  def index
    @sql = """
      #{select_sql}
      #{join_sql}
      #{where_sql}
      #{order_by_sql}
      #{paginate_sql}
    """
    
    query = ActiveRecord::Base.send(:sanitize_sql, sql_params)
    results = ActiveRecord::Base.connection.execute(query)

    results = results.uniq {|r| r['data']}

    activity = EventLog.new
    activities = results.map do |result|
      activity_type = result['activity_type']
      activity.data = result['data']
      activity.activity_type = result['activity_type']
      activity.created_at = DateTime.parse(result['created_at'].to_s)
      activity.updated_at = DateTime.parse(result['updated_at'].to_s)

      result.merge({
        "data" => activity.data,
        "activity_type" => activity.activity_type,
        "created_at" => activity.created_at,
        "updated_at" => activity.updated_at,
      })
    end

    activities.each do |activity|
      activity_type = activity['activity_type']
      data = activity['data']
    end

    render json: {
      custom_form_activities: activities,
      owner_names_list: owner_names_list,
      total: @activity_count,
      page_count: @page_count
    }, status: :ok
  end

  def select_sql
    """
      SELECT
        el.*,
        u.first_name,
        u.last_name
    """
  end

  def join_sql
    sql = """
      FROM event_logs AS el
        #{company_and_workspace_join_sql}
        #{company_user_join}
        #{user_join}
    """
    sql
  end

  def company_and_workspace_join_sql
    if params[:filter] == "helpdesk" && scoped_workspace.present?
      'INNER JOIN workspaces as resource ON resource.id = el.workspace_id'
    else
     'INNER JOIN companies as resource ON resource.id = el.company_id'
    end
  end

  def company_user_join
    'LEFT OUTER JOIN company_users AS cu ON cu.id = el.owner_id'
  end

  def user_join
    'LEFT OUTER JOIN users AS u ON u.id = cu.user_id'
  end

  def where_sql
    sql = 'WHERE resource.id in (?)'
    sql += ' AND el.module_name = ?'
    sql += ' AND el.entity_type IN (?)'
    sql += ' AND el.owner_id IN (?)'
    sql
  end

  def order_by_sql
    if params[:sort_field].present?
      " ORDER BY u.first_name #{params[:sort_direction]}"
    else
      ' ORDER BY el.created_at desc'
    end
  end

  def paginate_sql
    'LIMIT ? OFFSET ?'
  end

  def sql_params
    sql_params = params[:filter] == "helpdesk" && scoped_workspace.present? ? [scoped_workspace.id] : [scoped_company.id]
    per_page = (params[:per_page] || 25).to_f
    @page_count = (activity_count / per_page).ceil
    page = (params[:page] || 1).to_f
    offset = (page - 1) * per_page
    sql_params << EventLog.module_names[params[:filter]]
    sql_params << activity_type if activity_type
    sql_params << owner_ids
    sql_params << per_page
    sql_params << offset
    sql_params.unshift @sql
  end

  private

  def activity_type
    if params[:activity_type].present? && params[:activity_type] == 'all'
      return params[:filter] == "helpdesk" ? all_helpdesk_module_logs.pluck(:entity_type) : ['CustomForm', 'Group']
    elsif params[:activity_type].present?
      return params[:activity_type]
    end
  end

  def owner_ids
    if params[:changed_by] == 'all'
      if params[:filter] == "helpdesk"
        all_helpdesk_module_logs.pluck(:owner_id).uniq
      elsif params[:filter] == 'asset'
        all_asset_module_logs.pluck(:owner_id).uniq
      else
        all_company_module_logs.pluck(:owner_id).uniq
      end
    else
      params[:changed_by]
    end
  end

  def activity_count
    if params[:filter] == "helpdesk"
      helpdesk_activities_count
    elsif params[:filter] == "location"
      location_activities_count
    elsif params[:filter] == "company_user"
      company_user_activities_count
    elsif params[:filter] == "asset"
      asset_activities_count
    end
  end

  def helpdesk_activities_count
    if params[:activity_type] == "all" && params[:changed_by] == "all"
      all_helpdesk_module_logs.count
    elsif params[:activity_type] == "all"
      filtered_helpdesk_logs_with_owner.count
    elsif params[:changed_by] == "all"
      filtered_logs_with_entity_type.count
    else
      filtered_helpdesk_module_logs.count
    end
  end

  def asset_activities_count
    return unless params[:changed_by] == "all"
  
    @asset_activities_count ||= scoped_company.event_logs.where(
      module_name: 'asset',
      entity_type: params[:activity_type]
    ).count
  end  

  def location_activities_count
    @location_activities_count ||= scoped_company.event_logs.where(
      module_name: 'location'
    ).count
  end

  def company_user_activities_count
    if params[:activity_type] == "all" && params[:changed_by] == "all"
      all_company_module_logs.count
    else
      filtered_company_logs_with_owner.count
    end
  end

  def all_company_module_logs
    @all_company_logs ||= scoped_company.event_logs.where(
      module_name: ['company_user'],
      entity_type: ['CustomForm', 'Group']
    )
  end

  def all_asset_module_logs
    @all_asset_logs ||= scoped_company.event_logs.where(
      module_name: 'asset',
      entity_type: ['AssetAutomatedTask']
    )
  end

  def filtered_company_logs_with_owner
    @filtered_company_logs_with_owner ||= all_company_module_logs.where(owner_id: params[:changed_by])
  end

  def all_helpdesk_module_logs
    @all_helpdesk_logs ||= scoped_company.event_logs.where(
      module_name: 'helpdesk',
      workspace_id: scoped_workspace.id
    )
  end

  def filtered_helpdesk_module_logs
    @filtered_helpdesk_module_logs ||= scoped_company.event_logs.where(
      module_name: 'helpdesk',
      workspace_id: scoped_workspace.id,
      entity_type: params[:activity_type],
      owner_id: params[:changed_by]
    )
  end

  def filtered_helpdesk_logs_with_owner
    @filtered_helpdesk_logs_with_owner ||= scoped_company.event_logs.where(
      module_name: 'helpdesk',
      workspace_id: scoped_workspace.id,
      owner_id: params[:changed_by]
    )
  end

  def filtered_logs_with_entity_type
    @filtered_logs_with_entity_type ||= scoped_company.event_logs.where(
      module_name: 'helpdesk',
      workspace_id: scoped_workspace.id,
      entity_type: params[:activity_type]
    )
  end

  def owner_names_list
    owner_type_event_logs.map do |event_log|
      if event_log.owner&.super_admin?
        next { id: event_log.owner_id, name: "System", value: "System" }
      elsif event_log.data && event_log.data.key?("owner_name")
        next { id: event_log.owner_id, name: event_log.data["owner_name"], value: event_log.data["owner_name"] }
      end

      { id: event_log.owner_id, name: event_log.owner&.name, value: event_log.owner&.name }
    end
  end

  def owner_type_event_logs
    if params[:filter] == "helpdesk"
      scoped_company.event_logs
        .where(workspace: scoped_workspace)
        .group(:owner_id, :id)
        .select('DISTINCT ON (owner_id) *')
        .order(:owner_id)
    elsif params[:filter] == "asset"
      all_asset_module_logs
        .group(:owner_id, :id)
        .select('DISTINCT ON (owner_id) *')
        .order(:owner_id)
    else
      all_company_module_logs
        .group(:owner_id, :id)
        .select('DISTINCT ON (owner_id) *')
        .order(:owner_id)
    end
  end
end
