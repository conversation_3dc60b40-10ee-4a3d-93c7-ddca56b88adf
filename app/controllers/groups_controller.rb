class GroupsController < ModulesController
  include CreateGroupMembersHelper
  include ApplicationHelper
  before_action :set_privilege

  before_action :resource, only: [:show, :edit, :update, :destroy]
  before_action :new_resource, only: [:create]
  before_action :authorize_write, only: [:create, :update, :destroy]
  before_action :authorize_read, only: [:index, :show], unless: Proc.new { !request.format.json? }

  def index
    respond_to do |format|
      format.html { render 'companies/show' }
      format.json { render json: { groups: resources, groups_count: resources_count }, status: :ok }
    end
  end

  def show
    respond_to do |format|
      format.html { render 'companies/show' }
      format.json { render json: to_json(resource), status: :ok }
    end
  end

  def create
    ActiveRecord::Base.transaction do
      if new_resource.save
        # Because this is wrapped in a transaction, the after commit doesn't fire til after
        # the transaction is complete, which for us is too late.  :( 
        new_resource.create_contributor
        create_privileges
        members_ids = resource_params[:members].pluck(:id)
        create_members(new_resource, [], members_ids)
        save_related_items if params[:group][:universal_links].present?
        update_expanded_privileges
        render json: new_resource
      else
        render json: { message: "#{new_resource.errors.full_messages.to_sentence}" }, status: :unprocessable_entity
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  def destroy
    @group = resource
    if @group&.destroy
      disable_dependant_automated_tasks
      render json: {}, status: :ok
    else
      render json: { message: resource.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def group_dependent_automated_task
    group = Group.find_by(id: params[:id])
    helpdesk_dependent_tasks, assets_dependant_tasks = AutomatedTasks::DependentAutomatedTasksService.new(workspace_ids, [group&.contributor_id], [], false, false, false, true, scoped_company.id).call
    tasks = {
      dependant_tasks: helpdesk_dependent_tasks,
      assets_dependant_tasks: assets_dependant_tasks
    }
    render json: { tasks: tasks }, status: :ok
  end

  def update
    ActiveRecord::Base.transaction do
      if params[:permissions_changed] && resource[:name] == 'Admins'
        render json: { message: "You cannot change Admins group permissions" }, status: :conflict
      elsif !is_last_admin?(resource)
        new_resource.name = resource_params[:name]
        previous_contributor_ids = new_resource.group_members.pluck(:contributor_id)
        new_contributor_ids = resource_params[:members].pluck(:id)
        if new_resource.save
          create_privileges if params[:permissions_changed]
          unless new_contributor_ids.to_set == previous_contributor_ids.to_set
            delete_sample_company_users(resource)
            create_members(resource, previous_contributor_ids, new_contributor_ids)
          end
          update_expanded_privileges if params[:permissions_changed]
          render json: resource, status: :ok
        else
          render json: { message: "#{resource.errors.full_messages.to_sentence}" }, status: :unprocessable_entity
        end
      else
        render json: { message: "Sorry, you cannot remove the last admin" }, status: :conflict
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      render json: { message: e.message }, status: :unprocessable_entity
      raise e
    end
  end

  def admins_group
    last_admin = scoped_company.groups
                                  .joins(:group_members)
                                  .where(name: 'Admins')
                                  .group('groups.id')
                                  .having('COUNT(group_members.id) = 1')
                                  .exists?

    render json: { is_last_admin: last_admin }, status: :ok
  end

  def update_expanded_privileges
    UpdateExpandedPrivilegesWorker.new.perform(new_resource.contributor_id)
    UpdateArticleExpandedPrivilegesWorker.new.perform(new_resource.contributor_id)
  end

  def delete_sample_company_users resource
    is_resource_admin_member = scoped_company.groups.find_by(name: "Admins").contains?(resource.contributor)
    if (resource.name == "Admins" || is_resource_admin_member) && resource_params[:members]
      new_ids = resource_params[:members].pluck(:id)
      old_ids = resource.group_members.pluck(:contributor_id)
      removed_ids = old_ids - new_ids
      removed_ids.each { |id| resource.group_members.find_by(contributor_id: id)&.delete_access_to_sample_company }
    end
  end

  def save_related_items
    links_array = params[:group][:universal_links].map { |ul| ul['target'] }
    LinkableService.new(@resource).save_related_items(links_array)
  end

  private

  def resources
    page_size = params[:page_size].try(:to_f) || 25.0
    my_list = scoped_company.groups.includes(:company, :linkable, :workspace, { contributor: :privileges }, group_members: [ contributor: [:group, company_user: :user]])
    my_list = my_list.joins(:privileges).where('privileges.name = ?', params[:privilege]) if params[:privilege]
    my_list = my_list.where("name ilike ?", "%#{params[:search_terms]}%") if params[:search_terms].present?
    my_list = my_list.paginate(page: params[:page], per_page: page_size)
    everyone_group = my_list.find_by(include_all: true) || scoped_company.groups.find_by(include_all: true) #There is only one everyone group in a company
    my_list = my_list.reject { |g| !g.workspace_id.nil? && !has_subscription?("it_help_desk") }
    my_list = my_list.map { |g| to_json(g, everyone_group) }
  end

  def to_json(group, everyone_group = nil)
    {
      id: group.id,
      name: group.name,
      permissions: privileges_json(group, everyone_group),
      contributor: group.contributor,
      include_all: group.include_all,
      members: members_json(group),
      linkable_id: group.linkable.id,
      default: group.default,
      parent_company_group_id: group.parent_company_group_id,
      parent_company_name: scoped_company.reseller_company&.name,
      workspace_name: group.workspace&.name
    }
  end

  def members_json(group)
    group_members = group.group_members.select do |member|
      is_workspace_group = member.contributor.contributor_type == "Group" && !member.contributor.group.workspace_id.nil?
      if is_workspace_group
        has_subscription?("it_help_desk")
      else
        member.contributor.contributor_type == "Group" || !member.contributor.company_user&.is_sample_company_user
      end
    end

    if params[:index_page]
      group_members.map do |member|
        { name: member.contributor.name }
      end.compact
    else
      group_members.map do |member|
        contributor = member.contributor
        company_user = contributor.company_user

        {
          id: contributor.id,
          name: contributor.name,
          email: company_user&.email,
          type: contributor.contributor_type,
          avatar_thumb_url: company_user&.avatar_url
        }
      end.compact
    end
  end

  def privileges_json(group, everyone_group)
    mapping = Permissions::Reducer.new(group, params[:index_page], everyone_group).calculate.permissions
    filtered_privileges = filter_permissions(mapping)
    map_permissions(filtered_privileges)
  end

  def map_permissions(perms)
    group_permissions ||= begin
      perms.keys.map do |name|
        {
          name: name
        }.merge(perms[name])
      end
    end
  end

  def resources_count
    scoped_company.groups.size
  end

  def create_privileges
    resource.clear_permissions if resource.id
    privileges = []
    if params[:group][:permissions]
      params[:group][:permissions].each do |permission|
        if permission.present?
          if permission[:name] != 'HelpTicket'
            # there should never be more than one now
            if permission[:root]
              priv = permission[:root][0]
            elsif permission[:permission_types]
              priv = permission[:permission_types][0]
            end

            privileges << Privilege.new(
              contributor_id: resource.contributor_id,
              name: permission[:name],
              workspace_id: permission[:workspace_id],
              permission_type: priv
            )
          elsif permission[:name] == 'HelpTicket'
            permission.keys.each do |id|
              if id.to_i > 0
                permission[id].each do |priv|
                  privileges << Privilege.new(
                    contributor_id: resource.contributor_id,
                    name: 'HelpTicket',
                    workspace_id: id.to_i,
                    permission_type: priv
                  )
                end
              end
            end
          end
        end
      end
      Privilege.import privileges
      privileges.each do |priv|
        priv.run_callbacks(:save) { true }
      end
    end
  end

  def create_members(res, contributor_ids = [], new_contributor_ids = [])
    contributors_to_remove = contributor_ids - new_contributor_ids
    contributors_to_add = new_contributor_ids - contributor_ids

    if res.id
      group_members = res.group_members
      group_members.each do |gm|
        if contributors_to_remove.include?(gm.contributor_id)
          gm.skip_field_permissions = true
          gm.destroy!
        end
      end
    end

    contributors_to_add.each do |contributor_id|
      group_member = GroupMember.find_or_initialize_by(group_id: res.id, contributor_id: contributor_id)
      group_member.skip_field_permissions = true if params['action'] == 'update' && contributor_ids.include?(contributor_id)
      group_member.save
    end
    copy_members_to_child_group(res)
  end

  def copy_members_to_child_group(res)
    res.reload
    child_groups = Group.includes(company: :custom_forms).where(parent_company_group_id: res.id)
    return unless child_groups.present?
    child_company_form = child_groups.last.company.custom_forms.find do |form|
      form.company_module == "company_user" && 
      (form.form_name == "Partners" || form.default)
    end
    child_groups.each do |c_group|
      next if c_group.contributor_id == res.contributor_id
      c_group.group_members.destroy_all
      create_group_members(c_group.company, c_group, res, child_company_form.id) if child_company_form
    end
  end

  def is_last_admin? res
    ids = resource_params[:members].map{|m| m['id']}.compact
    active_company_users = CompanyUser.where(contributor_id: ids).where.not(granted_access_at: nil)
    resource_params[:name] == "Admins" && res.company.admin_company_users.active.count == 1 && active_company_users.blank?
  end

  def new_resource
    @resource ||= scoped_company.groups.new(name: resource_params_limited[:name])
  end

  def resource
    @resource ||= scoped_company.groups.find(params[:id])
  rescue ActiveRecord::RecordNotFound => e
    respond_to do |format|
      format.json { render json: { message: "Group was not found." }, status: :not_found }
      format.html { render 'shared/not_found', status: :not_found }
    end
    nil
  end

  def resource_params_limited
    params.require(:group).permit(:id,
                                  :name,
                                  permissions: [
                                    :name,
                                    :workspace_id,
                                    permission_types: []
                                  ],
                                  members: [
                                    :id,
                                    :name
                                  ]
    )
  end

  def resource_params_unlimited
    params.require(:group).permit(:id,
                                  :name,
                                  permissions: [
                                    :name,
                                    :workspace_id,
                                    permission_types: []
                                  ],
                                  members: [
                                    :id,
                                    :name
                                  ]
    )
  end

  def resource_params
    if resource.include_all && resource.default
      resource_params_limited
    else
      resource_params_unlimited
    end
  end

  def set_privilege
    if params['company_module'] == 'helpdesk'
      self.class.set_privilege_name('HelpTicket')
    else
      self.class.set_privilege_name('CompanyUser')
    end
  end
  
  def workspace_ids
    @workspace_ids ||= scoped_company.workspaces.pluck(:id)
  end

  def disable_dependant_automated_tasks
    AutomatedTasks::DisableDependantTasksWorker.perform_async(workspace_ids, [@group&.contributor_id], [], false, true, scoped_company.id)
  end
end
