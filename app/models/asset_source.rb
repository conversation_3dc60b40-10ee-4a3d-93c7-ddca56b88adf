class AssetSource < ApplicationRecord
	belongs_to :discovered_asset
	belongs_to :managed_asset
	belongs_to :company_integration

	validates_uniqueness_of :discovered_asset_id, scope: :source, 
	                        :allow_nil => true, :allow_blank => true,
	                        message: "has already been taken with this source."
	validates_uniqueness_of :managed_asset_id, scope: :source,
													conditions: -> { where.not(source: :merged_assets) },
	                        :allow_nil => true, :allow_blank => true,
	                        message: "has already been taken with this source."

	enum source: [:probe, :selfonboarding, :agent, :meraki, :ubiquiti, :manually_added, :uploaded, :aws, :azure, :google, :merged_assets, :ms_intune, :azure_ad_devices, :kaseya, :kandji, :jamf_pro, :mosyle, :google_workspace, :sophos]
end
