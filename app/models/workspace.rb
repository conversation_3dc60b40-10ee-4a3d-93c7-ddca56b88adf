class Workspace < ApplicationRecord
  include UniquelyIdentified
  include DefaultEmailTemplates
  include HelpTicketMethods
  include HandleCompanyCacheKeys
  include QuickViewFilterUpdate
  include CompanyCache

  attr_accessor :current_user_id, :params, :is_first_workspace

  belongs_to :company

  has_one :helpdesk_email_format, dependent: :destroy
  has_many :blocked_entities, dependent: :destroy
  has_many :articles, dependent: :destroy
  has_many :privileges, dependent: :destroy
  has_many :expanded_privileges, dependent: :destroy
  has_many :helpdesk_settings, dependent: :destroy
  before_destroy :destroy_dependent_help_tickets
  has_many :help_tickets
  has_many :help_ticket_drafts
  has_many :company_mailers, dependent: :destroy
  has_many :library_documents, dependent: :destroy
  has_many :ticket_emails, dependent: :destroy
  has_many :automated_tasks, class_name: 'AutomatedTasks::AutomatedTask', dependent: :destroy
  has_many :execution_logs, class_name: "AutomatedTasks::ExecutionLog", dependent: :destroy
  has_many :ticket_list_columns, dependent: :destroy
  has_many :actionable_alerts, dependent: :destroy
  has_many :custom_reports, dependent: :destroy
  has_many :slack_configs, class_name: 'Integrations::Slack::Config', dependent: :destroy
  has_many :ms_teams_configs, class_name: 'Integrations::MsTeams::Config', dependent: :destroy
  has_many :custom_forms, dependent: :destroy
  has_many :company_users, foreign_key: :default_workspace_id, dependent: :nullify
  has_many :guests, dependent: :destroy
  has_many :event_logs, as: :entity, dependent: :destroy
  has_many :company_cache_keys, dependent: :destroy
  has_many :helpdesk_faqs, dependent: :destroy
  has_many :snippets, dependent: :destroy
  has_many :tasks, dependent: :destroy
  has_many :task_checklists, dependent: :destroy
  has_many :analytics_report_templates, dependent: :destroy
  has_many :categories, dependent: :destroy
  has_many :email_templates, dependent: :destroy
  has_many :valid_email_extensions, dependent: :destroy
  has_many :automated_task_groups, dependent: :destroy

  has_one :admin_overview_customization, dependent: :destroy
  has_one :business_hour, dependent: :destroy
  has_many :quick_view_filters, dependent: :destroy
  has_many :scheduled_tasks, dependent: :destroy
  has_many :custom_surveys, dependent: :destroy
  has_many :reports, dependent: :destroy
  has_many :responses, class_name: 'CustomSurvey::Response', dependent: :destroy
  has_many :groups

  validates :name, uniqueness: { scope: :company_id, message: 'must be unique per company'}

  after_create :create_default_ticket_list_columns, :create_new_workspace_activity, :create_default_workspace_categories
  after_create :create_default_checklists, :create_quick_view_filter_options
  after_create :create_default_valid_email_extensions
  after_create :create_default_email_templates
  after_create :create_workspace_agents

  before_destroy :remove_event_logs
  after_commit :create_default_helpdesk_settings, on: :create, unless: Proc.new { |w| w.company&.is_sample_company? }
  after_commit :create_company_mailers, on: :create, unless: Proc.new { |w| w.company&.is_sample_company? }
  after_commit :create_default_custom_form, on: :create, unless: Proc.new { |w| w.company&.is_sample_company? }
  after_commit :send_pusher_events, on: [:destroy], unless: Proc.new { |w| w.company&.is_sample_company? }
  after_commit :create_default_custom_surveys, on: :create, unless: Proc.new { |w| w.company&.is_sample_company? }
  after_save -> { delete_options_cache_keys('workspace_options') }
  after_update -> { delete_options_cache_keys('custom_form_options') }
  after_destroy -> { delete_options_cache_keys('workspace_options') }

  after_update_commit :create_workspace_setting_activity
  before_destroy :nullify_dependent_groups
  after_destroy :delete_workspace_default_group_cache_keys
  after_update :check_quick_view_filter, if: :saved_change_to_name?
  after_commit -> { check_quick_view_filter(true) }, on: :destroy
  after_create :create_helpdesk_email_format
  after_create :create_default_business_hour
  after_commit :create_automated_task_groups, on: :create, unless: Proc.new { |w| w.company&.is_sample_company? }

  def set_params(current_user_id, params)
    @current_user_id = current_user_id
    @params = params
  end

  def destroy_dependent_help_tickets
    tickets = self.help_tickets
                  .includes(:help_ticket_activities, :help_ticket_comments)
                  .includes(custom_form_values: :custom_form_attachment)

    options = { skip_ticket_event_trigger: true, skip_cfv_event_trigger: false }
    bulk_delete_tickets(tickets, options)
  end

  def create_helpdesk_email_format
    HelpdeskEmailFormat.create(
      workspace_id: self.id,
      company_id: self.company_id
    )
  end

  def ticket_list_column
    self.ticket_list_columns.find_by(user_id: nil)
  end

  def ticket_list_column=(value)
    self.ticket_list_column.destroy! if self.ticket_list_column
    self.ticket_list_columns << value
  end

  def create_company_mailers
    DefaultMailer.find_each do |default_mailer|
      next if default_mailer.event == "survey_sent"

      company_mailer = CompanyMailer.find_or_create_by(default_mailer: default_mailer, workspace: self)
      company_mailer.update_automated_task
      company_mailer.automated_task&.save!
    end
  end

  def create_default_helpdesk_settings
    by_default_enabled_settings = [
      'include_workspace_in_help_center',
      'allow_public_custom_forms_to_logged_out_users',
      'allow_self_action_notification'
    ]
    DefaultHelpdeskSetting.find_each do |setting|
      if setting.setting_type == "select_new_ui_for_help_center"
        workspaces = company.workspaces.where.not(id: self.id)
        if workspaces.count > 0
          setting_enabled = workspaces.first.helpdesk_settings.includes(:default_helpdesk_setting)
          .find_by(default_helpdesk_settings: { setting_type: 'select_new_ui_for_help_center' }).enabled
        else
          setting_enabled = true
        end
      elsif by_default_enabled_settings.include?(setting.setting_type)
        setting_enabled = true
      elsif quick_settings_types.include?(setting.setting_type)
        quick_setting = self.helpdesk_settings.includes(:default_helpdesk_setting)
                                        .find_by(default_helpdesk_settings: { setting_type: setting.setting_type})
        setting_enabled = quick_setting&.enabled || false
      end
      helpdesk_setting = HelpdeskSetting.find_or_create_by(
        workspace: self,
        default_helpdesk_setting_id: setting.id,
        company: self.company
      )
      helpdesk_setting.assign_attributes(enabled: setting_enabled)
      if setting.setting_type == 'allow_self_action_notification'
        helpdesk_setting.assign_attributes(selected_option: "admins_agents, other_users")
      end
      if setting.setting_type == "response_to_closed_ticket" && !helpdesk_setting.selected_option
        helpdesk_setting.assign_attributes(selected_option: "add_comment")
      end
      if setting.setting_type == "customize_ticket_number_for_help_desk" && !helpdesk_setting.options.present? 
        helpdesk_setting.assign_attributes(options: { custom_prefix: "GEN", custom_ticket_number: 1 })
      end
      helpdesk_setting.save!
    end
  end

  def create_default_custom_form
    # We only want to create a default form if we are creating the default workspace
    if self.company.workspaces.count == 1
      ActiveRecord::Base.transaction do
        template ||= CustomFormTemplate.find_by(form_name: "IT General Request")
        if template.present?
          attrs = template.attributes.except('id', 'created_at', 'updated_at', 'image_url', 'description')
          attrs['company'] = self.company
          attrs['workspace'] = self
          attrs['form_name'] = 'IT General Request'
          attrs['default'] = true
          custom_form = self.custom_forms.find_by(default: true) || self.custom_forms.find_or_initialize_by(attrs)
          if !custom_form.id? && self.custom_forms.pluck(:id).compact.blank?
            email = "#{self.name.camelcase.gsub(/\s/, '').underscore}@#{company.subdomain}.#{Rails.application.credentials.root_domain}"
            custom_form.helpdesk_custom_form = HelpdeskCustomForm.new(email: email, show_in_open_portal: true)
            custom_form.save!
            everyone = self.company.groups.find_by(name: 'Everyone')
            fields = template.custom_form_field_templates.order('order_position ASC')
            fields.each_with_index do |field, idx|
              attrs = field.attributes.except('id', 'created_at', 'updated_at', 'custom_form_template_id')
              attrs[:custom_form_id] = custom_form.id
              attrs[:order_position] = idx
              if field.name === "created_by"
                attrs[:private] = false
              else
                attrs[:private] = ["people_list", "asset_list", "location_list", "contract_list", "vendor_list", "telecom_list"].include?( attrs["field_attribute_type"] )
              end
              new_field = custom_form.custom_form_fields.create(attrs)
              field_position = field.field_position_template
              if field_position
                FieldPosition.create(custom_form_field_id: new_field.id, position: field_position.position)
              end
            end
            custom_form.save!
          end
          ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
          custom_form
        end
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end

  def send_pusher_events
    guids = {}
    current_user = CompanyUser.find_by_cache(id: current_user_id)&.user
    Pusher.trigger("user=#{current_user.guid}", "workspaces-updated", { }) if current_user&.super_admin?
    self.privileges.includes(contributor: [:guest, :company_user, :group]).where(permission_type: "write").find_each do |priv|
      ids = priv.contributor.contributor_ids_only_users
      Contributor.includes(company_user: :user).where(id: ids).find_each do |contributor|
        user = contributor.company_user.user
        Rails.logger.debug("trigger-sent: user=#{user.guid}/workspaces-updated");
        Pusher.trigger("user=#{user.guid}",
                       "workspaces-updated",
                       { }) unless guids[user.guid]
        guids[user.guid] = true
      end
    end
  end

  def create_workspace_agents
    if self.is_first_workspace
      default_workspace_agent = Group.find_by(company: self.company, name: "Help Desk Agents")
      default_workspace_agent.workspace_id = self.id
      default_workspace_agent.save!
    elsif current_user_id.present?
      workspace_name = self.name == 'Help Desk' ? "#{self.name} Agents (New)" : "#{self.name} Agents"
      agent_group = Group.find_or_create_by(company: self.company, name: workspace_name)
      agent_group.workspace_id = self.id
      agent_group.create_contributor
      agent_group.default = true
      agent_group.save!
      agent_group.contributor.privileges.destroy_all
      privileges = [
        { name: 'MobileApp', permission_type: 'write' },
        { name: 'HelpTicket', permission_type: 'write', workspace_id: self.id }
      ]
      privileges.each do |privilege|
        created_privilege = agent_group.contributor.privileges.create(privilege)
        GroupPrivilege.create(name: created_privilege.name, permission_type: created_privilege.permission_type)
      end
      company_user = CompanyUser.find_by_cache(id: current_user_id)
      GroupMember.create!(contributor: company_user.contributor || self.company.admin_company_users.first.contributor, group: agent_group)
    end
  end

  def create_default_ticket_list_columns
    columns = [
      {
        :field_name=>"ticket_number",
        :name=>"ticket_number",
        :description=>"Ticket Number",
        :active=>false
      },
      {
        :field_name=>"assigned_to",
        :name=>"people_list",
        :description=>"Assigned To",
        :active=>false,
        :field_type=>"people_list"
      },
      {
        :field_name=>"created_by",
        :name=>"people_list",
        :description=>"Created By",
        :active=>false,
        :field_type=>"people_list"
      },
      {
        :field_name=>"status",
        :name=>"status",
        :description=>"Status",
        :active=>false,
        :field_type=>"status"
      },
      {
        :field_name=>"priority",
        :name=>"priority",
        :description=>"Priority",
        :active=>false,
        :field_type=>"priority"
      },
      {
        :field_name=>"subject",
        :name=>"text",
        :description=>"Subject",
        :active=>false,
        :field_type=>"text"
      }
    ]
    self.company.ticket_list_columns.create(columns: columns, workspace: self)
  end

  def create_workspace_setting_activity
    if current_user_id.present?
      activity_type = params[:action]
      event_log_service = EventLogService.new(self, current_user_id, activity_type)
      if params.present? && params['action'] != 'create'
        event_log_service.workspace_activity_data
      end
    end
  end

  def create_new_workspace_activity
    if current_user_id.present?
      event_log_service = EventLogService.new(self, current_user_id, "created")
      if params.present? && params['action'] == 'create'
        event_log_service.create_new_workspace_activity
      end
    end
  end

  def create_default_workspace_categories
    if self.categories.empty?
      categories = []
      categories = DefaultCategory.all.map do |cat|
        {
          name: cat.name,
          company_id: self.company_id,
          workspace_id: self.id,
          module_type: 'helpdesk',
        }
      end
      Category.insert_all(categories)
    end
  end

  def create_default_checklists
    default_checklists = {
      'Sample Offboarding User' => [
        "Backup user data folders",
        "Backup user email",
        "Disable user account",
        "Remove devices from email sync if applicable",
        "Collect company's equipment if applicable",
        "Provide access to user's data to manager",
        "Verify all user's data is accessible"
      ],
      'Sample Onboarding User' => [
        'Order new laptop or desktop with monitor(s)',
        'Create new user account using department user template',
        'Assign M365 license as required',
        'Setup new laptop or desktop for new user',
        'Setup remote access',
        'Create new hire equipment sign off form',
        'Setup workspace for new hire',
        'Train new user on new computer and accessing company data',
        'Return signed equipment sign off to HR'
      ],
    }

    default_checklists.each do |checklist, task_descriptions|
      task_ids = task_descriptions.map do |desc|
        begin
          task = Task.create(
            description: desc,
            priority: 'low',
            company: self.company,
            workspace: self
          )
          task.id if task.persisted?
        rescue => e
          Rails.logger.warn "Error creating task: #{e.message}"
          next
        end
      end.compact

      TaskChecklist.create(
        name: checklist,
        task_ids: task_ids,
        company: self.company,
        workspace: self
      )
    rescue => e
      Task.where(id: task_ids).destroy_all
      Rails.logger.warn "Error creating task checklist: #{e.message}"
      next
    end
  end

  def create_default_valid_email_extensions
    extensions_data = [
      { category_type: :html_file_types, extensions: [".html"] },
      { category_type: :image_types, extensions: [".jpg", ".jpeg", ".png", ".gif"] },
      { category_type: :document_types, extensions: [".doc", ".xls", ".docx", ".xlsx", ".pdf", ".msg", ".txt", ".ppt", ".pptx", ".tiff", ".csv"] },
      { category_type: :video_types, extensions: [".mov", ".mp4"] },
      { category_type: :compression_types, extensions: [] },
      { category_type: :audio_types, extensions: [] },
      { category_type: :misc_types, extensions: [] }
    ]

    extensions_data.each do |extension_data|
      begin
        ValidEmailExtension.create!(
          extension_data.merge(workspace: self, company: self.company)
        )
      rescue => e
        Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
        Rails.logger.warn "Error creating valid email extensions: #{e.message}"
        next
      end
    end
  end

  def create_quick_view_filter_options
    filters_data = [
      {
        title: "My Active Tickets",
        name: "My Active Tickets",
        active: false,
        id: 1,
        order: 1,
        is_default: true
      },
      {
        title: "All Active Tickets",
        name: "All Active Tickets",
        active: false,
        id: 2,
        order: 2,
        is_default: true
      },
      {
        title: "Unassigned",
        name: "Unassigned",
        active: false,
        id: 3,
        order: 3,
        is_default: true
      },
      {
        title: "Closed",
        name: "Closed",
        active: false,
        id: 4,
        order: 4,
        is_default: true
      }
    ]

    service_option = ServiceOption.find_by(service_name: "help_tickets/drafts", status: true)

    if !service_option.present?
      closed_filter = filters_data.find { |f| f[:name] == "Closed" }
      if closed_filter
        closed_filter[:id] = 5
        closed_filter[:order] = 5
      end

      filters_data << {
        title: "Drafts",
        name: "Drafts",
        active: false,
        id: 4,
        order: 4,
        is_default: true
      }

    end

    QuickViewFilter.create(company_id: self.company_id, filters_data: filters_data, workspace_id: self.id)
  end

  def remove_event_logs
    EventLog.where(workspace_id: self.id).destroy_all
  end

  def workspace_agents
    if is_cache_enabled?("workspace_default_group")
      key = "workspace_default_group_cache_#{self.id}"
      Rails.cache.fetch(key, :expires_in => 8.hours) do
        groups.find_by(default: true)
      end
    else
      groups.find_by(default: true)
    end
  end

  def workspace_agent_company_users
    contributor_ids = workspace_agents.contributor.contributor_ids_only_users
    CompanyUser.where(contributor_id: contributor_ids)
  end

  def nullify_dependent_groups
    group = self.workspace_agents.as_json
    if group.present?
      group['workspace_name'] = self.name

      if self.workspace_agents.name == "Help Desk Agents"
        self.workspace_agents.update(workspace_id: nil)
      else
        self.workspace_agents.update(workspace_id: nil, default: false)
      end

      if current_user_id.present?
        event_log_service = EventLogService.new(group, current_user_id, 'updated')
        event_log_service.group_activity_data
      end
    end
  end

  def quick_settings_types
    [
      'allow_incoming_requests',
      'allow_non_user_to_get_update_about_their_tickets',
      'only_include_last_email_response_to_ticket_comment',
      'customize_ticket_number_for_help_desk',
      'allow_followers_see_tickets'
    ]
  end

  def create_default_custom_surveys
    CustomSurveys::SurveyCreator.new(id).call
  end

  def create_automated_task_groups
    AutomatedTasks::TaskTemplate.find_each do |template|
      automated_task_groups.create!(
        name: "#{template.name} Group",
        workspace_id: self.id,
        company_id: self.company_id,
        task_template_id: template.id
      )
    end
  end

  private
  def create_default_business_hour
    default_hour = self.company.business_hours.find_by(workspace_id: nil)
    hash = {
      company_id: self.company_id,
      schedule: default_hour.schedule,
      description: default_hour.description,
      timezone: default_hour.timezone,
      holidays: default_hour.holidays
    }
    self.create_business_hour(hash)
  end
end
