class Integrations::Sophos::Config < ApplicationRecord
  include CompanyCache
  include DiscoveryToolsLogs

  self.table_name = "sophos_configs"
  attr_accessor :skip_callbacks

  belongs_to :company
  belongs_to :company_user
  has_one :company_integration, as: :integrable, dependent: :destroy
  has_one :sophos_tenant, class_name: 'Integrations::Sophos::Tenant', foreign_key: 'sophos_config_id', dependent: :destroy
  validates :client_id, :client_secret, :access_token, :company_id, presence: true
  validates_uniqueness_of :company_id, message: "Sophos is already integrated"

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new { |app| app.skip_callbacks }
  after_update :create_credentials_update_log
  before_destroy :destroy_integration_data
  enum import_type: [:managed_asset, :discovered_asset]

  def create_comp_intg_and_execute_job
    sophos_integration = Integration.find_by(name: 'sophos')
    comp_intg = CompanyIntegration.find_or_initialize_by(company_id: company_id,
                                                         integrable_type: 'Integrations::Sophos::Config',
                                                         integrable_id: id,
                                                         integration_id: sophos_integration.id)

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
  end

  def destroy_integration_data
    self.company.discovered_assets.sophos.where.not(status: :imported).destroy_all
  end

  def create_credentials_update_log
    if self.saved_changes?
      change_log = []
      if self.saved_changes.key?(:client_id)
        change_log << element_data("Client ID", self.saved_changes[:client_id]&.first, self.saved_changes[:client_id]&.last)
      end
      if self.saved_changes.key?(:client_secret)
        change_log << element_data("Client Secret", mask_value(self.saved_changes[:client_secret]&.first), mask_value(self.saved_changes[:client_secret]&.last))
      end
      create_asset_connector_log(:credentials_updated, :sophos, change_log, :successful, company_user_id, company_id) unless change_log.empty?
    end
  end
end
