class DiscoveredAsset < ActiveRecord::Base
  include Manufacturers
  include IntegrationPrecedence
  include IgnoreExistingAttrWithLowerPrecedence
  include SanitizesIpAddress
  include CompanyCache

  attr_accessor :skip_callbacks

  has_many :asset_softwares, dependent: :destroy
  has_many :asset_user_accounts, dependent: :destroy
  has_many :system_details, dependent: :destroy
  has_many :asset_sources, dependent: :destroy
  belongs_to :unrecognized_asset_type, class_name: 'DiscoveredAssetType', foreign_key: 'discovered_asset_type_id'

  belongs_to :company
  belongs_to :managed_asset, optional: true # unscoped association
  belongs_to :probe_location, optional: true
  belongs_to :integration_location, class_name: 'Integrations::Location', foreign_key: 'integrations_locations_id'
  
  has_many :xml_discovery_logs, dependent: :destroy
  has_many :cloud_asset_attributes, dependent: :destroy
  has_one  :google_project, class_name: "Integrations::GoogleAssets::Project", dependent: :nullify
  has_one  :discovered_assets_hardware_detail, dependent: :destroy

  enum source: [:probe, :selfonboarding, :agent, :meraki, :ubiquiti, :aws, :azure, :google, :ms_intune, :azure_ad_devices, :kaseya, :kandji, :jamf_pro, :mosyle, :google_workspace, :sophos]
  enum status: [:ready_for_import, :incomplete, :imported, :ignored, :unrecognized]
  enum discovered_asset_type: [:device, :client, :sm_device]

  before_validation :set_default_serial_number, if: Proc.new { |asset| asset.machine_serial_no.nil? }
  before_validation :sanitize_ip_address, if: Proc.new { |asset| asset.ip_address.present? && asset.ip_address.include?('%') }
  validates_uniqueness_of :managed_asset_id, :allow_nil => true, :allow_blank => true
  validates_uniqueness_of :machine_serial_no, scope: :company, case_sensitive: false, :allow_blank => true

  after_commit :update_managed_asset, on: [:create, :update], if: Proc.new { |u| !u.skip_callbacks }

  def as_json(options = nil)
    super.merge(
      asset_softwares: asset_softwares&.sort_by(&:name).as_json || [],
      asset_sources_data: asset_sources_data,
      asset_sources: asset_sources.pluck(:source),
      cloud_asset_attributes: cloud_asset_attributes,
      hardware_detail: discovered_assets_hardware_detail
    )
  end

  def self.search_text(query)
    sql = """
      discovered_assets.display_name ilike ? OR
        discovered_assets.machine_serial_no ilike ? OR
        discovered_assets.ip_address ilike ? OR
        discovered_assets.asset_type ilike ? OR
        array_to_string(discovered_assets.mac_addresses, '||') ilike ?
    """
    where(sql, "%\\#{query}%", "%\\#{query}%", "%\\#{query}%", "%\\#{query}%", "%\\#{query}%")
  end

  def set_default_serial_number
    self.machine_serial_no = ''
  end

  def update_managed_asset
    return if transaction_include_any_action?([:create]) && ["agent", "selfonboarding"].include?(source)
    if managed_asset.present?
      @asset = self.managed_asset

      mac_addresses_data = mac_addresses.present? ? mac_addresses : @asset.mac_addresses
      ip_data = ip_address.present? ? ip_address : @asset.ip_address
      manufacturer_data = manufacturer.present? && has_manufacturer_precedence? ? manufacturer : @asset.manufacturer
      os_data = os.present? ? os : @asset.operating_system
      serial_data = machine_serial_no.present? ? machine_serial_no : @asset.machine_serial_number
      details_data = optional_details.present? ? optional_details : @asset.details
      system_up_time_data = system_up_time.present? ? system_up_time : @asset.system_up_time

      #update all softwares and user accounts for managed asset.
      self.asset_softwares.update_all(managed_asset_id: @asset.id)
      self.asset_user_accounts.update_all(managed_asset_id: @asset.id)

      @asset.assign_attributes(
        name: asset_name,
        mac_addresses: mac_addresses_data,
        ip_address: ip_data,
        manufacturer: manufacturer_data,
        operating_system: os_data,
        machine_serial_number: serial_data || "",
        details: details_data,
        source: self.source,
        system_up_time: system_up_time_data,
        firmware: self.firmware,
        lower_precedence: lower_precedence
      )

      if self.integration_location&.location_id.present? && !has_manually_updated_location?
        @asset.location_id = self.integration_location.location_id
      end

      #update all asset  sources for managed asset.
      self.asset_sources.update_all(managed_asset_id: @asset.id)

      if @asset.save(validate: false)
        hardware_detail = @asset.hardware_detail

        #update managed asset hardware details
        if hardware_detail.present? && self.discovered_assets_hardware_detail.present?
          valid_keys = hardware_detail.attributes.keys - ["id", "created_at", "updated_at", "managed_asset_id"]
          valid_keys.each do |key|
            next if self.discovered_assets_hardware_detail[key.to_sym].blank?
            hardware_detail[key.to_sym] = self.discovered_assets_hardware_detail[key.to_sym]
          end

          hardware_detail.lower_precedence = lower_precedence
          hardware_detail.save!
        end
      end
    end
  end

  def managed_asset
    ManagedAsset.unscoped { super }
  end
  
  private

  def has_manufacturer_precedence?
    return true unless source == "probe" && @asset.audits.any? { |audit| audit.audited_changes["manufacturer"] && audit.audited_changes["source"] != "probe" }
  end

  def has_manually_updated_location?
    @asset.audits.where(associated_type: "ComputerDetail").any? do |audit|
      audit.audited_changes["location_id"] && audit.audited_changes["source"] == "manually_added"
    end
  end

  def asset_sources_data
    asset_sources.map { |asset_source| (asset_source.asset_data).merge(updated_at: asset_source.updated_at) }
  end

  def asset_name
    # If the user manually modified the name, it will remain unchanged and will not be updated.
    if manually_added_attributes(@asset, "name")
      @asset.name
    else
      display_name.present? ? display_name : @asset.name
    end
  end

  def precedence_data
    { 
      asset_sources: managed_asset&.sources,
      current_source: self.source,
      discovered_asset: self,
      incoming_discovered_asset: self
    }
  end
end
