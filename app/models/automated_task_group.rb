class AutomatedTaskGroup < ApplicationRecord
  belongs_to :company
  belongs_to :workspace
  belongs_to :task_template, class_name: "AutomatedTasks::TaskTemplate", optional: true

  has_many :automated_tasks, class_name: 'AutomatedTasks::AutomatedTask', foreign_key: :automated_task_group_id, dependent: :nullify

  validates :name, :company_id, :workspace_id, presence: true

  def as_json(options = nil)
    super.merge(task_template: self.task_template_id)
  end
end
