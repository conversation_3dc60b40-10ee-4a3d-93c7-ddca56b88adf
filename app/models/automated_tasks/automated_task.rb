module AutomatedTasks
  class AutomatedTask < ApplicationRecord
    include DeleteAutomatedTask<PERSON><PERSON><PERSON>ey

    attr_accessor :form, :current_user_id, :params

    belongs_to :contributor
    belongs_to :workspace

    delegate :company, to: :workspace

    has_one :company_mailer, foreign_key: 'automated_tasks_automated_task_id'
    has_many :scheduled_automated_tasks, class_name: "ScheduledAutomatedTasks",
                                         foreign_key: 'automated_tasks_automated_task_id',
                                         dependent: :destroy
    has_one :custom_form, foreign_key: 'automated_tasks_automated_task_id', dependent: :nullify
    has_many :task_events, dependent: :destroy
    has_many :task_actions, dependent: :destroy

    has_many :task_criteria
    has_many :execution_dates
    has_many :execution_logs, class_name: "AutomatedTasks::ExecutionLog", foreign_key: 'automated_tasks_automated_task_id', dependent: :nullify

    belongs_to :msp_templates_automated_task, class_name: "Msp::Templates::AutomatedTask", optional: true
    belongs_to :automated_task_group, class_name: 'AutomatedTaskGroup', optional: true

    delegate :company, to: :workspace

    before_destroy :delete_task_recipients_keys
    after_save :create_custom_form_activity
    after_commit :create_automated_task_activity
    after_destroy :create_destroy_task_activity, :set_workspace_order

    def set_params(form, current_user_id, params, task_status = nil)
      @form = form
      @current_user_id = current_user_id
      @params = params
      @task_status = task_status
    end

    def event_type_ids
      ids = [ task_event.id ]
      detail = task_event.event_details.first
      return ids unless detail
      while detail.children.present?
        detail = detail.children.first
        ids << detail.event_subject_type.id
      end
      ids
    end

    def create_custom_form_activity
      if @form.present? && self.previous_changes.key?('id') && self.previous_changes['id'][0].nil?
        EventLogService.new(@form, current_user_id, 'updated').form_automated_task_activity
      end
    end

    def create_destroy_task_activity
      if @form.present?
        EventLogService.new(@form, current_user_id, 'updated', nil, nil, nil, nil, 'disabled').form_automated_task_activity
      end
    end

    def set_workspace_order
      tasks = self.workspace.automated_tasks.order(:order)
      tasks_to_update = tasks.where('automated_tasks_automated_tasks.order > ?', self.order)
      tasks_to_update.each do |task|
        task.update_column(:order, task.order - 1)
      end
    end

    def activity_type
      if params[:action] == 'update' && @task_status == 'enable'
        return 'enable'
      elsif params[:action] == 'destroy' && @task_status == 'disable'
        return 'disable'
      else
        return params[:action]
      end
    end

    def create_automated_task_activity
      if current_user_id.present?
        event_log_service = EventLogService.new(self, current_user_id, activity_type)
        if params.present? && activity_type != 'update'
          event_log_service.create_automated_task_activity
        else
          event_log_service.automated_task_updated_activity
        end
      end
    end
  end
end
