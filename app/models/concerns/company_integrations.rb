module CompanyIntegrations
  extend ActiveSupport::Concern

  included do
    has_many :company_integrations
    has_many :integrations, through: :company_integrations

    has_many :integrations_locations, class_name: 'Integrations::Location', foreign_key: 'company_id', dependent: :destroy
    has_many :integrations_apps, class_name: 'Integrations::App', foreign_key: 'company_id', dependent: :destroy
    has_many :integrations_users, class_name: 'Integrations::User', foreign_key: 'company_id', dependent: :destroy

    has_many :slack_configs, class_name: 'Integrations::Slack::Config', dependent: :destroy
    has_many :ms_teams_configs, class_name: 'Integrations::MsTeams::Config', dependent: :destroy
    has_many :aws_configs, class_name: 'Integrations::Aws::Config', dependent: :destroy
    has_one :aws_assets_config, class_name: 'Integrations::AwsAssets::Config', dependent: :destroy
    has_one :azure_ad_config, class_name: "Integrations::AzureAd::Config", dependent: :destroy
    has_one :azure_config, class_name: "Integrations::Azure::Config", dependent: :destroy
    has_one :azure_assets_config, class_name: "Integrations::AzureAssets::Config", dependent: :destroy
    has_one :azure_ad_assets_config, class_name: "Integrations::AzureAdAssets::Config", dependent: :destroy
    has_one :kaseya_config, class_name: "Integrations::Kaseya::Config", dependent: :destroy
    has_one :kandji_config, class_name: 'Integrations::Kandji::Config', dependent: :destroy

    has_one :gsuite_config, class_name: "Integrations::Gsuite::Config", dependent: :destroy
    has_one :gsuite_ad_config, class_name: "Integrations::GsuiteAd::Config", dependent: :destroy

    has_one :microsoft_config, class_name: "Integrations::Microsoft::Config", dependent: :destroy
    has_one :sage_accounting_config, class_name: "Integrations::SageAccounting::Config", dependent: :destroy
    has_one :okta_config, class_name: 'Integrations::Okta::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :one_login_config, class_name: "Integrations::OneLogin::Config", dependent: :destroy
    has_one :quickbooks_config, class_name: 'Integrations::Quickbooks::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :salesforce_config, class_name: "Integrations::Salesforce::Config", dependent: :destroy
    has_one :xero_config, class_name: 'Integrations::Xero::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :sage_intacct_config, class_name: 'Integrations::SageIntacct::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :netsuite_config, class_name: 'Integrations::Netsuite::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :meraki_config, class_name: 'Integrations::Meraki::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :ubiquiti_config, class_name: 'Integrations::Ubiquiti::Config', foreign_key: 'company_id', dependent: :destroy
    has_many :meraki_records

    has_many :one_login_users, class_name: 'Integrations::OneLogin::User', foreign_key: 'company_id', dependent: :destroy
    has_many :one_login_apps, class_name: 'Integrations::OneLogin::App', foreign_key: 'company_id', dependent: :destroy

    has_many :microsoft_users, class_name: 'Integrations::Microsoft::User', foreign_key: 'company_id', dependent: :destroy
    has_many :microsoft_apps, class_name: 'Integrations::Microsoft::App', foreign_key: 'company_id', dependent: :destroy
    has_many :microsoft_services, class_name: 'Integrations::Microsoft::Service', foreign_key: 'company_id', dependent: :destroy

    has_many :gsuite_apps, class_name: 'Integrations::Gsuite::App', foreign_key: 'company_id', dependent: :destroy
    has_many :gsuite_users, class_name: 'Integrations::Gsuite::App', foreign_key: 'company_id', dependent: :destroy

    has_many :okta_users, class_name: 'Integrations::Okta::User', foreign_key: 'company_id', dependent: :destroy
    has_many :okta_apps, class_name: 'Integrations::Okta::App', foreign_key: 'company_id', dependent: :destroy

    has_one :expensify_config, class_name: 'Integrations::Expensify::Config', foreign_key: 'company_id', dependent: :destroy

    has_one :bill_config, class_name: 'Integrations::Bill::Config', foreign_key: 'company_id', dependent: :destroy
    has_one :google_assets_config, class_name: "Integrations::GoogleAssets::Config", dependent: :destroy
    has_one :ms_intune_assets_config, class_name: "Integrations::MsIntuneAssets::Config", dependent: :destroy
    has_one :jamf_pro_config, class_name: "Integrations::JamfPro::Config", dependent: :destroy
    has_one :mosyle_config, class_name: "Integrations::Mosyle::Config", dependent: :destroy
    has_one :google_workspace_config, class_name: "Integrations::GoogleWorkspace::Config", dependent: :destroy
    has_one :sophos_config, class_name: "Integrations::Sophos::Config", dependent: :destroy
  end

end
