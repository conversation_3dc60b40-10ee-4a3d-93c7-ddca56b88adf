module SyncCompanyUserFields
  extend ActiveSupport::Concern
  include ReadReplicaDb

  included do
    def update_fields(discovered_user, staff_user, company_id, is_update_location = false)
      company, company_user, cu_custom_form = nil
      set_read_replica_db do
        company = Company.find_by_cache(id: company_id)
        company_user = CompanyUser.find_by_cache(user_id: staff_user.id, company_id: company.id) if company
        cu_custom_form = company_user&.custom_form
      end
      if company.present?
        title_field, department_field, location_field, supervisor_field = map_fields(cu_custom_form, company_user, is_update_location)
        update_title(discovered_user, title_field, company, company_user)
        update_department(discovered_user, department_field, company, company_user)
        update_supervisor(discovered_user, supervisor_field, company, company_user)
        update_location(discovered_user, location_field, company, company_user, cu_custom_form) if is_update_location && discovered_user.location_id.present? && location_field.present?
      end
    end

    private

    def update_title(discovered_user, title_field, company, company_user)
      if discovered_user.title.present? && title_field.present?
        value = set_read_replica_db do 
          title_field.custom_form_values.find_or_initialize_by(module_type: "CompanyUser", module: company_user, company: company)
        end
        value.value_str = discovered_user.title
        value.save!
      end
    end
    

    def update_department(discovered_user, department_field, company, company_user)
      if discovered_user.department.present? && department_field.present?
        value = set_read_replica_db do 
          department_field.custom_form_values.find_or_initialize_by(module_type: "CompanyUser", module: company_user, company: company)
        end
        value.value_str = discovered_user.department
        value.save!
      end
    end

    def update_supervisor(discovered_user, supervisor_field, company, company_user)
      if discovered_user.supervisor.present? && supervisor_field.present?
        supervisor_company_user = company.company_users.joins(:user).find_by('lower(users.email) = ?', discovered_user.supervisor.downcase)

        if supervisor_company_user.present?
          value = supervisor_field.custom_form_values.find_or_initialize_by(
            module_type: "CompanyUser", 
            module: company_user, 
            company: company,
            value_int: supervisor_company_user.contributor_id
          )
          value.save!
        end
      end
    end

    def update_location(discovered_user, location_field, company, company_user, cu_custom_form)
      matching_location = set_read_replica_db do
        company.locations
              .joins(custom_form_fields: :custom_form_values)
              .where('custom_form_fields.name = ?', 'name')
              .find_by('custom_form_values.value_str = ?', discovered_user.location.address)
      end
      
      return unless matching_location.present?

      existing_location_value = set_read_replica_db do 
        location_field.custom_form_values.find_by(module_id: company_user.id, value_int: matching_location.id)
      end

      if existing_location_value.nil?
        location_field.custom_form_values.create!(
        module_type: "CompanyUser",
        module: company_user,
        company: company,
        custom_form_id: cu_custom_form.id,
        value_int: matching_location.id
      )
      end
    end

    def map_fields(cu_custom_form, company_user, is_update_location)
      custom_form_fields, title_field, department_field, location_field, supervisor_field = nil
      set_read_replica_db do
        custom_form_fields = cu_custom_form.custom_form_fields
        title_field = custom_form_fields.find_by(name: 'title')
        department_field = custom_form_fields.find_by(name: 'department')
        location_field = custom_form_fields.find_by(name: 'location') if is_update_location
        supervisor_field = custom_form_fields.find_by(name: 'supervisor')
      end

      if title_field.nil? || department_field.nil?
        field_mapping = company_user.field_mapping
        if field_mapping.present?
          title_field_id = field_mapping.mapping["title"]
          department_field_id = field_mapping.mapping["department"]
          set_read_replica_db do
            title_field = custom_form_fields.find_by(id: title_field_id) if title_field.nil? && title_field_id.present?
            department_field = custom_form_fields.find_by(id: department_field_id) if department_field.nil? && department_field_id.present?
          end
        end
      end

      [title_field, department_field, location_field, supervisor_field]
    end
  end
end
