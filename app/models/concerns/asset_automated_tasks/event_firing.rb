module AssetAutomatedTasks
  module EventFiring
    extend ActiveSupport::Concern

    included do
      attr_accessor :before_attributes, :skip_asset_automated_task_processing

      before_save :record_before_attributes
      after_commit :fire_event_for_asset_automation
    end

    def skip_automated_tasks
      self.skip_asset_automated_task_processing = true
    end

    def process_automated_tasks
      self.skip_asset_automated_task_processing = false
    end

    def record_before_attributes
      self.before_attributes = nil
      self.before_attributes = self.class.find_by(id: self.id)&.attributes if self.id
    end

    def fire_event_for_asset_automation
      if !self.skip_asset_automated_task_processing
        AssetAutomatedTasks::EventRouting.perform_async(self.id, self.class.name)
      end
    end
  end
end
