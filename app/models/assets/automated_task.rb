module Assets
  class AutomatedTask < ApplicationRecord
    attr_accessor :form, :current_user_id, :params
    
    belongs_to :contributor
    belongs_to :company

    # has_one :company_mailer // to identify default tasks
    has_many :task_events, dependent: :destroy
    has_many :task_actions, dependent: :destroy
    after_commit :create_automated_task_activity
    after_destroy :set_tasks_order

    def set_params(form, current_user_id, params, task_status = nil)
      @form = form
      @current_user_id = current_user_id
      @params = params
      @task_status = task_status
    end

    def activity_type
      if params[:action] == 'update' && @task_status == 'enable'
        return 'enable'
      elsif params[:action] == 'destroy' && @task_status == 'disable'
        return 'disable'
      else
        return params[:action]
      end
    end

    def create_automated_task_activity
      if current_user_id.present?
        event_log_service = EventLogService.new(self, current_user_id, activity_type)
        if params.present? && activity_type != 'update'
          event_log_service.create_asset_automated_task_activity
        else
          event_log_service.asset_automated_task_updated_activity
        end
      end
    end

    def set_tasks_order
      tasks = self.company.assets_automated_tasks.order(:order)
      tasks_to_update = tasks.where('assets_automated_tasks.order > ?', self.order)
      tasks_to_update.each do |task|
        task.update_column(:order, task.order - 1)
      end
    end
  end
end
